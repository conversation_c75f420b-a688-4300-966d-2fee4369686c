{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\Profile.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\Profile.vue", "mtime": 1757558749942}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Profile.vue"], "names": [], "mappings": ";AA4KA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Profile.vue", "sourceRoot": "src/views/profile", "sourcesContent": ["<template>\n  <div class=\"profile-page\">\n    <AppHeader />\n    \n    <div class=\"container\">\n      <div class=\"profile-layout\">\n        <!-- 侧边栏 -->\n        <div class=\"sidebar\">\n          <div class=\"user-card\">\n            <div class=\"avatar\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"user-info\">\n              <h3>{{ userInfo?.name || '用户' }}</h3>\n              <p>{{ userInfo?.mobile }}</p>\n              <StatusTag :status=\"userInfo?.auditStatus\" type=\"audit\" />\n            </div>\n          </div>\n          \n          <nav class=\"nav-menu\">\n            <p @click=\"handleNavItemClick('/profile')\" class=\"nav-item router-link-active\">\n              <i class=\"el-icon-user\"></i>\n              个人资料\n            </p>\n            <p @click=\"handleNavItemClick('/profile/bids')\" class=\"nav-item\">\n              <i class=\"el-icon-price-tag\"></i>\n              我的出价\n            </p>\n            <p @click=\"handleNavItemClick('/profile/projects')\" class=\"nav-item\">\n              <i class=\"el-icon-folder\"></i>\n              我的项目\n            </p>\n          </nav>\n        </div>\n\n        <!-- 主内容 -->\n        <div class=\"main-content\">\n          <div class=\"content-header\">\n            <h2>个人资料</h2>\n          </div>\n\n          <div class=\"profile-content\">\n            <!-- 统计卡片 -->\n            <div class=\"stats-cards\">\n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.totalBids || 0 }}</div>\n                  <div class=\"stat-label\">总出价次数</div>\n                </div>\n              </div>\n              \n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.wonBids || 0 }}</div>\n                  <div class=\"stat-label\">中标次数</div>\n                </div>\n              </div>\n              \n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.participatedProjects || 0 }}</div>\n                  <div class=\"stat-label\">参与项目</div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 个人信息表单 -->\n            <div class=\"profile-form-section\">\n              <h3>基本信息</h3>\n              <el-form\n                ref=\"profileForm\"\n                :model=\"profileForm\"\n                :rules=\"profileRules\"\n                label-width=\"120px\"\n                class=\"profile-form\"\n              >\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"手机号\">\n                      <el-input v-model=\"profileForm.mobile\" disabled />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"姓名\" prop=\"name\">\n                      <el-input v-model=\"profileForm.name\" />\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"企业名称\" prop=\"companyName\">\n                      <el-input v-model=\"profileForm.companyName\" />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"联系人\" prop=\"contactPerson\">\n                      <el-input v-model=\"profileForm.contactPerson\" />\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n\n                <el-form-item label=\"企业地址\" prop=\"companyAddress\">\n                  <el-input\n                    v-model=\"profileForm.companyAddress\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"联系电话\" prop=\"contactPhone\">\n                  <el-input v-model=\"profileForm.contactPhone\" />\n                </el-form-item>\n\n                <el-form-item>\n                  <el-button type=\"primary\" @click=\"updateProfile\" :loading=\"updating\">\n                    保存修改\n                  </el-button>\n                  <el-button @click=\"resetForm\">重置</el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n\n            <!-- 密码修改 -->\n            <div class=\"password-section\">\n              <h3>修改密码</h3>\n              <el-form\n                ref=\"passwordForm\"\n                :model=\"passwordForm\"\n                :rules=\"passwordRules\"\n                label-width=\"120px\"\n                class=\"password-form\"\n              >\n                <el-form-item label=\"当前密码\" prop=\"oldPassword\">\n                  <el-input\n                    v-model=\"passwordForm.oldPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"新密码\" prop=\"newPassword\">\n                  <el-input\n                    v-model=\"passwordForm.newPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n                  <el-input\n                    v-model=\"passwordForm.confirmPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item>\n                  <el-button type=\"primary\" @click=\"changePassword\" :loading=\"changingPassword\">\n                    修改密码\n                  </el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { getMyStats } from '@/api/user'\nimport { changePassword } from '@/api/auth'\n\nexport default {\n  name: 'Profile',\n  data() {\n    // 确认密码验证\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.passwordForm.newPassword) {\n        callback(new Error('两次输入的密码不一致'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      stats: {},\n      profileForm: {\n        mobile: '',\n        name: '',\n        companyName: '',\n        companyAddress: '',\n        contactPerson: '',\n        contactPhone: ''\n      },\n      profileRules: {\n        name: [\n          { required: true, message: '请输入姓名', trigger: 'blur' }\n        ],\n        companyName: [\n          { required: true, message: '请输入企业名称', trigger: 'blur' }\n        ],\n        companyAddress: [\n          { required: true, message: '请输入企业地址', trigger: 'blur' }\n        ],\n        contactPerson: [\n          { required: true, message: '请输入联系人', trigger: 'blur' }\n        ],\n        contactPhone: [\n          { required: true, message: '请输入联系电话', trigger: 'blur' }\n        ]\n      },\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      passwordRules: {\n        oldPassword: [\n          { required: true, message: '请输入当前密码', trigger: 'blur' }\n        ],\n        newPassword: [\n          { required: true, message: '请输入新密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认新密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      updating: false,\n      changingPassword: false\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['userInfo'])\n  },\n  async mounted() {\n    await this.fetchStats()\n    this.initProfileForm()\n  },\n  methods: {\n    // 获取统计数据\n    async fetchStats() {\n      try {\n        const response = await getMyStats()\n        if (response.data.code === 200) {\n          this.stats = response.data.data\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error)\n      }\n    },\n\n    // 初始化个人信息表单\n    initProfileForm() {\n      if (this.userInfo) {\n        this.profileForm = {\n          mobile: this.userInfo.mobile || '',\n          name: this.userInfo.name || '',\n          companyName: this.userInfo.companyName || '',\n          companyAddress: this.userInfo.companyAddress || '',\n          contactPerson: this.userInfo.contactPerson || '',\n          contactPhone: this.userInfo.contactPhone || ''\n        }\n      }\n    },\n\n    // 更新个人资料\n    async updateProfile() {\n      try {\n        await this.$refs.profileForm.validate()\n      } catch (error) {\n        return\n      }\n\n      this.updating = true\n      try {\n        const result = await this.$store.dispatch('user/updateProfile', this.profileForm)\n        if (result.success) {\n          this.$message.success('个人资料更新成功')\n          // 更新用户信息\n          await this.$store.dispatch('auth/getUserInfo')\n        } else {\n          this.$message.error(result.message)\n        }\n      } catch (error) {\n        this.$message.error('更新失败，请稍后重试')\n      } finally {\n        this.updating = false\n      }\n    },\n\n    // 重置表单\n    resetForm() {\n      this.initProfileForm()\n    },\n\n    // 修改密码\n    async changePassword() {\n      try {\n        await this.$refs.passwordForm.validate()\n      } catch (error) {\n        return\n      }\n\n      this.changingPassword = true\n      try {\n        const response = await changePassword({\n          oldPassword: this.passwordForm.oldPassword,\n          newPassword: this.passwordForm.newPassword\n        })\n        \n        if (response.data.code === 200) {\n          this.$message.success('密码修改成功')\n          this.passwordForm = {\n            oldPassword: '',\n            newPassword: '',\n            confirmPassword: ''\n          }\n          this.$refs.passwordForm.resetFields()\n        } else {\n          this.$message.error(response.data.msg || '密码修改失败')\n        }\n      } catch (error) {\n        this.$message.error('密码修改失败，请稍后重试')\n      } finally {\n        this.changingPassword = false\n      }\n    },\n    handleNavItemClick(path) {\n      this.$router.push(path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-page {\n  min-height: 110vh;\n  background: $bg-color;\n}\n\n.container {\n  padding: 20px;\n}\n\n.profile-layout {\n  display: flex;\n  gap: 30px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.sidebar {\n  width: 280px;\n  flex-shrink: 0;\n\n  .user-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    padding: 30px 20px;\n    text-align: center;\n    margin-bottom: 20px;\n\n    .avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background: $primary-color;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      margin: 0 auto 20px;\n    }\n\n    .user-info {\n      h3 {\n        color: $text-primary;\n        margin-bottom: 10px;\n      }\n\n      p {\n        color: $text-secondary;\n        margin-bottom: 15px;\n      }\n    }\n  }\n\n  .nav-menu {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 15px 20px;\n      color: $text-regular;\n      text-decoration: none;\n      border-bottom: 1px solid $border-color;\n      transition: all 0.3s;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover,\n      &.router-link-active {\n        background: $primary-color;\n        color: white;\n      }\n\n      i {\n        margin-right: 10px;\n        font-size: 16px;\n      }\n    }\n  }\n}\n\n.main-content {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: $box-shadow;\n  overflow: hidden;\n\n  .content-header {\n    padding: 30px 30px 0;\n    border-bottom: 1px solid $border-color;\n\n    h2 {\n      color: $text-primary;\n      margin-bottom: 30px;\n    }\n  }\n\n  .profile-content {\n    padding: 30px;\n\n    .stats-cards {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 40px;\n\n      .stat-card {\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        padding: 25px;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        gap: 15px;\n\n        .stat-icon {\n          font-size: 32px;\n          opacity: 0.8;\n        }\n\n        .stat-info {\n          .stat-number {\n            font-size: 24px;\n            font-weight: 700;\n            margin-bottom: 5px;\n          }\n\n          .stat-label {\n            font-size: 14px;\n            opacity: 0.9;\n          }\n        }\n      }\n    }\n\n    .profile-form-section,\n    .password-section {\n      margin-bottom: 40px;\n\n      h3 {\n        color: $text-primary;\n        margin-bottom: 20px;\n        padding-bottom: 10px;\n        border-bottom: 2px solid $primary-color;\n        display: inline-block;\n      }\n    }\n\n    .profile-form,\n    .password-form {\n      max-width: 600px;\n    }\n  }\n}\n\n@media (max-width: $tablet) {\n  .profile-layout {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n\n    .user-card {\n      display: flex;\n      align-items: center;\n      text-align: left;\n      gap: 20px;\n\n      .avatar {\n        margin: 0;\n      }\n    }\n\n    .nav-menu {\n      display: flex;\n      overflow-x: auto;\n\n      .nav-item {\n        white-space: nowrap;\n        border-bottom: none;\n        border-right: 1px solid $border-color;\n\n        &:last-child {\n          border-right: none;\n        }\n      }\n    }\n  }\n}\n\n@media (max-width: $mobile) {\n  .container {\n    padding: 15px;\n  }\n\n  .profile-content {\n    padding: 20px !important;\n\n    .stats-cards {\n      grid-template-columns: 1fr;\n    }\n  }\n}\n</style>\n"]}]}