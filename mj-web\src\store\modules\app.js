import Cookies from 'js-cookie';

const app={
  state: {
    sidebar: {
      opened: !+Cookies.get('sidebarStatus'), //菜单展开-收缩按钮状态布尔值
      withoutAnimation: false                 //默认没有动画
    }
  },
  mutations: {
    TOGGLE_SIDEBAR: state => {
      if(state.sidebar.opened){
        Cookies.set('sidebarStatus',1);
      }else{
        Cookies.set('sidebarStatus',0);
      }
      // 每次切换完成后，更新当前状态值，为下一次能正确切换而改变opened值
      // 若当前操作完之后为关闭状态，则将当前opened更新为true，下次再次操作就会执行打开，后续将opened更新为false，往复循环
      state.sidebar.opened=!state.sidebar.opened;
      state.sidebar.withoutAnimation=false;
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
      Cookies.set('sidebarStatus',1);
      state.sidebar.opened=false;
      state.sidebar.withoutAnimation=withoutAnimation;
    }
  },
  actions: {
    ToggleSideBar: ({ commit }) => {
      commit('TOGGLE_SIDEBAR');
    },
    CloseSideBar: ({ commit }, { withoutAnimation }) => {
      commit('CLOSE_SIDEBAR',withoutAnimation);
    }
  }
}

export default app;