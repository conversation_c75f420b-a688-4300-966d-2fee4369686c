/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 13:36:21
 * @LastEditors: dlg
 * @LastEditTime: 2020-07-02 12:11:34
 */
import Cookies from 'js-cookie'

// 将token写入cookie
const TokenKey = 'Token'

export function getToken() {
    return Cookies.get(TokenKey)
}

export function setToken(token, expires) {
    return Cookies.set(Token<PERSON>ey, token, { expires: expires })
}

export function removeToken() {
    return Cookies.remove(TokenKey)
}


// 将expiresAt过期时间写入cookie
const expiresAtKey = 'ExpiresAt'

export function getExpires() {
    return Cookies.get(expiresAtKey)
}

export function setExpires(expiresAt, expires) {
    return Cookies.set(expiresAtKey, expiresAt, { expires: expires })
}

export function removeExpires() {
    return Cookies.remove(expiresAtKey)
}

// 将userInfo用户信息写入cookie
const userInfoKey = 'UserInfo'

export function getUserInfo() {
    return Cookies.get(userInfoKey)
}

export function setUserInfo(userInfo, expires) {
    // cookie存储对象时，必须转化为JSON字符串，否则读取不到对象里的属性值
    return Cookies.set(userInfoKey, JSON.stringify(userInfo), { expires: expires })
}

export function removeUserInfo() {
    return Cookies.remove(userInfoKey)
}