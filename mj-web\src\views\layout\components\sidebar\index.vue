<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 13:36:21
 * @LastEditors: dlg
 * @LastEditTime: 2020-08-25 11:58:51
-->
<template>
  <div class="sidebar-div">
    <div class="bars-toggle">
      <svg
        style="cursor: pointer;color:#a5acb3;"
        class="svg-icon"
        aria-hidden="true"
        width="64"
        height="64"
        :class="{'is-active':sidebar.opened}"
        @click="toggleSideBar"
      >
        <use xlink:href="#icon-menu" />
      </svg>
    </div>
    <!-- native属性表示是否使用本地，设为true则不会启用element-ui自定义的滚动条 -->
    <el-scrollbar :native="true" wrap-class="scrollbar-wrapper">
      <el-menu
        :show-timeout="200"
        :hide-timeout="200"
        :default-active="$route.path"
        :collapse="isCollapse"
        mode="vertical"
        background-color="#001528"
        text-color="#a5acb3"
        active-text-color="#409eff"
      >
        <side-bar-item
          v-for="route in permission_routers"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import SideBarItem from "./sideBarItem";

export default {
  components: {
    SideBarItem
  },
  // mapGetters 辅助函数仅仅是将 store 中的 getter 映射到局部计算属性
  computed: {
    // 使用对象展开运算符将 getter 混入 computed 对象中
    ...mapGetters(["permission_routers", "sidebar"]),
    isCollapse() {
      return !this.sidebar.opened;
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("ToggleSideBar");
    }
  }
};
</script>