<template>
  <div class="home-page">
    <AppHeader />
    
    <!-- 轮播横幅 -->
    <section class="hero-section">
      <div class="hero-content">
        <h1>新疆生产建设兵团第一师棉麻有限责任公司竞价平台</h1>
        <p>专业的棉副产品在线竞价交易平台，公开透明，安全可靠</p>
        <div class="hero-actions">
          <el-button type="primary" size="large" @click="scrollToAuctions">
            立即竞价
          </el-button>
          <el-button size="large" @click="$router.push('/register')" v-if="!isLoggedIn">
            免费注册
          </el-button>
        </div>
      </div>
    </section>

    <!-- 竞价项目列表 -->
    <section class="auctions-section" ref="auctionsSection">
      <div class="container">
        <div class="section-header">
          <h2>热门竞价</h2>
          <div class="filters">
            <el-select v-model="filters.status" placeholder="项目状态" clearable @change="fetchAuctions">
              <el-option label="即将开始" :value="0" />
              <el-option label="竞价中" :value="1" />
              <el-option label="已结束" :value="2" />
            </el-select>
            <el-select v-model="filters.categoryId" placeholder="商品分类" clearable @change="fetchAuctions">
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </div>
        </div>

        <div class="auctions-grid" v-loading="loading">
          <div
            v-for="auction in auctions"
            :key="auction.id"
            class="auction-card"
            @click="goToAuction(auction)"
          >

            <div class="auction-content">
              <StatusTag :status="auction.status" class="status-overlay" />
              <h3 class="auction-title">{{ auction.title }}</h3>
              <div class="auction-info">
                
                <!-- 已登录用户显示价格信息 -->
                <template v-if="isLoggedIn">
                  <div class="info-item">
                    <span class="label">起拍价</span>
                    <span class="value">¥{{ formatMoney(auction.startPrice) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">当前价</span>
                    <span class="value current-price">¥{{ formatMoney(auction.currentPrice) }}</span>
                  </div>
                </template>
                <!-- 未登录用户显示基本信息 -->
                <template v-else>
                  <div class="info-item">
                    <span class="label">数量</span>
                    <span class="value">{{ auction.quantity }}{{ auction.unit }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">商品类别</span>
                    <span class="value">{{ auction.productCategory }}</span>
                  </div>
                </template>
                <div class="info-item">
                  <span class="label">出价次数</span>
                  <span class="value">{{ auction.bidCount }}次</span>
                </div>
              </div>
              
              <div class="auction-time">
                <template v-if="auction.status === 0">
                  <span class="time-label">开始时间：</span>
                  <span class="time-value">{{ auction.startTime | formatTime('MM-DD HH:mm') }}</span>
                </template>
                <template v-else-if="auction.status === 1">
                  <span class="time-label">剩余时间：</span>
                  <CountdownTimer :end-time="auction.endTime" />
                </template>
                <template v-else>
                  <span class="time-label">结束时间：</span>
                  <span class="time-value">{{ auction.endTime | formatTime('MM-DD HH:mm') }}</span>
                </template>
              </div>
            </div>
          </div>
        </div>

        <div class="load-more" v-if="hasMore">
          <el-button @click="loadMore" :loading="loadingMore">加载更多</el-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getCategories, getPublicAuctionList } from '@/api/auction'

export default {
  name: 'Home',
  data() {
    return {
      stats: {},
      auctions: [],
      categories: [],
      filters: {
        status: '',
        categoryId: ''
      },
      loading: false,
      loadingMore: false,
      page: 1,
      pageSize: 12,
      hasMore: true
    }
  },
  computed: {
    ...mapGetters('auth', ['isLoggedIn'])
  },
  async mounted() {
    await Promise.all([
      this.fetchStats(),
      this.fetchCategories(),
      this.fetchAuctions()
    ])
  },
  methods: {
    // 获取统计数据
    async fetchStats() {
      // 这里可以调用统计API
      this.stats = {
        totalProjects: 156,
        totalMembers: 1280,
        totalBids: 8960,
        totalAmount: 2580
      }
    },

    // 获取商品分类
    async fetchCategories() {
      try {
        const response = await getCategories()
        if (response.data.code === 200) {
          this.categories = response.data.data || []
        }
      } catch (error) {
        console.error('获取分类失败:', error)
      }
    },

    // 获取竞价列表
    async fetchAuctions(reset = false) {
      if (reset) {
        this.page = 1
        this.auctions = []
        this.hasMore = true
      }

      this.loading = reset
      this.loadingMore = !reset

      try {
        let result

        if (this.isLoggedIn) {
          // 已登录用户：使用会员接口（包含价格信息和权限）
          result = await this.$store.dispatch('auction/fetchAuctionList', {
            page: this.page,
            pageSize: this.pageSize,
            ...this.filters
          })
        } else {
          // 未登录用户：使用公开接口（不包含价格信息）
          const requestData = {
            page: this.page,
            pageSize: this.pageSize,
            status: this.filters.status || '',
            categoryId: this.filters.categoryId || ''
          }

          const response = await getPublicAuctionList(requestData)
          if (response.data.code === 200) {
            result = { success: true, data: response.data.data }
          } else {
            result = { success: false, message: response.data.msg }
          }
        }

        if (result.success) {
          const newAuctions = result.data.list || []
          if (reset) {
            this.auctions = newAuctions
          } else {
            this.auctions.push(...newAuctions)
          }

          this.hasMore = newAuctions.length === this.pageSize
        }
      } catch (error) {
        console.error('获取竞价列表失败:', error)
      } finally {
        this.loading = false
        this.loadingMore = false
      }
    },

    // 加载更多
    loadMore() {
      this.page++
      this.fetchAuctions()
    },

    // 跳转到竞价详情
    goToAuction(auction) {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      this.$router.push(`/auction/${auction.id}`)
    },

    // 滚动到竞价区域
    scrollToAuctions() {
      this.$refs.auctionsSection.scrollIntoView({ behavior: 'smooth' })
    },

    // 格式化金额
    formatMoney(value) {
      if (!value && value !== 0) return '0'
      return Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }
  },
  watch: {
    filters: {
      handler() {
        this.fetchAuctions(true)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
}

// 英雄区域
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  text-align: center;

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 20px;
    }

    p {
      font-size: 20px;
      margin-bottom: 40px;
      opacity: 0.9;
    }

    .hero-actions {
      display: flex;
      gap: 20px;
      justify-content: center;
    }
  }
}

// 竞价区域
.auctions-section {
  padding: 60px 0;
  min-height: 500px;
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;

    h2 {
      font-size: 32px;
      color: $text-primary;
    }

    .filters {
      display: flex;
      gap: 15px;
    }
  }

  .auctions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
  }

  .auction-card {
    background: white;
    border-radius: 8px;
    box-shadow: $box-shadow;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .auction-content {
      position: relative;
      overflow: hidden;
      padding: 20px;
      .status-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
      }
      .auction-title {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: 15px;
        @include text-ellipsis;
      }

      .auction-info {
        margin-bottom: 15px;

        .info-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          .label {
            color: $text-secondary;
          }

          .value {
            font-weight: 600;
            color: $text-primary;

            &.current-price {
              color: $danger-color;
            }
          }
        }
      }

      .auction-time {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 15px;
        border-top: 1px solid $border-color;

        .time-label {
          color: $text-secondary;
          font-size: 14px;
        }

        .time-value {
          font-weight: 600;
          color: $text-primary;
        }
      }
    }
  }

  .load-more {
    text-align: center;
  }
}

@include mobile {
  .hero-section {
    padding: 60px 0;

    .hero-content {
      h1 {
        font-size: 32px;
      }

      p {
        font-size: 16px;
      }

      .hero-actions {
        flex-direction: column;
        align-items: center;
      }
    }
  }

  .stats-section {
    padding: 40px 0;

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;

      .stat-item .stat-number {
        font-size: 24px;
      }
    }
  }

  .auctions-section {
    padding: 40px 0;

    .section-header {
      flex-direction: column;
      gap: 20px;
      align-items: flex-start;

      .filters {
        width: 100%;
        justify-content: space-between;
      }
    }

    .auctions-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
}
</style>
