/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 13:36:21
 * @LastEditors: dlg
 * @LastEditTime: 2020-09-27 14:52:55
 */
import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken, setToken, getExpires, setExpires, getUserInfo, setUserInfo } from '@/utils/auth'
import { refreshToken } from "@/api/refreshToken"


// 是否正在刷新的标志
window.isRefreshing = false;
// 存储请求的数组
let cacheRequestArr = [];

// 将所有的请求都push到数组中,其实数组是[function(token){}, function(token){},...]
function cacheRequestArrHandle(cb) {
    cacheRequestArr.push(cb);
}
// 数组中的请求得到新的token之后自执行，用新的token去重新发起请求
function afreshRequest(token) {
    cacheRequestArr.map(cb => cb(token));
}
// 判断token是否即将过期
function isTokenExpired() {
    let curTime = new Date().getTime();
    let expiresTime = Number(getExpires()) - curTime;
    let minutesTime = new Date(expiresTime).getMinutes()
    if ((expiresTime >= 0 && minutesTime < 10) || (expiresTime < 0 && Math.abs(expiresTime) <= 1200000)) {
        return true
    }
    return false;
}

// 创建一个 axios 接口
const service = axios.create({
    baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
    timeout: 60000 // 请求超时时间设置
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        // console.log(config);
        // 在请求发送之前做一些处理
        if (store.getters.token) {
            // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
            config.headers['Authorization'] = getToken();

            // 判断token是否即将过期，且不是请求刷新token的接口
            if (isTokenExpired() && config.url !== '/admin/base/refresh') {
                // 所有的请求来了，先判断是否正在刷新token，
                // 如果不是，将刷新token标志置为true并请求刷新token.
                // 如果是，则先将请求缓存到数组中
                // 等到刷新完token后再次重新请求之前缓存的请求接口即可
                if (!window.isRefreshing) {
                    // 标志改为true，表示正在刷新
                    window.isRefreshing = true;
                    let userInfo = getUserInfo() ? JSON.parse(getUserInfo()) : '';
                    const data = {
                        uuid: userInfo.uuid,
                        expiresAt: Number(getExpires())
                    }
                    refreshToken(data).then(res => {
                        if (res.data.code == 200) {
                            // 更新cookie里的值
                            setToken(res.data.data.token, new Date(res.data.data.expiresAt));
                            setExpires(res.data.data.expiresAt, new Date(res.data.data.expiresAt));
                            setUserInfo(JSON.parse(getUserInfo()), new Date(res.data.data.expiresAt))
                                // 更新 store里的值
                            store.commit('SET_TOKEN', res.data.data.token);
                            store.commit('SET_EXPIRESAT', res.data.data.expiresAt);
                            // 将刷新的token替代老的token
                            config.headers['Authorization'] = getToken();
                            // 刷新token完成后重新请求之前的请求
                            afreshRequest(getToken())
                        } else {
                            Message({
                                message: "网络错误",
                                type: 'error',
                            })
                            store.dispatch('resetUserCookies').then(() => {
                                location.reload() // 为了重新实例化vue-router对象 避免bug
                            })
                        }
                    }).catch(err => {
                        console.log('refreshToken err =>' + err);
                        store.dispatch('resetUserCookies').then(() => {
                            location.reload() // 为了重新实例化vue-router对象 避免bug
                        })
                    }).finally(() => {
                        window.isRefreshing = false;
                    })
                    let retry = new Promise((resolve) => {
                        cacheRequestArrHandle((token) => {
                            config.headers['Authorization'] = token; // token为刷新完成后传入的token
                            // 将请求挂起
                            resolve(config)
                        })
                    })
                    return retry;
                } else {
                    let retry = new Promise((resolve) => {
                        cacheRequestArrHandle((token) => {
                            config.headers['Authorization'] = token; // token为刷新完成后传入的token
                            // 将请求挂起
                            resolve(config)
                        })
                    })
                    return retry;
                }
            } else {
                return config
            }
        }
        return config
    },
    error => {
        // 请求错误处理
        console.log(error)
        Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        const res = response.data;
        if (res.code == 401 || res.code == 402) {
            Message({
                message: res.msg,
                type: 'error',
                duration: 5 * 1000
            })
            store.dispatch('resetUserCookies').then(() => {
                location.reload() // 为了重新实例化vue-router对象 避免bug
            })
            return Promise.reject('error')
        }
        return response
    },
    error => {
        console.log(error)
        Message({
                message: "网络错误",
                type: 'error',
                duration: 5 * 1000
            })
            // return Promise.reject(error)
    }
)

export default service
