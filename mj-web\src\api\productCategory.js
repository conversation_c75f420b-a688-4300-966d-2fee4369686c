import request from '@/utils/request'

// 商品类别管理相关API

// 获取商品类别列表
export function getProductCategoryList(data) {
  return request({
    url: '/admin/productCategory/getProductCategoryList',
    method: 'post',
    data
  })
}

// 添加商品类别
export function addProductCategory(data) {
  return request({
    url: '/admin/productCategory/addProductCategory',
    method: 'post',
    data
  })
}

// 更新商品类别
export function updateProductCategory(data) {
  return request({
    url: '/admin/productCategory/addProductCategory',
    method: 'post',
    data
  })
}

// 删除商品类别
export function deleteProductCategory(data) {
  return request({
    url: '/admin/productCategory/deleteProductCategory',
    method: 'post',
    data
  })
}

// 根据ID获取商品类别详情
export function getProductCategoryById(data) {
  return request({
    url: '/admin/productCategory/getProductCategory',
    method: 'post',
    data
  })
}

// 更新商品类别状态
export function updateProductCategoryStatus(data) {
  return request({
    url: '/admin/productCategory/updateProductCategoryStatus',
    method: 'post',
    data
  })
}

// 获取商品类别下拉选项
export function getProductCategoryCondition() {
  return request({
    url: '/admin/productCategory/getProductCategoryCondition',
    method: 'post'
  })
}