package model

// 单位
type WxUser struct {
    ID          uint   `json:"id" grom:"primary_key"`
    NickName    string `json:"nickName" gorm:"comment:'昵称'"`
    AvatarUrl   string `json:"avatarUrl" gorm:"comment:'头像'"`
    Gender      int8   `json:"gender" gorm:"comment:'性别 0未知  1男  2女'"`
    Country     string `json:"country" gorm:"comment:'国家'"`
    Province    string `json:"province" gorm:"comment:'省'"`
    City        string `json:"city" gorm:"comment:'市'"`
    Language    string `json:"language" gorm:"comment:'语言 en英文  zh_CN简体中文  zh_TW繁体中文'"`
    CountryCode string `json:"countryCode" gorm:"comment:'区号'"`
    PhoneNumber string `json:"phoneNumber" gorm:"comment:'手机号'"`
    Openid      string `json:"openid" gorm:"comment:'微信唯一标识'"`
}

func (WxUser) TableName() string {
    return "wx_user"
}
