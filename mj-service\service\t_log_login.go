package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"time"

	"github.com/Gre-Z/common/jtime"
)

// @Title 添加登录日志
// @Description
// <AUTHOR>
// @Param user 	resp.LoginUserStruct	用户信息

func AddLoginLog(user resp.LoginUserStruct) {
	var loginLog model.TLogLogin
	loginLog.Username = user.Username
	loginLog.Nickname = user.NickName
	loginLog.LoginTime = jtime.JsonTime{time.Now()}
	loginLog.LoginMethod = constants.LOGIN_METHOD_WEB
	loginLog.CompanyCode = user.CompanyCode
	err := global.GVA_DB.Create(&loginLog).Error
	if err != nil {
		global.GVA_LOG.Error("登录成功，登录日志写入失败，错误:%v", err)
	}
}

// @Title 登录日志统计查询
// @Description
// <AUTHOR>
// @Param uuid 			string						用户唯一标识
// @Param logRequest 	request.TLogListRequest		查询条件
// @Return err 			error 						错误信息
// @Return list 		interface{} 				列表
// @Return total 		int 						总条数

func GetLoginLogList(uuid string, logRequest req.TLogListRequest) (err error, list interface{}, total int) {
	err, currentUser := GetCurrentUser(uuid)
	if err != nil {
		return err, list, total
	}

	limit := logRequest.PageSize
	offset := logRequest.PageSize * (logRequest.Page - 1)
	db := global.GVA_DB
	var loginLogList []resp.LoginLogList
	if logRequest.StartTime != "" {
		db = db.Where("date_format(login_time,'%Y-%m-%d') >= ?", logRequest.StartTime)
	}
	if logRequest.EndTime != "" {
		db = db.Where("? >= date_format(login_time,'%Y-%m-%d')", logRequest.EndTime)
	}
	// 如果不是系统管理员，限制只能看自己公司
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		db = db.Where("company_code = ?", currentUser.CompanyCode)
	}
	if len(logRequest.CompanyCode) > 0 {
		db = db.Where("company_code in (?)", logRequest.CompanyCode)
	}
	if logRequest.Username != "" {
		db = db.Where("instr(username,?)", logRequest.Username)
	}
	err = db.Find(&[]model.TLogLogin{}).Group("username").Count(&total).Error
	err = db.Select("username, nickname").Group("username").Limit(limit).Offset(offset).Find(&[]model.TLogLogin{}).Scan(&loginLogList).Order("login_time desc").Error
	for index, loginLog := range loginLogList {
		// 查询最后一次登录时间
		var timeLog model.TLogLogin
		db.Table("t_log_logins").Where("username = ?", loginLog.Username).Order("login_time desc").Last(&timeLog)
		loginLog.LoginTime = timeLog.LoginTime
		loginLog.Nickname = timeLog.Nickname
		// 查询登录次数
		var loginCount int
		db.Where("username = ?", loginLog.Username).Find(&[]model.TLogLogin{}).Count(&loginCount)
		loginLog.LoginCount = loginCount
		// 查询Web登录次数
		var loginMethodWeb int
		db.Where("username = ? and login_method = ?", loginLog.Username, constants.LOGIN_METHOD_WEB).Find(&[]model.TLogLogin{}).Count(&loginMethodWeb)
		loginLog.WebLogin = loginMethodWeb
		// 查询小程序登录次数
		var loginMethodApplets int
		db.Where("username = ? and login_method = ?", loginLog.Username, constants.LOGIN_METHOD_APPLETS).Find(&[]model.TLogLogin{}).Count(&loginMethodApplets)
		loginLog.AppletsLogin = loginMethodApplets
		loginLogList[index] = loginLog
	}
	list = loginLogList
	return err, list, total
}
