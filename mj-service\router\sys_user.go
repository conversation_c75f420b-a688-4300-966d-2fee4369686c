package router

import (
	v1 "auction-sys/api/v1"
	"auction-sys/middleware"

	"github.com/gin-gonic/gin"
)

func InitUserRouter(Router *gin.RouterGroup) {
	UserRouter := Router.Group("user").Use(middleware.JWTAuth())
	{
		UserRouter.POST("changePassword", v1.ChangePassword)     // 修改密码
		UserRouter.POST("uploadHeaderImg", v1.UploadHeaderImg)   // 上传头像
		UserRouter.POST("getUserList", v1.GetUserList)           // 分页获取用户列表
		UserRouter.POST("deleteUser", v1.DeleteUser)             // 删除用户
		UserRouter.POST("resetPassword", v1.ResetPassword)       // 重置密码
		UserRouter.POST("updateUserStatus", v1.UpdateUserStatus) // 修改用户状态
		UserRouter.POST("updateUser", v1.UpdateUser)             // 更新用户
		UserRouter.POST("updateUserAuth", v1.UpdateUserAuth)     // 更新用户角色
		UserRouter.POST("getPersonInfo", v1.GetPersonInfo)       // 个人信息
		UserRouter.POST("updatePersonInfo", v1.UpdatePersonInfo) // 修改个人信息
		UserRouter.POST("listSample", v1.GetListSample)          // 人员列表
		UserRouter.POST("wxInfo", v1.PostWxInfo)                 // 获取用户信息
		UserRouter.POST("wxlogin", v1.WxLogin)                   // 微信登录
		UserRouter.POST("setParams", v1.SetSettingParams)        // 微信登录
	}
}
