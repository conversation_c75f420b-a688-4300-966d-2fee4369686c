package v1

import (
	"auction-sys/global/response"
	"auction-sys/utils"

	"github.com/gin-gonic/gin"
)

// UploadFile
// @Summary 上传公司Logo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":200,"data":{},"msg":"上传成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"上传失败"}"
// @Router /base/uploadFile [post]
func UploadFile(c *gin.Context) {
	// 获取请求头

	_, header, err := c.Request.FormFile("file")
	if err != nil {
		response.FailWithMessage("上传文件失败", c)
	} else {
		// 文件上传后拿到文件路径
		err, filePath, _ := utils.Upload(header)

		if err != nil {
			response.FailWithMessage("上传文件失败", c)
		} else {
			data := make(map[string]string)
			data["imageUrl"] = filePath
			response.OkWithData(data, c)
		}
	}
}
