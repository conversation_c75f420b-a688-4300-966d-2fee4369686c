<template>
  <div class="profile-page">
    <AppHeader />
    
    <div class="container">
      <div class="profile-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
          <div class="user-card">
            <div class="avatar">
              <i class="el-icon-user"></i>
            </div>
            <div class="user-info">
              <h3>{{ userInfo?.name || '用户' }}</h3>
              <p>{{ userInfo?.mobile }}</p>
              <StatusTag :status="userInfo?.auditStatus" type="audit" />
            </div>
          </div>
          
          <nav class="nav-menu">
            <p @click="handleNavItemClick('/profile')" class="nav-item router-link-active">
              <i class="el-icon-user"></i>
              个人资料
            </p>
            <p @click="handleNavItemClick('/profile/bids')" class="nav-item">
              <i class="el-icon-price-tag"></i>
              我的出价
            </p>
            <p @click="handleNavItemClick('/profile/projects')" class="nav-item">
              <i class="el-icon-folder"></i>
              我的项目
            </p>
          </nav>
        </div>

        <!-- 主内容 -->
        <div class="main-content">
          <div class="content-header">
            <h2>个人资料</h2>
          </div>

          <div class="profile-content">
            <!-- 统计卡片 -->
            <div class="stats-cards">
              <div class="stat-card">
                <div class="stat-info">
                  <div class="stat-number">{{ stats.totalBids || 0 }}</div>
                  <div class="stat-label">总出价次数</div>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-info">
                  <div class="stat-number">{{ stats.wonBids || 0 }}</div>
                  <div class="stat-label">中标次数</div>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-info">
                  <div class="stat-number">{{ stats.participatedProjects || 0 }}</div>
                  <div class="stat-label">参与项目</div>
                </div>
              </div>
            </div>

            <!-- 个人信息表单 -->
            <div class="profile-form-section">
              <h3>基本信息</h3>
              <el-form
                ref="profileForm"
                :model="profileForm"
                :rules="profileRules"
                label-width="120px"
                class="profile-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="手机号">
                      <el-input v-model="profileForm.mobile" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="姓名" prop="name">
                      <el-input v-model="profileForm.name" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="企业名称" prop="companyName">
                      <el-input v-model="profileForm.companyName" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系人" prop="contactPerson">
                      <el-input v-model="profileForm.contactPerson" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="企业地址" prop="companyAddress">
                  <el-input
                    v-model="profileForm.companyAddress"
                    type="textarea"
                    :rows="3"
                  />
                </el-form-item>

                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input v-model="profileForm.contactPhone" />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="updateProfile" :loading="updating">
                    保存修改
                  </el-button>
                  <el-button @click="resetForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 密码修改 -->
            <div class="password-section">
              <h3>修改密码</h3>
              <el-form
                ref="passwordForm"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="120px"
                class="password-form"
              >
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    show-password
                  />
                </el-form-item>

                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    show-password
                  />
                </el-form-item>

                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    show-password
                  />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="changePassword" :loading="changingPassword">
                    修改密码
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getMyStats } from '@/api/user'
import { changePassword } from '@/api/auth'

export default {
  name: 'Profile',
  data() {
    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    return {
      stats: {},
      profileForm: {
        mobile: '',
        name: '',
        companyName: '',
        companyAddress: '',
        contactPerson: '',
        contactPhone: ''
      },
      profileRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' }
        ],
        companyAddress: [
          { required: true, message: '请输入企业地址', trigger: 'blur' }
        ],
        contactPerson: [
          { required: true, message: '请输入联系人', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' }
        ]
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      updating: false,
      changingPassword: false
    }
  },
  computed: {
    ...mapGetters('auth', ['userInfo'])
  },
  async mounted() {
    await this.fetchStats()
    this.initProfileForm()
  },
  methods: {
    // 获取统计数据
    async fetchStats() {
      try {
        const response = await getMyStats()
        if (response.data.code === 200) {
          this.stats = response.data.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },

    // 初始化个人信息表单
    initProfileForm() {
      if (this.userInfo) {
        this.profileForm = {
          mobile: this.userInfo.mobile || '',
          name: this.userInfo.name || '',
          companyName: this.userInfo.companyName || '',
          companyAddress: this.userInfo.companyAddress || '',
          contactPerson: this.userInfo.contactPerson || '',
          contactPhone: this.userInfo.contactPhone || ''
        }
      }
    },

    // 更新个人资料
    async updateProfile() {
      try {
        await this.$refs.profileForm.validate()
      } catch (error) {
        return
      }

      this.updating = true
      try {
        const result = await this.$store.dispatch('user/updateProfile', this.profileForm)
        if (result.success) {
          this.$message.success('个人资料更新成功')
          // 更新用户信息
          await this.$store.dispatch('auth/getUserInfo')
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        this.$message.error('更新失败，请稍后重试')
      } finally {
        this.updating = false
      }
    },

    // 重置表单
    resetForm() {
      this.initProfileForm()
    },

    // 修改密码
    async changePassword() {
      try {
        await this.$refs.passwordForm.validate()
      } catch (error) {
        return
      }

      this.changingPassword = true
      try {
        const response = await changePassword({
          oldPassword: this.passwordForm.oldPassword,
          newPassword: this.passwordForm.newPassword
        })
        
        if (response.data.code === 200) {
          this.$message.success('密码修改成功')
          this.passwordForm = {
            oldPassword: '',
            newPassword: '',
            confirmPassword: ''
          }
          this.$refs.passwordForm.resetFields()
        } else {
          this.$message.error(response.data.msg || '密码修改失败')
        }
      } catch (error) {
        this.$message.error('密码修改失败，请稍后重试')
      } finally {
        this.changingPassword = false
      }
    },
    handleNavItemClick(path) {
      this.$router.push(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 110vh;
  background: $bg-color;
}

.container {
  padding: 20px;
}

.profile-layout {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.sidebar {
  width: 280px;
  flex-shrink: 0;

  .user-card {
    background: white;
    border-radius: 8px;
    box-shadow: $box-shadow;
    padding: 30px 20px;
    text-align: center;
    margin-bottom: 20px;

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: $primary-color;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      margin: 0 auto 20px;
    }

    .user-info {
      h3 {
        color: $text-primary;
        margin-bottom: 10px;
      }

      p {
        color: $text-secondary;
        margin-bottom: 15px;
      }
    }
  }

  .nav-menu {
    background: white;
    border-radius: 8px;
    box-shadow: $box-shadow;
    overflow: hidden;

    .nav-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      color: $text-regular;
      text-decoration: none;
      border-bottom: 1px solid $border-color;
      transition: all 0.3s;

      &:last-child {
        border-bottom: none;
      }

      &:hover,
      &.router-link-active {
        background: $primary-color;
        color: white;
      }

      i {
        margin-right: 10px;
        font-size: 16px;
      }
    }
  }
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: $box-shadow;
  overflow: hidden;

  .content-header {
    padding: 30px 30px 0;
    border-bottom: 1px solid $border-color;

    h2 {
      color: $text-primary;
      margin-bottom: 30px;
    }
  }

  .profile-content {
    padding: 30px;

    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 40px;

      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 15px;

        .stat-icon {
          font-size: 32px;
          opacity: 0.8;
        }

        .stat-info {
          .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
          }

          .stat-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }
    }

    .profile-form-section,
    .password-section {
      margin-bottom: 40px;

      h3 {
        color: $text-primary;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid $primary-color;
        display: inline-block;
      }
    }

    .profile-form,
    .password-form {
      max-width: 600px;
    }
  }
}

@media (max-width: $tablet) {
  .profile-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;

    .user-card {
      display: flex;
      align-items: center;
      text-align: left;
      gap: 20px;

      .avatar {
        margin: 0;
      }
    }

    .nav-menu {
      display: flex;
      overflow-x: auto;

      .nav-item {
        white-space: nowrap;
        border-bottom: none;
        border-right: 1px solid $border-color;

        &:last-child {
          border-right: none;
        }
      }
    }
  }
}

@media (max-width: $mobile) {
  .container {
    padding: 15px;
  }

  .profile-content {
    padding: 20px !important;

    .stats-cards {
      grid-template-columns: 1fr;
    }
  }
}
</style>
