// @Title 公用组件
// @Description 请填写文件描述
// <AUTHOR> 2020/11/27 10:21

package utils

import (
	"auction-sys/global"
	"bytes"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"time"
)

// @Title Http发送Get请求
// @Description 函数的详细描述
// <AUTHOR> 2020/11/27 10:22
// @Param
// @Return
func Get(url string, params map[string]string, headers map[string]string) ([]byte, error) {
	//new request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return nil, errors.New("new request is fail ")
	}
	//add params
	q := req.URL.Query()
	if params != nil {
		for key, val := range params {
			q.Add(key, val)
		}
		req.URL.RawQuery = q.Encode()
	}
	//add headers
	if headers != nil {
		for key, val := range headers {
			req.Header.Add(key, val)
		}
	}
	//http client
	client := &http.Client{Timeout: 5 * time.Second}
	global.GVA_LOG.Info("Go GET URL : %s \n", req.URL.String())

	//发送请求
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close() //一定要关闭res.Body
	//读取body
	resBody, err := ioutil.ReadAll(res.Body) //把  body 内容读入字符串 s
	if err != nil {
		return nil, err
	}

	return resBody, nil
}

// @Title Http发送Post请求
// @Description 函数的详细描述
// <AUTHOR> 2020/11/27 10:23
// @Param
// @Return
//Post http post method
func Post(url string, body map[string]interface{}, params map[string]string, headers map[string]string) ([]byte, error) {
	//add post body
	var bodyJson []byte
	var req *http.Request
	if body != nil {
		var err error
		bodyJson, err = json.Marshal(body)
		if err != nil {
			global.GVA_LOG.Error(err.Error())
			return nil, errors.New("http post body to json failed")
		}
	}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(bodyJson))
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return nil, errors.New("new request is fail: %v \n")
	}
	req.Header.Set("Content-type", "application/json")
	//add params
	q := req.URL.Query()
	if params != nil {
		for key, val := range params {
			q.Add(key, val)
		}
		req.URL.RawQuery = q.Encode()
	}
	//add headers
	if headers != nil {
		for key, val := range headers {
			req.Header.Add(key, val)
		}
	}
	//http client
	client := &http.Client{Timeout: 5 * time.Second}
	global.GVA_LOG.Info("Go POST URL : %s \n", req.URL.String())

	//发送请求
	res, err := client.Do(req)
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return nil, err
	}
	defer res.Body.Close() //一定要关闭res.Body
	//读取body
	resBody, err := ioutil.ReadAll(res.Body) //把  body 内容读入字符串 s
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return nil, err
	}

	return resBody, nil
}
