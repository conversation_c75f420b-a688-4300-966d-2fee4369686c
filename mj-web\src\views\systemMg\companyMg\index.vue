<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-07-08 15:28:06
 * @LastEditors: dlg
 * @LastEditTime: 2020-09-29 15:11:28
-->
<template>
  <div class="app-container">
    <div style="margin-bottom: 20px; min-width: 360px">
      <span style="color: #606266; font-size: 14px">企业名称：</span>
      <el-input
        v-model="companyNameSearch"
        placeholder="请输入企业名称"
        style="width: 200px; margin-right: 12px"
        clearable
      />
      <el-button @click="getTableList" type="primary">查询</el-button>
    </div>
    <div>
      <div style="min-width: 460px">
        <el-button
          type="primary"
          @click="handleAddCom"
          v-if="permissBtn.indexOf('addCompany') !== -1"
        >添加企业</el-button>
        <el-button
          type="success"
          @click="batchEnable"
          v-if="permissBtn.indexOf('batchEnable') !== -1"
        >批量启用</el-button>
        <el-button
          type="info"
          @click="batchDisable"
          v-if="permissBtn.indexOf('batchDisable') !== -1"
        >批量禁用</el-button>
        <el-button
          type="danger"
          @click="batchDel"
          v-if="permissBtn.indexOf('batchDelete') !== -1"
        >批量删除</el-button>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="100%"
      style="width: 100%; margin-top: 30px"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="id" prop="id" />
      <el-table-column
        align="center"
        label="企业名称"
        width="200"
        show-overflow-tooltip
        prop="name"
      />
      <el-table-column
        align="center"
        label="邮政编码"
        min-width="100"
        prop="postcode"
      />
      <el-table-column
        align="center"
        label="企业地址"
        width="220"
        show-overflow-tooltip
        prop="address"
      />
      <el-table-column
        align="center"
        label="联系人"
        prop="personLiable"
      />
      <el-table-column
        align="center"
        label="手机号码"
        width="120"
        prop="telephone"
      />
      <el-table-column
        align="center"
        label="添加时间"
        sortable
        min-width="200"
        prop="createdAt"
      />
      <el-table-column
        align="center"
        fixed="right"
        label="认证状态"
        :filters="disArray"
        :filter-method="filterStatus"
        min-width="100"
        v-if="userInfo.isRoot === 1 ? true : false"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            class="mySwitch"
            active-text="开"
            inactive-text="关"
            @change="switchDisable(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center"
                       fixed="right"
                       label="操作"
                       width="300">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(scope.row)"
            v-if="permissBtn.indexOf('edit') !== -1"
          >编辑</el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(scope.row)"
            v-if="permissBtn.indexOf('del') !== -1"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer
      custom-class="myDrawer"
      :title="drawerTitle"
      :visible.sync="dialogVisible"
      direction="rtl"
    >
      <div class="drawerDiv" ref="drawerDiv">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          style="height: 100%; overflow: auto"
        >
          <el-form-item label="企业名称" prop="name" class="elFormItem">
            <el-input
              placeholder="请输入企业名称"
              v-model="ruleForm.name"
              :disabled="drawerTitle == '编辑企业' ? true : false"
            />
          </el-form-item>
          <el-form-item label="组织代码" prop="code" class="elFormItem">
            <el-input
              placeholder="请输入企业组织机构代码"
              v-model="ruleForm.code"
              :disabled="drawerTitle == '编辑企业' ? true : false"
            />
          </el-form-item>
          <el-form-item label="邮政编码" class="elFormItem" prop="postcode">
            <el-input
              placeholder="请输入邮政编码"
              v-model="ruleForm.postcode"
            />
          </el-form-item>
          <el-form-item label="企业地址" class="elFormItem">
            <el-input
              placeholder="请输入企业地址"
              v-model="ruleForm.address"
            />
          </el-form-item>
          <el-form-item label="联系人" prop="personLiable" class="elFormItem">
            <el-input
              placeholder="请输入联系人姓名"
              v-model="ruleForm.personLiable"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="telephone" class="elFormItem">
            <el-input
              placeholder="请输入手机号码"
              v-model="ruleForm.telephone"
              :disabled="
                drawerTitle == '编辑企业' && userInfo.isRoot !== 1
                  ? true
                  : false
              "
            />
          </el-form-item>
          <el-form-item label="邮箱地址" class="elFormItem" prop="eamil">
            <el-input
              placeholder="请输入邮箱地址"
              v-model="ruleForm.email"
            />
          </el-form-item>
          <el-form-item
            label="认证状态"
            class="elFormItem"
            v-if="userInfo.isRoot === 1 ? true : false"
          >
            <el-radio-group v-model="ruleForm.status">
              <el-radio :label="1">认证</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="企业LOGO" class="elFormItemLogo">
            <el-upload
              class="avatar-uploader"
              action="/admin/company/uploadCompanyLogo"
              :headers="headerMsg"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img
                v-if="ruleForm.logoPreviewPath"
                :src="ruleForm.logoPreviewPath"
                class="avatar"
              >
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
            <p class="avatar-uploader-p">图片大小限制120px*120px</p>
            <p class="avatar-uploader-p">图片大小小于1MB</p>
            <p class="avatar-uploader-p">支持jpg,jpeg,png</p>
          </el-form-item>
        </el-form>
        <div class="drawerBtn">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRole">提交</el-button>
        </div>
      </div>
    </el-drawer>
    <Page
      :total="total"
      :page.sync="page"
      :limit.sync="limit"
      :page-sizes="pageSizes"
      @search="getTableList"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getCompanyList,
  addCompany,
  delCompany,
  changeStatus,
} from "@/api/company";
import Page from "@/components/page";
import { getToken } from "@/utils/auth";

export default {
  name: "Company",
  components: { Page },
  data() {
    return {
      permissBtn: [],
      headerMsg: { Authorization: getToken() },
      disArray: [
        { text: "禁用", value: 0 },
        { text: "启用", value: 1 },
      ],
      companyNameSearch: "",
      tableData: [], // 保存页面列表显示数据
      dialogVisible: false, // 控制弹框显示或隐藏
      drawerTitle: "",
      multipleSelection: [],
      ruleForm: {
        id: "",
        name: "",
        code: "",
        postcode: "",
        address: "",
        personLiable: "",
        telephone: "",
        email: "",
        status: 1,
        logoPreviewPath: "",
      },
      rules: {
        name: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
        code: [
          {
            required: true,
            message: "请输入企业组织机构代码",
            trigger: "blur",
          },
          {
            pattern: /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/,
            message: "请输入合法的组织机构代码",
          },
        ],
        personLiable: [
          { required: true, message: "请输入联系人姓名", trigger: "blur" },
        ],
        telephone: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的手机号码",
          },
        ],
        postcode: [
          { pattern: /[1-9]\d{5}(?!\d)/, message: "请输入正确的邮政编码" },
        ],
        eamil: [
          {
            pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
            message: "请输入正确的邮箱地址",
          },
        ],
      },
      page: 1,
      limit: 15,
      pageSizes: [15, 30, 50, 100],
      total: 0,
    };
  },
  computed: {
    ...mapGetters(["userInfo", "addRouters"]),
  },
  created() {
    let itemOne = [],
      authBtns = [];
    for (const item of this.addRouters) {
      if (item.name == "SystemSetting") {
        itemOne = item.children;
        break;
      }
    }
    for (const item of itemOne) {
      if (item.name == "Company") {
        authBtns = item.meta.btns;
        break;
      }
    }
    this.permissBtn = [];
    for (const item of authBtns) {
      this.permissBtn.push(item.key);
    }
    this.getTableList();
  },
  methods: {
    // 列表状态栏过滤
    filterStatus(value, row) {
      return row.status == value;
    },
    // 获取企业列表
    async getTableList() {
      const data = {
        name: this.companyNameSearch,
        page: this.page,
        pageSize: this.limit,
      };
      const res = await getCompanyList(data);
      if (res.data.code == 200) {
        this.tableData = res.data.data.list;
        this.page = res.data.data.page;
        this.limit = res.data.data.pageSize;
        this.total = res.data.data.total;
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    // 添加企业
    handleAddCom() {
      this.$nextTick(() => {
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate();
        }
      });
      // 置空
      this.ruleForm.id = "";
      this.ruleForm.name = "";
      this.ruleForm.code = "";
      this.ruleForm.postcode = "";
      this.ruleForm.address = "";
      this.ruleForm.personLiable = "";
      this.ruleForm.telephone = "";
      this.ruleForm.email = "";
      this.ruleForm.status = 1;
      this.ruleForm.logoPreviewPath = "";

      this.drawerTitle = "添加企业";
      this.dialogVisible = true;
    },
    // 编辑
    handleEdit(row) {
      this.$nextTick(() => {
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate();
        }
      });
      this.ruleForm.id = row.id;
      this.ruleForm.name = row.name;
      this.ruleForm.code = row.code;
      this.ruleForm.postcode = row.postcode;
      this.ruleForm.address = row.address;
      this.ruleForm.personLiable = row.personLiable;
      this.ruleForm.telephone = row.telephone;
      this.ruleForm.email = row.email;
      this.ruleForm.status = row.status;
      this.ruleForm.logoPreviewPath = row.logoPreviewPath;

      this.drawerTitle = "编辑企业";
      this.dialogVisible = true;
    },
    // 删除
    handleDelete(row) {
      this.$confirm("确认删除角色?", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await delCompany([{ id: row.id }]);
          if (res.data.code == 200) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.getTableList();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 确认
    async confirmRole() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const res = await addCompany(this.ruleForm);
          if (res.data.code == 200) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.dialogVisible = false;
            this.getTableList();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    async switchDisable(row) {
      const res = await changeStatus([{ id: row.id, status: row.status }]);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    handleAvatarSuccess(res) {
      if (res.code == 200) {
        this.ruleForm.logoPreviewPath = res.data.imageUrl;
      } else {
        this.$message({
          type: "error",
          message: res.msg,
        });
      }
      // this.ruleForm.logoPreviewPath = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isPNG = file.type === "image/png";
      const isLt1M = file.size / 1024 / 1024 < 1;

      if (!isJPG && !isPNG) {
        this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
        return Promise.reject();
      }
      if (!isLt1M) {
        this.$message.error("上传头像图片大小不能超过 1MB!");
        return Promise.reject();
      }
      const isSize = new Promise(function (resolve, reject) {
        let width = 120;
        let height = 120;
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = function () {
          let valid = img.width > width || img.height > height;
          valid ? reject() : resolve();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          return file;
        },
        () => {
          this.$message.error("上传的图片宽高必须是小于或等于120*120!");
          return Promise.reject();
        }
      );
      return (isJPG || isPNG) && isLt1M && isSize;
    },
    //启用或禁用
    async disOrEn(num) {
      let arrSelect = [];
      for (const item of this.multipleSelection) {
        arrSelect.push({ id: item.id, status: num });
      }
      const res = await changeStatus(arrSelect);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.getTableList();
        this.multipleSelection = [];
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    isEmpty() {
      return this.multipleSelection.length === 0;
    },
    //批量启用
    async batchEnable() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      const bool = this.multipleSelection.every((item) => {
        return item.status == 1;
      });
      if (bool) {
        this.$message({
          type: "warning",
          message: "所选数据全部是已启用状态，请重新选择",
        });
      } else {
        this.disOrEn(1);
      }
    },
    //批量禁用
    async batchDisable() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      const bool = this.multipleSelection.every((item) => {
        return item.status == 0;
      });
      if (bool) {
        this.$message({
          type: "warning",
          message: "所选数据全部是已禁用状态，请重新选择",
        });
      } else {
        this.disOrEn(0);
      }
    },
    //批量删除
    async batchDel() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      let arrSelect = [];
      for (const item of this.multipleSelection) {
        arrSelect.push({ id: item.id });
      }
      const res = await delCompany(arrSelect);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.getTableList();
        this.multipleSelection = [];
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .drawerDiv {
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .drawerBtn {
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
}
</style>

<style>
.myDrawer,
.myDrawer .el-drawer__close-btn,
.myDrawer .el-drawer__header span {
  outline: none;
}
.myDrawer .el-drawer__body {
  overflow: hidden;
}
.elFormItem {
  border: 1px solid #ededed;
}
.elFormItem .el-form-item__label {
  border-right: 1px solid #ededed;
}
.elFormItem .el-input__inner {
  border: 0;
}
.elFormItem .el-radio-group {
  padding-left: 16px;
}
.elFormItemLogo {
  text-align: center;
  border: 1px solid #ededed;
}
.elFormItemLogo .el-form-item__label {
  margin-top: 100px;
}
.elFormItemLogo .el-form-item__content {
  border-left: 1px solid #ededed;
}
.elFormItemLogo .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  margin-top: 12px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.elFormItemLogo .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.elFormItemLogo .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.elFormItemLogo .avatar {
  width: 120px;
  height: 120px;
  display: block;
}
.avatar-uploader-p {
  line-height: 1;
  color: #c0c4cc;
}
</style>