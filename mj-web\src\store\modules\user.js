/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 13:36:21
 * @LastEditors: dlg
 * @LastEditTime: 2020-10-09 12:01:20
 */
// eslint-disable-next-line no-unused-vars
import { login, logout } from '@/api/login'
import { resetRouter } from '@/router'
import { getToken, setToken, removeToken, getExpires, setExpires, removeExpires, getUserInfo, setUserInfo, removeUserInfo } from '@/utils/auth'
import { Message } from 'element-ui'

const user = {
    state: {
        token: getToken(),
        expiresAt: Number(getExpires()),
        userInfo: getUserInfo() ? JSON.parse(getUserInfo()) : ''
    },

    mutations: {
        SET_TOKEN: (state, token) => {
            state.token = token;
        },
        SET_EXPIRESAT(state, expiresAt) {
            state.expiresAt = expiresAt;
        },
        SET_USERINFO: (state, userInfo) => {
            state.userInfo = userInfo;
        },
        EMPTY: (state) => {
            state.token = "";
            state.expiresAt = "";
            state.userInfo = {};
        }
    },

    actions: {
        // 用户名登录
        Login({ commit }, userInfo) {
            userInfo.username = userInfo.username.trim()
            return new Promise((resolve, reject) => {
                login(userInfo).then(res => {
                    if (res.data.code == 200) {
                        if (res.data.data.user.passwordStatus == 1) {
                            Message({
                                showClose: true,
                                message: "你的密码为初始密码,建议及时修改密码",
                                type: 'warning',
                            })
                        }
                        commit('SET_TOKEN', res.data.data.token);
                        commit('SET_EXPIRESAT', res.data.data.expiresAt);
                        commit('SET_USERINFO', res.data.data.user);
                        setToken(res.data.data.token, new Date(res.data.data.expiresAt));
                        setExpires(res.data.data.expiresAt, new Date(res.data.data.expiresAt));
                        setUserInfo(res.data.data.user, new Date(res.data.data.expiresAt));
                        resolve();
                    } else {
                        reject(res.data.msg);
                    }
                }).catch(error => {
                    reject(error);
                })
            })
        },
        LogOut({ commit, dispatch }) {
            // eslint-disable-next-line no-unused-vars
            return new Promise((resolve, reject) => {
                commit("EMPTY");
                removeToken();
                removeExpires();
                removeUserInfo();
                // 重置注册路由
                resetRouter();
                // 退出时重置缓存的动态路由
                dispatch('resetRoutes');
                // reset visited views and cached views（vuex）
                dispatch('delAllViews', null, { root: true });
                resolve();

            })
        },
        // 移除掉 cookies
        resetUserCookies({ commit }) {
            return new Promise(resolve => {
                commit("EMPTY");
                removeToken();
                removeExpires();
                removeUserInfo();
                resolve();
            })
        },
        // 更新用户信息
        updateUserInfo({ commit }, userInfo) {
            return new Promise(resolve => {
                commit('SET_USERINFO', userInfo);
                setUserInfo(userInfo, new Date(Number(getExpires())));
                resolve();
            })
        }
    }
}

export default user
