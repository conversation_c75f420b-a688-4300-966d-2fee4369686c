@font-face {font-family: "iconfont";
  src: url('iconfont.eot?t=1596789351328'); /* IE9 */
  src: url('iconfont.eot?t=1596789351328#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALQAAsAAAAABuAAAAKBAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgqBPIEqATYCJAMMCwgABCAFhG0HQRv7BREVlBtkXxzYxozDJtNynQ6fTqgbNWc+qtCUdJ+DR36OfPlJe8Cs7iQhTc0CglCT8yzHjkABy+k2AEiAzAAoQNPVqRkNGoAGpBdWvOOX59h9eXLcHZ+4d/xH510g8wHlNMfiT12AcUCBjjUosgJJ0BvGbiJwCMchQDjZKklre+8ooRhgnACya2tjidBcFMOsQiiCX7PUkIt4hLpT7gVc4N+XP75QcHgKOGlwvW2Vxu+t35/owQCzepCD87kBaxUoUAkMZL42O4kiFkcJ11p7pY8huLkSQcClodqHf3jgEBAfUB8JQKmWc/C9FUtT3RNVftdDFQAf8HgJWw/AaGNNGqleY7bn1dfR19/G3v6YePN9dvHqZL1xO2npmqWserxCXF3+6uu2i9AI3JqrB995ubneO2guoky3TU+wa2/OxVTFnHtj1x9nCQ68t9xcU9nvS6n6bt09CBIAf/M3DDa8GliIqf0fGmEAfByp3uBjg4OGHgRCcSNk/07Ws81CBbCp+VIsjf1K6n24HahiO0AACMb7Mt6YdH95IZSkFI4QsqCEUkgYqxIeEdTAJ5QGhFOhY3UECXajiIUB5Y4pEGLdgSOaR1BivSSM9Rkeyf7BJ3aKcMYlYcsIigfYdAujEfRg/7C5nAg5RIvGOy5nMJxVcO4buW4JGNq+WLhhQp5jRv0sowgBcY4wK6dhCBkK5x2dtKtImbqO6t7UuhyVVi2MRtCD/cPmciLaXXTp83dczmC4hTEQfSPXrX8Y2r4HcdOmXox7eaV+llGEgDhHmJVBGEKGUj9oRyftOiJdpg52or7adn19/L49QLgCd+vFiYphJZj/tZizotvYBWxoC0EpAAAA') format('woff2'),
  url('iconfont.woff?t=1596789351328') format('woff'),
  url('iconfont.ttf?t=1596789351328') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  url('iconfont.svg?t=1596789351328#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-play:before {
  content: "\e6c8";
}

.icon-pausecircle-fill:before {
  content: "\e644";
}

