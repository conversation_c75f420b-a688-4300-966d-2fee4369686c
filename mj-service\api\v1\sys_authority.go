package v1

import (
	"auction-sys/constants"
	"auction-sys/global/response"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/service"
	"auction-sys/utils"
	"encoding/json"
	"fmt"

	"github.com/gin-gonic/gin"
)

// @Tags authority ：角色管理
// @Summary 创建角色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []request.SaveOrUpdateAuthority true "创建角色"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"创建成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"创建失败"}"
// @Router /authority/createAuthority [post]
func CreateAuthority(c *gin.Context) {
	// 获取请求头
	agent := c.<PERSON>eader("User-Agent")

	var auths []req.SaveOrUpdateAuthority
	_ = c.ShouldBindJSON(&auths)
	AuthorityVerify := utils.Rules{
		"AuthorityName": {utils.NotEmpty()},
	}
	for _, auth := range auths {
		AuthorityVerifyErr := utils.Verify(auth, AuthorityVerify)
		if AuthorityVerifyErr != nil {
			response.FailWithMessage(AuthorityVerifyErr.Error(), c)
			return
		}
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	err := service.CreateAuthority(auths, claims.UUID.String())
	requestParam, _ := json.Marshal(auths)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-添加角色", c.ClientIP(), string(requestParam), "添加失败", response.ERROR, agent)
		response.FailWithMessage("添加失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-添加角色", c.ClientIP(), string(requestParam), "添加成功", response.SUCCESS, agent)
		response.OkWithMessage("添加成功", c)
	}
}

// @Tags authority ：角色管理
// @Summary 删除角色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []request.GetById true "删除角色"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"删除成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"删除失败"}"
// @Router /authority/deleteAuthority [post]
func DeleteAuthority(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var reqIds []req.GetById
	_ = c.ShouldBindJSON(&reqIds)
	for _, reqId := range reqIds {
		IdVerifyErr := utils.Verify(reqId, utils.CustomizeMap["IdVerify"])
		if IdVerifyErr != nil {
			response.FailWithMessage(IdVerifyErr.Error(), c)
			return
		}
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	// 删除角色之前需要判断是否有用户正在使用此角色
	err := service.DeleteAuthority(reqIds)
	requestParam, _ := json.Marshal(reqIds)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-删除角色", c.ClientIP(), string(requestParam), "删除失败", response.ERROR, agent)
		response.FailWithMessage("删除失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-删除角色", c.ClientIP(), string(requestParam), "删除成功", response.SUCCESS, agent)
		response.OkWithMessage("删除成功", c)
	}
}

// @Tags authority ：角色管理
// @Summary 修改角色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SaveOrUpdateAuthority true "修改角色"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /authority/updateAuthority [post]
func UpdateAuthority(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var auth req.SaveOrUpdateAuthority
	_ = c.ShouldBindJSON(&auth)
	AuthorityVerify := utils.Rules{
		"AuthorityName": {utils.NotEmpty()},
	}
	AuthorityVerifyErr := utils.Verify(auth, AuthorityVerify)
	if AuthorityVerifyErr != nil {
		response.FailWithMessage(AuthorityVerifyErr.Error(), c)
		return
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	err := service.UpdateAuthority(auth)
	requestParam, _ := json.Marshal(auth)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-编辑角色", c.ClientIP(), string(requestParam), "更新成功", response.ERROR, agent)
		response.FailWithMessage("更新失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-编辑角色", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags authority ：角色管理
// @Summary 分页获取角色列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetAuthorityList true "分页获取角色列表"
// @Success 200 {string} string "{"code":200,"data":{PageResult},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取数据失败"}"
// @Router /authority/getAuthorityList [post]
func GetAuthorityList(c *gin.Context) {
	claims, _ := c.Get("claims")
	// 这里我们通过断言获取 claims内的所有内容
	waitUse := claims.(*req.CustomClaims)
	uuid := waitUse.UUID
	var pageInfo req.GetAuthorityList
	_ = c.ShouldBindJSON(&pageInfo)
	err, list, total, enableNum, disableNum := service.GetAuthorityInfoList(pageInfo, uuid.String())
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取数据失败，%v", err), c)
	} else {
		response.OkWithData(resp.GetAuthorityList{
			List:       list,
			Total:      total,
			Page:       pageInfo.Page,
			PageSize:   pageInfo.PageSize,
			Name:       pageInfo.Name,
			EnableNum:  enableNum,
			DisableNum: disableNum,
		}, c)
	}
}

// @Tags authority ：角色管理
// @Summary 更改角色状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []model.SysAuthority true "更改角色状态"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /authority/updateAuthorityStatus [post]
func UpdateAuthorityStatus(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var auths []model.SysAuthority
	_ = c.ShouldBindJSON(&auths)
	AuthVerify := utils.Rules{
		"ID": {utils.NotEmpty()},
	}
	for _, auth := range auths {
		AuthVerifyErr := utils.Verify(auth, AuthVerify)
		if AuthVerifyErr != nil {
			response.FailWithMessage(AuthVerifyErr.Error(), c)
			return
		}
	}

	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	err := service.UpdateAuthorityStatus(auths)
	requestParam, _ := json.Marshal(auths)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-更新状态", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage("更新失败", c)
	} else {
		if auths[0].Status == constants.STATUS_ENABLED {
			go service.AddOperationLog(claims.UUID.String(), "角色权限-更新状态", c.ClientIP(), string(requestParam), "已启用", response.SUCCESS, agent)
			response.OkWithMessage("已启用", c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "角色权限-更新状态", c.ClientIP(), string(requestParam), "已禁用", response.SUCCESS, agent)
			response.OkWithMessage("已禁用", c)
		}

	}
}

// @Tags authority ：角色管理
// @Summary 获取角色和绑定的用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetAuthAndUsers true "获取角色和绑定的用户"
// @Success 200 {string} string "{"code":200,"data":{BindingUsersResult},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取失败"}"
// @Router /authority/getAuthorityAndUsers [post]
func GetAuthorityAndUsers(c *gin.Context) {
	var pageInfo req.GetAuthAndUsers
	_ = c.ShouldBindJSON(&pageInfo)
	PageInfoVerify := utils.Rules{
		"Page":     {utils.NotEmpty()},
		"PageSize": {utils.NotEmpty()},
		"ID":       {utils.NotEmpty()},
	}
	PageInfoVerifyErr := utils.Verify(pageInfo, PageInfoVerify)
	if PageInfoVerifyErr != nil {
		response.FailWithMessage(PageInfoVerifyErr.Error(), c)
		return
	}
	err, list, total, authId, authName := service.GetAuthorityAndUsers(pageInfo)
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取失败,%v", err), c)
	} else {
		response.OkWithData(resp.BindingUsersResult{
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
			Total:    total,
			List:     list,
			AuthId:   authId,
			AuthName: authName,
		}, c)
	}
}

// @Tags authority ：角色管理
// @Summary 获取变更角色用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "获取变更角色用户列表"
// @Success 200 {string} string "{"code":200,"data":{authBindingUsers},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取失败"}"
// @Router /authority/getAuthBindingUsers [post]
func GetAuthBindingUsers(c *gin.Context) {
	var reqId req.GetById
	_ = c.ShouldBindJSON(&reqId)
	IdVerifyErr := utils.Verify(reqId, utils.CustomizeMap["IdVerify"])
	if IdVerifyErr != nil {
		response.FailWithMessage(IdVerifyErr.Error(), c)
		return
	}
	err, authBindingUsers := service.GetAuthBindingUsers(reqId.Id)
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取失败%v", err), c)
	} else {
		response.OkWithData(authBindingUsers, c)
	}
}

// @Tags authority ：角色管理
// @Summary 更新变更角色用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateAuthBindingUsers true "更新变更角色用户列表"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /authority/updateAuthBindingUsers [post]
func UpdateAuthBindingUsers(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var auth req.UpdateAuthBindingUsers
	_ = c.ShouldBindJSON(&auth)
	AuthVerify := utils.Rules{
		"ID": {utils.NotEmpty()},
	}
	AuthVerifyErr := utils.Verify(auth, AuthVerify)
	if AuthVerifyErr != nil {
		response.FailWithMessage(AuthVerifyErr.Error(), c)
		return
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	err := service.UpdateAuthBindingUsers(auth)
	requestParam, _ := json.Marshal(auth)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-更新绑定用户", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage(fmt.Sprintf("更新失败，%v", err), c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "角色权限-更新绑定用户", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags authority ：角色管理
// @Summary 获取自己公司的角色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":200,"data":{authList},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"加载失败"}"
// @Router /authority/getAuthBySelfCompany [post]
func GetAuthBySelfCompany(c *gin.Context) {
	claims, _ := c.Get("claims")
	// 这里我们通过断言获取 claims内的所有内容
	waitUse := claims.(*req.CustomClaims)
	uuid := waitUse.UUID
	err, authList := service.GetAuthBySelfCompany(uuid.String())
	if err != nil {
		response.FailWithMessage("加载失败", c)
	} else {
		response.OkWithData(authList, c)
	}
}
