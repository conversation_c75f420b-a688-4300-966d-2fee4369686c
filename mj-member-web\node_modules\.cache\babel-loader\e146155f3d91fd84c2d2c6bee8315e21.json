{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\src\\api\\request.js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\api\\request.js", "mtime": 1757553707442}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "Message", "getToken", "removeToken", "router", "service", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "headers", "error", "console", "Promise", "reject", "response", "res", "data", "responseType", "code", "push", "Error", "msg", "status"], "sources": ["E:/pywsp/auction-sys/mj-member-web/src/api/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { Message } from 'element-ui'\r\nimport { getToken, removeToken } from '@/utils/auth'\r\nimport router from '@/router'\r\n\r\n// 创建axios实例\r\nconst service = axios.create({\r\n  baseURL: '/admin/',\r\n  timeout: 10000\r\n})\r\n\r\n// 请求拦截器\r\nservice.interceptors.request.use(\r\n  config => {\r\n    // 添加token\r\n    const token = getToken()\r\n    if (token) {\r\n      config.headers['Authorization'] = `${token}`\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    console.error('请求错误:', error)\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器\r\nservice.interceptors.response.use(\r\n  response => {\r\n    const res = response.data\r\n    \r\n    // 如果是文件下载，直接返回\r\n    if (response.config.responseType === 'blob') {\r\n      return response\r\n    }\r\n    \r\n    // 处理业务错误\r\n    if (res.code !== 200) {\r\n      // 401 未授权，跳转到登录页\r\n      if (res.code === 401) {\r\n        Message.error('登录已过期，请重新登录')\r\n        removeToken()\r\n        router.push('/login')\r\n        return Promise.reject(new Error('登录已过期'))\r\n      }\r\n      \r\n      // 其他错误显示提示\r\n      if (res.msg) {\r\n        Message.error(res.msg)\r\n      }\r\n      \r\n      return Promise.reject(new Error(res.msg || '请求失败'))\r\n    }\r\n    \r\n    return response\r\n  },\r\n  error => {\r\n    console.error('响应错误:', error)\r\n    \r\n    if (error.response) {\r\n      const { status, data } = error.response\r\n      \r\n      switch (status) {\r\n        case 401:\r\n          Message.error('登录已过期，请重新登录')\r\n          removeToken()\r\n          router.push('/login')\r\n          break\r\n        case 403:\r\n          Message.error('没有权限访问')\r\n          break\r\n        case 404:\r\n          Message.error('请求的资源不存在')\r\n          break\r\n        case 500:\r\n          Message.error('服务器内部错误')\r\n          break\r\n        default:\r\n          Message.error(data?.msg || '网络错误，请稍后重试')\r\n      }\r\n    } else if (error.code === 'ECONNABORTED') {\r\n      Message.error('请求超时，请稍后重试')\r\n    } else {\r\n      Message.error('网络错误，请检查网络连接')\r\n    }\r\n    \r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nexport default service\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AACpD,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AACA,MAAMC,OAAO,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC3BC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAH,OAAO,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAI;EACR;EACA,MAAMC,KAAK,GAAGX,QAAQ,CAAC,CAAC;EACxB,IAAIW,KAAK,EAAE;IACTD,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC,GAAG,GAAGD,KAAK,EAAE;EAC9C;EACA,OAAOD,MAAM;AACf,CAAC,EACDG,KAAK,IAAI;EACPC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAC7B,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAV,OAAO,CAACI,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC/BQ,QAAQ,IAAI;EACV,MAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEzB;EACA,IAAIF,QAAQ,CAACP,MAAM,CAACU,YAAY,KAAK,MAAM,EAAE;IAC3C,OAAOH,QAAQ;EACjB;;EAEA;EACA,IAAIC,GAAG,CAACG,IAAI,KAAK,GAAG,EAAE;IACpB;IACA,IAAIH,GAAG,CAACG,IAAI,KAAK,GAAG,EAAE;MACpBtB,OAAO,CAACc,KAAK,CAAC,aAAa,CAAC;MAC5BZ,WAAW,CAAC,CAAC;MACbC,MAAM,CAACoB,IAAI,CAAC,QAAQ,CAAC;MACrB,OAAOP,OAAO,CAACC,MAAM,CAAC,IAAIO,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C;;IAEA;IACA,IAAIL,GAAG,CAACM,GAAG,EAAE;MACXzB,OAAO,CAACc,KAAK,CAACK,GAAG,CAACM,GAAG,CAAC;IACxB;IAEA,OAAOT,OAAO,CAACC,MAAM,CAAC,IAAIO,KAAK,CAACL,GAAG,CAACM,GAAG,IAAI,MAAM,CAAC,CAAC;EACrD;EAEA,OAAOP,QAAQ;AACjB,CAAC,EACDJ,KAAK,IAAI;EACPC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAE7B,IAAIA,KAAK,CAACI,QAAQ,EAAE;IAClB,MAAM;MAAEQ,MAAM;MAAEN;IAAK,CAAC,GAAGN,KAAK,CAACI,QAAQ;IAEvC,QAAQQ,MAAM;MACZ,KAAK,GAAG;QACN1B,OAAO,CAACc,KAAK,CAAC,aAAa,CAAC;QAC5BZ,WAAW,CAAC,CAAC;QACbC,MAAM,CAACoB,IAAI,CAAC,QAAQ,CAAC;QACrB;MACF,KAAK,GAAG;QACNvB,OAAO,CAACc,KAAK,CAAC,QAAQ,CAAC;QACvB;MACF,KAAK,GAAG;QACNd,OAAO,CAACc,KAAK,CAAC,UAAU,CAAC;QACzB;MACF,KAAK,GAAG;QACNd,OAAO,CAACc,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;QACEd,OAAO,CAACc,KAAK,CAAC,CAAAM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,GAAG,KAAI,YAAY,CAAC;IAC5C;EACF,CAAC,MAAM,IAAIX,KAAK,CAACQ,IAAI,KAAK,cAAc,EAAE;IACxCtB,OAAO,CAACc,KAAK,CAAC,YAAY,CAAC;EAC7B,CAAC,MAAM;IACLd,OAAO,CAACc,KAAK,CAAC,cAAc,CAAC;EAC/B;EAEA,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeV,OAAO", "ignoreList": []}]}