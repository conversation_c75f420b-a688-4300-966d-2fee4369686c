{"name": "TBM", "version": "1.0.0", "description": "A Vue.js project", "author": "dlg <1399477141.qq.com>", "scripts": {"serve": "vue-cli-service serve", "start": "npm run serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "axios": "0.19.0", "core-js": "3.21.1", "echarts": "4.8.0", "element-ui": "2.13.0", "js-cookie": "2.2.0", "js-file-download": "^0.4.12", "normalize.css": "8.0.1", "nprogress": "0.2.0", "sass": "1.32.13", "screenfull": "5.0.2", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-router": "3.0.2", "vue-seamless-scroll": "1.1.21", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.1.0", "@vue/cli-plugin-eslint": "4.1.0", "@vue/cli-plugin-unit-mocha": "4.1.0", "@vue/cli-service": "4.1.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.7.3", "babel-eslint": "10.0.3", "chai": "4.1.2", "eslint": "5.16.0", "eslint-plugin-vue": "5.0.0", "less": "^3.11.3", "less-loader": "^6.1.1", "lint-staged": "9.4.3", "mockjs": "1.1.0", "sass-loader": "8.0.0", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">= 8.9", "npm": ">= 3.0.0"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}