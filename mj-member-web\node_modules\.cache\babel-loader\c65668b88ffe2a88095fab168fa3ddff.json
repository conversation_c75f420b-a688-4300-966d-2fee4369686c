{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue", "mtime": 1757556310840}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "getCategories", "getPublicAuctionList", "name", "data", "stats", "auctions", "categories", "filters", "status", "categoryId", "loading", "loadingMore", "page", "pageSize", "hasMore", "computed", "mounted", "Promise", "all", "fetchStats", "fetchCategories", "fetchAuctions", "methods", "totalProjects", "totalMembers", "totalBids", "totalAmount", "response", "code", "error", "console", "reset", "result", "isLoggedIn", "$store", "dispatch", "requestData", "success", "message", "msg", "newAuctions", "list", "push", "length", "loadMore", "goToAuction", "auction", "$message", "warning", "$router", "id", "scrollToAuctions", "$refs", "auctionsSection", "scrollIntoView", "behavior", "formatMoney", "value", "Number", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "watch", "handler", "deep"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\n  <div class=\"home-page\">\n    <AppHeader />\n    \n    <!-- 轮播横幅 -->\n    <section class=\"hero-section\">\n      <div class=\"hero-content\">\n        <h1>新疆生产建设兵团第一师棉麻有限责任公司竞价平台</h1>\n        <p>专业的棉副产品在线竞价交易平台，公开透明，安全可靠</p>\n        <div class=\"hero-actions\">\n          <el-button type=\"primary\" size=\"large\" @click=\"scrollToAuctions\">\n            立即竞价\n          </el-button>\n          <el-button size=\"large\" @click=\"$router.push('/register')\" v-if=\"!isLoggedIn\">\n            免费注册\n          </el-button>\n        </div>\n      </div>\n    </section>\n\n    <!-- 竞价项目列表 -->\n    <section class=\"auctions-section\" ref=\"auctionsSection\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h2>热门竞价</h2>\n          <div class=\"filters\">\n            <el-select v-model=\"filters.status\" placeholder=\"项目状态\" clearable @change=\"fetchAuctions\">\n              <el-option label=\"即将开始\" :value=\"0\" />\n              <el-option label=\"竞价中\" :value=\"1\" />\n              <el-option label=\"已结束\" :value=\"2\" />\n            </el-select>\n            <el-select v-model=\"filters.categoryId\" placeholder=\"商品分类\" clearable @change=\"fetchAuctions\">\n              <el-option\n                v-for=\"category in categories\"\n                :key=\"category.id\"\n                :label=\"category.name\"\n                :value=\"category.id\"\n              />\n            </el-select>\n          </div>\n        </div>\n\n        <div class=\"auctions-grid\" v-loading=\"loading\">\n          <div\n            v-for=\"auction in auctions\"\n            :key=\"auction.id\"\n            class=\"auction-card\"\n            @click=\"goToAuction(auction)\"\n          >\n\n            <div class=\"auction-content\">\n              <StatusTag :status=\"auction.status\" class=\"status-overlay\" />\n              <h3 class=\"auction-title\">{{ auction.title }}</h3>\n              <div class=\"auction-info\">\n                \n                <!-- 已登录用户显示价格信息 -->\n                <template v-if=\"isLoggedIn\">\n                  <div class=\"info-item\">\n                    <span class=\"label\">起拍价</span>\n                    <span class=\"value\">¥{{ formatMoney(auction.startPrice) }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <span class=\"label\">当前价</span>\n                    <span class=\"value current-price\">¥{{ formatMoney(auction.currentPrice) }}</span>\n                  </div>\n                </template>\n                <!-- 未登录用户显示基本信息 -->\n                <template v-else>\n                  <div class=\"info-item\">\n                    <span class=\"label\">数量</span>\n                    <span class=\"value\">{{ auction.quantity }}{{ auction.unit }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <span class=\"label\">商品类别</span>\n                    <span class=\"value\">{{ auction.productCategory }}</span>\n                  </div>\n                </template>\n                <div class=\"info-item\">\n                  <span class=\"label\">出价次数</span>\n                  <span class=\"value\">{{ auction.bidCount }}次</span>\n                </div>\n              </div>\n              \n              <div class=\"auction-time\">\n                <template v-if=\"auction.status === 0\">\n                  <span class=\"time-label\">开始时间：</span>\n                  <span class=\"time-value\">{{ auction.startTime | formatTime('MM-DD HH:mm') }}</span>\n                </template>\n                <template v-else-if=\"auction.status === 1\">\n                  <span class=\"time-label\">剩余时间：</span>\n                  <CountdownTimer :end-time=\"auction.endTime\" />\n                </template>\n                <template v-else>\n                  <span class=\"time-label\">结束时间：</span>\n                  <span class=\"time-value\">{{ auction.endTime | formatTime('MM-DD HH:mm') }}</span>\n                </template>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"load-more\" v-if=\"hasMore\">\n          <el-button @click=\"loadMore\" :loading=\"loadingMore\">加载更多</el-button>\n        </div>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { getCategories, getPublicAuctionList } from '@/api/auction'\n\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      stats: {},\n      auctions: [],\n      categories: [],\n      filters: {\n        status: '',\n        categoryId: ''\n      },\n      loading: false,\n      loadingMore: false,\n      page: 1,\n      pageSize: 12,\n      hasMore: true\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['isLoggedIn'])\n  },\n  async mounted() {\n    await Promise.all([\n      this.fetchStats(),\n      this.fetchCategories(),\n      this.fetchAuctions()\n    ])\n  },\n  methods: {\n    // 获取统计数据\n    async fetchStats() {\n      // 这里可以调用统计API\n      this.stats = {\n        totalProjects: 156,\n        totalMembers: 1280,\n        totalBids: 8960,\n        totalAmount: 2580\n      }\n    },\n\n    // 获取商品分类\n    async fetchCategories() {\n      try {\n        const response = await getCategories()\n        if (response.data.code === 200) {\n          this.categories = response.data.data || []\n        }\n      } catch (error) {\n        console.error('获取分类失败:', error)\n      }\n    },\n\n    // 获取竞价列表\n    async fetchAuctions(reset = false) {\n      if (reset) {\n        this.page = 1\n        this.auctions = []\n        this.hasMore = true\n      }\n\n      this.loading = reset\n      this.loadingMore = !reset\n\n      try {\n        let result\n\n        if (this.isLoggedIn) {\n          // 已登录用户：使用会员接口（包含价格信息和权限）\n          result = await this.$store.dispatch('auction/fetchAuctionList', {\n            page: this.page,\n            pageSize: this.pageSize,\n            ...this.filters\n          })\n        } else {\n          // 未登录用户：使用公开接口（不包含价格信息）\n          const requestData = {\n            page: this.page,\n            pageSize: this.pageSize,\n            status: this.filters.status || '',\n            categoryId: this.filters.categoryId || ''\n          }\n\n          const response = await getPublicAuctionList(requestData)\n          if (response.data.code === 200) {\n            result = { success: true, data: response.data.data }\n          } else {\n            result = { success: false, message: response.data.msg }\n          }\n        }\n\n        if (result.success) {\n          const newAuctions = result.data.list || []\n          if (reset) {\n            this.auctions = newAuctions\n          } else {\n            this.auctions.push(...newAuctions)\n          }\n\n          this.hasMore = newAuctions.length === this.pageSize\n        }\n      } catch (error) {\n        console.error('获取竞价列表失败:', error)\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n      }\n    },\n\n    // 加载更多\n    loadMore() {\n      this.page++\n      this.fetchAuctions()\n    },\n\n    // 跳转到竞价详情\n    goToAuction(auction) {\n      if (!this.isLoggedIn) {\n        this.$message.warning('请先登录')\n        this.$router.push('/login')\n        return\n      }\n      \n      this.$router.push(`/auction/${auction.id}`)\n    },\n\n    // 滚动到竞价区域\n    scrollToAuctions() {\n      this.$refs.auctionsSection.scrollIntoView({ behavior: 'smooth' })\n    },\n\n    // 格式化金额\n    formatMoney(value) {\n      if (!value && value !== 0) return '0'\n      return Number(value).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })\n    }\n  },\n  watch: {\n    filters: {\n      handler() {\n        this.fetchAuctions(true)\n      },\n      deep: true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.home-page {\n  min-height: 100vh;\n}\n\n// 英雄区域\n.hero-section {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 100px 0;\n  text-align: center;\n\n  .hero-content {\n    max-width: 800px;\n    margin: 0 auto;\n    padding: 0 20px;\n\n    h1 {\n      font-size: 48px;\n      font-weight: 700;\n      margin-bottom: 20px;\n    }\n\n    p {\n      font-size: 20px;\n      margin-bottom: 40px;\n      opacity: 0.9;\n    }\n\n    .hero-actions {\n      display: flex;\n      gap: 20px;\n      justify-content: center;\n    }\n  }\n}\n\n// 竞价区域\n.auctions-section {\n  padding: 60px 0;\n  min-height: 300px;\n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 40px;\n\n    h2 {\n      font-size: 32px;\n      color: $text-primary;\n    }\n\n    .filters {\n      display: flex;\n      gap: 15px;\n    }\n  }\n\n  .auctions-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: 30px;\n    margin-bottom: 40px;\n  }\n\n  .auction-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n    cursor: pointer;\n    transition: transform 0.3s, box-shadow 0.3s;\n\n    &:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n    }\n\n    .auction-content {\n      position: relative;\n      overflow: hidden;\n      padding: 20px;\n      .status-overlay {\n        position: absolute;\n        top: 10px;\n        right: 10px;\n      }\n      .auction-title {\n        font-size: 18px;\n        font-weight: 600;\n        color: $text-primary;\n        margin-bottom: 15px;\n        @include text-ellipsis;\n      }\n\n      .auction-info {\n        margin-bottom: 15px;\n\n        .info-item {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n\n          .label {\n            color: $text-secondary;\n          }\n\n          .value {\n            font-weight: 600;\n            color: $text-primary;\n\n            &.current-price {\n              color: $danger-color;\n            }\n          }\n        }\n      }\n\n      .auction-time {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding-top: 15px;\n        border-top: 1px solid $border-color;\n\n        .time-label {\n          color: $text-secondary;\n          font-size: 14px;\n        }\n\n        .time-value {\n          font-weight: 600;\n          color: $text-primary;\n        }\n      }\n    }\n  }\n\n  .load-more {\n    text-align: center;\n  }\n}\n\n@include mobile {\n  .hero-section {\n    padding: 60px 0;\n\n    .hero-content {\n      h1 {\n        font-size: 32px;\n      }\n\n      p {\n        font-size: 16px;\n      }\n\n      .hero-actions {\n        flex-direction: column;\n        align-items: center;\n      }\n    }\n  }\n\n  .stats-section {\n    padding: 40px 0;\n\n    .stats-grid {\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n\n      .stat-item .stat-number {\n        font-size: 24px;\n      }\n    }\n  }\n\n  .auctions-section {\n    padding: 40px 0;\n\n    .section-header {\n      flex-direction: column;\n      gap: 20px;\n      align-items: flex-start;\n\n      .filters {\n        width: 100%;\n        justify-content: space-between;\n      }\n    }\n\n    .auctions-grid {\n      grid-template-columns: 1fr;\n      gap: 20px;\n    }\n  }\n}\n</style>\n"], "mappings": ";AA8GA,SAAAA,UAAA;AACA,SAAAC,aAAA,EAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,QAAA;MACAC,UAAA;MACAC,OAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACAC,OAAA;MACAC,WAAA;MACAC,IAAA;MACAC,QAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAhB,UAAA;EACA;EACA,MAAAiB,QAAA;IACA,MAAAC,OAAA,CAAAC,GAAA,EACA,KAAAC,UAAA,IACA,KAAAC,eAAA,IACA,KAAAC,aAAA,GACA;EACA;EACAC,OAAA;IACA;IACA,MAAAH,WAAA;MACA;MACA,KAAAf,KAAA;QACAmB,aAAA;QACAC,YAAA;QACAC,SAAA;QACAC,WAAA;MACA;IACA;IAEA;IACA,MAAAN,gBAAA;MACA;QACA,MAAAO,QAAA,SAAA3B,aAAA;QACA,IAAA2B,QAAA,CAAAxB,IAAA,CAAAyB,IAAA;UACA,KAAAtB,UAAA,GAAAqB,QAAA,CAAAxB,IAAA,CAAAA,IAAA;QACA;MACA,SAAA0B,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAR,cAAAU,KAAA;MACA,IAAAA,KAAA;QACA,KAAAnB,IAAA;QACA,KAAAP,QAAA;QACA,KAAAS,OAAA;MACA;MAEA,KAAAJ,OAAA,GAAAqB,KAAA;MACA,KAAApB,WAAA,IAAAoB,KAAA;MAEA;QACA,IAAAC,MAAA;QAEA,SAAAC,UAAA;UACA;UACAD,MAAA,cAAAE,MAAA,CAAAC,QAAA;YACAvB,IAAA,OAAAA,IAAA;YACAC,QAAA,OAAAA,QAAA;YACA,QAAAN;UACA;QACA;UACA;UACA,MAAA6B,WAAA;YACAxB,IAAA,OAAAA,IAAA;YACAC,QAAA,OAAAA,QAAA;YACAL,MAAA,OAAAD,OAAA,CAAAC,MAAA;YACAC,UAAA,OAAAF,OAAA,CAAAE,UAAA;UACA;UAEA,MAAAkB,QAAA,SAAA1B,oBAAA,CAAAmC,WAAA;UACA,IAAAT,QAAA,CAAAxB,IAAA,CAAAyB,IAAA;YACAI,MAAA;cAAAK,OAAA;cAAAlC,IAAA,EAAAwB,QAAA,CAAAxB,IAAA,CAAAA;YAAA;UACA;YACA6B,MAAA;cAAAK,OAAA;cAAAC,OAAA,EAAAX,QAAA,CAAAxB,IAAA,CAAAoC;YAAA;UACA;QACA;QAEA,IAAAP,MAAA,CAAAK,OAAA;UACA,MAAAG,WAAA,GAAAR,MAAA,CAAA7B,IAAA,CAAAsC,IAAA;UACA,IAAAV,KAAA;YACA,KAAA1B,QAAA,GAAAmC,WAAA;UACA;YACA,KAAAnC,QAAA,CAAAqC,IAAA,IAAAF,WAAA;UACA;UAEA,KAAA1B,OAAA,GAAA0B,WAAA,CAAAG,MAAA,UAAA9B,QAAA;QACA;MACA,SAAAgB,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;QACA,KAAAnB,OAAA;QACA,KAAAC,WAAA;MACA;IACA;IAEA;IACAiC,SAAA;MACA,KAAAhC,IAAA;MACA,KAAAS,aAAA;IACA;IAEA;IACAwB,YAAAC,OAAA;MACA,UAAAb,UAAA;QACA,KAAAc,QAAA,CAAAC,OAAA;QACA,KAAAC,OAAA,CAAAP,IAAA;QACA;MACA;MAEA,KAAAO,OAAA,CAAAP,IAAA,aAAAI,OAAA,CAAAI,EAAA;IACA;IAEA;IACAC,iBAAA;MACA,KAAAC,KAAA,CAAAC,eAAA,CAAAC,cAAA;QAAAC,QAAA;MAAA;IACA;IAEA;IACAC,YAAAC,KAAA;MACA,KAAAA,KAAA,IAAAA,KAAA;MACA,OAAAC,MAAA,CAAAD,KAAA,EAAAE,cAAA;QACAC,qBAAA;QACAC,qBAAA;MACA;IACA;EACA;EACAC,KAAA;IACAvD,OAAA;MACAwD,QAAA;QACA,KAAA1C,aAAA;MACA;MACA2C,IAAA;IACA;EACA;AACA", "ignoreList": []}]}