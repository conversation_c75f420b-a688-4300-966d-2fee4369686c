{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\MyProjects.vue?vue&type=style&index=0&id=78d5ec26&lang=scss&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\MyProjects.vue", "mtime": 1757558268816}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757485139209}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757485152120}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757485142383}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757485135872}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MyProjects.vue"], "names": [], "mappings": ";AA6QA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "MyProjects.vue", "sourceRoot": "src/views/profile", "sourcesContent": ["<template>\n  <div class=\"my-projects-page\">\n    <AppHeader />\n    \n    <div class=\"container\">\n      <div class=\"profile-layout\">\n        <!-- 侧边栏 -->\n        <div class=\"sidebar\">\n          <div class=\"user-card\">\n            <div class=\"avatar\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"user-info\">\n              <h3>{{ userInfo?.name || '用户' }}</h3>\n              <p>{{ userInfo?.mobile }}</p>\n              <StatusTag :status=\"userInfo?.auditStatus\" type=\"audit\" />\n            </div>\n          </div>\n          \n          <nav class=\"nav-menu\">\n            <p @click=\"handleNavItemClick('/profile')\" class=\"nav-item\" >\n              <i class=\"el-icon-user\"></i>\n              个人资料\n            </p>\n            <p @click=\"handleNavItemClick('/profile/bids')\" class=\"nav-item\">\n              <i class=\"el-icon-price-tag\"></i>\n              我的出价\n            </p>\n            <p @click=\"handleNavItemClick('/profile/projects')\" class=\"nav-item\" exact>\n              <i class=\"el-icon-folder\"></i>\n              我的项目\n            </p>\n          </nav>\n        </div>\n\n        <!-- 主内容 -->\n        <div class=\"main-content\">\n          <div class=\"content-header\">\n            <h2>我的项目</h2>\n            <div class=\"filters\">\n              <el-select v-model=\"filters.status\" placeholder=\"项目状态\" clearable @change=\"fetchProjects\">\n                <el-option label=\"即将开始\" :value=\"0\" />\n                <el-option label=\"竞价中\" :value=\"1\" />\n                <el-option label=\"已结束\" :value=\"2\" />\n                <el-option label=\"已终止\" :value=\"3\" />\n              </el-select>\n              <el-select v-model=\"filters.participated\" placeholder=\"参与状态\" clearable @change=\"fetchProjects\">\n                <el-option label=\"已参与\" :value=\"true\" />\n                <el-option label=\"未参与\" :value=\"false\" />\n              </el-select>\n            </div>\n          </div>\n\n          <div class=\"projects-content\" v-loading=\"loading\">\n            <!-- 统计卡片 -->\n            <div class=\"stats-summary\">\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.totalProjects || 0 }}</div>\n                <div class=\"summary-label\">可参与项目</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.participatedProjects || 0 }}</div>\n                <div class=\"summary-label\">已参与项目</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.activeProjects || 0 }}</div>\n                <div class=\"summary-label\">进行中项目</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.wonProjects || 0 }}</div>\n                <div class=\"summary-label\">中标项目</div>\n              </div>\n            </div>\n\n            <!-- 项目列表 -->\n            <div class=\"projects-list\">\n              <div\n                v-for=\"project in projects\"\n                :key=\"project.id\"\n                class=\"project-item\"\n                :class=\"{ 'participated': project.participated, 'won': project.isWon }\"\n              >\n                <div class=\"project-header\">\n                  <h4 class=\"project-title\" @click=\"goToProject(project.id)\">\n                    {{ project.title }}\n                  </h4>\n                  <div class=\"project-status\">\n                    <StatusTag :status=\"project.status\" />\n                    <div class=\"participation-badge\" v-if=\"project.participated\">\n                      <i class=\"el-icon-check\"></i>\n                      已参与\n                    </div>\n                    <div class=\"won-badge\" v-if=\"project.isWon\">\n                      <i class=\"el-icon-trophy\"></i>\n                      中标\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"project-meta\">\n                  <span class=\"category\">{{ project.categoryName }}</span>\n                  <span class=\"quantity\">{{ project.quantity }}{{ project.unit }}</span>\n                </div>\n\n                <div class=\"project-info\">\n                  <div class=\"info-grid\">\n                    <div class=\"info-item\">\n                      <span class=\"label\">起拍价：</span>\n                      <span class=\"value\">¥{{ formatMoney(project.startPrice) }}</span>\n                    </div>\n                    <div class=\"info-item\">\n                      <span class=\"label\">当前价：</span>\n                      <span class=\"value current-price\">¥{{ formatMoney(project.currentPrice) }}</span>\n                    </div>\n                    <div class=\"info-item\">\n                      <span class=\"label\">出价次数：</span>\n                      <span class=\"value\">{{ project.bidCount }}次</span>\n                    </div>\n                    <div class=\"info-item\" v-if=\"project.participated\">\n                      <span class=\"label\">我的出价：</span>\n                      <span class=\"value my-bid\">¥{{ formatMoney(project.myBidAmount) }}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"project-time\">\n                  <template v-if=\"project.status === 0\">\n                    <span class=\"time-label\">开始时间：</span>\n                    <span class=\"time-value\">{{ project.startTime | formatTime }}</span>\n                  </template>\n                  <template v-else-if=\"project.status === 1\">\n                    <span class=\"time-label\">剩余时间：</span>\n                    <CountdownTimer :end-time=\"project.endTime\" />\n                  </template>\n                  <template v-else>\n                    <span class=\"time-label\">结束时间：</span>\n                    <span class=\"time-value\">{{ project.endTime | formatTime }}</span>\n                  </template>\n                </div>\n\n                <div class=\"project-actions\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"goToProject(project.id)\"\n                    v-if=\"project.status === 1\"\n                  >\n                    {{ project.participated ? '继续出价' : '立即参与' }}\n                  </el-button>\n                  <el-button\n                    size=\"small\"\n                    @click=\"goToProject(project.id)\"\n                  >\n                    查看详情\n                  </el-button>\n                </div>\n              </div>\n\n              <!-- 空状态 -->\n              <div v-if=\"projects.length === 0 && !loading\" class=\"empty-state\">\n                <el-empty description=\"暂无相关项目\">\n                  <el-button type=\"primary\" @click=\"$router.push('/home')\">\n                    浏览项目\n                  </el-button>\n                </el-empty>\n              </div>\n            </div>\n\n            <!-- 分页 -->\n            <div class=\"pagination\" v-if=\"total > 0\">\n              <el-pagination\n                @current-change=\"handlePageChange\"\n                :current-page=\"page\"\n                :page-size=\"pageSize\"\n                :total=\"total\"\n                layout=\"total, prev, pager, next, jumper\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'MyProjects',\n  data() {\n    return {\n      projects: [],\n      summary: {},\n      filters: {\n        status: '',\n        participated: ''\n      },\n      loading: false,\n      page: 1,\n      pageSize: 10,\n      total: 0\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['userInfo'])\n  },\n  mounted() {\n    this.fetchProjects()\n  },\n  methods: {\n    // 获取项目列表\n    async fetchProjects() {\n      this.loading = true\n      try {\n        const result = await this.$store.dispatch('user/fetchMyProjects', {\n          page: this.page,\n          pageSize: this.pageSize,\n          ...this.filters\n        })\n        \n        if (result.success) {\n          this.projects = result.data.list || []\n          this.total = result.data.total || 0\n          this.summary = result.data.summary || {}\n        }\n      } catch (error) {\n        console.error('获取项目列表失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 页码变化\n    handlePageChange(newPage) {\n      this.page = newPage\n      this.fetchProjects()\n    },\n\n    // 跳转到项目详情\n    goToProject(projectId) {\n      this.$router.push(`/auction/${projectId}`)\n    },\n\n    // 格式化金额\n    formatMoney(value) {\n      if (!value && value !== 0) return '0'\n      return Number(value).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })\n    },\n    handleNavItemClick(path) {\n      this.$router.push(path)\n    }\n  },\n  watch: {\n    filters: {\n      handler() {\n        this.page = 1\n        this.fetchProjects()\n      },\n      deep: true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.my-projects-page {\n  min-height: 110vh;\n  background: $bg-color;\n}\n\n.container {\n  padding: 20px;\n}\n\n.profile-layout {\n  display: flex;\n  gap: 30px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.sidebar {\n  width: 280px;\n  flex-shrink: 0;\n\n  .user-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    padding: 30px 20px;\n    text-align: center;\n    margin-bottom: 20px;\n\n    .avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background: $primary-color;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      margin: 0 auto 20px;\n    }\n\n    .user-info {\n      h3 {\n        color: $text-primary;\n        margin-bottom: 10px;\n      }\n\n      p {\n        color: $text-secondary;\n        margin-bottom: 15px;\n      }\n    }\n  }\n\n  .nav-menu {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 15px 20px;\n      color: $text-regular;\n      text-decoration: none;\n      border-bottom: 1px solid $border-color;\n      transition: all 0.3s;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover,\n      &.router-link-active {\n        background: $primary-color;\n        color: white;\n      }\n\n      i {\n        margin-right: 10px;\n        font-size: 16px;\n      }\n    }\n  }\n}\n\n.main-content {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: $box-shadow;\n  overflow: hidden;\n\n  .content-header {\n    padding: 30px 30px 0;\n    border-bottom: 1px solid $border-color;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    h2 {\n      color: $text-primary;\n      margin-bottom: 30px;\n    }\n\n    .filters {\n      display: flex;\n      gap: 15px;\n      margin-bottom: 30px;\n    }\n  }\n\n  .projects-content {\n    padding: 30px;\n\n    .stats-summary {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 30px;\n\n      .summary-item {\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        padding: 20px;\n        border-radius: 8px;\n        text-align: center;\n\n        .summary-number {\n          font-size: 24px;\n          font-weight: 700;\n          margin-bottom: 8px;\n        }\n\n        .summary-label {\n          font-size: 14px;\n          opacity: 0.9;\n        }\n      }\n    }\n\n    .projects-list {\n      .project-item {\n        background: $light-gray;\n        border-radius: 8px;\n        padding: 20px;\n        margin-bottom: 20px;\n        border-left: 4px solid transparent;\n        transition: all 0.3s;\n\n        &.participated {\n          border-left-color: $primary-color;\n          background: linear-gradient(90deg, #f0f9ff 0%, $light-gray 100%);\n        }\n\n        &.won {\n          border-left-color: $success-color;\n          background: linear-gradient(90deg, #f0f9f0 0%, $light-gray 100%);\n        }\n\n        .project-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 15px;\n\n          .project-title {\n            color: $text-primary;\n            cursor: pointer;\n            transition: color 0.3s;\n            flex: 1;\n            margin-right: 20px;\n\n            &:hover {\n              color: $primary-color;\n            }\n          }\n\n          .project-status {\n            display: flex;\n            align-items: center;\n            gap: 10px;\n\n            .participation-badge,\n            .won-badge {\n              padding: 4px 8px;\n              border-radius: 12px;\n              font-size: 12px;\n              font-weight: 500;\n              display: flex;\n              align-items: center;\n              gap: 4px;\n            }\n\n            .participation-badge {\n              background: $primary-color;\n              color: white;\n            }\n\n            .won-badge {\n              background: $success-color;\n              color: white;\n            }\n          }\n        }\n\n        .project-meta {\n          display: flex;\n          align-items: center;\n          gap: 15px;\n          margin-bottom: 15px;\n\n          .category,\n          .quantity {\n            padding: 4px 8px;\n            background: white;\n            border-radius: 4px;\n            font-size: 12px;\n            color: $text-secondary;\n          }\n        }\n\n        .project-info {\n          margin-bottom: 15px;\n\n          .info-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n            gap: 10px;\n\n            .info-item {\n              display: flex;\n              justify-content: space-between;\n\n              .label {\n                color: $text-secondary;\n              }\n\n              .value {\n                font-weight: 500;\n                color: $text-primary;\n\n                &.current-price {\n                  color: $danger-color;\n                }\n\n                &.my-bid {\n                  color: $primary-color;\n                  font-weight: 600;\n                }\n              }\n            }\n          }\n        }\n\n        .project-time {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n          padding-top: 15px;\n          border-top: 1px solid #e0e0e0;\n\n          .time-label {\n            color: $text-secondary;\n            font-size: 14px;\n          }\n\n          .time-value {\n            font-weight: 600;\n            color: $text-primary;\n          }\n        }\n\n        .project-actions {\n          text-align: right;\n        }\n      }\n\n      .empty-state {\n        padding: 60px 0;\n        text-align: center;\n      }\n    }\n\n    .pagination {\n      margin-top: 30px;\n      text-align: center;\n    }\n  }\n}\n\n@media (max-width: $tablet) {\n  .profile-layout {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n\n    .user-card {\n      display: flex;\n      align-items: center;\n      text-align: left;\n      gap: 20px;\n\n      .avatar {\n        margin: 0;\n      }\n    }\n\n    .nav-menu {\n      display: flex;\n      overflow-x: auto;\n\n      .nav-item {\n        white-space: nowrap;\n        border-bottom: none;\n        border-right: 1px solid $border-color;\n\n        &:last-child {\n          border-right: none;\n        }\n      }\n    }\n  }\n\n  .main-content .content-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n}\n\n@media (max-width: $mobile) {\n  .container {\n    padding: 15px;\n  }\n\n  .projects-content {\n    padding: 20px !important;\n\n    .stats-summary {\n      grid-template-columns: repeat(2, 1fr);\n    }\n\n    .projects-list .project-item {\n      .project-header {\n        flex-direction: column;\n        gap: 10px;\n      }\n\n      .project-info .info-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  }\n}\n</style>\n"]}]}