package service

import (
	"auction-sys/global"
	"auction-sys/model"
	"errors"
)

// @Title 删除基础路由
// @Description
// <AUTHOR>
// @Param id 		float64		路由 id
// @Return err 		error 		错误信息

func DeleteBaseMenu(id int) (err error) {
	err = global.GVA_DB.Where("parent_id = ?", id).First(&model.SysBaseMenu{}).Error
	if err != nil {
		var menu model.SysBaseMenu
		db := global.GVA_DB.Preload("SysAuthoritys").Where("id = ?", id).First(&menu).Unscoped().Delete(&menu)
		if len(menu.SysAuthoritys) > 0 {
			err = db.Association("SysAuthoritys").Delete(menu.SysAuthoritys).Error
		} else {
			err = db.Error
		}
	} else {
		return errors.New("此菜单存在子菜单或按钮不可删除")
	}
	if err != nil {
		return errors.New("删除失败")
	}
	return err
}

// @Title 更新路由
// @Description
// <AUTHOR>
// @Param menu 		model.SysBaseMenu		路由信息
// @Return err 		error 					错误信息

func UpdateBaseMenu(menu model.SysBaseMenu) (err error) {
	var oldMenu model.SysBaseMenu
	upDateMap := make(map[string]interface{})
	upDateMap["keep_alive"] = menu.KeepAlive
	upDateMap["default_menu"] = menu.DefaultMenu
	upDateMap["parent_id"] = menu.ParentId
	upDateMap["path"] = menu.Path
	upDateMap["name"] = menu.Name
	upDateMap["hidden"] = menu.Hidden
	upDateMap["component"] = menu.Component
	upDateMap["title"] = menu.Title
	upDateMap["icon"] = menu.Icon
	upDateMap["sort"] = menu.Sort
	upDateMap["keyval"] = menu.Keyval
	upDateMap["disabled"] = menu.Disabled
	db := global.GVA_DB.Where("id = ?", menu.ID).Find(&oldMenu)
	if oldMenu.Name != menu.Name {
		notSame := global.GVA_DB.Where("id <> ? AND name = ?", menu.ID, menu.Name).First(&model.SysBaseMenu{}).RecordNotFound()
		if !notSame {
			global.GVA_LOG.Debug("存在相同name修改失败")
			return errors.New("存在相同name修改失败")
		}
	}
	err = db.Updates(upDateMap).Error
	global.GVA_LOG.Debug("菜单修改时候，关联菜单err:%v", err)
	return err
}

// @Title 根据 id 获取路由
// @Description
// <AUTHOR>
// @Param id 		float64					路由 id
// @Return err 		error 					错误信息
// @Return menu 	model.SysBaseMenu		路由信息

func GetBaseMenuById(id int) (err error, menu model.SysBaseMenu) {
	err = global.GVA_DB.Where("id = ?", id).First(&menu).Error
	return
}
