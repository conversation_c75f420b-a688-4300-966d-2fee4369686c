<template>
  <div class="my-bids-page">
    <AppHeader />
    
    <div class="container">
      <div class="profile-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
          <div class="user-card">
            <div class="avatar">
              <i class="el-icon-user"></i>
            </div>
            <div class="user-info">
              <h3>{{ userInfo?.name || '用户' }}</h3>
              <p>{{ userInfo?.mobile }}</p>
              <StatusTag :status="userInfo?.auditStatus" type="audit" />
            </div>
          </div>
          
          <nav class="nav-menu">
            <p @click="handleNavItemClick('/profile')" class="nav-item" >
              <i class="el-icon-user"></i>
              个人资料
            </p>
            <p @click="handleNavItemClick('/profile/bids')" class="nav-item router-link-active" >
              <i class="el-icon-price-tag"></i>
              我的出价
            </p>
            <p @click="handleNavItemClick('/profile/projects')" class="nav-item" >
              <i class="el-icon-folder"></i>
              我的项目
            </p>
          </nav>
        </div>

        <!-- 主内容 -->
        <div class="main-content">
          <div class="content-header">
            <h2>我的出价</h2>
            <div class="filters">
              <el-select v-model="filters.status" placeholder="项目状态" clearable @change="fetchBids">
                <el-option label="即将开始" :value="0" />
                <el-option label="竞价中" :value="1" />
                <el-option label="已结束" :value="2" />
                <el-option label="已终止" :value="3" />
              </el-select>
              <el-select v-model="filters.isWinning" placeholder="出价状态" clearable @change="fetchBids">
                <el-option label="领先" :value="true" />
                <el-option label="被超越" :value="false" />
              </el-select>
            </div>
          </div>

          <div class="bids-content" v-loading="loading">
            <!-- 统计卡片 -->
            <div class="stats-summary">
              <div class="summary-item">
                <div class="summary-number">{{ summary.totalBids || 0 }}</div>
                <div class="summary-label">总出价次数</div>
              </div>
              <div class="summary-item">
                <div class="summary-number">{{ summary.wonBids || 0 }}</div>
                <div class="summary-label">中标次数</div>
              </div>
              <div class="summary-item">
                <div class="summary-number">¥{{ formatMoney(summary.totalAmount) }}</div>
                <div class="summary-label">累计出价金额</div>
              </div>
            </div>

            <!-- 出价列表 -->
            <div class="bids-list">
              <div
                v-for="bid in bids"
                :key="bid.id"
                class="bid-item"
                :class="{ 'is-winning': bid.isWinning, 'is-won': bid.isWon }"
              >
                <div class="bid-project">
                  <h4 class="project-title" @click="goToProject(bid.projectId)">
                    {{ bid.projectTitle }}
                  </h4>
                  <div class="project-meta">
                    <StatusTag :status="bid.projectStatus" />
                    <span class="category">{{ bid.categoryName }}</span>
                    <span class="quantity">{{ bid.quantity }}{{ bid.unit }}</span>
                  </div>
                </div>

                <div class="bid-info">
                  <div class="bid-details">
                    <div class="detail-item">
                      <span class="label">我的出价：</span>
                      <span class="value my-bid">¥{{ formatMoney(bid.bidAmount) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">当前最高价：</span>
                      <span class="value current-price">¥{{ formatMoney(bid.currentPrice) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">出价时间：</span>
                      <span class="value">{{ bid.bidTime | formatTime }}</span>
                    </div>
                  </div>

                  <div class="bid-status">
                    <div class="status-badge" v-if="bid.isWon">
                      <i class="el-icon-trophy"></i>
                      中标
                    </div>
                    <div class="status-badge winning" v-else-if="bid.isWinning">
                      <i class="el-icon-star-on"></i>
                      领先
                    </div>
                    <div class="status-badge" v-else>
                      <i class="el-icon-star-off"></i>
                      被超越
                    </div>
                  </div>
                </div>

                <div class="bid-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="goToProject(bid.projectId)"
                    v-if="bid.projectStatus === 1"
                  >
                    继续出价
                  </el-button>
                  <el-button
                    size="small"
                    @click="goToProject(bid.projectId)"
                  >
                    查看详情
                  </el-button>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="bids.length === 0 && !loading" class="empty-state">
                <el-empty description="暂无出价记录">
                  <el-button type="primary" @click="$router.push('/home')">
                    去竞价
                  </el-button>
                </el-empty>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination" v-if="total > 0">
              <el-pagination
                @current-change="handlePageChange"
                :current-page="page"
                :page-size="pageSize"
                :total="total"
                layout="total, prev, pager, next, jumper"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'MyBids',
  data() {
    return {
      bids: [],
      summary: {},
      filters: {
        status: '',
        isWinning: ''
      },
      loading: false,
      page: 1,
      pageSize: 10,
      total: 0
    }
  },
  computed: {
    ...mapGetters('auth', ['userInfo'])
  },
  mounted() {
    this.fetchBids()
  },
  methods: {
    // 获取出价记录
    async fetchBids() {
      this.loading = true
      try {
        const result = await this.$store.dispatch('user/fetchMyBids', {
          page: this.page,
          pageSize: this.pageSize,
          ...this.filters
        })
        
        if (result.success) {
          this.bids = result.data.list || []
          this.total = result.data.total || 0
          this.summary = result.data.summary || {}
        }
      } catch (error) {
        console.error('获取出价记录失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 页码变化
    handlePageChange(newPage) {
      this.page = newPage
      this.fetchBids()
    },

    // 跳转到项目详情
    goToProject(projectId) {
      this.$router.push(`/auction/${projectId}`)
    },

    // 格式化金额
    formatMoney(value) {
      if (!value && value !== 0) return '0'
      return Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    handleNavItemClick(path) {
      this.$router.push(path)
    }
  },
  watch: {
    filters: {
      handler() {
        this.page = 1
        this.fetchBids()
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.my-bids-page {
  min-height: 110vh;
  background: $bg-color;
}

.container {
  padding: 20px;
}

.profile-layout {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.sidebar {
  width: 280px;
  flex-shrink: 0;

  .user-card {
    background: white;
    border-radius: 8px;
    box-shadow: $box-shadow;
    padding: 30px 20px;
    text-align: center;
    margin-bottom: 20px;

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: $primary-color;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      margin: 0 auto 20px;
    }

    .user-info {
      h3 {
        color: $text-primary;
        margin-bottom: 10px;
      }

      p {
        color: $text-secondary;
        margin-bottom: 15px;
      }
    }
  }

  .nav-menu {
    background: white;
    border-radius: 8px;
    box-shadow: $box-shadow;
    overflow: hidden;

    .nav-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      color: $text-regular;
      text-decoration: none;
      border-bottom: 1px solid $border-color;
      transition: all 0.3s;

      &:last-child {
        border-bottom: none;
      }

      &:hover,
      &.router-link-active {
        background: $primary-color;
        color: white;
      }

      i {
        margin-right: 10px;
        font-size: 16px;
      }
    }
  }
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: $box-shadow;
  overflow: hidden;

  .content-header {
    padding: 30px 30px 0;
    border-bottom: 1px solid $border-color;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      color: $text-primary;
      margin-bottom: 30px;
    }

    .filters {
      display: flex;
      gap: 15px;
      margin-bottom: 30px;
    }
  }

  .bids-content {
    padding: 30px;

    .stats-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;

      .summary-item {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;

        .summary-number {
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 8px;
        }

        .summary-label {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }

    .bids-list {
      .bid-item {
        background: $light-gray;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid transparent;
        transition: all 0.3s;

        &.is-winning {
          border-left-color: $warning-color;
          background: linear-gradient(90deg, #fff7e6 0%, $light-gray 100%);
        }

        &.is-won {
          border-left-color: $success-color;
          background: linear-gradient(90deg, #f0f9f0 0%, $light-gray 100%);
        }

        .bid-project {
          margin-bottom: 15px;

          .project-title {
            color: $text-primary;
            margin-bottom: 10px;
            cursor: pointer;
            transition: color 0.3s;

            &:hover {
              color: $primary-color;
            }
          }

          .project-meta {
            display: flex;
            align-items: center;
            gap: 15px;

            .category,
            .quantity {
              padding: 2px 8px;
              background: white;
              border-radius: 4px;
              font-size: 12px;
              color: $text-secondary;
            }
          }
        }

        .bid-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;

          .bid-details {
            .detail-item {
              display: flex;
              gap: 10px;
              margin-bottom: 5px;

              .label {
                color: $text-secondary;
                min-width: 100px;
              }

              .value {
                font-weight: 500;
                color: $text-primary;

                &.my-bid {
                  color: $primary-color;
                  font-weight: 600;
                }

                &.current-price {
                  color: $danger-color;
                  font-weight: 600;
                }
              }
            }
          }

          .bid-status {
            .status-badge {
              padding: 8px 12px;
              border-radius: 20px;
              font-size: 14px;
              font-weight: 500;
              background: $text-secondary;
              color: white;
              display: flex;
              align-items: center;
              gap: 5px;

              &.winning {
                background: $warning-color;
              }

              i {
                font-size: 16px;
              }
            }
          }
        }

        .bid-actions {
          text-align: right;
        }
      }

      .empty-state {
        padding: 60px 0;
        text-align: center;
      }
    }

    .pagination {
      margin-top: 30px;
      text-align: center;
    }
  }
}

@media (max-width: $tablet) {
  .profile-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;

    .user-card {
      display: flex;
      align-items: center;
      text-align: left;
      gap: 20px;

      .avatar {
        margin: 0;
      }
    }

    .nav-menu {
      display: flex;
      overflow-x: auto;

      .nav-item {
        white-space: nowrap;
        border-bottom: none;
        border-right: 1px solid $border-color;

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  .main-content .content-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: $mobile) {
  .container {
    padding: 15px;
  }

  .bids-content {
    padding: 20px !important;

    .stats-summary {
      grid-template-columns: repeat(2, 1fr);
    }

    .bids-list .bid-item .bid-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }
  }
}
</style>
