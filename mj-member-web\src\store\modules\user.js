import { getMyBids, getMyProjects, updateProfile } from '@/api/user'

const state = {
  myBids: [],
  myProjects: [],
  profile: null
}

const mutations = {
  SET_MY_BIDS(state, bids) {
    state.myBids = bids
  },
  SET_MY_PROJECTS(state, projects) {
    state.myProjects = projects
  },
  SET_PROFILE(state, profile) {
    state.profile = profile
  }
}

const actions = {
  // 获取我的出价记录
  async fetchMyBids({ commit }, params = {}) {
    try {
      const response = await getMyBids(params)
      if (response.data.code === 200) {
        commit('SET_MY_BIDS', response.data.data.list || [])
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('获取出价记录失败:', error)
      return { success: false, message: '获取出价记录失败' }
    }
  },

  // 获取我的项目
  async fetchMyProjects({ commit }, params = {}) {
    try {
      const response = await getMyProjects(params)
      if (response.data.code === 200) {
        commit('SET_MY_PROJECTS', response.data.data.list || [])
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('获取项目列表失败:', error)
      return { success: false, message: '获取项目列表失败' }
    }
  },

  // 更新个人资料
  async updateProfile({ commit }, profileData) {
    try {
      const response = await updateProfile(profileData)
      if (response.data.code === 200) {
        commit('SET_PROFILE', response.data.data)
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('更新个人资料失败:', error)
      return { success: false, message: '更新个人资料失败' }
    }
  }
}

const getters = {
  myBids: state => state.myBids,
  myProjects: state => state.myProjects,
  profile: state => state.profile
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
