package resp

type PageResult struct {
	List     interface{} `json:"list"`
	Total    int         `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"pageSize"`
}

type RespKv struct {
	Id   int    `json:"id"`
	Name string `json:"value"`
}

type KvResult struct {
	Id    int    `json:"id"`
	Label string `json:"label"`
}

type TreeData struct {
	Id       int        `json:"id"`
	Label    string     `json:"label"`
	Children []TreeData `json:"children"`
}

type TreeDataApp struct {
	Value    int           `json:"value"`
	Text     string        `json:"text"`
	Children []TreeDataApp `json:"children"`
}
