// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
ElementUI.Dialog.props.closeOnClickModal.default = false;

import '@/assets/globalStyle/index.scss' //引入自定义全局样式

import '@/assets/svgIcon/menuIconfont' //引入SVG图标js文件

import '@/styles/palyFontIcon/playIconfont.css' // 引入播放功能字体图标

import './permission' // permission control


Vue.use(ElementUI,{ size: 'small' })

Vue.config.productionTip = false

Date.prototype.Format=function(fmt){
    //author: meizz
   var o = {
     "M+" : this.getMonth()+1,                 //月份
     "d+" : this.getDate(),                    //日
     "h+" : this.getHours(),                   //小时
     "m+" : this.getMinutes(),                 //分
     "s+" : this.getSeconds(),                 //秒
     "q+" : Math.floor((this.getMonth()+3)/3), //季度
     "S"  : this.getMilliseconds()             //毫秒
   };
   if(/(y+)/.test(fmt))
     fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
   for(var k in o)
     if(new RegExp("("+ k +")").test(fmt))
   fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
   return fmt;
 }



/* eslint-disable no-new */
// 允许new创建对象但可以不将结果对象分配给变量，如var obj=new Object(),允许使用new Object()这种形式
new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App)
})
