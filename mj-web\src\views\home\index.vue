<template>
  <div class="home-bgColor">
    <div class="home-wrap">
        竞拍系统
    
    </div>
  </div>
</template>

<script>

export default {
  name: "Home",
  data() {
    return {

    };
  },

  created() {

  },
  methods: {

  },    
};
</script>

<style lang="scss" scoped>
.home-bgColor {
  height: 100%;
  .home-wrap {
    padding: 10px 0px;
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    overflow: auto;
    #mapDiv{
      flex: 1;
    }
    .float-window{
      position:absolute;
      z-index: 1000;
      padding: 10px;
      width: 0px;
      height: 0px;
      border: 1px solid #747171;
      background: #0008;
      transition: all 0.1s linear;
      overflow: hidden;
      color: #FFFFFF;
      .window-close{
        text-align: right;
        cursor: pointer;
        :hover{
          transform: scale(1.2);
        }
      }
      .device-info{
        font-size: 14px;
        background: #4e4d4d88;
        padding: 5px;
        border-radius: 4px;
        color: #d6d1d1;
        span{
          display:inline-block;
          width: 70px;
          text-align:right;
        }
      }
    }
    .float-window.show{
      width: 300px;
      height: 500px;
    }
  }
}
</style>
