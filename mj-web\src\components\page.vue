<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-24 11:07:57
 * @LastEditors: dlg
 * @LastEditTime: 2020-06-28 13:53:48
-->
<template>
  <div class="page">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="currentPage"
      :page-sizes="pageSizes"
      :page-size.sync="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: "Page",
  components: {},
  props: {
    total: Number,
    page: Number,
    limit: Number,
    pageSizes: Array
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(val) {
        this.$emit("update:page", val);
      }
    },
    pageSize: {
      get() {
        return this.limit;
      },
      set(val) {
        this.$emit("update:limit", val);
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.$emit("search", { currentPage: this.currentPage, limit: val });
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.$emit("search", { currentPage: val, limit: this.limit });
    }
  }
};
</script>

<style lang='scss' scoped>
.page {
  text-align: center;
  padding: 20px 0;
}
</style>