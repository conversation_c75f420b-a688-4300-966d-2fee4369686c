<template>
  <div class="app-container">
    <div style="margin-bottom: 20px;min-width:720px;">
      <span style="color:#606266;font-size:14px;">用户账号/姓名：</span>
      <el-input
        v-model="userNameSearch"
        placeholder="请输入用户账号/姓名"
        style="width: 200px;margin-right:32px;"
        clearable
      />
      <span style="color:#606266;font-size:14px;">所属企业：</span>
      <el-select v-model="comany"
                 placeholder="请选择"
                 style="margin-right:12px;"
                 clearable>
        <el-option
          v-for="item in companyOptions"
          :key="item.code"
          :label="item.name"
          :value="item.code"
        />
      </el-select>
      <el-button @click="getTableData" type="primary">查询</el-button>
    </div>
    <div class="button-box">
      <el-button @click="addUser" type="primary">新增用户</el-button>
      <el-button type="success" @click="batchEnable">批量启用</el-button>
      <el-button type="info" @click="batchDisable">批量禁用</el-button>
      <el-button type="danger" @click="batchDel">批量删除</el-button>
    </div>
    <el-table :data="tableData" height="100%" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="序号" min-width="80">
        <template slot-scope="scope">{{ scope.$index + (page - 1) * limit + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="用户账号" min-width="150" prop="userName" />
      <el-table-column align="center" label="真实姓名" min-width="150" prop="nickName" />
      <el-table-column align="center" label="邮箱地址" min-width="150" prop="email" />
      <el-table-column align="center" label="添加时间" min-width="180" sortable prop="createdAt" />
      <el-table-column align="center" label="登陆状态" min-width="80">
        <template slot-scope="scope">{{ scope.row.loginStatus === 1?'在线':'离线' }}</template>
      </el-table-column>
      <el-table-column align="center" label="最后登陆" min-width="180" sortable>
        <template slot-scope="scope">
          {{ new Date(scope.row.loginTime.split(' ')[0]).getTime() &lt; 0 ? '' : scope.row.loginTime }}
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="状态" :filters="disArray" :filter-method="filterStatus" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            class="mySwitch"
            active-text="开"
            inactive-text="关"
            @change="switchDisable(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="300">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="resetPass(scope.row)">重置密码</el-button>
          <!-- <el-button type="primary" size="small" @click="permissHandle(scope.row)">权限</el-button> -->
          <el-button type="primary" size="small" @click="editHandle(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="deleteUser(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer
      :title="drawerTitle"
      :visible.sync="addUserDialog"
      direction="rtl"
      size="50%"
      custom-class="myDrawer mysection"
    >
      <el-tabs v-model="editableTabsValue" type="border-card" class="userTabs">
        <el-tab-pane label="用户信息" name="1">
          <div class="userInfoMess">
            <el-form
              :rules="rules"
              ref="userForm"
              :model="userInfo"
              style="overflow:hidden;height:100%;display:flex;flex-direction:column;"
            >
              <el-form-item label="用户账号" label-width="80px" prop="userName">
                <el-input v-model="userInfo.userName" :disabled="drawerTitle != '新增用户'?true:false" />
              </el-form-item>
              <el-form-item label="姓名" label-width="80px" prop="nickName">
                <el-input v-model="userInfo.nickName" />
              </el-form-item>
              <el-form-item label="邮箱地址" label-width="80px" prop="email">
                <el-input v-model="userInfo.email" />
              </el-form-item>
              <el-form-item label="是否启用" label-width="80px" prop="status">
                <el-radio v-model="userInfo.status" :label="1">启用</el-radio>
                <el-radio v-model="userInfo.status" :label="0">禁用</el-radio>
              </el-form-item>
              <el-form-item label="用户角色" label-width="80px" prop="authorityIds">
                <el-select
                  ref="authRef"
                  v-model="userInfo.authorityIds"
                  @change="changeAuth"
                  @remove-tag="removeTag"
                  multiple
                  placeholder="请选择"
                  style="width:300px;"
                >
                  <el-option v-for="item in authOptions" :key="item.id" :label="item.authorityName" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="treeDatas && treeDatas.length>0" style="overflow:auto;height:100%;">
                <el-tree :data="treeDatas" :props="defaultProps" :default-expand-all="true" />
              </el-form-item>
            </el-form>
            <div style="text-align:right;">
              <el-button @click="closeAddUserDialog">取 消</el-button>
              <el-button @click="enterAddUserDialog" type="primary">确 定</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    <Page
      v-if="tableData.length>0"
      :total="total"
      :page.sync="page"
      :limit.sync="limit"
      :page-sizes="pageSizes"
      @search="getTableData"
    />
  </div>
</template>
<script>
import {
  getAuthList,
  getUserList,
  setUserAuthority,
  register,
  enableOrDisable,
  resetPass,
  deleteUser,
  getCompany,
  setUserTemp,
} from "@/api/superMg/userMg";
import { getAuthSearch } from "@/api/superMg/roleMg";
import Page from "@/components/page";

export default {
  name: "UserMg",
  components: {
    Page,
  },
  data() {
    return {
      userNameSearch: "",
      comany: "",
      companyOptions: [],
      disArray: [
        { text: "禁用", value: 0 },
        { text: "启用", value: 1 },
      ],
      defaultProps: {
        // tree控件 props 属性配置
        children: "children",
        label: "title",
      },
      treeDatas: [],
      tableData: [],
      authOptions: [],
      addUserDialog: false,
      userInfo: {
        id: "",
        userName: "",
        nickName: "",
        email: "",
        status: 1,
        authorityIds: [], // 用户角色
      },
      rules: {
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的手机号",
          },
        ],
        nickName: [
          { required: true, message: "请输入用户昵称", trigger: "blur" },
        ],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: "blur",
          },
        ],
        status: [
          { required: true, message: "请选择是否启用", trigger: "change" },
        ],
        authorityIds: [
          { required: true, message: "请选择用户角色", trigger: "blur" },
        ],
      },
      multipleSelection: [],
      drawerTitle: "",
      editableTabsValue: "1",
      areas: [],

      areaProps: {
        checkStrictly: true,
        value: "id",
        label: "name",
      },
      page: 1,
      limit: 15,
      pageSizes: [15, 30, 50, 100],
      total: 0,
    };
  },
  async created() {
    const res = await getAuthList(); //获取所有用户角色信息
    if (res.data.code == 200) {
      this.authOptions = res.data.data;
    }
    this.getTableData();
    this.getCompanys();
  },
  methods: {
    // 列表状态栏过滤
    filterStatus(value, row) {
      return row.status == value;
    },
    //查询列表
    async getTableData() {
      const data = {
        companyCode: this.comany,
        username: this.userNameSearch,
        page: this.page,
        pageSize: this.limit,
      };

      const res = await getUserList(data);
      if (res.data.code == 200) {
        this.tableData = res.data.data.list;
        for (let i = 0; i < this.tableData.length; i++) {
          let arrT = this.tableData[i].authoritys;
          let handArr = [];
          for (const item of arrT) {
            handArr.push(item.id);
          }
          this.tableData[i].authorityIds = handArr;
        }
        this.total = res.data.data.total;
        this.page = res.data.data.page;
        this.limit = res.data.data.pageSize;
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    // 获取企业
    async getCompanys() {
      const res = await getCompany();
      if (res.data.code == 200) {
        this.companyOptions = res.data.data;
      } else {
        this.companyOptions = [];
      }
    },

    //删除用户
    async deleteUser(row) {
      this.$confirm("确认要删除此用户吗?", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await deleteUser([{ id: row.id }]);
          if (res.data.code == 200) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.getTableData();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 重置密码
    async resetPass(row) {
      this.$confirm("确认要重置密码吗?", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await resetPass({ id: row.id });
          if (res.data.code == 200) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    //新增/编辑用户
    async enterAddUserDialog() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          const res = await register(this.userInfo);
          if (res.data.code == 200) {
            this.$message({ type: "success", message: res.data.msg });
            this.getTableData();
            this.closeAddUserDialog();
            this.treeDatas = []; // 置空
          } else {
            this.$message({ type: "error", message: res.data.msg });
          }
        }
      });
    },
    // 新增/编辑权限
    async enterTempAuth() {
      const data = {
        areaId: this.areas.length > 0 ? this.areas[this.areas.length - 1] : "",
        type:
          this.areas.length > 0
            ? this.areas.length == 1
              ? "com"
              : this.areas.length == 2
              ? "temp"
              : this.areas.length == 3
              ? "area"
              : "small"
            : "",
        id: this.userInfo.id,
      };
      const res = await setUserTemp(data);
      if (res.data.code == 200) {
        this.$message({ type: "success", message: res.data.msg });
        this.getTableData();
        this.closeAddUserDialog();
        this.treeDatas = []; // 置空
      } else {
        this.$message({ type: "error", message: res.data.msg });
      }
    },
    // 选择角色
    async changeAuth(val) {
      //判断是否在删除最后一项数据，提示不允许删除
      if (this.userInfo.authorityIds.length === 0) {
        this.$message({ type: "warning", message: "至少保留一个角色" });
        return;
      }

      let arr = [];
      for (let i = 0; i < val.length; i++) {
        arr.push({ id: val[i] });
      }
      const res = await getAuthSearch(arr);
      if (res.data.code == 200) {
        this.treeDatas = res.data.data;
        this.getTableData();
      } else {
        this.treeDatas = [];
      }
    },
    //删除角色tag时判断是否为最后一个,如果是，则不允许删除
    removeTag(val) {
      if (this.userInfo.authorityIds.length === 0) {
        this.userInfo.authorityIds.push(val);
      }
    },
    // 关闭新增弹框
    closeAddUserDialog() {
      this.addUserDialog = false;
    },
    // 打开新增弹框
    addUser() {
      this.treeDatas = []; //新增时打开置空tree菜单
      if (this.$refs.userForm) {
        this.$refs.userForm.clearValidate();
      }
      // 清空操作
      this.areas = [];
      this.userInfo.id = "";
      this.userInfo.userName = "";
      this.userInfo.nickName = "";
      this.userInfo.email = "";
      this.userInfo.status = 1;
      this.userInfo.authorityIds = [];
      this.areas = []; // 重置温区权限选择区域值为空
      this.editableTabsValue = "1";
      this.drawerTitle = "新增用户";
      this.addUserDialog = true;
    },
    //设置用户角色权限
    async changeAuthority(row) {
      const res = await setUserAuthority({
        id: row.id,
        authorityIds: row.authorityIds,
      });
      if (res.data.code == 200) {
        this.$message({ type: "success", message: res.data.msg });
      } else {
        this.$message({ type: "error", message: res.data.msg });
      }
    },
    // 列表禁用/启用状态更改
    async switchDisable(row) {
      const res = await enableOrDisable([{ id: row.id, status: row.status }]);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //权限编辑
    permissHandle(row) {
      if (this.$refs.userForm) {
        this.$refs.userForm.clearValidate();
      }
      this.areas = []; // 重置温区权限选择区域值为空
      this.getValues(row);
      this.editableTabsValue = "2";
      this.drawerTitle = "权限";
      this.addUserDialog = true;
    },
    // 用户信息编辑
    editHandle(row) {
      if (this.$refs.userForm) {
        this.$refs.userForm.clearValidate();
      }
      this.areas = []; // 重置温区权限选择区域值为空
      this.getValues(row);
      this.editableTabsValue = "1";
      this.drawerTitle = "编辑";
      this.addUserDialog = true;
    },
    //编辑携带信息
    getValues(row) {
      (this.userInfo.id = row.id), (this.userInfo.userName = row.userName);
      this.userInfo.nickName = row.nickName;
      this.userInfo.email = row.email;
      this.userInfo.status = row.status;
      this.userInfo.authorityIds = row.authorityIds;
      this.changeAuth(row.authorityIds); //获取对应角色权限菜单树
    },
    //启用或禁用
    async disOrEn(num) {
      let arrSelect = [];
      for (const item of this.multipleSelection) {
        arrSelect.push({ id: item.id, status: num });
      }
      const res = await enableOrDisable(arrSelect);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.getTableData();
        this.multipleSelection = [];
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    isEmpty() {
      return this.multipleSelection.length === 0;
    },
    //批量启用
    async batchEnable() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      const bool = this.multipleSelection.every((item) => {
        return item.status == 1;
      });
      if (bool) {
        this.$message({
          type: "warning",
          message: "所选数据全部是已启用状态，请重新选择",
        });
      } else {
        this.disOrEn(1);
      }
    },
    //批量禁用
    async batchDisable() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      const bool = this.multipleSelection.every((item) => {
        return item.status == 0;
      });
      if (bool) {
        this.$message({
          type: "warning",
          message: "所选数据全部是已禁用状态，请重新选择",
        });
      } else {
        this.disOrEn(0);
      }
    },
    //批量删除
    async batchDel() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      let arrSelect = [];
      for (const item of this.multipleSelection) {
        arrSelect.push({ id: item.id });
      }
      const res = await deleteUser(arrSelect);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.getTableData();
        this.multipleSelection = [];
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.button-box {
  padding-bottom: 20px;
  min-width: 460px;
}
.userTabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.userInfoMess {
  padding: 15px 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>

<style>
.myDrawer,
.myDrawer .el-drawer__close-btn,
.myDrawer .el-drawer__header span {
  outline: none;
}
.userTabs .el-tabs__content {
  height: 100%;
}
.userTabs .el-tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.mysection .el-drawer__body {
  overflow: hidden;
}
.mysection .el-select__tags-text {
  display: inline-block;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mysection .el-select .el-tag__close.el-icon-close {
  top: -7px;
}
</style>
