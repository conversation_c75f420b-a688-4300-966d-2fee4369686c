package model

// 菜单对象
type SysBaseMenu struct {
	Model
	MenuLevel     uint   `json:"-"`
	ParentId      int    `json:"parentId" gorm:"default:0;comment:'父菜单ID'"`
	Path          string `json:"path" gorm:"comment:'路由path'"`
	Name          string `json:"name" gorm:"comment:'路由name'"`
	Hidden        bool   `json:"hidden" gorm:"default:'0'';comment:'是否在列表隐藏'"`
	Component     string `json:"component" gorm:"comment:'对应前端文件路径'"`
	Sort          int    `json:"sort" gorm:"default:'0'';comment:'排序标记'"`
	Category      int8   `json:"category" gorm:"comment:'菜单类别 菜单：0  按钮：1'"`
	Keyval        string `json:"keyval" gorm:"comment:'按钮标识'"`
	Disabled      bool   `json:"disabled" gorm:"default:'0';comment:'是否权限必选'"`
	Meta          `json:"meta" gorm:"comment:'附加属性'"`
	SysAuthoritys []SysAuthority `json:"authoritys" gorm:"many2many:sys_authority_menus;"`
	Children      []SysBaseMenu  `json:"children"`
}

// 附加属性
type Meta struct {
	KeepAlive   bool   `json:"noCache" gorm:"comment:'是否缓存'"`
	DefaultMenu bool   `json:"defaultMenu" gorm:"comment:'是否是基础路由（开发中）'"`
	Title       string `json:"title" gorm:"comment:'菜单名'"`
	Icon        string `json:"icon" gorm:"comment:'菜单图标'"`
}
