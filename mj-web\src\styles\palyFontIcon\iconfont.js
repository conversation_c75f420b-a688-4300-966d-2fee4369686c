!function(e){var t,n,o,i,c,l,d,a='<svg><symbol id="icon-play" viewBox="0 0 1024 1024"><path d="M512 0C229.376 0 0 229.376 0 512s229.376 512 512 512 512-229.376 512-512S794.624 0 512 0z m189.952 537.6l-261.12 150.528c-17.92 10.24-39.936-2.56-39.936-23.04V364.032c0-20.48 22.528-33.28 39.936-23.04l261.12 150.528c17.92 10.24 17.92 35.84 0 46.08z"  ></path></symbol><symbol id="icon-pausecircle-fill" viewBox="0 0 1024 1024"><path d="M512 1024A512 512 0 1 1 512 0a512 512 0 0 1 0 1024zM320 320v384h128V320H320z m256 0v384h128V320H576z" fill="#595959" ></path></symbol></svg>',s=(t=document.getElementsByTagName("script"))[t.length-1].getAttribute("data-injectcss");if(s&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}function r(){l||(l=!0,i())}n=function(){var e,t,n,o,i,c=document.createElement("div");c.innerHTML=a,a=null,(e=c.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",t=e,(n=document.body).firstChild?(o=t,(i=n.firstChild).parentNode.insertBefore(o,i)):n.appendChild(t))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(n,0):(o=function(){document.removeEventListener("DOMContentLoaded",o,!1),n()},document.addEventListener("DOMContentLoaded",o,!1)):document.attachEvent&&(i=n,c=e.document,l=!1,(d=function(){try{c.documentElement.doScroll("left")}catch(e){return void setTimeout(d,50)}r()})(),c.onreadystatechange=function(){"complete"==c.readyState&&(c.onreadystatechange=null,r())})}(window);