import io from 'socket.io-client'
import { getToken } from './auth'
import store from '@/store'

class WebSocketService {
  constructor() {
    this.socket = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
  }

  connect() {
    if (this.socket && this.socket.connected) {
      return
    }

    const token = getToken()
    if (!token) {
      console.warn('No token found, cannot connect to WebSocket')
      return
    }

    this.socket = io(process.env.VUE_APP_WS_URL || 'ws://localhost:8888', {
      auth: {
        token: token
      },
      transports: ['websocket']
    })

    this.socket.on('connect', () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
    })

    this.socket.on('disconnect', () => {
      console.log('WebSocket disconnected')
      this.handleReconnect()
    })

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      this.handleReconnect()
    })

    // 监听竞价更新
    this.socket.on('bid_update', (data) => {
      console.log('Received bid update:', data)
      store.dispatch('auction/updateAuctionPrice', {
        auctionId: data.projectId,
        newPrice: data.currentPrice,
        bidCount: data.bidCount
      })
    })

    // 监听项目状态更新
    this.socket.on('project_status_update', (data) => {
      console.log('Received project status update:', data)
      // 可以在这里处理项目状态变化
    })
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval)
    } else {
      console.error('Max reconnection attempts reached')
    }
  }

  // 加入竞价房间
  joinAuctionRoom(auctionId) {
    if (this.socket && this.socket.connected) {
      this.socket.emit('join_auction', { auctionId })
    }
  }

  // 离开竞价房间
  leaveAuctionRoom(auctionId) {
    if (this.socket && this.socket.connected) {
      this.socket.emit('leave_auction', { auctionId })
    }
  }
}

export default new WebSocketService()
