import service from '@/utils/request'

// @Tags {{.StructName}}
// @Summary 创建{{.StructName}}
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.{{.StructName}} true "创建{{.StructName}}"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /admin/{{.Abbreviation}}/create [post]
export const create{{.StructName}} = (data) => {
     return service({
         url: "/admin/{{.Abbreviation}}/create",
         method: 'post',
         data
     })
 }


// @Tags {{.StructName}}
// @Summary 删除{{.StructName}}
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.{{.StructName}} true "删除{{.StructName}}"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /admin/{{.Abbreviation}}/delete [post]
 export const delete{{.StructName}} = (data) => {
     return service({
         url: "/admin/{{.Abbreviation}}/delete",
         method: 'post',
         data
     })
 }

// @Tags {{.StructName}}
// @Summary 删除{{.StructName}}
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除{{.StructName}}"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /admin/{{.Abbreviation}}/delete [post]
 export const delete{{.StructName}}ByIds = (data) => {
     return service({
         url: "/admin/{{.Abbreviation}}/deleteByIds",
         method: 'post',
         data
     })
 }

// @Tags {{.StructName}}
// @Summary 更新{{.StructName}}
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.{{.StructName}} true "更新{{.StructName}}"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /admin/{{.Abbreviation}}/update [post]
 export const update{{.StructName}} = (data) => {
     return service({
         url: "/admin/{{.Abbreviation}}/update",
         method: 'post',
         data
     })
 }


// @Tags {{.StructName}}
// @Summary 用id查询{{.StructName}}
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.{{.StructName}} true "用id查询{{.StructName}}"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /admin/{{.Abbreviation}}/find [post]
 export const find{{.StructName}} = (data) => {
     return service({
         url: "/admin/{{.Abbreviation}}/find",
         method: 'post',
         data
     })
 }

// @Tags {{.StructName}}
// @Summary 查询{{.StructName}}下拉列表(Kv)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.PageInfo true "查询{{.StructName}}下拉列表(Kv)"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /admin/{{.Abbreviation}}/getKvList [post]
 export const get{{.StructName}}KvList = (data) => {
     return service({
         url: "/admin/{{.Abbreviation}}/getKvList",
         method: 'post',
         data
     })
 }

// @Tags {{.StructName}}
// @Summary 分页获取{{.StructName}}列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.PageInfo true "分页获取{{.StructName}}列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /admin/{{.Abbreviation}}/getList [post]
 export const get{{.StructName}}List = (data) => {
     return service({
         url: "/admin/{{.Abbreviation}}/getList",
         method: 'post',
         data
     })
 }
