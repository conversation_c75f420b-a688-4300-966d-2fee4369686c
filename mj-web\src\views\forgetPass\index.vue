<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-10-09 14:30:27
 * @LastEditors: dlg
 * @LastEditTime: 2020-10-09 19:01:19
-->
<template>
  <div class="forget">
    <div class="f-main">
      <el-steps :active="active" finish-status="success">
        <el-step title="用户验证" />
        <el-step title="密码重置" />
        <el-step title="重置完成" />
      </el-steps>
      <div class="f-con">
        <el-form
          :model="formData"
          :rules="passRules"
          ref="formData"
          class="formCls"
        >
          <el-form-item label="手机号" v-show="active === 0">
            <el-input v-model="formData.user" placeholder="手机号" />
          </el-form-item>
          <el-form-item label="验证码" v-show="active === 0" class="phone">
            <el-input v-model="formData.captcha" placeholder="验证码">
              <el-button
                slot="append"
                @click="sendMess"
                :disabled="isDisable"
              >{{ btnText }}</el-button
              ></el-input
            >
          </el-form-item>
          <el-form-item v-show="active === 0">
            <el-button
              type="primary"
              @click="nextStep"
              :disabled="formData.user == '' || formData.captcha == ''"
            >下一步</el-button
            >
          </el-form-item>
          <div class="tips" v-show="active === 0">
            <h5>没收到短信验证码?</h5>
            <p>1、网络通讯异常可能会造成短信丢失，请重新获取或稍后再试</p>
            <p>2、请核实手机是否已欠费停机，或者屏蔽了系统短信</p>
            <p>3、您也可以尝试将SIM卡移动到另一部手机，然后重试</p>
          </div>
          <el-form-item label="密码" prop="pass" v-show="active === 1">
            <el-input
              type="password"
              v-model="formData.pass"
              placeholder="密码"
            />
          </el-form-item>
          <el-form-item
            label="确认密码"
            prop="confirmpass"
            v-show="active === 1"
          >
            <el-input
              type="password"
              v-model="formData.confirmpass"
              placeholder="确认密码"
            />
          </el-form-item>
          <el-form-item v-show="active === 1">
            <el-button type="primary" @click="finished">完成</el-button>
          </el-form-item>
        </el-form>
        <div class="finishedTip" v-show="active === 3">
          <span>新登录密码已重置成功，请重新登录</span>
          <router-link to="/login" style="font-size: 16px;color:#409eff;">
            到登录页
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { sendMess, checkCaptcha, forgetPassword } from "@/api/forgetPass";

export default {
  name: "ForgetPass",
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.formData.pass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      isDisable: false,
      active: 0,
      formData: {
        user: "",
        captcha: "",
        pass: "",
        confirmpass: "",
      },
      passRules: {
        pass: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          {
            pattern: /^[a-zA-Z]\w{5,20}$/,
            message: "以字母开头，长度在6~20之间，只能包含字母、数字和下划线",
          },
        ],
        confirmpass: [{ validator: validatePass, trigger: "blur" }],
      },
      tokenString: "",
      btnText: "获取短信验证码",
      timeCount: 60,
      timer: null,
    };
  },
  methods: {
    sendMess() {
      if (this.formData.user == "") {
        this.$message({
          message: "请输入手机号",
          type: "error",
        });
        return;
      }
      if (!/^1\d{10}$/.test(this.formData.user)) {
        this.$message({
          message: "请输入正确的手机号",
          type: "error",
        });
        return;
      }
      const data = {
        username: this.formData.user,
      };
      sendMess(data).then((res) => {
        if (res.data.code == 200) {
          this.countDown();
          this.$message({
            message: res.data.msg,
            type: "success",
          });
        } else {
          this.$message({
            message: res.data.msg,
            type: "error",
          });
        }
      });
    },
    nextStep() {
      const data = {
        username: this.formData.user,
        code: this.formData.captcha,
      };
      checkCaptcha(data).then((res) => {
        if (res.data.code == 200) {
          if (this.active++ > 2) this.active = 0;
          this.tokenString = res.data.data;
        } else {
          this.$message({
            message: res.data.msg,
            type: "error",
          });
        }
      });
    },
    finished() {
      this.$refs["formData"].validate(async (valid) => {
        if (valid) {
          const data = {
            username: this.formData.user,
            password: this.formData.pass,
            token: this.tokenString,
          };
          forgetPassword(data).then((res) => {
            if (res.data.code == 200) {
              this.$message({
                message: res.data.msg,
                type: "success",
              });
              this.active = this.active + 2;
            } else {
              this.$message({
                message: res.data.msg,
                type: "error",
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    countDown() {
      this.timer = setTimeout(() => {
        if (this.timeCount > 0 && this.timeCount <= 60) {
          this.isDisable = true;
          this.btnText = this.timeCount + "秒后重新获取";
          this.timeCount--;
          this.countDown();
        } else {
          this.isDisable = false;
          this.timeCount = 60;
          this.btnText = "获取短信验证码";
          clearTimeout(this.timer);
          this.timer = null;
        }
      }, 1000);
    },
  },
};
</script>

<style scoped>
.forget {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
}
.f-main {
  width: 600px;
  margin-top: 16vh;
}
.f-con {
  width: 100%;
  margin-top: 32px;
}
.formCls {
  width: 80%;
}
.tips > h5 {
  color: #555;
}
.tips > p {
  font-size: 12px;
  color: #999;
}
.finishedTip {
  width: 100%;
  text-align: center;
}
.finishedTip > span {
  color: #555;
  font-size: 20px;
}
</style>
<style>
.f-main .el-step__head {
  position: relative;
  left: 18px;
}
.forget .formCls .phone .el-button.is-disabled,
.forget .formCls .phone .el-button.is-disabled:focus,
.forget .formCls .phone .el-button.is-disabled:hover {
  border-color: transparent;
  background-color: transparent;
  color: inherit;
  border-top: 0;
  border-bottom: 0;
}
</style>