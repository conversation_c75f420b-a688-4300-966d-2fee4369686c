import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router)

import Layout from '@/views/layout';

// 初始化时，一些不需要权限加载的页面
export const constantRouterMap = [{
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
        path: '/redirect/:path*',
        component: () =>
            import('@/views/redirect')
    }]
},
{
    path: '/forgetPass',
    component: () =>
        import('@/views/forgetPass'),
    hidden: true
},
{
    path: '/login',
    component: () =>
        import('@/views/login'),
    hidden: true
},
{
    path: '/404',
    component: () =>
        import('@/views/errorPage/404'),
    hidden: true
},
{
    path: '/401',
    component: () =>
        import('@/views/errorPage/401'),
    hidden: true
},
{
    path: '/personalMess',
    component: Layout,
    redirect: "/personalMess/index",
    children: [{
        path: 'index',
        component: () =>
            import('@/views/personalMess'),
        name: 'PersonalMess',
        meta: {
            title: '基础信息',
            icon: '',
            noCache: true
        },
    },],
    hidden: true
},
{
    path: '/',
    component: Layout,
    redirect: "/",
    children: [
        {
            path: '/',
            component: () =>
                import('@/views/home'),
            name: 'Home',
            meta: {
                title: '首页',
                icon: 'home',
                noCache: true,
                defaultShow: true // 进去默认显示的页面,即tagview标签固定不能删除
            }
        },
    ]
}

];

// 路由使用了vue-router的懒加载,进一步优化项目加载性能
// export default new Router({
//   //实例化vue的时候只挂载constantRouter
//   routes: constantRouterMap
// })

// 创建路由实例
const createRouter = () => new Router({
    routes: constantRouterMap
})

const router = createRouter()

// 重置注册路由
export function resetRouter() {
    // 新建一个空的Router实例，将它的matcher重新赋值给我们之前定义的路由。巧妙的实现了动态路由的清除
    const newRouter = createRouter()
    router.matcher = newRouter.matcher // reset router
}

export default router
