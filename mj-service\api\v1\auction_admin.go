package v1

import (
	"auction-sys/global/response"
	"auction-sys/model/req"
	"auction-sys/service"
	"auction-sys/utils"

	"github.com/gin-gonic/gin"
)

// @Tags 竞价管理-会员管理
// @Summary 获取会员列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetMemberListReq true "获取会员列表"
// @Success 200 {object} resp.GetMemberListResp "获取成功"
// @Router /auction/admin/members [post]
func GetMemberList(c *gin.Context) {
	var listReq req.GetMemberListReq
	_ = c.ShouldBindJSON(&listReq)

	// 参数验证
	PageVerifyErr := utils.Verify(listReq, utils.CustomizeMap["PageVerify"])
	if PageVerifyErr != nil {
		response.FailWithMessage(PageVerifyErr.Error(), c)
		return
	}

	err, listResp := service.GetMemberList(listReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(listResp, c)
	}
}

// @Tags 竞价管理-会员管理
// @Summary 审核会员
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.MemberAuditReq true "审核会员"
// @Success 200 {string} string "审核成功"
// @Router /auction/admin/members/audit [post]
func AuditMember(c *gin.Context) {
	var auditReq req.MemberAuditReq
	_ = c.ShouldBindJSON(&auditReq)

	// 参数验证
	AuditVerify := utils.Rules{
		"MemberID": {utils.NotEmpty()},
		"Status":   {utils.NotEmpty()},
	}
	AuditVerifyErr := utils.Verify(auditReq, AuditVerify)
	if AuditVerifyErr != nil {
		response.FailWithMessage(AuditVerifyErr.Error(), c)
		return
	}

	// 获取当前用户信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err := service.AuditMember(auditReq, claims.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("审核成功", c)
	}
}

// @Tags 竞价管理-会员管理
// @Summary 获取会员详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetById true "获取会员详情"
// @Success 200 {object} model.SysMember "获取成功"
// @Router /auction/admin/members/detail [post]
func GetMemberDetail(c *gin.Context) {
	var idReq req.GetById
	_ = c.ShouldBindJSON(&idReq)

	IdVerifyErr := utils.Verify(idReq, utils.CustomizeMap["IdVerify"])
	if IdVerifyErr != nil {
		response.FailWithMessage(IdVerifyErr.Error(), c)
		return
	}

	err, member := service.GetMemberDetail(idReq.Id)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(member, c)
	}
}

// @Tags 竞价管理-会员管理
// @Summary 更新会员状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.UpdateMemberStatusReq true "更新会员状态"
// @Success 200 {string} string "更新成功"
// @Router /auction/admin/members/status [post]
func UpdateMemberStatus(c *gin.Context) {
	var statusReq struct {
		MemberID int  `json:"memberId" binding:"required"`
		Status   int8 `json:"status" binding:"required"`
	}
	_ = c.ShouldBindJSON(&statusReq)

	err := service.UpdateMemberStatus(statusReq.MemberID, statusReq.Status)
	if err != nil {
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags 竞价管理-项目管理
// @Summary 创建竞价项目
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.CreateAuctionProjectReq true "创建竞价项目"
// @Success 200 {string} string "创建成功"
// @Router /auction/admin/projects/create [post]
func CreateAuctionProject(c *gin.Context) {
	var createReq req.CreateAuctionProjectReq
	_ = c.ShouldBindJSON(&createReq)

	// 参数验证
	CreateVerify := utils.Rules{
		"Title":             {utils.NotEmpty()},
		"ProductCategoryID": {utils.NotEmpty()},
		"Quantity":          {utils.NotEmpty()},
		"StartPrice":        {utils.NotEmpty()},
		"MinIncrement":      {utils.NotEmpty()},
		"StartTime":         {utils.NotEmpty()},
		"EndTime":           {utils.NotEmpty()},
	}
	CreateVerifyErr := utils.Verify(createReq, CreateVerify)
	if CreateVerifyErr != nil {
		response.FailWithMessage(CreateVerifyErr.Error(), c)
		return
	}

	// 获取当前用户信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err := service.CreateAuctionProject(createReq, claims.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// @Tags 竞价管理-项目管理
// @Summary 更新竞价项目
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.UpdateAuctionProjectReq true "更新竞价项目"
// @Success 200 {string} string "更新成功"
// @Router /auction/admin/projects/update [post]
func UpdateAuctionProject(c *gin.Context) {
	var updateReq req.UpdateAuctionProjectReq
	_ = c.ShouldBindJSON(&updateReq)

	// 参数验证
	UpdateVerify := utils.Rules{
		"ID":                {utils.NotEmpty()},
		"Title":             {utils.NotEmpty()},
		"ProductCategoryID": {utils.NotEmpty()},
		"Quantity":          {utils.NotEmpty()},
		"StartPrice":        {utils.NotEmpty()},
		"MinIncrement":      {utils.NotEmpty()},
		"StartTime":         {utils.NotEmpty()},
		"EndTime":           {utils.NotEmpty()},
		"Owner":             {utils.NotEmpty()},
		"WarehouseAddress":  {utils.NotEmpty()},
		"MemberIDs":         {utils.NotEmpty()},
	}
	UpdateVerifyErr := utils.Verify(updateReq, UpdateVerify)
	if UpdateVerifyErr != nil {
		response.FailWithMessage(UpdateVerifyErr.Error(), c)
		return
	}

	// 获取当前用户信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err := service.UpdateAuctionProject(updateReq, claims.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags 竞价管理-项目管理
// @Summary 获取竞价项目列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetAuctionProjectListReq true "获取竞价项目列表"
// @Success 200 {object} resp.GetAuctionProjectListResp "获取成功"
// @Router /auction/admin/projects [post]
func GetAuctionProjectList(c *gin.Context) {
	var listReq req.GetAuctionProjectListReq
	_ = c.ShouldBindJSON(&listReq)

	// 参数验证
	PageVerifyErr := utils.Verify(listReq, utils.CustomizeMap["PageVerify"])
	if PageVerifyErr != nil {
		response.FailWithMessage(PageVerifyErr.Error(), c)
		return
	}

	err, listResp := service.GetAuctionProjectList(listReq, 0) // 管理员查看所有项目
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(listResp, c)
	}
}

// @Tags 竞价管理-项目管理
// @Summary 获取竞价项目详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetById true "获取竞价项目详情"
// @Success 200 {object} resp.AuctionProjectDetailResp "获取成功"
// @Router /auction/admin/projects/detail [post]
func GetAuctionProjectDetail(c *gin.Context) {
	var idReq req.GetById
	_ = c.ShouldBindJSON(&idReq)

	IdVerifyErr := utils.Verify(idReq, utils.CustomizeMap["IdVerify"])
	if IdVerifyErr != nil {
		response.FailWithMessage(IdVerifyErr.Error(), c)
		return
	}

	err, detail := service.GetAuctionProjectDetail(idReq.Id, 0) // 管理员查看详情
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(detail, c)
	}
}

// @Tags 竞价管理-项目管理
// @Summary 终止竞价项目
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.TerminateAuctionReq true "终止竞价项目"
// @Success 200 {string} string "终止成功"
// @Router /auction/admin/projects/terminate [post]
func TerminateAuctionProject(c *gin.Context) {
	var terminateReq req.TerminateAuctionReq
	_ = c.ShouldBindJSON(&terminateReq)

	// 参数验证
	TerminateVerify := utils.Rules{
		"ProjectID": {utils.NotEmpty()},
	}
	TerminateVerifyErr := utils.Verify(terminateReq, TerminateVerify)
	if TerminateVerifyErr != nil {
		response.FailWithMessage(TerminateVerifyErr.Error(), c)
		return
	}

	// 获取当前用户信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err := service.TerminateAuctionProject(terminateReq, claims.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("终止成功", c)
	}
}

// @Tags 竞价管理-权限管理
// @Summary 管理竞价权限
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.ManageAuctionPermissionReq true "管理竞价权限"
// @Success 200 {string} string "操作成功"
// @Router /auction/admin/permissions [post]
func ManageAuctionPermission(c *gin.Context) {
	var permissionReq req.ManageAuctionPermissionReq
	_ = c.ShouldBindJSON(&permissionReq)

	// 获取当前用户信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err := service.ManageAuctionPermission(permissionReq, claims.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("操作成功", c)
	}
}

// 根据项目ID查询已经授权的用户
func GetAuctionProjectPermission(c *gin.Context) {
	var idReq req.GetById
	_ = c.ShouldBindJSON(&idReq)

	if idReq.ProjectId <= 0 {
		response.FailWithMessage("项目ID不能为空", c)
		return
	}

	err, list := service.GetAuctionProjectPermission(idReq.ProjectId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(list, c)
	}
}

// @Tags 竞价管理-监控管理
// @Summary 获取出价记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetBidListReq true "获取出价记录列表"
// @Success 200 {object} resp.GetBidListResp "获取成功"
// @Router /auction/admin/bids [post]
func GetBidList(c *gin.Context) {
	var listReq req.GetBidListReq
	_ = c.ShouldBindJSON(&listReq)

	err, listResp := service.GetBidList(listReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(listResp, c)
	}
}

// @Tags 竞价管理-统计管理
// @Summary 获取竞价统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} resp.AuctionStatsResp "获取成功"
// @Router /auction/admin/stats [post]
func GetAuctionStats(c *gin.Context) {
	err, stats := service.GetAuctionStats()
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(stats, c)
	}
}

// @Tags 竞价管理-会员管理
// @Summary 获取已审核通过的会员列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {array} resp.MemberInfo "获取成功"
// @Router /auction/admin/members/approved [post]
func GetApprovedMembers(c *gin.Context) {
	err, members := service.GetApprovedMembers()
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(members, c)
	}
}
