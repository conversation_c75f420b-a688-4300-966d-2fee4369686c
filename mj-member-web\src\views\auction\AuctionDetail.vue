<template>
  <div class="auction-detail-page">
    <AppHeader />
    
    <div class="container" v-loading="loading">
      <div class="auction-detail" v-if="auction">
        <!-- 返回按钮 -->
        <div class="back-button">
          <el-button @click="$router.go(-1)" icon="el-icon-arrow-left">返回</el-button>
        </div>

        <!-- 竞价信息 -->
        <div class="auction-header">
          <div class="auction-basic">
            <h1 class="auction-title">{{ auction.title }}</h1>
            <div class="auction-meta">
              <StatusTag :status="auction.status" />
              <span class="category">{{ auction.categoryName }}</span>
              <span class="quantity">数量：{{ auction.quantity }}{{ auction.unit }}</span>
            </div>
          </div>
          
          <div class="auction-status">
            <div class="price-info">
              <div class="current-price">
                <span class="label">当前价格</span>
                <span class="price">¥{{ formatMoney(auction.currentPrice) }}</span>
              </div>
              <div class="start-price">
                起拍价：¥{{ formatMoney(auction.startPrice) }}
              </div>
            </div>
            
            <div class="time-info">
              <template v-if="auction.status === 0">
                <div class="time-label">开始时间</div>
                <div class="time-value">{{ auction.startTime | formatTime }}</div>
              </template>
              <template v-else-if="auction.status === 1">
                <div class="time-label">剩余时间</div>
                <CountdownTimer :end-time="auction.endTime" @finished="onAuctionFinished" />
              </template>
              <template v-else>
                <div class="time-label">结束时间</div>
                <div class="time-value">{{ auction.endTime | formatTime }}</div>
              </template>
            </div>
          </div>
        </div>

        <!-- 出价区域 -->
        <div class="bid-section" v-if="auction.status === 1">
          <div class="bid-form">
            <h3>我要出价</h3>
            <div class="bid-input">
              <el-input-number
                v-model="bidAmount"
                :min="minBidAmount"
                :step="auction.minIncrement"
                :precision="2"
                placeholder="请输入出价金额"
                size="large"
              />
              <el-button
                type="primary"
                size="large"
                @click="handleBid"
                :loading="bidLoading"
                :disabled="!canBid"
              >
                出价
              </el-button>
            </div>
            <div class="bid-tips">
              <p>最小加价幅度：¥{{ formatMoney(auction.minIncrement) }}</p>
              <p>最低出价：¥{{ formatMoney(minBidAmount) }}</p>
            </div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="auction-details">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="项目详情" name="details">
              <div class="details-content">
                <h4>项目描述</h4>
                <p>{{ auction.description || '暂无详细描述' }}</p>
                
                <h4>基本信息</h4>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">商品分类：</span>
                    <span class="value">{{ auction.categoryName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">数量：</span>
                    <span class="value">{{ auction.quantity }}{{ auction.unit }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">起拍价：</span>
                    <span class="value">¥{{ formatMoney(auction.startPrice) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">最小加价：</span>
                    <span class="value">¥{{ formatMoney(auction.minIncrement) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">开始时间：</span>
                    <span class="value">{{ auction.startTime | formatTime }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">结束时间：</span>
                    <span class="value">{{ auction.endTime | formatTime }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="出价记录" name="bids">
              <div class="bids-content">
                <div class="bids-stats">
                  <div class="stat">
                    <span class="number">{{ auction.bidCount }}</span>
                    <span class="label">出价次数</span>
                  </div>
                  <div class="stat">
                    <span class="number">{{ auction.bidderCount || 0 }}</span>
                    <span class="label">参与人数</span>
                  </div>
                </div>
                
                <div class="bids-list" v-loading="bidsLoading">
                  <div
                    v-for="(bid, index) in bidHistory"
                    :key="bid.id"
                    class="bid-item"
                    :class="{ 'is-winner': index === 0 && auction.status === 2 }"
                  >
                    <div class="bid-rank">{{ index + 1 }}</div>
                    <div class="bid-info">
                      <div class="bidder">{{ maskName(bid.bidderName) }}</div>
                      <div class="bid-time">{{ bid.bidTime | formatTime('MM-DD HH:mm:ss') }}</div>
                    </div>
                    <div class="bid-amount">¥{{ formatMoney(bid.bidAmount) }}</div>
                  </div>
                  
                  <div v-if="bidHistory.length === 0" class="empty-bids">
                    <el-empty description="暂无出价记录" />
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBidHistory } from '@/api/auction'
import WebSocketService from '@/utils/websocket'

export default {
  name: 'AuctionDetail',
  data() {
    return {
      auction: null,
      bidHistory: [],
      bidAmount: null,
      activeTab: 'details',
      loading: false,
      bidLoading: false,
      bidsLoading: false
    }
  },
  computed: {
    auctionId() {
      return parseInt(this.$route.params.id)
    },
    
    minBidAmount() {
      if (!this.auction) return 0
      return this.auction.currentPrice + this.auction.minIncrement
    },
    
    canBid() {
      return this.auction && 
             this.auction.status === 1 && 
             this.bidAmount >= this.minBidAmount
    }
  },
  async mounted() {
    await this.fetchAuctionDetail()
    await this.fetchBidHistory()
    
    // 连接WebSocket
    WebSocketService.connect()
    WebSocketService.joinAuctionRoom(this.auctionId)
    
    // 设置默认出价金额
    if (this.auction && this.auction.status === 1) {
      this.bidAmount = this.minBidAmount
    }
  },
  beforeDestroy() {
    // 离开WebSocket房间
    WebSocketService.leaveAuctionRoom(this.auctionId)
  },
  methods: {
    // 获取竞价详情
    async fetchAuctionDetail() {
      this.loading = true
      try {
        const result = await this.$store.dispatch('auction/fetchAuctionDetail', this.auctionId)
        if (result.success) {
          this.auction = result.data
        } else {
          this.$message.error(result.message)
          this.$router.go(-1)
        }
      } catch (error) {
        this.$message.error('获取竞价详情失败')
        this.$router.go(-1)
      } finally {
        this.loading = false
      }
    },

    // 获取出价历史
    async fetchBidHistory() {
      this.bidsLoading = true
      try {
        const response = await getBidHistory(this.auctionId, { page: 1, pageSize: 50 })
        if (response.data.code === 200) {
          this.bidHistory = response.data.data.list || []
        }
      } catch (error) {
        console.error('获取出价历史失败:', error)
      } finally {
        this.bidsLoading = false
      }
    },

    // 处理出价
    async handleBid() {
      if (!this.canBid) {
        this.$message.warning('请输入有效的出价金额')
        return
      }

      this.bidLoading = true
      try {
        const result = await this.$store.dispatch('auction/placeBid', {
          auctionId: this.auctionId,
          bidAmount: this.bidAmount
        })
        
        if (result.success) {
          this.$message.success('出价成功')
          // 刷新出价历史
          await this.fetchBidHistory()
          // 更新出价金额为新的最低出价
          this.bidAmount = this.minBidAmount
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        this.$message.error('出价失败，请稍后重试')
      } finally {
        this.bidLoading = false
      }
    },

    // 竞价结束回调
    onAuctionFinished() {
      this.auction.status = 2
      this.$message.info('竞价已结束')
    },

    // 格式化金额
    formatMoney(value) {
      if (!value && value !== 0) return '0'
      return Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    // 脱敏姓名
    maskName(name) {
      if (!name) return '匿名用户'
      if (name.length <= 2) return name
      return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.auction-detail-page {
  min-height: 100vh;
  background: $bg-color;
}

.container {
  padding: 20px;
}

.back-button {
  margin-bottom: 20px;
}

.auction-detail {
  background: white;
  border-radius: 8px;
  box-shadow: $box-shadow;
  overflow: hidden;
}

.auction-header {
  padding: 30px;
  border-bottom: 1px solid $border-color;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;

  .auction-basic {
    flex: 1;

    .auction-title {
      font-size: 28px;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 15px;
    }

    .auction-meta {
      display: flex;
      align-items: center;
      gap: 20px;
      color: $text-secondary;

      .category,
      .quantity {
        padding: 4px 8px;
        background: $light-gray;
        border-radius: 4px;
        font-size: 14px;
      }
    }
  }

  .auction-status {
    display: flex;
    gap: 40px;

    .price-info {
      text-align: right;

      .current-price {
        .label {
          display: block;
          color: $text-secondary;
          font-size: 14px;
          margin-bottom: 5px;
        }

        .price {
          display: block;
          font-size: 32px;
          font-weight: 700;
          color: $danger-color;
        }
      }

      .start-price {
        color: $text-secondary;
        font-size: 14px;
        margin-top: 10px;
      }
    }

    .time-info {
      text-align: center;

      .time-label {
        color: $text-secondary;
        font-size: 14px;
        margin-bottom: 10px;
      }

      .time-value {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
      }
    }
  }
}

.bid-section {
  padding: 30px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid $border-color;

  .bid-form {
    max-width: 600px;

    h3 {
      color: $text-primary;
      margin-bottom: 20px;
    }

    .bid-input {
      display: flex;
      gap: 15px;
      margin-bottom: 15px;

      .el-input-number {
        flex: 1;
      }
    }

    .bid-tips {
      color: $text-secondary;
      font-size: 14px;

      p {
        margin: 5px 0;
      }
    }
  }
}

.auction-details {
  padding: 30px;

  .details-content {
    h4 {
      color: $text-primary;
      margin: 20px 0 15px 0;
      font-size: 16px;

      &:first-child {
        margin-top: 0;
      }
    }

    p {
      color: $text-regular;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;

      .info-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;

        .label {
          color: $text-secondary;
        }

        .value {
          font-weight: 500;
          color: $text-primary;
        }
      }
    }
  }

  .bids-content {
    .bids-stats {
      display: flex;
      gap: 40px;
      margin-bottom: 30px;
      padding: 20px;
      background: $light-gray;
      border-radius: 8px;

      .stat {
        text-align: center;

        .number {
          display: block;
          font-size: 24px;
          font-weight: 700;
          color: $primary-color;
          margin-bottom: 5px;
        }

        .label {
          color: $text-secondary;
          font-size: 14px;
        }
      }
    }

    .bids-list {
      .bid-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid $border-color;

        &.is-winner {
          background: linear-gradient(90deg, #fff7e6 0%, transparent 100%);
          border-left: 4px solid $warning-color;
          padding-left: 15px;
        }

        .bid-rank {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: $primary-color;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-right: 15px;
        }

        .bid-info {
          flex: 1;

          .bidder {
            font-weight: 500;
            color: $text-primary;
            margin-bottom: 5px;
          }

          .bid-time {
            color: $text-secondary;
            font-size: 14px;
          }
        }

        .bid-amount {
          font-size: 18px;
          font-weight: 600;
          color: $danger-color;
        }
      }

      .empty-bids {
        padding: 40px 0;
      }
    }
  }
}

@media (max-width: $mobile) {
  .auction-header {
    flex-direction: column;
    gap: 20px;

    .auction-status {
      width: 100%;
      justify-content: space-between;
    }
  }

  .bid-section .bid-form .bid-input {
    flex-direction: column;
  }

  .auction-details .details-content .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
