<template>
  <div class="page404">
    <div class="pic-404 fl">
      <img src="@/assets/images/image404/404.gif" alt="404" class="pic404-img" />
    </div>
    <div class="message fl">
      <div class="messageOne">
        {{ messageOne }}
      </div>
      <div class="status">
        <span>404</span>
        <span>Not Found</span>
      </div>
      <div class="messageTwo">
        <span>{{ messageTwo }}</span>
        <a href="" class="goHome fr">Back to home</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Page404",
  computed: {
    messageOne() {
      return "呀！！";
    },
    messageTwo() {
      return "似乎没有找到你想要的页面......";
    }
  }
};
</script>

<style lang="scss" scoped>
.page404 {
  width: 1100px;
  transform: translate(-50%, -50%);
  position: absolute;
  left: 50%;
  top: 40%;
  .pic-404 {
    margin-right: 60px;
    width: 400px;
    .pic404-img {
      width: 100%;
    }
  }
  .message {
    width: 580px;
    position: relative;
    .messageOne {
      width: 220px;
      transform: rotate(7deg);
      font-size: 68px;
      color: #333;
    }
    .status{
      width: 400px;
      text-align: right;
      font-weight: bold;
      span:nth-of-type(1){
        display: block;
        color: rgb(247, 7, 7);
        font-size: 100px;
      }
      span:nth-of-type(2){
        display: inline-block;
        padding-right: 16px;
        padding-bottom: 20px;
        font-size: 26px;
      }
    }
    .messageTwo {
      font-size: 30px;
      .goHome{
        position: relative;
        top: 4px;
        padding: 6px 10px;
        background-color: rgb(64, 158, 255);
        border-radius: 14px;
        font-size: 14px;
        color: #fff;
        cursor: pointer;
      }
    }
  }
}
</style>