<script>
// 在不刷新页面的情况下，更新页面
// 当遇到你需要刷新页面的情况，你就手动重定向页面到redirect页面，
// 它会将页面重新redirect重定向回来，由于页面的 key 发生了变化，
// 从而间接实现了刷新页面组件的效果

// 判断当前点击的菜单路由和当前的路由是否一致，在一致的时候，会先跳转到一个专门 Redirect 的页面，代码如下
// 手动重定向页面到 '/redirect' 页面
// const { fullPath } = this.$route
// this.$router.replace({
//   path: '/redirect' + fullPath
// })

export default {
  beforeCreate() {
    const { params, query } = this.$route
    const { path } = params
    this.$router.replace({ path: '/' + path, query })
  },
  render: function(h) {
    return h() // avoid warning message
  }
}
</script>
