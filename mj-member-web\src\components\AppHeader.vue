<template>
  <header class="app-header" v-if="!$route.meta.hideHeader">
    <div class="container" style="height: 100%;">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo" @click="$router.push('/')">
          <h2>新疆生产建设兵团第一师棉麻有限责任公司竞价平台</h2>
        </div>

        <!-- 用户信息 -->
        <div class="user-info">
          <template v-if="isLoggedIn">
            <el-dropdown @command="handleCommand">
              <span class="user-name">
                {{ userInfo?.name || '用户' }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="bids">我的出价</el-dropdown-item>
                <el-dropdown-item command="projects">我的项目</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button type="text" @click="$router.push('/login')">登录</el-button>
            <el-button type="primary" size="small" @click="$router.push('/register')">注册</el-button>
          </template>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'AppHeader',
  computed: {
    ...mapGetters('auth', ['isLoggedIn', 'userInfo'])
  },
  methods: {
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/profile')
          break
        case 'bids':
          this.$router.push('/profile/bids')
          break
        case 'projects':
          this.$router.push('/profile/projects')
          break
        case 'logout':
          this.handleLogout()
          break
      }
    },
    
    async handleLogout() {
      try {
        await this.$store.dispatch('auth/logout')
        this.$message.success('退出登录成功')
        this.$router.push('/home')
      } catch (error) {
        console.error('退出登录失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: $header-height;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
  }

  .logo {
    cursor: pointer;
    
    h2 {
      color: $primary-color;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;

    .nav-item {
      color: $text-regular;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s;

      &:hover,
      &.router-link-active {
        color: $primary-color;
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 15px;

    .user-name {
      color: $text-primary;
      cursor: pointer;
      font-weight: 500;
    }
  }
}

@media (max-width: $mobile) {
  .app-header {
    .nav-menu {
      display: none;
    }
    
    .logo h2 {
      font-size: 16px;
    }
  }
}
</style>
