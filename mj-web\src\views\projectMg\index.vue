<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div style="margin-bottom: 20px;min-width:720px;">
      <span style="color:#606266;font-size:14px;">项目名称：</span>
      <el-input
        v-model="searchForm.title"
        placeholder="请输入项目名称"
        style="width: 200px;margin-right:32px;"
        clearable
      />
      <span style="color:#606266;font-size:14px;">商品分类：</span>
      <el-select v-model="searchForm.productCategoryId"
                 placeholder="请选择商品分类"
                 style="margin-right:32px;"
                 clearable>
        <el-option
          v-for="item in categoryOptions"
          :key="item.ID"
          :label="item.name"
          :value="item.ID"
        />
      </el-select>
      <span style="color:#606266;font-size:14px;">项目状态：</span>
      <el-select v-model="searchForm.status"
                 placeholder="请选择项目状态"
                 style="margin-right:12px;"
                 clearable>
        <el-option label="即将开始" :value="0" />
        <el-option label="竞价中" :value="1" />
        <el-option label="已结束" :value="2" />
        <el-option label="已终止" :value="3" />
      </el-select>
      <el-button @click="getTableData" type="primary">查询</el-button>
      <el-button @click="resetSearch" type="default">重置</el-button>
    </div>

    <!-- 操作按钮 -->
    <div class="button-box">
      <el-button type="primary" @click="addProject">新增项目</el-button>
      <el-button type="warning" @click="batchTerminate">批量终止</el-button>
    </div>

    <!-- 项目列表表格 -->
    <el-table :data="tableData" height="100%" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="序号" min-width="80">
        <template slot-scope="scope">{{ scope.$index + (page - 1) * limit + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="项目名称" min-width="200" prop="title" />
      <el-table-column align="center" label="商品分类" min-width="120">
        <template slot-scope="scope">{{ scope.row.productCategory}}</template>
      </el-table-column>
      <el-table-column align="center" label="起拍价" min-width="100">
        <template slot-scope="scope">¥{{ scope.row.startPrice }}</template>
      </el-table-column>
      <el-table-column align="center" label="当前价" min-width="100">
        <template slot-scope="scope">¥{{ scope.row.currentPrice }}</template>
      </el-table-column>
      <el-table-column align="center" label="出价次数" min-width="100" prop="bidCount" />
      <el-table-column align="center" label="开始时间" min-width="160" prop="startTime" />
      <el-table-column align="center" label="结束时间" min-width="160" prop="endTime" />
      <el-table-column align="center" label="状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="viewDetail(scope.row)">查看详情</el-button>
          <el-button
            v-if="scope.row.status === 0"
            type="success"
            size="small"
            @click="editProject(scope.row)">
            编辑
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="managePermission(scope.row)">
            权限管理
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            type="danger"
            size="small"
            @click="terminateProject(scope.row)">
            终止
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="limit"
        :total="total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 项目详情弹窗 -->
    <el-dialog
      title="项目详情"
      :visible.sync="detailDialog"
      width="700px"
      :close-on-click-modal="false"
    >
      <div v-if="projectDetail" class="project-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>项目名称：</label>
              <span>{{ projectDetail.title }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>商品分类：</label>
              <span>{{ projectDetail.productCategory ? projectDetail.productCategory.name : '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>商品数量：</label>
              <span>{{ projectDetail.quantity }} {{ projectDetail.unit }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>起拍价：</label>
              <span>¥{{ projectDetail.startPrice }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>当前价：</label>
              <span>¥{{ projectDetail.currentPrice }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>最小加价幅度：</label>
              <span>¥{{ projectDetail.minIncrement }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>开始时间：</label>
              <span>{{ projectDetail.startTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>结束时间：</label>
              <span>{{ projectDetail.endTime }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>出价次数：</label>
              <span>{{ projectDetail.bidCount }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>项目状态：</label>
              <el-tag :type="getStatusType(projectDetail.status)">
                {{ getStatusText(projectDetail.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="projectDetail.description">
          <el-col :span="24">
            <div class="detail-item">
              <label>项目描述：</label>
              <span>{{ projectDetail.description }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="projectDetail.winnerName">
          <el-col :span="12">
            <div class="detail-item">
              <label>中标者：</label>
              <span>{{ projectDetail.winnerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>中标价：</label>
              <span>¥{{ projectDetail.winnerPrice }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 项目编辑弹窗 -->
    <el-dialog
      :title="editForm.ID ? '编辑项目' : '新增项目'"
      :visible.sync="editDialog"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="title">
              <el-input v-model="editForm.title" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品分类" prop="productCategoryId">
              <el-select v-model="editForm.productCategoryId" placeholder="请选择商品分类" style="width: 100%">
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品数量" prop="quantity">
              <el-input-number v-model="editForm.quantity" :min="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计量单位" prop="unit">
              <el-input v-model="editForm.unit" placeholder="如：吨、公斤等" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="起拍价" prop="startPrice">
              <el-input-number v-model="editForm.startPrice" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最小加价幅度" prop="minIncrement">
              <el-input-number v-model="editForm.minIncrement" :min="0.01" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="editForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="editForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="项目描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入项目描述"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProject">保存</el-button>
      </div>
    </el-dialog>

    <!-- 权限管理弹框 -->
    <PermissionDialog
      :visible.sync="permissionDialog"
      :project-info="currentProject"
    />

    <BidManagement
      v-if="bidMgShow"
      :project-info="currentProject"
    />

  </div>
</template>

<script>
import {
  getAuctionProjectList,
  createAuctionProject,
  updateAuctionProject,
  getAuctionProjectDetail,
  terminateAuctionProject,
  getProductCategories
} from '@/api/auction'
import PermissionDialog from './components/PermissionDialog.vue'
import BidManagement from './components/PermissionDialog.vue'

export default {
  name: 'ProjectManagement',
  components: {
    PermissionDialog,
    BidManagement
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        title: '',
        productCategoryId: '',
        status: ''
      },
      // 表格数据
      tableData: [],
      multipleSelection: [],
      // 分页
      page: 1,
      limit: 10,
      total: 0,
      // 弹窗控制
      detailDialog: false,
      editDialog: false,
      permissionDialog: false,
      // 项目详情
      projectDetail: null,
      // 当前项目（用于权限管理）
      currentProject: {},
      // 编辑表单
      editForm: {
        ID: '',
        title: '',
        productCategoryId: '',
        quantity: 1,
        unit: '',
        startPrice: 0,
        minIncrement: 0,
        startTime: '',
        endTime: '',
        description: ''
      },
      bidMgShow:false,
      // 表单验证规则
      editRules: {
        title: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        productCategoryId: [
          { required: true, message: '请选择商品分类', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入商品数量', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入计量单位', trigger: 'blur' }
        ],
        startPrice: [
          { required: true, message: '请输入起拍价', trigger: 'blur' }
        ],
        minIncrement: [
          { required: true, message: '请输入最小加价幅度', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      },
      // 商品分类选项
      categoryOptions: []
    }
  },
  created() {
    this.getTableData()
    this.getCategoryOptions()
  },
  methods: {
    // 获取表格数据
    async getTableData() {
      try {
        const params = {
          page: this.page,
          pageSize: this.limit,
          ...this.searchForm
        }
        const response = await getAuctionProjectList(params)
        if (response.data.code === 200) {
          this.tableData = response.data.data.list || []
          this.total = response.data.data.total || 0
        } else {
          this.$message.error(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        this.$message.error('获取数据失败')
      }
    },

    // 获取商品分类选项
    async getCategoryOptions() {
      try {
        const response = await getProductCategories({})
        if (response.data.code === 200) {
          this.categoryOptions = response.data.data.list || []
        }
      } catch (error) {
        console.error('获取商品分类失败:', error)
      }
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        title: '',
        productCategoryId: '',
        status: ''
      }
      this.page = 1
      this.getTableData()
    },

    // 查看详情
    async viewDetail(row) {
      try {
        const response = await getAuctionProjectDetail({ id: row.id })
        if (response.data.code === 200) {
          this.projectDetail = response.data.data
          this.detailDialog = true
        } else {
          this.$message.error(response.data.msg || '获取详情失败')
        }
      } catch (error) {
        console.error('获取项目详情失败:', error)
        this.$message.error('获取详情失败')
      }
    },

    // 新增项目
    addProject() {
      this.editForm = {
        ID: '',
        title: '',
        productCategoryId: '',
        quantity: 1,
        unit: '',
        startPrice: 0,
        minIncrement: 0,
        startTime: '',
        endTime: '',
        description: ''
      }
      this.editDialog = true
    },

    // 编辑项目
    editProject(row) {
      this.editForm = {
        ID: row.ID,
        title: row.title,
        productCategoryId: row.productCategoryId,
        quantity: row.quantity,
        unit: row.unit,
        startPrice: row.startPrice,
        minIncrement: row.minIncrement,
        startTime: row.startTime,
        endTime: row.endTime,
        description: row.description
      }
      this.editDialog = true
    },

    // 保存项目
    saveProject() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (this.editForm.ID) {
              // 更新项目
              response = await updateAuctionProject(this.editForm)
            } else {
              // 创建项目
              response = await createAuctionProject(this.editForm)
            }

            if (response.data.code === 200) {
              this.$message.success(this.editForm.ID ? '更新成功' : '创建成功')
              this.editDialog = false
              this.getTableData()
            } else {
              this.$message.error(response.data.msg || '保存失败')
            }
          } catch (error) {
            console.error('保存项目失败:', error)
            this.$message.error('保存失败')
          }
        }
      })
    },

    // 终止项目
    terminateProject(row) {
      this.$confirm('确定要终止该项目吗？终止后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await terminateAuctionProject({ projectId: row.ID })
          if (response.data.code === 200) {
            this.$message.success('项目终止成功')
            this.getTableData()
          } else {
            this.$message.error(response.data.msg || '终止失败')
          }
        } catch (error) {
          console.error('终止项目失败:', error)
          this.$message.error('终止失败')
        }
      })
    },

    // 批量终止
    batchTerminate() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要终止的项目')
        return
      }

      this.$confirm('确定要批量终止选中的项目吗？终止后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const projectIds = this.multipleSelection.map(item => item.ID)
          const response = await terminateAuctionProject({ projectIds: projectIds })
          if (response.data.code === 200) {
            this.$message.success('批量终止成功')
            this.getTableData()
          } else {
            this.$message.error(response.data.msg || '批量终止失败')
          }
        } catch (error) {
          console.error('批量终止失败:', error)
          this.$message.error('批量终止失败')
        }
      })
    },

    // 权限管理
    managePermission(row) {
      this.currentProject = {
        id: row.id,
        title: row.title
      }
      this.permissionDialog = true
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        0: 'info',     // 即将开始
        1: 'success',  // 竞价中
        2: 'warning',  // 已结束
        3: 'danger'    // 已终止
      }
      return typeMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '即将开始',
        1: '竞价中',
        2: '已结束',
        3: '已终止'
      }
      return textMap[status] || '未知'
    },

    // 表格选择变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.limit = val
      this.page = 1
      this.getTableData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.page = val
      this.getTableData()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.button-box {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.project-detail {
  padding: 20px;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: bold;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.detail-item span {
  color: #303133;
}
</style>
