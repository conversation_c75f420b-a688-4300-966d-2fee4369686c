package initialize

import (
	_ "auction-sys/docs"
	"auction-sys/global"
	"auction-sys/middleware"
	"auction-sys/middleware/logger"
	"auction-sys/router"

	"github.com/gin-gonic/gin"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/swaggo/gin-swagger/swaggerFiles"
)

// 初始化总路由

func Routers() *gin.Engine {
	var Router = gin.Default()
	Router.Use(logger.SetUp())
	// Router.Use(middleware.LoadTls())  // 打开就能玩https了
	global.GVA_LOG.Debug("use middleware logger")
	// 跨域
	Router.Use(middleware.Cors())
	global.GVA_LOG.Debug("use middleware cors")
	Router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	global.GVA_LOG.Debug("register swagger handler")
	// 方便统一添加路由组前缀 多服务器上线使用
	ApiGroup := Router.Group("/admin")
	router.InitUserRouter(ApiGroup)            // 注册用户路由
	router.InitBaseRouter(ApiGroup)            // 注册基础功能路由 不做鉴权
	router.InitMenuRouter(ApiGroup)            // 注册menu路由
	router.InitAuthorityRouter(ApiGroup)       // 注册角色路由
	router.InitJwtRouter(ApiGroup)             // jwt相关路由
	router.InitSystemRouter(ApiGroup)          // system相关路由
	router.InitCompanyRouter(ApiGroup)         // 注册公司路由
	router.InitProductCategoryRouter(ApiGroup) // 注册商品类别路由
	router.InitTLogRouter(ApiGroup)            // 注册日志路由
	router.InitAutoCodeRouter(ApiGroup)        // 注册 实时数据
	router.InitAuctionRouter(ApiGroup)         // 注册竞价系统路由

	global.GVA_LOG.Info("router register success")
	return Router
}
