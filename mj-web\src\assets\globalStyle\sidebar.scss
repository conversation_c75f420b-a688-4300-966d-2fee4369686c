#app {
    // 主体区域 Main container
    .main-container {
        min-height: calc(100% - 60px); //除去navbar高度，将剩下屏幕高度的占满
        transition: margin-left .28s;
        margin-left: $sideBarWidth;
        position: relative;
        // 侧边栏 Sidebar container
        .sidebar-div {
            transition: width .28s;
            width: $sideBarWidth !important;
            height: 100%;
            position: fixed;
            font-size: 0px;
            top: 60px;
            bottom: 0;
            left: 0;
            z-index: 1001;
            overflow: hidden;
            user-select: none;
            .bars-toggle {
                height: 30px;
                background: #2b2f3a;
                text-align: center;
                line-height: 30px;
                font-size: 16px;
                .is-active {
                    transform: rotate(180deg);
                }
            }
            .el-scrollbar {
                height: calc(100% - 90px);
                //reset element-ui css
                .horizontal-collapse-transition {
                    transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
                }
                .scrollbar-wrapper {
                    overflow-x: hidden !important;
                    .el-scrollbar__view {
                        height: 100%;
                    }
                }
                .el-scrollbar__bar.is-vertical {
                    right: 0px;
                }
                .is-horizontal {
                    display: none;
                }
                a {
                    display: inline-block;
                    width: 100%;
                    overflow: hidden;
                }
                .el-menu {
                    border: none;
                    height: 100%;
                    width: 100% !important;
                }
                .submenu-title-noDropdown,
                .el-submenu__title {
                    &:hover {
                        background-color: $subMenuHover !important;
                    }
                }
                & .menu-ul .el-submenu>.el-submenu__title,
                & .el-submenu .el-menu-item {
                    min-width: $sideBarWidth !important;
                    background-color: $subMenuBg !important;
                    &:hover {
                        background-color: $subMenuHover !important;
                    }
                }
            }
        }
    }
    .hideSidebar {
        .main-container {
            margin-left: 36px;
            .sidebar-div {
                width: 36px !important;
            }
        }
        // 只有自身节点项时的title前面的图标在sidebar未展开时的显示位置
        .submenu-title-noDropdown {
            padding-left: 10px !important;
            position: relative;
            .el-tooltip {
                padding: 0 10px !important;
            }
        }
        .el-submenu {
            overflow: hidden;
            &>.el-submenu__title {
                // 存在孩子节点时的自身节点项 title 前面的图标在sidebar未展开时的显示位置
                padding-left: 10px !important;
                .el-submenu__icon-arrow {
                    display: none;
                }
            }
        }
        .el-menu--collapse {
            .el-submenu {
                &>.el-submenu__title {
                    &>span {
                        height: 0;
                        width: 0;
                        overflow: hidden;
                        visibility: hidden;
                        display: inline-block;
                    }
                }
            }
        }
    }
    .sidebar-div {
        .item-title {
            margin-left: 10px;
        }
    }
    .menu-ul .el-submenu>.el-submenu__title,
    .el-menu-item {
        &:hover {
            background-color: $subMenuHover !important;
        }
    }
}