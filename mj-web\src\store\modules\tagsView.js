const tagsView = {
  state: {
    visitedViews: [], //缓存要显示的动态标签路由
    cachedViews: [] //缓存要缓存的标签路由
  },
  mutations: {
    ADD_VISITED_VIEW: (state, view) => {
      if (state.visitedViews.some(v => v.path === view.path)) return;   // 如果已经存在，则直接返回
      state.visitedViews.push(                                          // 否则，添加进去
        Object.assign({}, view, {
          title: view.meta.title || 'no-name'
        })
      );
    },
    DEL_VISITED_VIEW: (state, view) => {
      for (const [i, v] of state.visitedViews.entries()) {
        if (v.path === view.path) {
          state.visitedViews.splice(i, 1);
          break;
        }
      }
    },
    ADD_CACHED_VIEW: (state, view) => {
      if (state.cachedViews.includes(view.name)) return;
      if (!view.meta.noCache) {
        state.cachedViews.push(view.name);
      }
    },
    DEL_CACHED_VIEW: (state, view) => {
      for (let i of state.cachedViews) {
        if (i === view.name) {
          let index = state.cachedViews.indexOf(i);
          state.cachedViews.splice(index, 1);
          break;
        }
      }
    },
    DEL_ALL_VISITED_VIEWS: state => {
      // keep defaultShow tags
      // 保持默认页面一直存在
      const defaultShowTags = state.visitedViews.filter(tag => tag.meta.defaultShow)
      state.visitedViews = defaultShowTags  // visitedViews 里保留当前用户有权限页面下默认显示的页面
      // state.visitedViews = []
    },
    DEL_ALL_CACHED_VIEWS: state => {
      state.cachedViews = []
    }
  },
  actions: {
    addView({
      dispatch
    }, view) {
      dispatch('addVisitedView', view);
      dispatch('addCachedView', view);
    },
    delView({
      dispatch,
      state
    }, view) {
      return new Promise(resolve => {
        dispatch('delVisitedView', view);
        dispatch('delCachedView', view);
        resolve({
          visitedViews: [...state.visitedViews],
          cachedView: [...state.cachedViews]
        })
      });
    },
    addVisitedView({
      commit
    }, view) {
      commit('ADD_VISITED_VIEW', view);
    },
    delVisitedView({
      commit,
      state
    }, view) {
      return new Promise(resolve => {
        commit('DEL_VISITED_VIEW', view);
        resolve([...state.visitedViews]);
      });
    },
    addCachedView({
      commit
    }, view) {
      commit('ADD_CACHED_VIEW', view);
    },

    delCachedView({
      commit,
      state
    }, view) {
      return new Promise(resolve => {
        commit('DEL_CACHED_VIEW', view);
        resolve([...state.cachedViews]);
      });
    },
    delAllViews({
      dispatch,
      state
    }, view) {
      return new Promise(resolve => {
        dispatch('delAllVisitedViews', view)
        dispatch('delAllCachedViews', view)
        resolve({
          visitedViews: [...state.visitedViews],
          cachedViews: [...state.cachedViews]
        })
      })
    },
    delAllVisitedViews({
      commit,
      state
    }) {
      return new Promise(resolve => {
        commit('DEL_ALL_VISITED_VIEWS')
        resolve([...state.visitedViews])
      })
    },
    delAllCachedViews({
      commit,
      state
    }) {
      return new Promise(resolve => {
        commit('DEL_ALL_CACHED_VIEWS')
        resolve([...state.cachedViews])
      })
    }
  }
}

export default tagsView;
