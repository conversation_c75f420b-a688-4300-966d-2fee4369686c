<template>
  <div class="error-page">
    <AppHeader />
    
    <div class="error-container">
      <div class="error-content">
        <div class="error-image">
          <img src="/404.svg" alt="404" />
        </div>
        
        <div class="error-info">
          <h1>404</h1>
          <h2>页面不存在</h2>
          <p>抱歉，您访问的页面不存在或已被删除</p>
          
          <div class="error-actions">
            <el-button type="primary" @click="goHome">
              返回首页
            </el-button>
            <el-button @click="goBack">
              返回上页
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotFound',
  methods: {
    goHome() {
      this.$router.push('/home')
    },
    
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  background: $bg-color;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - #{$header-height});
  padding: 40px 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;

  .error-image {
    margin-bottom: 40px;

    img {
      max-width: 300px;
      width: 100%;
      height: auto;
    }
  }

  .error-info {
    h1 {
      font-size: 72px;
      font-weight: 700;
      color: $primary-color;
      margin-bottom: 20px;
    }

    h2 {
      font-size: 32px;
      color: $text-primary;
      margin-bottom: 15px;
    }

    p {
      font-size: 16px;
      color: $text-secondary;
      margin-bottom: 40px;
      line-height: 1.6;
    }

    .error-actions {
      display: flex;
      gap: 20px;
      justify-content: center;
    }
  }
}

@media (max-width: $mobile) {
  .error-content {
    .error-info {
      h1 {
        font-size: 48px;
      }

      h2 {
        font-size: 24px;
      }

      .error-actions {
        flex-direction: column;
        align-items: center;
      }
    }
  }
}
</style>
