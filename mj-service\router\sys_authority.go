package router

import (
	v1 "auction-sys/api/v1"
	"auction-sys/middleware"

	"github.com/gin-gonic/gin"
)

func InitAuthorityRouter(Router *gin.RouterGroup) {
	AuthorityRouter := Router.Group("authority").Use(middleware.JWTAuth())
	{
		AuthorityRouter.POST("createAuthority", v1.CreateAuthority)               // 创建角色
		AuthorityRouter.POST("deleteAuthority", v1.DeleteAuthority)               // 删除角色
		AuthorityRouter.POST("updateAuthority", v1.UpdateAuthority)               // 更新角色
		AuthorityRouter.POST("getAuthorityList", v1.GetAuthorityList)             // 获取角色列表
		AuthorityRouter.POST("updateAuthorityStatus", v1.UpdateAuthorityStatus)   // 更改角色状态
		AuthorityRouter.POST("getAuthorityAndUsers", v1.GetAuthorityAndUsers)     // 获取角色和绑定用户
		AuthorityRouter.POST("getAuthBindingUsers", v1.GetAuthBindingUsers)       // 获取变更角色用户列表
		AuthorityRouter.POST("updateAuthBindingUsers", v1.UpdateAuthBindingUsers) // 更新变更角色用户
		AuthorityRouter.POST("getAuthBySelfCompany", v1.GetAuthBySelfCompany)     // 获取自己公司的角色
	}
}
