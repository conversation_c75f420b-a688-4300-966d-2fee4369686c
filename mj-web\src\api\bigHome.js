/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-07-22 17:56:16
 * @LastEditors: dlg
 * @LastEditTime: 2020-09-08 09:37:21
 */
import request from '@/utils/request'

/**
 * @description: 获取最低最高温度
 * @param {type}
 * @return:
 */
export function getMiddleTop() {
    return request({
        url: '/admin/home/<USER>',
        method: 'post',
    })
}

/**
 * @description: 获取最低最高温度
 * @param {type}
 * @return:
 */
export function getRightTop() {
    return request({
        url: '/admin/home/<USER>',
        method: 'post',
    })
}


export function weatherInfo() {
    return request({
        url: '/admin/home/<USER>',
        method: 'post',
    })
}
