package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"errors"
)

type AuthIds struct {
	Id int
}

type MenuIds struct {
	Id int
}

// @Title 获取路由总树map
// @Description
// <AUTHOR>
// @Param authIds 		[]int							角色数组
// @Return err 			error 							错误信息
// @Return treeMap 		map[int][]resp.RouterMenu 	路由总树

func getMenuTreeMap(authIds []int) (err error, treeMap map[int][]resp.RouterMenu) {
	var allMenus []resp.RouterMenu
	treeMap = make(map[int][]resp.RouterMenu)
	sql := "SELECT DISTINCT " +
		"base.id AS menu_id," +
		"base.parent_id," +
		"base.path," +
		"base.name," +
		"base.hidden," +
		"base.component," +
		"base.category ," +
		"base.keyval," +
		"base.title," +
		"base.icon," +
		"base.keep_alive " +
		"FROm sys_authority_menus auth " +
		"INNER JOIN sys_base_menus base ON auth.sys_base_menu_id = base.id " +
		"WHERE base.hidden = 0 AND auth.sys_authority_id IN (?) AND base.deleted_at is null " +
		"ORDER BY base.sort ASC"
	err = global.GVA_DB.Raw(sql, authIds).Scan(&allMenus).Error
	for _, v := range allMenus {
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	return err, treeMap
}

// @Title 获取动态菜单树
// @Description
// <AUTHOR>
// @Param userId 		int						用户 id
// @Return err 			error 					错误信息
// @Return menus 		[]resp.RouterMenu 	动态菜单树

func GetMenuTree(userId int) (err error, menus []resp.RouterMenu) {
	// 根据 userId 查询用户角色
	var authIds []AuthIds
	sql := "select auth.id from sys_authorities auth left join sys_authority_users au on au.sys_authority_id = auth.id where au.sys_user_id = ? and auth.status = ?"
	err = global.GVA_DB.Raw(sql, userId, constants.STATUS_ENABLED).Scan(&authIds).Error
	// 遍历角色，根据角色查询菜单路由
	if len(authIds) > 0 {
		// 查询角色下所有菜单
		ids := make([]int, 0)
		for _, id := range authIds {
			ids = append(ids, id.Id)
		}
		err, menuTree := getMenuTreeMap(ids)
		if err != nil {
			return err, menus
		}
		// 取出父节点菜单数组
		menus = menuTree[0]
		// 遍历父节点菜单组合子级菜单
		for i := 0; i < len(menus); i++ {
			err = getChildrenList(&menus[i], menuTree)
		}
		return err, menus
	}
	return err, menus
}

// @Title 获取子菜单
// @Description
// <AUTHOR>
// @Param menu 			*resp.RouterMenu			菜单
// @Param treeMap 		map[int][]resp.RouterMenu	菜单树
// @Return err 			error 							错误信息

func getChildrenList(menu *resp.RouterMenu, treeMap map[int][]resp.RouterMenu) (err error) {
	// 判断当前菜单的子菜单是不是按钮类型，如果是按钮类型，赋值到 Meta 的 Btns
	for j := 0; j < len(treeMap[menu.MenuId]); {
		if treeMap[menu.MenuId][j].Category == constants.CATEGORY_MENU {
			menu.Children = treeMap[menu.MenuId]
			break
		} else {
			var btns = make([]resp.Btn, 0)
			for x := 0; x < len(treeMap[menu.MenuId]); x++ {
				var btn resp.Btn
				btn.Name = treeMap[menu.MenuId][x].Title
				btn.Keyval = treeMap[menu.MenuId][x].Keyval
				btns = append(btns, btn)
			}
			menu.Meta.Btns = btns
			break
		}
	}

	for i := 0; i < len(menu.Children); i++ {
		err = getChildrenList(&menu.Children[i], treeMap)
	}
	return err
}

// @Title 获取路由
// @Description
// <AUTHOR>
// @Return err 		error 			错误信息
// @Return list 	interface{} 	列表

func GetInfoList() (err error, list interface{}) {
	var menuList []model.SysBaseMenu
	err, treeMap := getBaseMenuTreeMap()
	menuList = treeMap[0]
	for i := 0; i < len(menuList); i++ {
		err = getBaseChildrenList(&menuList[i], treeMap)
	}
	return err, menuList
}

// @Title 获取菜单的子菜单
// @Description
// <AUTHOR>
// @Param menu 		*model.SysBaseMenu			菜单
// @Param treeMap 	map[int][]model.SysBaseMenu	菜单树
// @Return err 		error 						错误信息

func getBaseChildrenList(menu *model.SysBaseMenu, treeMap map[int][]model.SysBaseMenu) (err error) {
	menu.Children = treeMap[int(menu.ID)]
	for i := 0; i < len(menu.Children); i++ {
		err = getBaseChildrenList(&menu.Children[i], treeMap)
	}
	return err
}

// @Title 增加基础路由
// @Description
// <AUTHOR>
// @Param menu 		model.SysBaseMenu			菜单
// @Return err 		error 						错误信息

func AddBaseMenu(menu model.SysBaseMenu) (err error) {
	if menu.Category == constants.CATEGORY_MENU {
		findOne := global.GVA_DB.Where("name = ?", menu.Name).Find(&model.SysBaseMenu{}).Error
		if findOne != nil {
			menu.Category = constants.CATEGORY_MENU // 菜单
			err = global.GVA_DB.Create(&menu).Error
		} else {
			err = errors.New("存在重复name，请修改name")
		}

	} else {
		findOne := global.GVA_DB.Where("parent_id = ? and (title = ? or keyval = ?)", menu.ParentId, menu.Title, menu.Keyval).Find(&req.AddMenuBtn{}).Error
		if findOne != nil {
			menu.Category = constants.CATEGORY_BTN //按钮
			err = global.GVA_DB.Create(&menu).Error
		} else {
			err = errors.New("存在重复title或keyval，请修改")
		}
		return err
	}
	return err
}

// @Title 获取路由总树map
// @Description
// <AUTHOR>
// @Return err 		error 							错误信息
// @Return treeMap 	map[int][]model.SysBaseMenu 	路由树

func getBaseMenuTreeMap() (err error, treeMap map[int][]model.SysBaseMenu) {
	var allMenus []model.SysBaseMenu
	treeMap = make(map[int][]model.SysBaseMenu)
	err = global.GVA_DB.Order("sort", true).Find(&allMenus).Error
	for _, v := range allMenus {
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	return err, treeMap
}

// @Title 查看当前角色树
// @Description
// <AUTHOR>
// @Param authorityId 	float64						角色 id
// @Param uuid 			string						用户唯一标识
// @Return err 			error 						错误信息
// @Return role 		resp.SysAuthorityResponse 	角色树

func GetMenuAuthorityInit(authorityId int, uuid string) (err error, role resp.AuthorityResponse) {
	// 1.查询所有菜单树
	// 2.判断角色id是否为空
	// 3.查询角色信息
	// 4.查询角色菜单
	// 5.对比角色菜单，更改菜单树状态

	// 获取当前用户
	err, currentUser := GetCurrentUser(uuid)
	if err != nil {
		return err, role
	}

	sql := "SELECT " +
		"menu.id, " +
		"menu.title, " +
		"menu.parent_id, " +
		"menu.disabled " +
		"FROM " +
		"sys_base_menus menu " +
		"left join sys_authority_menus am on menu.id = am.sys_base_menu_id " +
		"left join sys_authority_users au on au.sys_authority_id = am.sys_authority_id " +
		"WHERE " +
		"menu.hidden = 0 " +
		"AND menu.deleted_at IS NULL "
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		sql = sql + "AND au.sys_user_id = ? "
	}
	sql = sql + " GROUP BY menu.id ORDER BY " +
		"menu.sort "

	var allMenus []resp.MenuTrees
	treeMap := make(map[int][]resp.MenuTrees)
	// 获取所有菜单
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		err = global.GVA_DB.Raw(sql, currentUser.ID).Scan(&allMenus).Error
	} else {
		err = global.GVA_DB.Raw(sql).Scan(&allMenus).Error
	}
	// 如果角色id不为空，查询角色信息和角色菜单
	if authorityId != 0 {
		// 查询角色信息
		var authority model.SysAuthority
		err = global.GVA_DB.Where("id = ?", authorityId).Find(&authority).Error
		role.RoleId = int(authority.ID)
		role.RoleName = authority.AuthorityName
		role.Status = authority.Status
		role.Description = authority.Description
		// 查询角色菜单
		var authorityMenu []model.SysAuthorityMenu
		idsSql := "select sys_base_menu_id from sys_authority_menus where sys_authority_id = ?"
		err = global.GVA_DB.Raw(idsSql, authorityId).Find(&authorityMenu).Error
		// 循环打状态
		checkMenuStatus(allMenus, authorityMenu)
	}
	for _, v := range allMenus {
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	// 递归组合菜单
	menus := treeMap[0]
	for i := 0; i < len(menus); i++ {
		err = getMenuTreeChildrenList(&menus[i], treeMap)
	}
	role.Trees = menus
	return err, role
}

// @Title 递归组合菜单
// @Description
// <AUTHOR>
// @Param menu 		*resp.MenuTrees				菜单
// @Param treeMap 	map[int][]resp.MenuTrees	菜单树
// @Return err 		error 							错误信息

func getMenuTreeChildrenList(menu *resp.MenuTrees, treeMap map[int][]resp.MenuTrees) (err error) {
	menu.Children = treeMap[menu.ID]
	for i := 0; i < len(menu.Children); i++ {
		err = getMenuTreeChildrenList(&menu.Children[i], treeMap)
	}
	return err
}

// @Title 检查菜单状态并赋值
// @Description
// <AUTHOR>
// @Param menu 		*resp.MenuTrees				菜单
// @Param treeMap 	map[int][]resp.MenuTrees	菜单树
// @Return err 		error 							错误信息

func checkMenuStatus(allMenus []resp.MenuTrees, authorityMenu []model.SysAuthorityMenu) {
lab:
	for i, m := range allMenus {
		for _, am := range authorityMenu {
			if m.ID == am.SysBaseMenuId {
				m.Status = constants.STATUS_ENABLED
				allMenus[i] = m
				continue lab
			}
		}
	}
}

// @Title 根据角色id，查询可用菜单
// @Description
// <AUTHOR>
// @Param reqIds 	[]req.GetById	角色 id 数组
// @Return err 		error 				错误信息
// @Return menus 	[]resp.Menu 	融合后菜单数组

func GetAuthMenus(reqIds []req.GetById) (err error, menus []resp.Menu) {
	// 根据角色ids，去重查询菜单ids
	// 根据菜单ids，查询菜单信息
	// 递归组合
	if len(reqIds) > 0 {
		var ids = make([]int, 0)
		for _, reqId := range reqIds {
			ids = append(ids, int(reqId.Id))
		}
		var menuIdsStruct []MenuIds

		// 根据角色ids，去重查询菜单ids
		menuIdsSql := "select DISTINCT sys_base_menu_id as id from sys_authority_menus where sys_authority_id in (?)"
		err = global.GVA_DB.Table("sys_authority_menus").Raw(menuIdsSql, ids).Scan(&menuIdsStruct).Error
		if err != nil {
			return err, menus
		}
		var menuIds = make([]int, 0)
		for _, menuId := range menuIdsStruct {
			menuIds = append(menuIds, menuId.Id)
		}
		var allMenus []resp.Menu
		// 根据菜单ids，查询菜单信息
		menuSql := "select id, title, parent_id from sys_base_menus where id in (?) and hidden = ? and deleted_at is null"
		err = global.GVA_DB.Table("sys_base_menu").Raw(menuSql, menuIds, constants.STATUS_DISABLED).Scan(&allMenus).Error
		if err != nil {
			return err, menus
		}
		treeMap := make(map[int][]resp.Menu)
		// 递归组合菜单
		for _, v := range allMenus {
			treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
		}
		menus := treeMap[0]
		for i := 0; i < len(menus); i++ {
			err = getMenuChildrenList(&menus[i], treeMap)
		}
		return err, menus
	}
	return err, menus
}

// @Title 递归组合菜单
// @Description
// <AUTHOR>
// @Param menu 		*resp.Menu			菜单
// @Param treeMap 	map[int][]resp.Menu	树结构
// @Return err 		error 					错误信息

func getMenuChildrenList(menu *resp.Menu, treeMap map[int][]resp.Menu) (err error) {
	menu.Children = treeMap[menu.Id]
	for i := 0; i < len(menu.Children); i++ {
		err = getMenuChildrenList(&menu.Children[i], treeMap)
	}
	return err
}
