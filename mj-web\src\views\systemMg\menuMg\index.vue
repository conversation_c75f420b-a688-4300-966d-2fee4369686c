<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-19 09:30:10
 * @LastEditors: dlg
 * @LastEditTime: 2020-11-18 18:28:33
-->
<template>
  <div class="app-container">
    <div class="button-box clearflex">
      <el-button @click="addMenu(0)" type="primary">新增根菜单</el-button>
    </div>
    <el-table :data="tableData" border height="100%" row-key="id">
      <el-table-column align="center" label="id" min-width="100" prop="id" />
      <el-table-column align="center" label="展示名称" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.meta.title }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="路由Name" min-width="160" prop="name" />
      <el-table-column align="center" label="文件路径" min-width="360" prop="component" />
      <el-table-column align="center" label="路由Path" min-width="160" prop="path" />
      <el-table-column align="center" label="是否隐藏" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.hidden?"隐藏":"显示" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="父节点" min-width="90" prop="parentid" />
      <el-table-column align="center" label="排序" min-width="70" prop="sort" />

      
      <el-table-column align="center" label="图标" min-width="140">
        <template slot-scope="scope">
          <svg class="svg-icon" style="margin-right:6px;" aria-hidden="true">
            <use :xlink:href="`#icon-${ scope.row.meta.icon }`" />
          </svg>
          <span>{{ scope.row.meta.icon }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="360">
        <template slot-scope="scope">
          <el-button
            @click="addMenu(scope.row.id)"
            :disabled="scope.row.children && scope.row.children[0].category === 1 || (scope.row.children === null && scope.row.category === 1)"
            size="small"
            type="primary"
          >添加子菜单</el-button>
          <el-button
            @click="addBtn(scope.row.id)"
            :disabled="scope.row.children && scope.row.children[0].category === 0 || (scope.row.children === null && scope.row.category === 1)"
            size="small"
            type="primary"
          >添加按钮</el-button>
          <el-button @click="editMenu(scope.row)" size="small" type="primary">编辑</el-button>
          <el-button @click="deleteMenu(scope.row.id)" size="small" type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :before-close="handleClose" :title="dialogBtnTitle" :visible.sync="dialogAddBtn">
      <el-form :inline="true"
               :model="btnForms"
               :rules="btnRules"
               ref="btnForm">
        <el-form-item label="父节点id" style="width:30%">
          <el-cascader
            disabled
            :options="menuOption"
            :props="{ checkStrictly: true,label:'title',value:'id',disabled:'disabled',emitPath:false}"
            :show-all-levels="false"
            filterable
            v-model="btnForms.parentid"
          />
        </el-form-item>
        <el-form-item label="按钮键值" prop="keyval" style="width:30%">
          <el-input autocomplete="off" v-model="btnForms.keyval" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="按钮名称" prop="meta.title" style="width:30%">
          <el-input autocomplete="off" v-model="btnForms.meta.title" />
        </el-form-item>
        <el-form-item label="是否隐藏" style="width:30%">
          <el-select placeholder="是否在页面隐藏" v-model="btnForms.hidden">
            <el-option :value="false" label="否" />
            <el-option :value="true" label="是" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否必选" style="width:30%">
          <el-select placeholder="是否必选" v-model="btnForms.disabled">
            <el-option :value="false" label="否" />
            <el-option :value="true" label="是" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="warning" style="margin-top: 12px;">新增按钮需要在角色管理内配置权限才可使用</div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button @click="enterDialog" type="primary">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :before-close="handleClose" :title="dialogTitle" :visible.sync="dialogFormVisible">
      <el-form
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="top"
        label-width="85px"
        ref="menuForm"
      >
        <el-form-item label="路由name" prop="name" style="width:30%">
          <el-input autocomplete="off" placeholder="唯一英文字符串" v-model="form.name" />
        </el-form-item>
        <el-form-item label="路由path" prop="path" style="width:30%">
          <el-input autocomplete="off" placeholder="唯一路径" v-model="form.path" />
        </el-form-item>
        <el-form-item label="是否隐藏" style="width:30%">
          <el-select placeholder="是否在列表隐藏" v-model="form.hidden">
            <el-option :value="false" label="否" />
            <el-option :value="true" label="是" />
          </el-select>
        </el-form-item>
        <el-form-item label="父节点id" style="width:30%">
          <el-cascader
            disabled
            :options="menuOption"
            :props="{ checkStrictly:true,label:'title',value:'id',disabled:'disabled',emitPath:false}"
            :show-all-levels="false"
            filterable
            v-model="form.parentid"
          />
        </el-form-item>
        <el-form-item label="文件路径" prop="component" style="width:30%">
          <el-input autocomplete="off" v-model="form.component" />
        </el-form-item>
        <el-form-item label="展示名称" prop="meta.title" style="width:30%">
          <el-input autocomplete="off" v-model="form.meta.title" />
        </el-form-item>
        <el-form-item label="图标" prop="meta.icon" style="width:30%">
          <icon :meta="form.meta" />
        </el-form-item>
        <el-form-item label="排序标记" prop="sort" style="width:30%">
          <el-input autocomplete="off" v-model.number="form.sort" />
        </el-form-item>
        <el-form-item label="keepAlive" style="width:30%">
          <el-select placeholder="是否keepAlive缓存页面" v-model="form.meta.noCache">
            <el-option :value="false" label="否" />
            <el-option :value="true" label="是" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="warning" style="margin-top: 12px;">新增菜单需要在角色管理内配置权限才可使用</div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button @click="enterDialog" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  updateBaseMenu,
  getMenuList,
  addBaseMenu,
  deleteBaseMenu,
} from "@/api/superMg/menuMg";
import Icon from "./icon";

export default {
  name: "MenuMg",
  components: {
    Icon,
  },
  data() {
    return {
      switchVal: false,
      dialogAddBtn: false,
      dialogFormVisible: false,
      dialogTitle: "",
      dialogBtnTitle: "",
      menuOption: [
        {
          id: "0",
          title: "根菜单",
        },
      ],
      btnForms: {
        id: "",
        parentid: "",
        hidden: false,
        keyval: "",
        meta: {
          title: "",
        },
        category: "",
        disabled: false,
      },
      btnRules: {
        keyval: [
          { required: true, message: "请输入按钮键值", trigger: "blur" },
        ],
        "meta.title": [
          { required: true, message: "请输入按钮名称", trigger: "blur" },
        ],
      },
      tableData: [],
      form: {
        id: "",
        path: "",
        name: "",
        hidden: false,
        parentid: "",
        component: "",
        sort: "",
        category: "",
        meta: {
          title: "",
          icon: "",
          noCache: false,
        },
      },
      rules: {
        name: [{ required: true, message: "请输入菜单name", trigger: "blur" }],
        path: [{ required: true, message: "请输入菜单path", trigger: "blur" }],
        "meta.title": [
          { required: true, message: "请输入菜单展示名称", trigger: "blur" },
        ],
      },
      isEdit: false,
      test: "",
    };
  },
  async created() {
    await this.getTableData();
  },
  methods: {
    async getTableData() {
      const res = await getMenuList();
      if (res.data.code == 200) {
        this.tableData = res.data.data.list;
      } else {
        this.tableData = [];
        this.$message.error(res.data.msg);
      }
    },
    switchChange() {
      if (this.$refs.btnForm) {
        this.$refs.btnForm.resetFields();
      }

      if (this.$refs.menuForm) {
        this.$refs.menuForm.resetFields();
      }
    },
    setOptions() {
      this.menuOption = [
        {
          id: "0",
          title: "根菜单",
        },
      ];
      this.setMenuOptions(this.tableData, this.menuOption, false);
    },
    setMenuOptions(menuData, optionsData, disabled) {
      menuData &&
        menuData.map((item) => {
          if (item.children && item.children.length) {
            // 一定为页面,且子节点也是菜单节点，不是按钮节点，
            if (item.children[0].category === 0) {
              const option = {
                title: item.meta.title,
                id: String(item.id),
                disabled: disabled || String(item.id) == this.form.id,
                children: [],
              };
              this.setMenuOptions(
                item.children,
                option.children,
                disabled || String(item.id) == this.form.id
              );
              optionsData.push(option);
            } else {
              // 子节点是按钮节点，只写入本节点，不写入 children 字段
              const option = {
                title: item.meta.title,
                id: String(item.id),
                disabled: disabled || String(item.id) == this.form.id,
              };
              optionsData.push(option);
            }
          } else {
            const option = {
              title: item.meta.title,
              id: String(item.id),
              disabled: disabled || String(item.id) == this.form.id,
            };
            optionsData.push(option);
          }
        });
    },
    handleClose(done) {
      this.initForm();
      done();
    },
    // 删除菜单
    deleteMenu(id) {
      this.$confirm("此操作将永久删除所有角色下该菜单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await deleteBaseMenu({ id });
          if (res.data.code == 200) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.getTableData();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 初始化弹窗内表格方法
    initForm() {
      if (this.switchVal) {
        if (this.$refs.btnForm) {
          this.$refs.btnForm.resetFields();
        }
        this.btnForms = {
          id: "",
          parentid: "",
          hidden: false,
          keyval: "",
          meta: {
            title: "",
          },
          category: "",
          disabled: false,
        };
      } else {
        if (this.$refs.menuForm) {
          this.$refs.menuForm.resetFields();
        }
        this.form = {
          id: "",
          path: "",
          name: "",
          hidden: false,
          parentid: "",
          component: "",
          sort: "",
          category: "",
          meta: {
            title: "",
            icon: "",
            noCache: false,
          },
        };
      }
    },
    // 关闭弹窗
    closeDialog() {
      this.initForm();
      if (this.switchVal) {
        this.dialogAddBtn = false;
      } else {
        this.dialogFormVisible = false;
      }
    },
    // 添加menu
    async enterDialog() {
      if (this.switchVal) {
        //为按钮节点
        if (this.$refs.btnForm) {
          this.$refs.btnForm.validate(async (valid) => {
            if (valid) {
              await this.sureDialog();
            }
          });
        }
      } else {
        //菜单节点
        if (this.$refs.menuForm) {
          this.$refs.menuForm.validate(async (valid) => {
            if (valid) {
              await this.sureDialog();
            }
          });
        }
      }
    },
    //实际调用后台请求确认
    async sureDialog() {
      let res;
      let data = this.switchVal ? this.btnForms : this.form;
      data.id = data.id != "" ? Number(data.id) : "";
      data.parentid = Number(data.parentid);
      if (this.isEdit) {
        res = await updateBaseMenu(data);
      } else {
        this.switchVal ? (data.category = 1) : (data.category = 0);
        res = await addBaseMenu(data);
      }
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.getTableData();
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
      this.initForm();
      if (this.switchVal) {
        this.dialogAddBtn = false;
      } else {
        this.dialogFormVisible = false;
      }
    },
    // 添加菜单方法，id为 0则为添加根菜单
    addMenu(id) {
      this.dialogTitle = "新增菜单";
      this.form.parentid = String(id);
      this.isEdit = false;
      this.switchVal = false;
      this.setOptions();
      this.dialogFormVisible = true;
    },
    addBtn(id) {
      this.dialogBtnTitle = "新增按钮";
      this.btnForms.parentid = String(id);
      this.isEdit = false;
      this.switchVal = true;
      this.setOptions();
      this.dialogAddBtn = true;
    },
    // 修改菜单方法
    async editMenu(row) {
      if (row.category === 1) {
        // 当前为按钮类型
        this.switchVal = true;
        this.dialogBtnTitle = "编辑按钮";

        this.btnForms.id = String(row.id);
        this.btnForms.parentid = String(row.parentid);
        this.btnForms.category = row.category;
        this.btnForms.hidden = row.hidden;
        this.btnForms.keyval = row.keyval;
        this.btnForms.disabled = row.disabled;
        this.btnForms.meta.title = row.meta.title;

        this.isEdit = true;
        this.setOptions();
        this.dialogAddBtn = true;
      } else {
        //当前为菜单类型
        this.switchVal = false;
        this.dialogTitle = "编辑菜单";

        this.form.id = String(row.id);
        this.form.path = row.path;
        this.form.name = row.name;
        this.form.hidden = row.hidden;
        this.form.parentid = String(row.parentid);
        this.form.component = row.component;
        this.form.sort = row.sort;
        this.form.category = row.category;
        this.form.meta.title = row.meta.title;
        this.form.meta.icon = row.meta.icon;
        this.form.meta.noCache = row.meta.noCache;

        this.isEdit = true;
        this.setOptions();
        this.dialogFormVisible = true;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.button-box {
  padding: 10px 20px;
  .el-button {
    float: right;
  }
}
.warning {
  color: #dc143c;
}
</style>
