import Vue from 'vue'
import moment from 'moment'

// 时间格式化
Vue.filter('formatTime', (value, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!value) return ''
  return moment(value).format(format)
})

// 金额格式化
Vue.filter('formatMoney', (value, decimals = 2) => {
  if (!value && value !== 0) return ''
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
})

// 状态格式化
Vue.filter('formatStatus', (value) => {
  const statusMap = {
    0: '即将开始',
    1: '竞价中',
    2: '已结束',
    3: '已终止'
  }
  return statusMap[value] || '未知状态'
})

// 审核状态格式化
Vue.filter('formatAuditStatus', (value) => {
  const statusMap = {
    0: '待审核',
    1: '审核通过',
    2: '审核拒绝'
  }
  return statusMap[value] || '未知状态'
})

// 倒计时格式化
Vue.filter('formatCountdown', (seconds) => {
  if (!seconds || seconds <= 0) return '00:00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
})
