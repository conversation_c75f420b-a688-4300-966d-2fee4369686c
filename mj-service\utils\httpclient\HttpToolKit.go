package httpclient

import (
    "fmt"
    "io/ioutil"
    "net/http"
    "strings"
)

func DoPost(url, bodyStr, method string) (rs string, err error) {
	//post的body内容,当前为json格式
	reqbody := bodyStr
	rs = ""
	if method == "" {
		method = "POST"
	}
	fmt.Println(url)
    fmt.Println(bodyStr)
	//创建请求
	postReq, err := http.NewRequest(method,
		url,                        //post链接内容
		strings.NewReader(reqbody)) //post
	if err != nil {
		fmt.Println("POST请求1:创建请求失败", err)
		return
	}
	//增加header
	postReq.Header.Set("Content-Type", "application/json")
	//postReq.Cookie("")
	//执行请求
	client := &http.Client{}
	resp, err := client.Do(postReq)
	if err != nil {
		fmt.Println("POST请求2:创建请求失败", err)
		return rs, err
	} else {
		//读取响应
		body, err := ioutil.ReadAll(resp.Body) //此处可增加输入过滤
		if err != nil {
			fmt.Println("POST请求3:读取body失败", err)
			return rs, err
		}
		rs = string(body)
		return rs, nil
	}
	defer resp.Body.Close()

	return
}

func DoPostGetRecord(url, bodyStr, method string,tokenStr string) (rs string, err error) {
    //post的body内容,当前为json格式
    reqbody := bodyStr
    rs = ""
    if method == "" {
        method = "POST"
    }
    fmt.Println(url)
    //创建请求
    postReq, err := http.NewRequest(method,
        url,                        //post链接内容
        strings.NewReader(reqbody)) //post
    if err != nil {
        fmt.Println("POST请求1:创建请求失败", err)
        return
    }
    //增加header
    postReq.Header.Set("Content-Type", "application/json")
    postReq.Header.Set("Authorization", tokenStr)
    //postReq.Cookie("")
    //执行请求
    client := &http.Client{}
    resp, err := client.Do(postReq)
    if err != nil {
        fmt.Println("POST请求2:创建请求失败", err)
        return rs, err
    } else {
        //读取响应
        body, err := ioutil.ReadAll(resp.Body) //此处可增加输入过滤
        if err != nil {
            fmt.Println("POST请求3:读取body失败", err)
            return rs, err
        }
        rs = string(body)
        return rs, nil
    }
    defer resp.Body.Close()

    return
}
