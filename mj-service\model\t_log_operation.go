package model

import "github.com/Gre-Z/common/jtime"

// 操作日志
type TLogOperation struct {
	ID            int            `json:"ID" gorm:"primary_key" gorm:"comment:'id'"`
	Username      string         `json:"username" gorm:"comment:'用户名'"`
	Nickname      string         `json:"nickname" gorm:"comment:'真实姓名'"`
	Operation     string         `json:"operation" gorm:"comment:'操作模块'"`
	OperationTime jtime.JsonTime `json:"operationTime" gorm:"comment:'操作时间'"`
	LoginMethod   int8           `json:"loginMethod" gorm:"登录方式"`
	Ip            string         `json:"ip" gorm:"comment:'ip地址'"`
	Address       string         `json:"address" gorm:"comment:'ip对应真实地址'"`
	RequestParam  string         `json:"requestParam" gorm:"comment:'请求参数'"`
	ResponseParam string         `json:"responseParam" gorm:"comment:'响应参数'"`
	Result        int            `json:"result" gorm:"请求结果"`
	CompanyCode   string         `json:"company_code" gorm:"企业编码"`
}
