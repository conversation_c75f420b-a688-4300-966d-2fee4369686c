package service

import (
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/utils"
	"errors"
	"fmt"
	"time"

	"github.com/Gre-Z/common/jtime"
	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
)

// 会员注册
func MemberRegister(registerReq req.MemberRegisterReq) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 检查手机号是否已注册

		if registerReq.Mobile == "" {
			return errors.New("手机号不能为空")
		}
		if registerReq.Password == "" {
			return errors.New("密码不能为空")
		}
		if registerReq.Name == "" {
			return errors.New("姓名不能为空")
		}
		if registerReq.CompanyName == "" {
			return errors.New("公司名称不能为空")
		}
		if registerReq.CreditCode == "" {
			return errors.New("统一社会信用代码")
		}
		// if !utils.Usci(registerReq.CreditCode) {
		// 	return errors.New("统一社会信用代码格式错误")
		// }

		var existMember model.SysMember
		if !tx.Where("mobile = ?", registerReq.Mobile).First(&existMember).RecordNotFound() {
			return errors.New("该手机号已注册")
		}

		// // 验证短信验证码
		// code := global.GVA_REDIS.Get(fmt.Sprintf("%v-code", registerReq.Mobile)).Val()
		// if code == "" || code != registerReq.VerificationCode {
		// 	return errors.New("验证码错误或已过期")
		// }

		// 创建会员记录
		member := model.SysMember{
			UUID:            uuid.NewV4(),
			Mobile:          registerReq.Mobile,
			Password:        utils.MD5V([]byte(registerReq.Password)),
			Name:            registerReq.Name,
			CompanyName:     registerReq.CompanyName,
			BusinessLicense: registerReq.BusinessLicense,
			CreditCode:      registerReq.CreditCode,
			Email:           registerReq.Email,
			ContactPerson:   registerReq.ContactPerson,
			ContactPhone:    registerReq.ContactPhone,
			Address:         registerReq.Address,
			Remark:          registerReq.Remark,
			Status:          1, // 正常状态
			AuditStatus:     0, // 待审核
			RegisterTime:    jtime.JsonTime{time.Now()},
			LoginCount:      0,
		}

		if err := tx.Create(&member).Error; err != nil {
			return errors.New("注册失败")
		}

		// 删除验证码
		global.GVA_REDIS.Del(fmt.Sprintf("%v-code", registerReq.Mobile))

		return nil
	})
}

// 会员登录
func MemberLogin(loginReq req.MemberLoginReq) (error, resp.MemberLoginResp) {
	var loginResp resp.MemberLoginResp
	var member model.SysMember

	// 验证用户名密码
	password := utils.MD5V([]byte(loginReq.Password))
	err := global.GVA_DB.Where("mobile = ? AND password = ?", loginReq.Mobile, password).First(&member).Error
	if err != nil {
		return errors.New("手机号或密码错误"), loginResp
	}

	// 检查会员状态
	if member.Status != 1 {
		return errors.New("账号已被禁用"), loginResp
	}

	// 检查审核状态
	if member.AuditStatus != 1 {
		switch member.AuditStatus {
		case 0:
			return errors.New("账号正在审核中，请耐心等待"), loginResp
		case 2:
			return errors.New("账号审核未通过，请联系管理员"), loginResp
		default:
			return errors.New("账号状态异常"), loginResp
		}
	}

	// 更新登录信息
	updateData := map[string]interface{}{
		"last_login_time": jtime.JsonTime{time.Now()},
		"login_count":     gorm.Expr("login_count + 1"),
	}
	global.GVA_DB.Model(&member).Updates(updateData)

	// 构造响应
	loginResp.Member = resp.MemberInfo{
		ID:          member.ID,
		UUID:        member.UUID,
		Mobile:      member.Mobile,
		Name:        member.Name,
		CompanyName: member.CompanyName,
		AuditStatus: member.AuditStatus,
		Status:      member.Status,
	}

	return nil, loginResp
}

// 会员审核
func AuditMember(auditReq req.MemberAuditReq, auditorID int) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var member model.SysMember
		if err := tx.Where("id = ?", auditReq.MemberID).First(&member).Error; err != nil {
			return errors.New("会员不存在")
		}

		if member.AuditStatus != 0 {
			return errors.New("该会员已审核，无法重复审核")
		}

		// 更新审核信息
		updateData := map[string]interface{}{
			"audit_status": auditReq.Status,
			"audit_time":   jtime.JsonTime{time.Now()},
			"audit_by":     auditorID,
			"audit_remark": auditReq.Remark,
		}

		if err := tx.Model(&member).Updates(updateData).Error; err != nil {
			return errors.New("审核失败")
		}

		return nil
	})
}

// 获取会员列表
func GetMemberList(listReq req.GetMemberListReq) (error, resp.GetMemberListResp) {
	var listResp resp.GetMemberListResp
	var members []model.SysMember
	var total int

	db := global.GVA_DB.Model(&model.SysMember{})

	// 构建查询条件
	if listReq.Mobile != "" {
		db = db.Where("mobile LIKE ?", "%"+listReq.Mobile+"%")
	}
	if listReq.Name != "" {
		db = db.Where("name LIKE ?", "%"+listReq.Name+"%")
	}
	if listReq.CompanyName != "" {
		db = db.Where("company_name LIKE ?", "%"+listReq.CompanyName+"%")
	}
	if listReq.AuditStatus != 0 {
		db = db.Where("audit_status = ?", listReq.AuditStatus)
	}
	if listReq.Status > 0 {
		db = db.Where("status = ?", listReq.Status)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 分页查询
	offset := listReq.PageSize * (listReq.Page - 1)
	if err := db.Offset(offset).Limit(listReq.PageSize).Order("created_at DESC").Find(&members).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 构造响应
	var memberList []resp.MemberListItem
	for _, member := range members {
		memberList = append(memberList, resp.MemberListItem{
			ID:            member.ID,
			Mobile:        member.Mobile,
			Name:          member.Name,
			CompanyName:   member.CompanyName,
			CreditCode:    member.CreditCode,
			AuditStatus:   member.AuditStatus,
			Status:        member.Status,
			RegisterTime:  member.RegisterTime,
			AuditTime:     member.AuditTime,
			AuditRemark:   member.AuditRemark,
			LastLoginTime: member.LastLoginTime,
			LoginCount:    member.LoginCount,
		})
	}

	listResp.List = memberList
	listResp.Total = total
	listResp.Page = listReq.Page
	listResp.PageSize = listReq.PageSize

	return nil, listResp
}

// 获取会员详情
func GetMemberDetail(memberID int) (error, model.SysMember) {
	var member model.SysMember
	if err := global.GVA_DB.Where("id = ?", memberID).First(&member).Error; err != nil {
		return errors.New("会员不存在"), member
	}
	return nil, member
}

// 更新会员状态
func UpdateMemberStatus(memberID int, status int8) error {
	return global.GVA_DB.Model(&model.SysMember{}).Where("id = ?", memberID).Update("status", status).Error
}

// 检查会员是否存在
func CheckMemberExists(mobile string) error {
	var member model.SysMember
	if global.GVA_DB.Where("mobile = ?", mobile).First(&member).RecordNotFound() {
		return errors.New("该手机号未注册")
	}
	return nil
}

// 获取已审核通过的会员列表（用于权限分配）
func GetApprovedMembers() (error, []resp.MemberInfo) {
	var members []model.SysMember
	var memberInfos []resp.MemberInfo

	err := global.GVA_DB.Where("audit_status = ? AND status = ?", 1, 1).Find(&members).Error
	if err != nil {
		return errors.New("查询失败"), memberInfos
	}

	for _, member := range members {
		memberInfos = append(memberInfos, resp.MemberInfo{
			ID:          member.ID,
			UUID:        member.UUID,
			Mobile:      member.Mobile,
			Name:        member.Name,
			CompanyName: member.CompanyName,
			AuditStatus: member.AuditStatus,
			Status:      member.Status,
		})
	}

	return nil, memberInfos
}
