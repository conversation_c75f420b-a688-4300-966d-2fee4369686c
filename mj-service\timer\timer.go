package timer

import (
	"auction-sys/service"
	"time"
)

// @Title 秒级定时器 - 服务端用
// @Description
// <AUTHOR>
// @Param
// @Return
func ServerTimerInit() {
	timer := time.NewTicker(time.Second * 1)
	for {
		<-timer.C
		// do something
		cur := time.Now()

		// 每10秒 检查京东入库单状态
		if cur.Minute()%1 == 0 && cur.Second() == 0 {

		}

		// 每30秒更新竞价项目状态
		if cur.Second()%30 == 0 {
			service.UpdateAuctionProjectStatus()
		}

	}
}
