[BUG_MONITOR]2025/09/09 - 19:15:30.075 E:/pywsp/mj-system/mj-service/initialize/db_table.go:19 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 19:15:30.108 E:/pywsp/mj-system/mj-service/initialize/redis.go:19 ▶ [INFO] redis connect ping response: PONG
[BUG_MONITOR]2025/09/09 - 19:15:30.108 E:/pywsp/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 19:15:30.109 E:/pywsp/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 19:15:30.109 E:/pywsp/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 19:15:30.115 E:/pywsp/mj-system/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 19:15:30.116 E:/pywsp/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 21:40:03.490 D:/vue/mj-system/mj-service/initialize/mysql.go:14 ▶ [ERROR] MySQL启动异常 Error 1045: Access denied for user 'root'@'localhost' (using password: YES)
[BUG_MONITOR]2025/09/09 - 21:42:48.616 D:/vue/mj-system/mj-service/initialize/mysql.go:14 ▶ [ERROR] MySQL启动异常 Error 1045: Access denied for user 'root'@'localhost' (using password: YES)
[BUG_MONITOR]2025/09/09 - 21:44:33.118 D:/vue/mj-system/mj-service/initialize/db_table.go:19 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 21:44:35.154 D:/vue/mj-system/mj-service/initialize/redis.go:17 ▶ [ERROR] dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it.
[BUG_MONITOR]2025/09/09 - 21:44:35.155 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 21:44:35.155 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 21:44:35.156 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 21:44:35.161 D:/vue/mj-system/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 21:44:35.169 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 21:45:48.617 D:/vue/mj-system/mj-service/initialize/db_table.go:19 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 21:45:50.663 D:/vue/mj-system/mj-service/initialize/redis.go:17 ▶ [ERROR] dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it.
[BUG_MONITOR]2025/09/09 - 21:45:50.664 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 21:45:50.664 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 21:45:50.665 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 21:45:50.670 D:/vue/mj-system/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 21:45:50.678 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 21:54:56.672 D:/vue/mj-system/mj-service/initialize/db_table.go:19 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 21:54:58.709 D:/vue/mj-system/mj-service/initialize/redis.go:18 ▶ [ERROR] dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it.
[BUG_MONITOR]2025/09/09 - 21:54:58.710 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 21:54:58.710 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 21:54:58.710 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 21:54:58.716 D:/vue/mj-system/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 21:54:58.724 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 22:00:55.940 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426455938,"request_uri":"/admin/base/captcha","response_code":0,"response_data":null,"response_msg":"","response_time":1757426455940}
[BUG_MONITOR]2025/09/09 - 22:01:15.376 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426475374,"request_uri":"/admin/base/captcha","response_code":0,"response_data":null,"response_msg":"","response_time":1757426475376}
[BUG_MONITOR]2025/09/09 - 22:01:40.186 D:/vue/mj-system/mj-service/initialize/db_table.go:19 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 22:01:40.195 D:/vue/mj-system/mj-service/initialize/redis.go:20 ▶ [INFO] redis connect ping response: PONG
[BUG_MONITOR]2025/09/09 - 22:01:40.195 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 22:01:40.195 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 22:01:40.196 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 22:01:40.201 D:/vue/mj-system/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 22:01:40.212 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 22:01:44.118 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426504116,"request_uri":"/admin/base/captcha","response_code":0,"response_data":null,"response_msg":"","response_time":1757426504118}
[BUG_MONITOR]2025/09/09 - 22:01:48.475 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"57ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426508418,"request_uri":"/admin/base/login","response_code":0,"response_data":null,"response_msg":"","response_time":1757426508475}
[BUG_MONITOR]2025/09/09 - 22:01:48.812 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:31:48
[BUG_MONITOR]2025/09/09 - 22:01:48.831 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"31ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426508800,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757426508831}
[BUG_MONITOR]2025/09/09 - 22:01:49.038 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"0ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426509038,"request_uri":"/admin/bugDevice/getList","response_code":0,"response_data":null,"response_msg":"","response_time":1757426509038}
[BUG_MONITOR]2025/09/09 - 22:01:49.182 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"0ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426509182,"request_uri":"/admin/farmland/getMapData","response_code":0,"response_data":null,"response_msg":"","response_time":1757426509182}
[BUG_MONITOR]2025/09/09 - 22:06:01.600 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:36:01
[BUG_MONITOR]2025/09/09 - 22:06:01.607 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"9ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757426761598,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757426761607}
[BUG_MONITOR]2025/09/09 - 22:18:31.482 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:48:31
[BUG_MONITOR]2025/09/09 - 22:18:31.486 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427511480,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757427511486}
[BUG_MONITOR]2025/09/09 - 22:18:32.593 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:48:32
[BUG_MONITOR]2025/09/09 - 22:18:32.596 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427512591,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757427512596}
[BUG_MONITOR]2025/09/09 - 22:18:33.508 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:48:33
[BUG_MONITOR]2025/09/09 - 22:18:33.511 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427513506,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757427513511}
[BUG_MONITOR]2025/09/09 - 22:19:36.444 D:/vue/mj-system/mj-service/initialize/db_table.go:19 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 22:19:36.445 D:/vue/mj-system/mj-service/initialize/redis.go:20 ▶ [INFO] redis connect ping response: PONG
[BUG_MONITOR]2025/09/09 - 22:19:36.446 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 22:19:36.446 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 22:19:36.447 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 22:19:36.453 D:/vue/mj-system/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 22:19:36.455 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 22:19:39.642 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:39
[BUG_MONITOR]2025/09/09 - 22:19:39.645 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427579640,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757427579645}
[BUG_MONITOR]2025/09/09 - 22:19:41.601 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:41
[BUG_MONITOR]2025/09/09 - 22:19:41.606 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"8ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427581598,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427581606}
[BUG_MONITOR]2025/09/09 - 22:19:42.206 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:42
[BUG_MONITOR]2025/09/09 - 22:19:42.213 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"8ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427582205,"request_uri":"/admin/company/getCompanyList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427582213}
[BUG_MONITOR]2025/09/09 - 22:19:42.931 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:42
[BUG_MONITOR]2025/09/09 - 22:19:42.932 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:42
[BUG_MONITOR]2025/09/09 - 22:19:42.935 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427582930,"request_uri":"/admin/company/getCompanyCondition","response_code":0,"response_data":null,"response_msg":"","response_time":1757427582935}
[BUG_MONITOR]2025/09/09 - 22:19:43.074 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"144ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427582930,"request_uri":"/admin/log/getLogList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427583074}
[BUG_MONITOR]2025/09/09 - 22:19:43.286 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:43
[BUG_MONITOR]2025/09/09 - 22:19:43.287 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427583285,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427583287}
[BUG_MONITOR]2025/09/09 - 22:19:45.476 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:45
[BUG_MONITOR]2025/09/09 - 22:19:45.477 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427585475,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427585477}
[BUG_MONITOR]2025/09/09 - 22:19:46.821 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"0ms","request_client_ip":"127.0.0.1","request_method":"GET","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427586821,"request_uri":"/@/assets/images/baseImg/defaultPhoto.jpg","response_code":0,"response_data":null,"response_msg":"","response_time":1757427586821}
[BUG_MONITOR]2025/09/09 - 22:19:47.133 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"1ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427587132,"request_uri":"/admin/base/captcha","response_code":0,"response_data":null,"response_msg":"","response_time":1757427587133}
[BUG_MONITOR]2025/09/09 - 22:19:53.053 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"1ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427593052,"request_uri":"/admin/base/login","response_code":0,"response_data":null,"response_msg":"","response_time":1757427593053}
[BUG_MONITOR]2025/09/09 - 22:19:53.363 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427593361,"request_uri":"/admin/base/captcha","response_code":0,"response_data":null,"response_msg":"","response_time":1757427593363}
[BUG_MONITOR]2025/09/09 - 22:19:59.316 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"57ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427599259,"request_uri":"/admin/base/login","response_code":0,"response_data":null,"response_msg":"","response_time":1757427599316}
[BUG_MONITOR]2025/09/09 - 22:19:59.635 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:49:59
[BUG_MONITOR]2025/09/09 - 22:19:59.636 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427599634,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757427599636}
[BUG_MONITOR]2025/09/09 - 22:20:03.790 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:50:03
[BUG_MONITOR]2025/09/09 - 22:20:03.796 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"8ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427603788,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427603796}
[BUG_MONITOR]2025/09/09 - 22:20:04.457 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:50:04
[BUG_MONITOR]2025/09/09 - 22:20:04.463 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"8ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427604455,"request_uri":"/admin/company/getCompanyList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427604463}
[BUG_MONITOR]2025/09/09 - 22:20:04.723 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:50:04
[BUG_MONITOR]2025/09/09 - 22:20:04.838 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"116ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427604722,"request_uri":"/admin/log/getLogList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427604838}
[BUG_MONITOR]2025/09/09 - 22:20:04.863 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:50:04
[BUG_MONITOR]2025/09/09 - 22:20:04.866 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427604862,"request_uri":"/admin/company/getCompanyCondition","response_code":0,"response_data":null,"response_msg":"","response_time":1757427604866}
[BUG_MONITOR]2025/09/09 - 22:20:05.058 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:50:05
[BUG_MONITOR]2025/09/09 - 22:20:05.059 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427605057,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427605059}
[BUG_MONITOR]2025/09/09 - 22:21:59.574 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:51:59
[BUG_MONITOR]2025/09/09 - 22:21:59.578 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427719572,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757427719578}
[BUG_MONITOR]2025/09/09 - 22:21:59.652 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:51:59
[BUG_MONITOR]2025/09/09 - 22:21:59.654 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427719651,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427719654}
[BUG_MONITOR]2025/09/09 - 22:22:04.926 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:04
[BUG_MONITOR]2025/09/09 - 22:22:04.937 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"12ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427724925,"request_uri":"/admin/company/getCompanyList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427724937}
[BUG_MONITOR]2025/09/09 - 22:22:06.811 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:06
[BUG_MONITOR]2025/09/09 - 22:22:06.812 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:06
[BUG_MONITOR]2025/09/09 - 22:22:06.819 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"9ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427726810,"request_uri":"/admin/menu/getMenuAuthorityInit","response_code":0,"response_data":null,"response_msg":"","response_time":1757427726819}
[BUG_MONITOR]2025/09/09 - 22:22:06.824 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"14ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427726810,"request_uri":"/admin/authority/getAuthorityList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427726824}
[BUG_MONITOR]2025/09/09 - 22:22:07.079 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:07
[BUG_MONITOR]2025/09/09 - 22:22:07.082 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427727077,"request_uri":"/admin/authority/getAuthBySelfCompany","response_code":0,"response_data":null,"response_msg":"","response_time":1757427727082}
[BUG_MONITOR]2025/09/09 - 22:22:07.202 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:07
[BUG_MONITOR]2025/09/09 - 22:22:07.214 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"13ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427727201,"request_uri":"/admin/user/getUserList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427727214}
[BUG_MONITOR]2025/09/09 - 22:22:07.407 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:07
[BUG_MONITOR]2025/09/09 - 22:22:07.408 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427727406,"request_uri":"/admin/company/getCompanyCondition","response_code":0,"response_data":null,"response_msg":"","response_time":1757427727408}
[BUG_MONITOR]2025/09/09 - 22:22:07.489 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:07
[BUG_MONITOR]2025/09/09 - 22:22:07.493 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427727487,"request_uri":"/admin/menu/getMenuAuthorityInit","response_code":0,"response_data":null,"response_msg":"","response_time":1757427727493}
[BUG_MONITOR]2025/09/09 - 22:22:07.796 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:07
[BUG_MONITOR]2025/09/09 - 22:22:07.807 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"12ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427727795,"request_uri":"/admin/authority/getAuthorityList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427727807}
[BUG_MONITOR]2025/09/09 - 22:22:23.218 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 22:52:23
[BUG_MONITOR]2025/09/09 - 22:22:23.226 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"9ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757427743217,"request_uri":"/admin/authority/getAuthorityList","response_code":0,"response_data":null,"response_msg":"","response_time":1757427743226}
[BUG_MONITOR]2025/09/09 - 22:25:23.413 D:/vue/mj-system/mj-service/initialize/db_table.go:19 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 22:25:23.415 D:/vue/mj-system/mj-service/initialize/redis.go:20 ▶ [INFO] redis connect ping response: PONG
[BUG_MONITOR]2025/09/09 - 22:25:23.415 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 22:25:23.415 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 22:25:23.416 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 22:25:23.419 D:/vue/mj-system/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 22:25:23.435 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 23:25:39.162 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431539158,"request_uri":"/admin/base/captcha","response_code":0,"response_data":null,"response_msg":"","response_time":1757431539162}
[BUG_MONITOR]2025/09/09 - 23:25:42.134 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431542132,"request_uri":"/admin/base/captcha","response_code":0,"response_data":null,"response_msg":"","response_time":1757431542134}
[BUG_MONITOR]2025/09/09 - 23:25:49.482 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"93ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431549389,"request_uri":"/admin/base/login","response_code":0,"response_data":null,"response_msg":"","response_time":1757431549482}
[BUG_MONITOR]2025/09/09 - 23:25:49.492 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:55:49
[BUG_MONITOR]2025/09/09 - 23:25:49.495 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431549489,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431549495}
[BUG_MONITOR]2025/09/09 - 23:25:53.959 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:55:53
[BUG_MONITOR]2025/09/09 - 23:25:53.961 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431553957,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431553961}
[BUG_MONITOR]2025/09/09 - 23:27:18.414 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:18
[BUG_MONITOR]2025/09/09 - 23:27:18.473 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"60ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431638413,"request_uri":"/admin/menu/addBaseMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431638473}
[BUG_MONITOR]2025/09/09 - 23:27:18.665 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:18
[BUG_MONITOR]2025/09/09 - 23:27:18.666 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431638664,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431638666}
[BUG_MONITOR]2025/09/09 - 23:27:23.252 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:23
[BUG_MONITOR]2025/09/09 - 23:27:23.254 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431643250,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431643254}
[BUG_MONITOR]2025/09/09 - 23:27:23.325 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:23
[BUG_MONITOR]2025/09/09 - 23:27:23.327 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431643324,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431643327}
[BUG_MONITOR]2025/09/09 - 23:27:45.530 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:45
[BUG_MONITOR]2025/09/09 - 23:27:45.531 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431665528,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431665531}
[BUG_MONITOR]2025/09/09 - 23:27:45.583 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:45
[BUG_MONITOR]2025/09/09 - 23:27:45.585 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431665582,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431665585}
[BUG_MONITOR]2025/09/09 - 23:27:45.879 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:45
[BUG_MONITOR]2025/09/09 - 23:27:45.883 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431665877,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431665883}
[BUG_MONITOR]2025/09/09 - 23:27:46.104 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:46
[BUG_MONITOR]2025/09/09 - 23:27:46.105 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431666103,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431666105}
[BUG_MONITOR]2025/09/09 - 23:27:47.002 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:47
[BUG_MONITOR]2025/09/09 - 23:27:47.004 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431667001,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431667004}
[BUG_MONITOR]2025/09/09 - 23:27:47.047 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:47
[BUG_MONITOR]2025/09/09 - 23:27:47.053 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"8ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431667045,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431667053}
[BUG_MONITOR]2025/09/09 - 23:27:47.691 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:47
[BUG_MONITOR]2025/09/09 - 23:27:47.693 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431667690,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431667693}
[BUG_MONITOR]2025/09/09 - 23:27:47.846 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:57:47
[BUG_MONITOR]2025/09/09 - 23:27:47.847 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431667844,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431667847}
[BUG_MONITOR]2025/09/09 - 23:28:47.262 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:58:47
[BUG_MONITOR]2025/09/09 - 23:28:47.265 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431727260,"request_uri":"/admin/user/getPersonInfo","response_code":0,"response_data":null,"response_msg":"","response_time":1757431727265}
[BUG_MONITOR]2025/09/09 - 23:28:52.727 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:58:52
[BUG_MONITOR]2025/09/09 - 23:28:52.728 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431732726,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431732728}
[BUG_MONITOR]2025/09/09 - 23:29:21.259 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:59:21
[BUG_MONITOR]2025/09/09 - 23:29:21.261 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431761258,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431761261}
[BUG_MONITOR]2025/09/09 - 23:29:21.379 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-09 23:59:21
[BUG_MONITOR]2025/09/09 - 23:29:21.380 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"2ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431761378,"request_uri":"/admin/menu/getMenuList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431761380}
[BUG_MONITOR]2025/09/09 - 23:30:04.879 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:04
[BUG_MONITOR]2025/09/09 - 23:30:04.890 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"12ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431804878,"request_uri":"/admin/menu/getMenuAuthorityInit","response_code":0,"response_data":null,"response_msg":"","response_time":1757431804890}
[BUG_MONITOR]2025/09/09 - 23:30:05.183 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:05
[BUG_MONITOR]2025/09/09 - 23:30:05.190 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"8ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431805182,"request_uri":"/admin/authority/getAuthorityList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431805190}
[BUG_MONITOR]2025/09/09 - 23:30:08.551 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:08
[BUG_MONITOR]2025/09/09 - 23:30:08.555 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431808550,"request_uri":"/admin/menu/getMenuAuthorityInit","response_code":0,"response_data":null,"response_msg":"","response_time":1757431808555}
[BUG_MONITOR]2025/09/09 - 23:30:11.594 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:11
[BUG_MONITOR]2025/09/09 - 23:30:11.693 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"100ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431811593,"request_uri":"/admin/authority/updateAuthority","response_code":0,"response_data":null,"response_msg":"","response_time":1757431811693}
[BUG_MONITOR]2025/09/09 - 23:30:12.029 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:12
[BUG_MONITOR]2025/09/09 - 23:30:12.034 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431812028,"request_uri":"/admin/authority/getAuthorityList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431812034}
[BUG_MONITOR]2025/09/09 - 23:30:13.870 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:13
[BUG_MONITOR]2025/09/09 - 23:30:13.873 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431813869,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431813873}
[BUG_MONITOR]2025/09/09 - 23:30:14.008 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:14
[BUG_MONITOR]2025/09/09 - 23:30:14.010 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:00:14
[BUG_MONITOR]2025/09/09 - 23:30:14.014 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"7ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431814007,"request_uri":"/admin/menu/getMenuAuthorityInit","response_code":0,"response_data":null,"response_msg":"","response_time":1757431814014}
[BUG_MONITOR]2025/09/09 - 23:30:14.024 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"17ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431814007,"request_uri":"/admin/authority/getAuthorityList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431814024}
[BUG_MONITOR]2025/09/09 - 23:30:16.239 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"0ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431816239,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431816239}
[BUG_MONITOR]2025/09/09 - 23:30:44.433 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"0ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431844433,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431844433}
[BUG_MONITOR]2025/09/09 - 23:32:31.217 D:/vue/mj-system/mj-service/initialize/db_table.go:20 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 23:32:31.219 D:/vue/mj-system/mj-service/initialize/redis.go:20 ▶ [INFO] redis connect ping response: PONG
[BUG_MONITOR]2025/09/09 - 23:32:31.220 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 23:32:31.220 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 23:32:31.221 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 23:32:31.224 D:/vue/mj-system/mj-service/initialize/router.go:42 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 23:32:31.231 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 23:32:46.942 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:02:46
[BUG_MONITOR]2025/09/09 - 23:32:46.945 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431966940,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757431966945}
[BUG_MONITOR]2025/09/09 - 23:32:47.104 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:02:47
[BUG_MONITOR]2025/09/09 - 23:32:47.108 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431967103,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431967108}
[BUG_MONITOR]2025/09/09 - 23:32:53.273 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:02:53
[BUG_MONITOR]2025/09/09 - 23:32:53.277 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431973271,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431973277}
[BUG_MONITOR]2025/09/09 - 23:33:13.073 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:03:13
[BUG_MONITOR]2025/09/09 - 23:33:13.149 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"77ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431993072,"request_uri":"/admin/productCategory/addProductCategory","response_code":0,"response_data":null,"response_msg":"","response_time":1757431993149}
[BUG_MONITOR]2025/09/09 - 23:33:13.477 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:03:13
[BUG_MONITOR]2025/09/09 - 23:33:13.481 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757431993476,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757431993481}
[BUG_MONITOR]2025/09/09 - 23:34:48.949 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:04:48
[BUG_MONITOR]2025/09/09 - 23:34:48.952 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432088948,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432088952}
[BUG_MONITOR]2025/09/09 - 23:36:32.011 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:06:32
[BUG_MONITOR]2025/09/09 - 23:36:32.013 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432192009,"request_uri":"/admin/menu/getMenu","response_code":0,"response_data":null,"response_msg":"","response_time":1757432192013}
[BUG_MONITOR]2025/09/09 - 23:36:32.243 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:06:32
[BUG_MONITOR]2025/09/09 - 23:36:32.248 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432192242,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432192248}
[BUG_MONITOR]2025/09/09 - 23:37:04.161 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:07:04
[BUG_MONITOR]2025/09/09 - 23:37:04.165 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432224160,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432224165}
[BUG_MONITOR]2025/09/09 - 23:37:06.988 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:07:06
[BUG_MONITOR]2025/09/09 - 23:37:06.992 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432226987,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432226992}
[BUG_MONITOR]2025/09/09 - 23:41:26.993 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:11:26
[BUG_MONITOR]2025/09/09 - 23:41:26.996 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432486992,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432486996}
[BUG_MONITOR]2025/09/09 - 23:41:28.624 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:11:28
[BUG_MONITOR]2025/09/09 - 23:41:28.628 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432488623,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432488628}
[BUG_MONITOR]2025/09/09 - 23:45:17.123 D:/vue/mj-system/mj-service/initialize/db_table.go:20 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/09 - 23:45:17.125 D:/vue/mj-system/mj-service/initialize/redis.go:20 ▶ [INFO] redis connect ping response: PONG
[BUG_MONITOR]2025/09/09 - 23:45:17.125 D:/vue/mj-system/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/09 - 23:45:17.126 D:/vue/mj-system/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/09 - 23:45:17.126 D:/vue/mj-system/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/09 - 23:45:17.130 D:/vue/mj-system/mj-service/initialize/router.go:40 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/09 - 23:45:17.132 D:/vue/mj-system/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
[BUG_MONITOR]2025/09/09 - 23:45:22.051 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:22
[BUG_MONITOR]2025/09/09 - 23:45:22.056 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432722049,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432722055}
[BUG_MONITOR]2025/09/09 - 23:45:23.675 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:23
[BUG_MONITOR]2025/09/09 - 23:45:23.678 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432723674,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432723678}
[BUG_MONITOR]2025/09/09 - 23:45:23.930 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:23
[BUG_MONITOR]2025/09/09 - 23:45:23.933 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432723929,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432723933}
[BUG_MONITOR]2025/09/09 - 23:45:34.424 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:34
[BUG_MONITOR]2025/09/09 - 23:45:34.470 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"47ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432734423,"request_uri":"/admin/productCategory/addProductCategory","response_code":0,"response_data":null,"response_msg":"","response_time":1757432734470}
[BUG_MONITOR]2025/09/09 - 23:45:34.691 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:34
[BUG_MONITOR]2025/09/09 - 23:45:34.693 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"3ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432734690,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432734693}
[BUG_MONITOR]2025/09/09 - 23:45:44.203 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:44
[BUG_MONITOR]2025/09/09 - 23:45:44.262 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"60ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432744202,"request_uri":"/admin/productCategory/addProductCategory","response_code":0,"response_data":null,"response_msg":"","response_time":1757432744262}
[BUG_MONITOR]2025/09/09 - 23:45:44.580 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:44
[BUG_MONITOR]2025/09/09 - 23:45:44.584 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"5ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432744579,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432744584}
[BUG_MONITOR]2025/09/09 - 23:45:52.807 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:52
[BUG_MONITOR]2025/09/09 - 23:45:52.855 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"49ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432752806,"request_uri":"/admin/productCategory/addProductCategory","response_code":0,"response_data":null,"response_msg":"","response_time":1757432752855}
[BUG_MONITOR]2025/09/09 - 23:45:53.178 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:53
[BUG_MONITOR]2025/09/09 - 23:45:53.181 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432753177,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432753181}
[BUG_MONITOR]2025/09/09 - 23:45:59.891 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:15:59
[BUG_MONITOR]2025/09/09 - 23:45:59.946 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"56ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432759890,"request_uri":"/admin/productCategory/addProductCategory","response_code":0,"response_data":null,"response_msg":"","response_time":1757432759946}
[BUG_MONITOR]2025/09/09 - 23:46:00.279 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:16:00
[BUG_MONITOR]2025/09/09 - 23:46:00.284 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"6ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757432760278,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757432760284}
[BUG_MONITOR]2025/09/09 - 23:50:48.467 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:20:48
[BUG_MONITOR]2025/09/09 - 23:50:48.470 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757433048466,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757433048470}
[BUG_MONITOR]2025/09/09 - 23:50:48.719 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:20:48
[BUG_MONITOR]2025/09/09 - 23:50:48.722 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757433048718,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757433048722}
[BUG_MONITOR]2025/09/09 - 23:50:50.667 D:/vue/mj-system/mj-service/middleware/jwt.go:57 ▶ [DEBUG] 设置30分钟后时间为： 2025-09-10 00:20:50
[BUG_MONITOR]2025/09/09 - 23:50:50.670 D:/vue/mj-system/mj-service/middleware/logger/logger.go:77 ▶ [INFO] {"cost_time":"4ms","request_client_ip":"127.0.0.1","request_method":"POST","request_post_data":"","request_referer":"http://localhost:9000/","request_time":1757433050666,"request_uri":"/admin/productCategory/getProductCategoryList","response_code":0,"response_data":null,"response_msg":"","response_time":1757433050670}
