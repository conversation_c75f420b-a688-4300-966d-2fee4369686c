package v1

import (
	"auction-sys/constants"
	"auction-sys/global/response"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/service"
	"auction-sys/utils"
	"encoding/json"
	"fmt"

	"github.com/gin-gonic/gin"
)

// @Tags productCategory ：商品类别管理
// @Summary 新增商品类别
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysProductCategory true "新增商品类别"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"创建成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"创建失败"}"
// @Router /productCategory/addProductCategory [post]
func AddProductCategory(c *gin.Context) {
	// 获取请求头
	agent := c.<PERSON>eader("User-Agent")

	var category model.SysProductCategory
	_ = c.ShouldBindJSON(&category)

	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(category)

	if category.ID != 0 {
		err := service.UpdateProductCategory(category)
		if err != nil {
			go service.AddOperationLog(claims.UUID.String(), "商品类别管理-编辑商品类别", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
			response.FailWithMessage(fmt.Sprintf("更新失败:%v", err), c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "商品类别管理-编辑商品类别", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
			response.OkWithMessage("更新成功", c)
		}
	} else {
		err := service.AddProductCategory(category)
		if err != nil {
			go service.AddOperationLog(claims.UUID.String(), "商品类别管理-添加商品类别", c.ClientIP(), string(requestParam), "添加失败", response.ERROR, agent)
			response.FailWithMessage(err.Error(), c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "商品类别管理-添加商品类别", c.ClientIP(), string(requestParam), "添加成功", response.SUCCESS, agent)
			response.OkWithMessage("添加成功", c)
		}
	}
}

// @Tags productCategory ：商品类别管理
// @Summary 根据id查询商品类别
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "根据id查询商品类别"
// @Success 200 {string} string "{"code":200,"data":{SysProductCategoryResponse},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"查询失败"}"
// @Router /productCategory/getProductCategory [post]
func GetProductCategory(c *gin.Context) {
	var idInfo req.GetById
	_ = c.ShouldBindJSON(&idInfo)

	IdVerifyErr := utils.Verify(idInfo, utils.CustomizeMap["IdVerify"])
	if IdVerifyErr != nil {
		response.FailWithMessage(IdVerifyErr.Error(), c)
		return
	}

	err, category := service.GetProductCategory(idInfo.Id)
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("查询失败：%v", err), c)
	} else {
		response.OkWithData(category, c)
	}
}

// @Tags productCategory ：商品类别管理
// @Summary 删除商品类别
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []request.GetById true "删除商品类别"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"删除成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"删除失败"}"
// @Router /productCategory/deleteProductCategory [post]
func DeleteProductCategory(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var reqIds []req.GetById
	_ = c.ShouldBindJSON(&reqIds)
	for _, reqId := range reqIds {
		IdVerifyErr := utils.Verify(reqId, utils.CustomizeMap["IdVerify"])
		if IdVerifyErr != nil {
			response.FailWithMessage(IdVerifyErr.Error(), c)
			return
		}
	}

	err := service.DeleteProductCategory(reqIds)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(reqIds)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "商品类别管理-删除商品类别", c.ClientIP(), string(requestParam), "删除失败", response.ERROR, agent)
		response.FailWithMessage("删除失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "商品类别管理-删除商品类别", c.ClientIP(), string(requestParam), "删除成功", response.SUCCESS, agent)
		response.OkWithMessage("删除成功", c)
	}
}

// @Tags productCategory ：商品类别管理
// @Summary 获取商品类别列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetProductCategoryList true "获取商品类别列表"
// @Success 200 {string} string "{"code":200,"data":{GetProductCategoryList},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取数据失败"}"
// @Router /productCategory/getProductCategoryList [post]
func GetProductCategoryList(c *gin.Context) {
	var category req.GetProductCategoryList
	_ = c.ShouldBindJSON(&category)

	CategoryVerifyErr := utils.Verify(category, utils.CustomizeMap["PageInfoVerify"])
	if CategoryVerifyErr != nil {
		response.FailWithMessage(CategoryVerifyErr.Error(), c)
		return
	}

	err, list, total, enableNum, disableNum := service.GetProductCategoryList(category)
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取数据失败，%v", err), c)
	} else {
		response.OkWithData(map[string]interface{}{
			"list":       list,
			"total":      total,
			"page":       category.Page,
			"pageSize":   category.PageSize,
			"name":       category.Name,
			"enableNum":  enableNum,
			"disableNum": disableNum,
		}, c)
	}
}

// @Tags productCategory ：商品类别管理
// @Summary 更新商品类别状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []model.SysProductCategory true "更新商品类别状态"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /productCategory/updateProductCategoryStatus [post]
func UpdateProductCategoryStatus(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var categorys []model.SysProductCategory
	_ = c.ShouldBindJSON(&categorys)

	CategoryVerify := utils.Rules{
		"ID": {utils.NotEmpty()},
	}
	for _, category := range categorys {
		CategoryVerifyErr := utils.Verify(category, CategoryVerify)
		if CategoryVerifyErr != nil {
			response.FailWithMessage(CategoryVerifyErr.Error(), c)
			return
		}
	}

	err := service.UpdateProductCategoryStatus(categorys)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(categorys)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "商品类别管理-更新状态", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage("更新失败", c)
	} else {
		if categorys[0].Status == constants.STATUS_ENABLED {
			go service.AddOperationLog(claims.UUID.String(), "商品类别管理-更新状态", c.ClientIP(), string(requestParam), "已启用", response.SUCCESS, agent)
			response.OkWithMessage("已启用", c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "商品类别管理-更新状态", c.ClientIP(), string(requestParam), "已禁用", response.SUCCESS, agent)
			response.OkWithMessage("已禁用", c)
		}
	}
}

// @Tags productCategory ：商品类别管理
// @Summary 获取商品类别下拉选项
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":200,"data":{[]model.SysProductCategory},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"加载失败"}"
// @Router /productCategory/getProductCategoryCondition [post]
func GetProductCategoryCondition(c *gin.Context) {
	// 获取商品类别信息
	err, categoryList := service.GetProductCategoryCondition()
	if err != nil {
		response.FailWithMessage("加载失败", c)
	} else {
		response.OkWithData(categoryList, c)
	}
}
