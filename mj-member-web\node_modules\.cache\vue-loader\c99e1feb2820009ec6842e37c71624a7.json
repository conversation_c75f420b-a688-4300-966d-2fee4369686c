{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\auth\\Register.vue?vue&type=style&index=0&id=17c846a0&lang=scss&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\auth\\Register.vue", "mtime": 1757558980871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757485139209}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757485152120}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757485142383}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757485135872}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucmVnaXN0ZXItcGFnZSB7DQogIGhlaWdodDogMTAwdmg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQp9DQoNCi5yZWdpc3Rlci1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtICN7JGhlYWRlci1oZWlnaHR9KTsNCiAgcGFkZGluZzogNDBweCAyMHB4Ow0KfQ0KDQoucmVnaXN0ZXItY2FyZCB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJveC1zaGFkb3c6IDAgMTBweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgcGFkZGluZzogNDBweDsNCiAgd2lkdGg6IDEwMCU7ICANCiAgbWF4LXdpZHRoOiA1MDBweDsNCn0NCg0KLnJlZ2lzdGVyLWhlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMzBweDsNCg0KICBoMiB7DQogICAgY29sb3I6ICR0ZXh0LXByaW1hcnk7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgfQ0KDQogIHAgew0KICAgIGNvbG9yOiAkdGV4dC1zZWNvbmRhcnk7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICB9DQp9DQoNCi5yZWdpc3Rlci1mb3JtIHsNCiAgLnNtcy1pbnB1dCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBnYXA6IDEwcHg7DQoNCiAgICAuZWwtaW5wdXQgew0KICAgICAgZmxleDogMTsNCiAgICB9DQoNCiAgICAuZWwtYnV0dG9uIHsNCiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgfQ0KICB9DQoNCiAgLnJlZ2lzdGVyLWJ0biB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiA0NHB4Ow0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgfQ0KDQogIC5sb2dpbi1saW5rIHsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICBjb2xvcjogJHRleHQtc2Vjb25kYXJ5Ow0KICB9DQoNCiAgLmxpbmsgew0KICAgIGNvbG9yOiAkcHJpbWFyeS1jb2xvcjsNCiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7DQoNCiAgICAmOmhvdmVyIHsNCiAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOw0KICAgIH0NCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogJG1vYmlsZSkgew0KICAucmVnaXN0ZXItY2FyZCB7DQogICAgcGFkZGluZzogMzBweCAyMHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["Register.vue"], "names": [], "mappings": ";AAgPA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Register.vue", "sourceRoot": "src/views/auth", "sourcesContent": ["<template>\r\n  <div class=\"register-page\">\r\n    <AppHeader />\r\n    \r\n    <div class=\"register-container\">\r\n      <div class=\"register-card\">\r\n        <div class=\"register-header\">\r\n          <h2>用户注册</h2>\r\n        </div>\r\n\r\n        <el-form\r\n          ref=\"registerForm\"\r\n          :model=\"form\"\r\n          :rules=\"rules\"\r\n          label-width=\"100px\"\r\n          class=\"register-form\"\r\n        >\r\n          <el-form-item label=\"手机号\" prop=\"mobile\">\r\n            <el-input\r\n              v-model=\"form.mobile\"\r\n              placeholder=\"请输入手机号\"\r\n              maxlength=\"11\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"密码\" prop=\"password\">\r\n            <el-input\r\n              v-model=\"form.password\"\r\n              type=\"password\"\r\n              placeholder=\"请输入密码\"\r\n              show-password\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n            <el-input\r\n              v-model=\"form.confirmPassword\"\r\n              type=\"password\"\r\n              placeholder=\"请再次输入密码\"\r\n              show-password\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"form.name\"\r\n              placeholder=\"请输入真实姓名\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n            <el-input\r\n              v-model=\"form.companyName\"\r\n              placeholder=\"请输入企业名称\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"信用代码\" prop=\"creditCode\">\r\n            <el-input\r\n              v-model=\"form.creditCode\"\r\n              placeholder=\"信用代码\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n\r\n          </el-form-item>\r\n          \r\n          <div>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"handleRegister\"\r\n              :loading=\"loading\"\r\n              class=\"register-btn\"\r\n            >\r\n              注册\r\n            </el-button>\r\n          </div>\r\n          <div >\r\n            <el-checkbox v-model=\"form.agreeTerms\">\r\n              我已阅读并同意\r\n              <a href=\"#\" class=\"link\">《用户协议》</a>\r\n              和\r\n              <a href=\"#\" class=\"link\">《隐私政策》</a>\r\n            </el-checkbox>\r\n          </div>\r\n          <div class=\"login-link\">\r\n            已有账号？\r\n            <router-link to=\"/login\" class=\"link\">立即登录</router-link>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { sendSmsCode } from '@/api/auth'\r\n\r\nexport default {\r\n  name: 'Register',\r\n  data() {\r\n    // 确认密码验证\r\n    const validateConfirmPassword = (rule, value, callback) => {\r\n      if (value !== this.form.password) {\r\n        callback(new Error('两次输入的密码不一致'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n\r\n    // 手机号验证\r\n    const validateMobile = (rule, value, callback) => {\r\n      const mobileReg = /^1[3-9]\\d{9}$/\r\n      if (!mobileReg.test(value)) {\r\n        callback(new Error('请输入正确的手机号'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n\r\n    return {\r\n      form: {\r\n        mobile: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        name: '',\r\n        companyName: '',\r\n        creditCode: '',\r\n        agreeTerms: false\r\n      },\r\n      rules: {\r\n        mobile: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { validator: validateMobile, trigger: 'blur' }\r\n        ],\r\n        smsCode: [\r\n          { required: true, message: '请输入验证码', trigger: 'blur' },\r\n          { min: 6, max: 6, message: '验证码长度为6位', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' },\r\n          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, message: '请确认密码', trigger: 'blur' },\r\n          { validator: validateConfirmPassword, trigger: 'blur' }\r\n        ],\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' }\r\n        ],\r\n        companyName: [\r\n          { required: true, message: '请输入企业名称', trigger: 'blur' }\r\n        ],\r\n        creditCode: [\r\n          { required: true, message: '信用代码', trigger: 'blur' }\r\n        ],\r\n      },\r\n      loading: false,\r\n      smsLoading: false,\r\n      smsCountdown: 0,\r\n      smsTimer: null\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.smsTimer) {\r\n      clearInterval(this.smsTimer)\r\n    }\r\n  },\r\n  methods: {\r\n    // 发送短信验证码\r\n    async sendSmsCode() {\r\n      // 先验证手机号\r\n      try {\r\n        await this.$refs.registerForm.validateField('mobile')\r\n      } catch (error) {\r\n        return\r\n      }\r\n\r\n      this.smsLoading = true\r\n      try {\r\n        const response = await sendSmsCode(this.form.mobile)\r\n        if (response.data.code === 200) {\r\n          this.$message.success('验证码发送成功')\r\n          this.startSmsCountdown()\r\n        } else {\r\n          this.$message.error(response.data.msg || '验证码发送失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('验证码发送失败')\r\n      } finally {\r\n        this.smsLoading = false\r\n      }\r\n    },\r\n\r\n    // 开始短信倒计时\r\n    startSmsCountdown() {\r\n      this.smsCountdown = 60\r\n      this.smsTimer = setInterval(() => {\r\n        this.smsCountdown--\r\n        if (this.smsCountdown <= 0) {\r\n          clearInterval(this.smsTimer)\r\n          this.smsTimer = null\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    // 处理注册\r\n    async handleRegister() {\r\n      if (!this.form.agreeTerms) {\r\n        this.$message.warning('请先同意用户协议和隐私政策')\r\n        return\r\n      }\r\n\r\n      try {\r\n        await this.$refs.registerForm.validate()\r\n      } catch (error) {\r\n        return\r\n      }\r\n\r\n      this.loading = true\r\n      try {\r\n        const result = await this.$store.dispatch('auth/register', this.form)\r\n        if (result.success) {\r\n          this.$message.success(result.message)\r\n          this.$router.push('/login')\r\n        } else {\r\n          this.$message.error(result.message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('注册失败，请稍后重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.register-page {\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.register-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: calc(100vh - #{$header-height});\r\n  padding: 40px 20px;\r\n}\r\n\r\n.register-card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  padding: 40px;\r\n  width: 100%;  \r\n  max-width: 500px;\r\n}\r\n\r\n.register-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n\r\n  h2 {\r\n    color: $text-primary;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  p {\r\n    color: $text-secondary;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.register-form {\r\n  .sms-input {\r\n    display: flex;\r\n    gap: 10px;\r\n\r\n    .el-input {\r\n      flex: 1;\r\n    }\r\n\r\n    .el-button {\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .register-btn {\r\n    width: 100%;\r\n    height: 44px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .login-link {\r\n    text-align: center;\r\n    margin-top: 20px;\r\n    color: $text-secondary;\r\n  }\r\n\r\n  .link {\r\n    color: $primary-color;\r\n    text-decoration: none;\r\n\r\n    &:hover {\r\n      text-decoration: underline;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: $mobile) {\r\n  .register-card {\r\n    padding: 30px 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}