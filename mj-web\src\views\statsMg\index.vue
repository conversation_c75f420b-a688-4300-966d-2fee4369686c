<template>
  <div class="app-container">
    <!-- 总体统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 30px;">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-item">
            <div class="stat-icon project-icon">
              <i class="el-icon-goods"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.totalProjects || 0 }}</div>
              <div class="stat-label">总项目数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-item">
            <div class="stat-icon member-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.totalMembers || 0 }}</div>
              <div class="stat-label">注册会员</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-item">
            <div class="stat-icon bid-icon">
              <i class="el-icon-price-tag"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.totalBids || 0 }}</div>
              <div class="stat-label">总出价次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-item">
            <div class="stat-icon amount-icon">
              <i class="el-icon-money"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">¥{{ formatAmount(statistics.totalAmount) || 0 }}</div>
              <div class="stat-label">总交易金额</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计 -->
    <el-row :gutter="20">
      <!-- 项目状态统计 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>项目状态统计</span>
          </div>
          <el-table :data="projectStatusStats" border>
            <el-table-column label="状态" prop="statusText" />
            <el-table-column label="数量" prop="count" />
            <el-table-column label="占比" prop="percentage" />
          </el-table>
        </el-card>
      </el-col>

      <!-- 会员审核统计 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>会员审核统计</span>
          </div>
          <el-table :data="memberAuditStats" border>
            <el-table-column label="审核状态" prop="statusText" />
            <el-table-column label="数量" prop="count" />
            <el-table-column label="占比" prop="percentage" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 出价活跃度统计 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>出价活跃度统计</span>
          </div>
          <el-table :data="bidActivityStats" border>
            <el-table-column label="项目名称" prop="projectTitle" />
            <el-table-column label="出价次数" prop="bidCount" />
            <el-table-column label="参与人数" prop="memberCount" />
            <el-table-column label="当前价格" prop="currentPrice">
              <template slot-scope="scope">¥{{ scope.row.currentPrice }}</template>
            </el-table-column>
            <el-table-column label="状态" prop="status">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getAuctionStats } from '@/api/auction'

export default {
  name: 'StatisticsManagement',
  data() {
    return {
      dateRange: [],
      // 统计数据
      statistics: {
        totalProjects: 0,
        totalMembers: 0,
        totalBids: 0,
        totalAmount: 0
      },
      // 项目状态统计
      projectStatusStats: [],
      // 会员审核统计
      memberAuditStats: [],
      // 出价活跃度统计
      bidActivityStats: [],
      // 最近活动
      recentActivities: []
    }
  },
  created() {
    this.getStatistics()
  },
  methods: {
    // 获取统计数据
    async getStatistics() {
      try {
        const params = {}
        if (this.dateRange && this.dateRange.length === 2) {
          params.startTime = this.dateRange[0]
          params.endTime = this.dateRange[1]
        }

        const response = await getAuctionStats(params)
        if (response.data.code === 200) {
          const data = response.data.data
          this.statistics = data || {}
          this.projectStatusStats = this.formatProjectStatusStats(data.projectStatus || {})
          this.memberAuditStats = this.formatMemberAuditStats(data.memberAudit || {})
          this.bidActivityStats = data.bidActivity || []
          this.recentActivities = data.recentActivities || []
        } else {
          this.$message.error(response.data.msg || '获取统计数据失败')
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败')
      }
    },

    // 格式化项目状态统计
    formatProjectStatusStats(data) {
      const statusMap = {
        0: '即将开始',
        1: '竞价中',
        2: '已结束',
        3: '已终止'
      }
      
      const total = Object.values(data).reduce((sum, count) => sum + count, 0)
      
      return Object.entries(data).map(([status, count]) => ({
        statusText: statusMap[status] || '未知',
        count: count,
        percentage: total > 0 ? ((count / total) * 100).toFixed(1) + '%' : '0%'
      }))
    },

    // 格式化会员审核统计
    formatMemberAuditStats(data) {
      const statusMap = {
        0: '待审核',
        1: '审核通过',
        2: '审核拒绝'
      }
      
      const total = Object.values(data).reduce((sum, count) => sum + count, 0)
      
      return Object.entries(data).map(([status, count]) => ({
        statusText: statusMap[status] || '未知',
        count: count,
        percentage: total > 0 ? ((count / total) * 100).toFixed(1) + '%' : '0%'
      }))
    },

    // 格式化金额
    formatAmount(amount) {
      if (!amount) return '0'
      return (amount / 10000).toFixed(2) + '万'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        0: 'info',     // 即将开始
        1: 'success',  // 竞价中
        2: 'warning',  // 已结束
        3: 'danger'    // 已终止
      }
      return typeMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '即将开始',
        1: '竞价中',
        2: '已结束',
        3: '已终止'
      }
      return textMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.stat-card {
  height: 120px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.project-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.member-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bid-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.amount-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}
</style>
