package service

import (
	"auction-sys/global"
	"time"

	"qiniupkg.com/x/log.v7"
)

type Total struct {
	Name  string  `json:"name"`
	Count float64 `json:"count"`
}

type MiddleTop struct {
	TotalArea int `json:"totalArea"`
	MHArea    int `json:"mhArea"`
	HZArea    int `json:"hzArea"`
	XLArea    int `json:"xlArea"`
	SDArea    int `json:"sdArea"`
	YMArea    int `json:"ymArea"`
	BugNum    int `json:"bugNum"`
	SoilNum   int `json:"soilNum"`
}

// @Title
// @Description
// <AUTHOR>
// @Param
// @Return
func GetMiddleTop() (info MiddleTop) {
	info.TotalArea = ExecSql("select sum(fm.land_area) count from farmland fm where fm.deleted_at is null and fm.cur_year=? ", []interface{}{time.Now().Year()})
	info.MHArea = ExecSql("select sum(fm.land_area) count from farmland fm where fm.deleted_at is null and fm.cur_year=? AND fm.crop_type='棉花' ", []interface{}{time.Now().Year()})
	info.HZArea = ExecSql("select sum(fm.land_area) count from farmland fm where fm.deleted_at is null and fm.cur_year=? AND fm.crop_type='红枣' ", []interface{}{time.Now().Year()})
	info.XLArea = ExecSql("select sum(fm.land_area) count from farmland fm where fm.deleted_at is null and fm.cur_year=? AND fm.crop_type='香梨' ", []interface{}{time.Now().Year()})
	info.YMArea = ExecSql("select sum(fm.land_area) count from farmland fm where fm.deleted_at is null and fm.cur_year=? AND fm.crop_type='苹果' ", []interface{}{time.Now().Year()})
	//info.SDArea = ExecSql("select sum(fm.land_area) count from farmland fm where fm.deleted_at is null and fm.cur_year=? AND fm.crop_type='水稻' ", []interface{}{time.Now().Year()})
	info.BugNum = ExecSql("select count(*) count from bug_device bd where bd.deleted_at is null ", []interface{}{})
	info.SoilNum = ExecSql("select count(*) count from soil_device bd where bd.deleted_at is null ", []interface{}{})
	info.SDArea = info.TotalArea - info.MHArea - info.HZArea - info.XLArea - info.YMArea
	return info
}

// @Title       土地类别占比
// @Description
// <AUTHOR>
// @Param
// @Return
func GetRightTop() (total []Total) {

	err := global.GVA_DB.
		Raw("select fm.land_type name,count(*) count FROM farmland fm GROUP BY fm.`land_type`").
		//Raw("select fm.land_type name,count(*) count FROM farmland fm WHERE fm.cur_year=? GROUP BY fm.`land_type`", time.Now().Year()).
		Scan(&total).Error
	if err != nil {
		return
	}
	return total
}

// @Title       土地类别占比
// @Description
// <AUTHOR>
// @Param
// @Return
func GetRightSoilInfo() (total []Total) {

	err := global.GVA_DB.
		Raw("select fm.land_type name,count(*) count FROM farmland fm WHERE fm.cur_year=? GROUP BY fm.`land_type`", time.Now().Year()).
		Scan(&total).Error
	if err != nil {
		return
	}
	return total
}

// @Title
// @Description
// <AUTHOR>
// @Param
// @Return
func ExecSql(sqlStr string, params []interface{}) int {
	var (
		total Total
	)
	err := global.GVA_DB.
		Raw(sqlStr, params...).
		Scan(&total).Error
	if err != nil {
		log.Error(err.Error())
		return 0
	}
	return int(total.Count)
}
