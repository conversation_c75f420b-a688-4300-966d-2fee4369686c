/*
 * @Descripttion: 菜单管理
 * @version:
 * @Author: dlg
 * @Date: 2020-06-28 16:37:00
 * @LastEditors: dlg
 * @LastEditTime: 2020-07-13 15:08:38
 */
import request from '@/utils/request'

/**
 * @description: 获取 menu 列表
 * @param {type}
 * @return:
 */
export function getMenuList() {
    return request({
        url: "/admin/menu/getMenuList",
        method: 'post'
    })
}

/**
 * @description: 新增基础 menu
 * @param {type}
 * @return:
 */
export function addBaseMenu(data) {
    return request({
        url: "/admin/menu/addBaseMenu",
        method: 'post',
        data
    })
}

/**
 * @description: 修改 menu 列表
 * @param {type}
 * @return:
 */
export function updateBaseMenu(data) {
    return request({
        url: "/admin/menu/updateBaseMenu",
        method: 'post',
        data
    })
}

/**
 * @description:
 * @param {type}
 * @return:
 */
export function deleteBaseMenu(data) {
    return request({
        url: "/admin/menu/deleteBaseMenu",
        method: 'post',
        data
    })
}