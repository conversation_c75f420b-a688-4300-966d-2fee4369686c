basePath: /
definitions:
  config.Admin:
    properties:
      username:
        type: string
    type: object
  config.Captcha:
    properties:
      imgHeight:
        type: integer
      imgWidth:
        type: integer
      keyLong:
        type: integer
    type: object
  config.Casbin:
    properties:
      modelPath:
        type: string
    type: object
  config.Image:
    properties:
      allowExts:
        items:
          type: string
        type: array
      height:
        type: integer
      maxSize:
        type: integer
      prefixUrl:
        type: string
      runtimeRootPath:
        type: string
      savePath:
        type: string
      width:
        type: integer
    type: object
  config.JWT:
    properties:
      signingKey:
        type: string
    type: object
  config.Log:
    properties:
      file:
        type: string
      logFile:
        type: boolean
      prefix:
        type: string
      stdout:
        type: string
    type: object
  config.Logo:
    properties:
      company:
        type: string
      user:
        type: string
    type: object
  config.Mysql:
    properties:
      config:
        type: string
      dbname:
        type: string
      logMode:
        type: boolean
      maxIdleConns:
        type: integer
      maxOpenConns:
        type: integer
      password:
        type: string
      path:
        type: string
      username:
        type: string
    type: object
  config.Qiniu:
    properties:
      accessKey:
        type: string
      bucket:
        type: string
      imgPath:
        type: string
      secretKey:
        type: string
    type: object
  config.Redis:
    properties:
      addr:
        type: string
      db:
        type: integer
      password:
        type: string
    type: object
  config.Server:
    properties:
      admin:
        $ref: '#/definitions/config.Admin'
        type: object
      captcha:
        $ref: '#/definitions/config.Captcha'
        type: object
      casbin:
        $ref: '#/definitions/config.Casbin'
        type: object
      image:
        $ref: '#/definitions/config.Image'
        type: object
      jwt:
        $ref: '#/definitions/config.JWT'
        type: object
      log:
        $ref: '#/definitions/config.Log'
        type: object
      logo:
        $ref: '#/definitions/config.Logo'
        type: object
      mysql:
        $ref: '#/definitions/config.Mysql'
        type: object
      qiniu:
        $ref: '#/definitions/config.Qiniu'
        type: object
      redis:
        $ref: '#/definitions/config.Redis'
        type: object
      sqlite:
        $ref: '#/definitions/config.Sqlite'
        type: object
      system:
        $ref: '#/definitions/config.System'
        type: object
    type: object
  config.Sqlite:
    properties:
      config:
        type: string
      logMode:
        type: boolean
      password:
        type: string
      path:
        type: string
      username:
        type: string
    type: object
  config.System:
    properties:
      addr:
        type: integer
      dbType:
        type: string
      env:
        type: string
      useMultipoint:
        type: boolean
    type: object
  model.SysAuthority:
    properties:
      authorityName:
        description: 角色名
        type: string
      authorityType:
        description: 角色类别 0：系统开发管理员 1：企业管理员 2：普通用户
        type: integer
      bindingUsers:
        description: 角色绑定用户数
        type: integer
      companyCode:
        description: 企业标识，企业信用代码
        type: string
      description:
        description: 角色描述
        type: string
      menus:
        description: 菜单对象
        items:
          $ref: '#/definitions/model.SysBaseMenu'
        type: array
      status:
        description: 角色状态 1：启用 0：禁用
        type: integer
      users:
        description: 用户对象
        items:
          $ref: '#/definitions/model.SysUser'
        type: array
    type: object
  model.SysBaseMenu:
    properties:
      authoritys:
        description: 角色数组
        items:
          $ref: '#/definitions/model.SysAuthority'
        type: array
      category:
        description: 菜单类别 菜单：0  按钮：1
        type: integer
      children:
        description: 子级菜单数组
        items:
          $ref: '#/definitions/model.SysBaseMenu'
        type: array
      component:
        description: 前端文件路径
        type: string
      defaultMenu:
        description: 是否是基础路由
        type: boolean
      hidden:
        description: 是否隐藏
        type: boolean
      icon:
        description: 菜单图标
        type: string
      keepAlive:
        description: 是否缓存
        type: boolean
      keyval:
        description: 按钮标识
        type: string
      name:
        description: 路由name
        type: string
      parentId:
        description: 父菜单ID
        type: integer
      path:
        description: 路由path
        type: string
      sort:
        description: 排序标记
        type: integer
      title:
        description: 菜单名
        type: string
    type: object
  model.SysCompany:
    properties:
      address:
        description: 企业地址
        type: string
      adminUser:
        description: 企业管理员账号
        type: string
      code:
        description: 企业编码，信用代码
        type: string
      email:
        description: 企业邮箱
        type: string
      logoPreviewPath:
        description: 企业logo浏览地址
        type: string
      logoSavePath:
        description: 企业logo存储地址
        type: string
      name:
        description: 企业名称
        type: string
      personLiable:
        description: 责任人
        type: string
      postcode:
        type: integer
      status:
        description: 企业状态 1:启用 0:禁用
        type: integer
      telephone:
        description: 企业手机号
        type: string
    type: object
  model.SysUser:
    properties:
      authoritys:
        description: 角色对象
        items:
          $ref: '#/definitions/model.SysAuthority'
        type: array
      companyCode:
        description: 企业编码，唯一标识
        type: string
      email:
        description: 邮箱
        type: string
      headerImg:
        description: 用户浏览头像路径
        type: string
      headerImgSave:
        description: 头像存储路径
        type: string
      loginStatus:
        description: 登录状态 1：在线 0：离线
        type: integer
      loginTime:
        description: 最后登录时间
        type: string
      nickName:
        description: 真实姓名
        type: string
      passwordStatus:
        description: 密码状态 1：不用修改  0：必须修改
        type: integer
      status:
        description: 用户状态 1:启用 0:禁用
        type: integer
      userName:
        description: 用户登录名
        type: string
      uuid:
        description: UUID
        type: string
    type: object
  model.System:
    properties:
      config:
        $ref: '#/definitions/config.Server'
        type: object
    type: object
  request.AddMenuBtn:
    properties:
      category:
        description: 类别
        type: integer
      createdAt:
        type: string
      hidden:
        description: 是否隐藏
        type: boolean
      id:
        type: integer
      keyval:
        description: 按钮唯一标识
        type: string
      parentId:
        description: 父级id
        type: integer
      title:
        description: 按钮名称
        type: string
    type: object
  request.ChangePasswordStruct:
    properties:
      id:
        description: 用户id（重置密码时使用）
        type: integer
      newPassword:
        description: 新密码
        type: string
      password:
        description: 旧密码
        type: string
    type: object
  request.GetAuthAndUsers:
    properties:
      id:
        description: 角色id
        type: integer
      page:
        type: integer
      pageSize:
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  request.GetAuthorityList:
    properties:
      name:
        type: string
      page:
        type: integer
      pageSize:
        type: integer
    type: object
  request.GetById:
    properties:
      id:
        type: number
    type: object
  request.GetCompanyList:
    properties:
      code:
        description: 企业编码，信用代码
        type: string
      name:
        description: 企业名称
        type: string
      page:
        type: integer
      pageSize:
        type: integer
    type: object
  request.GetUserList:
    properties:
      companyCode:
        description: 企业编码
        type: string
      page:
        type: integer
      pageSize:
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  request.PageInfo:
    properties:
      page:
        type: integer
      pageSize:
        type: integer
    type: object
  request.RegisterAndLoginStruct:
    properties:
      captcha:
        description: 暂时不用
        type: string
      captchaId:
        description: 暂时不用
        type: string
      password:
        description: 面膜
        type: string
      username:
        description: 用户名
        type: string
    type: object
  request.RegisterStruct:
    properties:
      authorityIds:
        description: 角色id数组
        items:
          type: integer
        type: array
      email:
        description: 邮箱
        type: string
      headerImg:
        description: 头像
        type: string
      id:
        type: integer
      nickName:
        description: 真实姓名
        type: string
      passWord:
        description: 密码
        type: string
      status:
        description: 状态 1：启用 0：禁用
        type: integer
      userName:
        description: 用户登录名
        type: string
    type: object
info:
  contact: {}
  description: This is a sample Server pets
  license: {}
  title: Swagger Example API
  version: 0.0.1
paths:
  /authority/createAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建角色
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"创建成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"创建失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 创建角色
      tags:
      - authority ：角色管理
  /authority/deleteAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 删除角色
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/request.GetById'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"删除成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"删除失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 删除角色
      tags:
      - authority ：角色管理
  /authority/getAuthBindingUsers:
    post:
      consumes:
      - application/json
      parameters:
      - description: 获取变更角色用户列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{authBindingUsers},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"获取失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 获取变更角色用户列表
      tags:
      - authority ：角色管理
  /authority/getAuthBySelfCompany:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{authList},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"加载失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 获取自己公司的角色
      tags:
      - authority ：角色管理
  /authority/getAuthorityAndUsers:
    post:
      consumes:
      - application/json
      parameters:
      - description: 获取角色和绑定的用户
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetAuthAndUsers'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{BindingUsersResult},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"获取失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 获取角色和绑定的用户
      tags:
      - authority ：角色管理
  /authority/getAuthorityList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页获取角色列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetAuthorityList'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{PageResult},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"获取数据失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 分页获取角色列表
      tags:
      - authority ：角色管理
  /authority/updateAuthBindingUsers:
    post:
      consumes:
      - application/json
      parameters:
      - description: 更新变更角色用户列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更新变更角色用户列表
      tags:
      - authority ：角色管理
  /authority/updateAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 修改角色
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 修改角色
      tags:
      - authority ：角色管理
  /authority/updateAuthorityStatus:
    post:
      consumes:
      - application/json
      parameters:
      - description: 更改角色状态
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/model.SysAuthority'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更改角色状态
      tags:
      - authority ：角色管理
  /base/captcha:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"获取成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 生成验证码
      tags:
      - base
  /base/captcha/:captchaId:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"获取成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 生成验证码图片路径
      tags:
      - base
  /base/login:
    post:
      parameters:
      - description: 用户登录接口
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.RegisterAndLoginStruct'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"登陆成功"}'
          schema:
            type: string
      summary: 用户登录
      tags:
      - Base
  /company/addCompany:
    post:
      consumes:
      - application/json
      parameters:
      - description: 新增公司
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SysCompany'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"创建成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"创建失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 新增公司
      tags:
      - company ：企业管理
  /company/deleteCompany:
    post:
      consumes:
      - application/json
      parameters:
      - description: 删除公司
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/request.GetById'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"删除成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"删除失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 删除公司
      tags:
      - company ：企业管理
  /company/getCompany:
    post:
      consumes:
      - application/json
      parameters:
      - description: 根据id查询公司
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{SysCompanyResponse},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"查询失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 根据id查询公司
      tags:
      - company ：企业管理
  /company/getCompanyCondition:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{[]response.CompanyCondition},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"加载失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 筛选条件栏企业数据
      tags:
      - company ：企业管理
  /company/getCompanyList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 获取公司
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetCompanyList'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{GetCompanyList},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"后去数据失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 获取公司
      tags:
      - company ：企业管理
  /company/updateCompany:
    post:
      consumes:
      - application/json
      parameters:
      - description: 更新公司
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SysCompany'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更新公司
      tags:
      - company ：企业管理
  /company/updateCompanyStatus:
    post:
      consumes:
      - application/json
      parameters:
      - description: 更新公司状态
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/model.SysCompany'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更新公司状态
      tags:
      - company ：企业管理
  /company/uploadCompanyLogo:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"上传成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"上传失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 上传公司Logo
      tags:
      - company ：企业管理
  /jwt/jsonInBlacklist:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"拉黑成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: jwt加入黑名单
      tags:
      - jwt ：权限管理
  /menu/addBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 新增菜单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SysBaseMenu'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"添加成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"添加失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 新增菜单
      tags:
      - menu ：菜单管理
  /menu/addMenuBtn:
    post:
      consumes:
      - application/json
      parameters:
      - description: 新增按钮
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.AddMenuBtn'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"添加成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"添加失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 新增按钮
      tags:
      - menu ：菜单管理
  /menu/deleteBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 删除菜单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"删除成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"删除失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 删除菜单
      tags:
      - menu ：菜单管理
  /menu/getAuthMenus:
    post:
      consumes:
      - application/json
      parameters:
      - description: 根据角色id获取菜单，可多个角色
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/request.GetById'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{[]response.Menu},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"加载失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 根据角色id获取菜单，可多个角色
      tags:
      - menu ：菜单管理
  /menu/getBaseMenuById:
    post:
      consumes:
      - application/json
      parameters:
      - description: 根据角色id获取菜单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{SysAuthorityMenuResponse},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"查询失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 根据角色id获取菜单,并赋值状态
      tags:
      - menu ：菜单管理
  /menu/getMenu:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{RouterMenuResponse},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"获取失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 获取用户动态路由
      tags:
      - authorityAndMenu ：权限菜单
  /menu/getMenuList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页获取基础menu列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.PageInfo'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{PageResult},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"获取失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 分页获取基础menu列表
      tags:
      - menu ：菜单管理
  /menu/updateBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 更新菜单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SysBaseMenu'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更新菜单
      tags:
      - menu ：菜单管理
  /system/ReloadSystem:
    post:
      parameters:
      - description: 设置配置文件内容
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.System'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"返回成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 设置配置文件内容
      tags:
      - system
  /system/getSystemConfig:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"返回成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 获取配置文件内容
      tags:
      - system
  /system/setSystemConfig:
    post:
      parameters:
      - description: 设置配置文件内容
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.System'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"返回成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 设置配置文件内容
      tags:
      - system
  /user/addUser:
    post:
      parameters:
      - description: 添加用户
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.RegisterStruct'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"注册成功"}'
          schema:
            type: string
      summary: 添加用户
      tags:
      - SysUser ：用户管理
  /user/changePassword:
    put:
      parameters:
      - description: 用户修改密码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ChangePasswordStruct'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"修改成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"修改失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 用户修改密码
      tags:
      - SysUser ：用户管理
  /user/deleteUser:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除用户
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/request.GetById'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"删除成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"删除失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 删除用户
      tags:
      - SysUser ：用户管理
  /user/getUserList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页获取用户列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetUserList'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{GetUserList},"msg":"操作成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"获取数据失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 分页获取用户列表
      tags:
      - SysUser ：用户管理
  /user/resetPassword:
    put:
      parameters:
      - description: 重置用户密码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ChangePasswordStruct'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"重置成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"重置失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 重置用户密码
      tags:
      - SysUser ：用户管理
  /user/updateUser:
    post:
      consumes:
      - application/json
      parameters:
      - description: 更改用户
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.RegisterStruct'
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更改用户
      tags:
      - SysUser ：用户管理
  /user/updateUserStatus:
    post:
      consumes:
      - application/json
      parameters:
      - description: 更改用户状态
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/model.SysUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"更新成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"更新失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更改用户状态
      tags:
      - SysUser ：用户管理
  /user/uploadHeaderImg:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 用户上传头像
        in: formData
        name: headerImg
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: '{"code":200,"data":{},"msg":"上传成功"}'
          schema:
            type: string
        "400":
          description: '{"code":400,"data":{},"msg":"上传失败"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 用户上传头像
      tags:
      - SysUser ：用户管理
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: x-token
    type: apiKey
swagger: "2.0"
