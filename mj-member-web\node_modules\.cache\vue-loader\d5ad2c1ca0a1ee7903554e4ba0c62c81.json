{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\auth\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\auth\\Login.vue", "mtime": 1757555140738}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Login.vue"], "names": [], "mappings": ";AAqEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Login.vue", "sourceRoot": "src/views/auth", "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <AppHeader />\n    \n    <div class=\"login-container\">\n      <div class=\"login-card\">\n        <div class=\"login-header\">\n          <h2>用户登录</h2>\n          <p>欢迎回到棉副产品竞价平台</p>\n        </div>\n\n        <el-form\n          ref=\"loginForm\"\n          :model=\"form\"\n          :rules=\"rules\"\n          label-width=\"0\"\n          class=\"login-form\"\n        >\n          <el-form-item prop=\"mobile\">\n            <el-input\n              v-model=\"form.mobile\"\n              placeholder=\"请输入手机号\"\n              prefix-icon=\"el-icon-phone\"\n              size=\"large\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"form.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              prefix-icon=\"el-icon-lock\"\n              size=\"large\"\n              show-password\n              @keyup.enter.native=\"handleLogin\"\n            />\n          </el-form-item>\n\n          <el-form-item>\n            <div class=\"form-options\">\n              <el-checkbox v-model=\"form.rememberMe\">记住我</el-checkbox>\n              <a href=\"#\" class=\"forgot-password\">忘记密码？</a>\n            </div>\n          </el-form-item>\n\n          <el-form-item>\n            <el-button\n              type=\"primary\"\n              @click=\"handleLogin\"\n              :loading=\"loading\"\n              class=\"login-btn\"\n              size=\"large\"\n            >\n              登录\n            </el-button>\n          </el-form-item>\n\n          <div class=\"register-link\">\n            还没有账号？\n            <router-link to=\"/register\" class=\"link\">立即注册</router-link>\n          </div>\n        </el-form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Login',\n  data() {\n    // 手机号验证\n    const validateMobile = (rule, value, callback) => {\n      const mobileReg = /^1[3-9]\\d{9}$/\n      if (!mobileReg.test(value)) {\n        callback(new Error('请输入正确的手机号'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      form: {\n        mobile: '',\n        password: '',\n        rememberMe: false\n      },\n      rules: {\n        mobile: [\n          { required: true, message: '请输入手机号', trigger: 'blur' },\n          { validator: validateMobile, trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }\n        ]\n      },\n      loading: false\n    }\n  },\n  methods: {\n    async handleLogin() {\n      try {\n        await this.$refs.loginForm.validate()\n      } catch (error) {\n        return\n      }\n\n      this.loading = true\n      try {\n        const result = await this.$store.dispatch('auth/login', this.form)\n        if (result.success) {\n          this.$message.success('登录成功')\n          \n          // 检查是否有重定向地址\n          const redirect = this.$route.query.redirect || '/home'\n          this.$router.push(redirect)\n        } else {\n          this.$message.error(result.message)\n        }\n      } catch (error) {\n        // this.$message.error('登录失败，请稍后重试')\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.login-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: calc(100vh - #{$header-height});\n  padding: 40px 20px;\n}\n\n.login-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n\n  h2 {\n    color: $text-primary;\n    margin-bottom: 10px;\n  }\n\n  p {\n    color: $text-secondary;\n    font-size: 14px;\n  }\n}\n\n.login-form {\n  .form-options {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 10px;\n\n    .forgot-password {\n      color: $primary-color;\n      text-decoration: none;\n      font-size: 14px;\n\n      &:hover {\n        text-decoration: underline;\n      }\n    }\n  }\n\n  .login-btn {\n    width: 100%;\n    height: 44px;\n    font-size: 16px;\n  }\n\n  .register-link {\n    text-align: center;\n    margin-top: 20px;\n    color: $text-secondary;\n  }\n\n  .link {\n    color: $primary-color;\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n\n@media (max-width: $mobile) {\n  .login-card {\n    padding: 30px 20px;\n  }\n}\n</style>\n"]}]}