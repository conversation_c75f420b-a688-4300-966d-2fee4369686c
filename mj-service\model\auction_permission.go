package model

import (
	"github.com/Gre-Z/common/jtime"
)

// 竞价权限对象
type AuctionPermission struct {
	Model
	ProjectID int            `json:"projectId" gorm:"comment:'竞价项目ID'"`
	Project   AuctionProject `json:"project" gorm:"foreignkey:ProjectID"`
	MemberID  int            `json:"memberId" gorm:"comment:'会员ID'"`
	Member    SysMember      `json:"member" gorm:"foreignkey:MemberID"`
	GrantTime jtime.JsonTime `json:"grantTime" gorm:"comment:'授权时间'"`
	GrantBy   int            `json:"grantBy" gorm:"comment:'授权人ID'"`
	Grantor   SysUser        `json:"grantor" gorm:"foreignkey:GrantBy"`
	Status    int8           `json:"status" gorm:"default:1;comment:'状态(1:有效,2:已撤销)'"`
	Remark    string         `json:"remark" gorm:"comment:'备注'"`
}

func (AuctionPermission) TableName() string {
	return "auction_permissions"
}

// 检查权限是否有效
func (ap *AuctionPermission) IsValid() bool {
	return ap.Status == 1
}

// 撤销权限
func (ap *AuctionPermission) Revoke() {
	ap.Status = 2
}
