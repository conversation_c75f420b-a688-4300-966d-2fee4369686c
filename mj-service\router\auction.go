package router

import (
	v1 "auction-sys/api/v1"
	"auction-sys/middleware"

	"github.com/gin-gonic/gin"
)

// 初始化竞价系统路由
func InitAuctionRouter(Router *gin.RouterGroup) {
	// 后台管理路由（需要JWT认证）
	AuctionAdminRouter := Router.Group("auction/admin").Use(middleware.JWTAuth())
	{
		// 会员管理
		AuctionAdminRouter.POST("members", v1.GetMemberList)               // 获取会员列表
		AuctionAdminRouter.POST("members/audit", v1.AuditMember)           // 审核会员
		AuctionAdminRouter.POST("members/detail", v1.GetMemberDetail)      // 获取会员详情
		AuctionAdminRouter.POST("members/status", v1.UpdateMemberStatus)   // 更新会员状态
		AuctionAdminRouter.POST("members/approved", v1.GetApprovedMembers) // 获取已审核通过的会员列表

		// 项目管理
		AuctionAdminRouter.POST("projects", v1.GetAuctionProjectList)             // 获取竞价项目列表
		AuctionAdminRouter.POST("projects/create", v1.CreateAuctionProject)       // 创建竞价项目
		AuctionAdminRouter.POST("projects/update", v1.UpdateAuctionProject)       // 更新竞价项目
		AuctionAdminRouter.POST("projects/detail", v1.GetAuctionProjectDetail)    // 获取竞价项目详情
		AuctionAdminRouter.POST("projects/terminate", v1.TerminateAuctionProject) // 终止竞价项目

		AuctionAdminRouter.POST("projectsPermission", v1.GetAuctionProjectPermission)
		// 权限管理
		AuctionAdminRouter.POST("permissions", v1.ManageAuctionPermission) // 管理竞价权限

		// 监控管理
		AuctionAdminRouter.POST("bids", v1.GetBidList) // 获取出价记录列表

		// 统计管理
		AuctionAdminRouter.POST("stats", v1.GetAuctionStats) // 获取竞价统计信息

		// WebSocket连接
		AuctionAdminRouter.GET("ws", v1.AdminWebSocket) // 管理员WebSocket连接
	}

	// 会员端路由
	AuctionMemberRouter := Router.Group("auction/member")
	{
		// 无需认证的接口
		AuctionMemberRouter.POST("register", v1.MemberRegister) // 会员注册
		AuctionMemberRouter.POST("login", v1.MemberLogin)       // 会员登录
		AuctionMemberRouter.POST("sms", v1.SendMemberSms)       // 发送短信验证码

		// 需要认证的接口
		AuthMemberRouter := AuctionMemberRouter.Use(middleware.JWTAuth())
		{
			// 竞价大厅
			AuthMemberRouter.POST("projects", v1.GetMemberAuctionProjectList)          // 获取竞价项目列表
			AuthMemberRouter.POST("projects/detail", v1.GetMemberAuctionProjectDetail) // 获取竞价项目详情

			// 出价相关
			AuthMemberRouter.POST("bid", v1.PlaceBid)                // 参与出价
			AuthMemberRouter.POST("bid/check", v1.CheckBidFrequency) // 检查出价频率限制

			// 我的参与
			AuthMemberRouter.POST("participation", v1.GetMyParticipation) // 获取我的参与记录

			// WebSocket连接
			AuthMemberRouter.GET("ws", v1.MemberWebSocket) // 会员WebSocket连接
		}
	}

	// 公开路由（无需认证）
	AuctionPublicRouter := Router.Group("auction/public")
	{
		// 竞价项目
		AuctionPublicRouter.POST("projects", v1.GetPublicAuctionProjectList) // 获取公开竞价项目列表（不包含价格信息）

		// WebSocket连接
		AuctionPublicRouter.GET("ws", v1.PublicWebSocket) // 公开WebSocket连接
	}
}
