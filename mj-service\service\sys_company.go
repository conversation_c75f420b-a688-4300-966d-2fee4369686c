package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/utils"
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
)

// @Title 增加公司
// @Description
// <AUTHOR>
// @Param company 		model.SysCompany	企业信息
// @Return err 			error 				错误信息

func AddCompany(company model.SysCompany) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查询条件sql
		sql := fmt.Sprintf("(name = '%v' or code = '%v' or telephone = '%v') and deleted_at is null", company.Name, company.Code, company.Telephone)
		if company.Email != "" {
			sql = fmt.Sprintf("(name = '%v' or code = '%v' or telephone = '%v' or email = '%v') and deleted_at is null", company.Name, company.Code, company.Telephone, company.Email)
		}
		// 查询
		findOne := tx.Where(sql).First(&model.SysCompany{}).RecordNotFound()
		if !findOne {
			return errors.New("存在重复name或者code或邮箱，请修改")
		}

		if company.LogoPreviewPath == "" {
			company.LogoPreviewPath = global.GVA_CONFIG.Logo.Company // 默认logo
		}
		company.AdminUser = company.Telephone
		err = tx.Create(&company).Error
		if err != nil {
			return errors.New("企业创建失败")
		}
		// 初始化角色信息
		var authority model.SysAuthority
		authority.AuthorityName = company.Name                   // 角色名称用公司名称
		authority.CompanyCode = company.Code                     // 公司编码
		authority.AuthorityType = constants.AUTHORITY_TYPE_ADMIN // 角色类别 - 公司管理员
		authority.Status = constants.STATUS_ENABLED              //	角色状态
		err = tx.Create(&authority).Error
		if err != nil {
			return errors.New("初始化角色失败")
		}
		// 初始化用户信息
		var user model.SysUser
		user.Username = company.Telephone                              // 账号 - 公司手机号
		user.Password = utils.MD5V([]byte(constants.PASSWORD_DEFAULT)) // 默认密码
		user.CompanyCode = company.Code                                // 公司编码
		user.Email = company.Email                                     // 邮箱 - 公司邮箱
		user.PasswordStatus = constants.STATUS_ENABLED                 // 默认密码状态 - 必须修改
		user.Status = constants.STATUS_ENABLED                         // 默认用户状态 - 启用
		user.SysAuthoritys = []model.SysAuthority{authority}           // 角色
		user.UUID = uuid.NewV4()                                       // UUID
		user.NickName = company.PersonLiable                           // 企业负责人
		err = tx.Create(&user).Error
		if err != nil {
			return errors.New("初始化用户失败")
		}

		return nil
	})
}

// @Title 更新公司
// @Description
// <AUTHOR>
// @Param company 		model.SysCompany	企业信息
// @Return err 			error 				错误信息

func UpdateCompany(company model.SysCompany) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var oldCompany model.SysCompany
		upDataMap := make(map[string]interface{})
		upDataMap["name"] = company.Name                         // 公司名称
		upDataMap["address"] = company.Address                   // 公司地址
		upDataMap["logo_preview_path"] = company.LogoPreviewPath // 公司logo浏览地址
		upDataMap["person_liable"] = company.PersonLiable        // 责任人
		upDataMap["telephone"] = company.Telephone               // 手机号
		upDataMap["status"] = company.Status                     // 状态
		upDataMap["email"] = company.Email                       // 邮箱
		upDataMap["postcode"] = company.Postcode                 // 邮编
		tx.Where("id = ? ", company.ID).First(&oldCompany)

		//查重
		if oldCompany.Name != company.Name || oldCompany.Code != company.Code || oldCompany.Email != company.Email {
			sql := fmt.Sprintf("id <> %v and (name = '%v' or code = '%v') and deleted_at is null", company.ID, company.Name, company.Code)
			if company.Email != "" {
				sql = fmt.Sprintf("id <> %v and (name = '%v' or code = '%v' or email = '%v') and deleted_at is null", company.ID, company.Name, company.Code, company.Email)
			}
			notSame := tx.Where(sql).First(&model.SysCompany{}).RecordNotFound()
			if !notSame {
				global.GVA_LOG.Debug("存在相同name或code或邮箱，修改失败")
				return errors.New("存在相同name或code或邮箱，修改失败")
			}
		}

		err = tx.Table("sys_company").Where("id = ?", company.ID).Updates(upDataMap).Error
		if err != nil {
			return err
		}
		// 如果修改后的 logo 路径跟之前不一样，并且不是默认logo，删除原来的文件
		if oldCompany.LogoPreviewPath != global.GVA_CONFIG.Logo.Company {
			index := strings.LastIndex(oldCompany.LogoPreviewPath, "/")
			key := oldCompany.LogoPreviewPath[index+1 : len(oldCompany.LogoPreviewPath)]
			err = utils.DeleteFile(key)
		}
		// 如果邮箱有修改，同步修改用户的邮箱
		if company.Email != oldCompany.Email {
			err = tx.Table("sys_users").Where("username = ?", company.AdminUser).Update("email", company.Email).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// @Title 根据id获取公司
// @Description
// <AUTHOR>
// @Param id 		float64				企业 id
// @Return err 		error 				错误信息
// @Param company 	model.SysCompany	企业信息

func GetCompany(id int) (err error, company model.SysCompany) {
	err = global.GVA_DB.Where("id = ?", id).First(&company).Error
	return
}

// @Title 删除公司
// @Description
// <AUTHOR>
// @Param ids 		[]request.GetById	企业 ids
// @Return err 		error 				错误信息

func DeleteCompany(ids []req.GetById) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var company model.SysCompany
		for _, id := range ids {
			var oldCompany model.SysCompany
			tx.Where("id = ?", id.Id).First(&oldCompany)
			err = tx.Where("id = ?", id.Id).Delete(&company).Error
			if err != nil {
				return err
			}
			if oldCompany.LogoPreviewPath != global.GVA_CONFIG.Logo.Company {
				index := strings.LastIndex(oldCompany.LogoPreviewPath, "/")
				key := oldCompany.LogoPreviewPath[index+1 : len(oldCompany.LogoPreviewPath)]
				err = utils.DeleteFile(key)
			}
		}
		return nil
	})
}

// @Title 分页获取公司
// @Description
// <AUTHOR>
// @Param company 		request.GetCompanyList	查询条件
// @Param uuid 			string					用户唯一标识
// @Return err 			error 					错误信息
// @Return list 		interface{} 			列表
// @Return total 		int 					总数
// @Return enableNum 	int 					已启用数量
// @Return disableNum 	int 					已禁用数量

func GetCompanyList(company req.GetCompanyList, uuid string) (err error, list interface{}, total int, enableNum, disableNum int) {
	limit := company.PageSize
	if limit <= 0 {
		limit = constants.LIMIT
	}
	offset := company.PageSize * (company.Page - 1)
	var currentUser model.SysUser
	global.GVA_DB.Where("uuid = ?", uuid).First(&currentUser)
	db := global.GVA_DB
	var companyList []model.SysCompany
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		db = db.Where("code = ?", currentUser.CompanyCode)
	}
	// 模糊查询时拼接以下条件
	if len(company.Name) > 0 {
		db = db.Where("instr(name, ?) or instr(code, ?)", company.Name, company.Name)
	}
	err = db.Find(&companyList).Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&companyList).Error
	// 查询启用状态数量，禁用状态数量
	err = db.Where("status = ?", constants.STATUS_ENABLED).Find(&[]model.SysCompany{}).Count(&enableNum).Error
	err = db.Where("status = ?", constants.STATUS_DISABLED).Find(&[]model.SysCompany{}).Count(&disableNum).Error

	return err, companyList, total, enableNum, disableNum
}

// @Title 更新公司状态
// @Description
// <AUTHOR>
// @Param companys 	[]model.SysCompany	企业信息数组
// @Return err 		error 				错误信息

func UpdateCompanyStatus(companys []model.SysCompany) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, company := range companys {
			err = tx.Model(&company).Update("status", company.Status).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// @Title 下拉框中填充公司数据
// @Description
// <AUTHOR>
// @Param uuid 			string							用户唯一标识
// @Return err 			error 							错误信息
// @Return companys 	[]response.CompanyCondition 	企业数组

func GetCompanyCondition(uuid string) (err error, companys []resp.CompanyCondition) {
	var currentUser model.SysUser
	err = global.GVA_DB.Where("uuid = ?", uuid).First(&currentUser).Error
	if err != nil {
		return err, companys
	}
	sql := fmt.Sprintf("status = %v", constants.STATUS_ENABLED)
	// 如果不是系统管理员，查询当前用户公司
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		sql += fmt.Sprintf(" and code = '%v'", currentUser.CompanyCode)
	}
	err = global.GVA_DB.Table("sys_company").Select("name, code").Where(sql).Scan(&companys).Error
	if err != nil {
		return err, companys
	}
	return err, companys
}
