package resp

import "auction-sys/model"

// 返回企业列表
type GetCompanyList struct {
	List       interface{} `json:"list"`       // 企业列表
	Total      int         `json:"total"`      // 总条数
	Page       int         `json:"page"`       // 当前页
	PageSize   int         `json:"pageSize"`   // 每页条数
	Name       string      `json:"name"`       // 筛选条件——名称
	Code       string      `json:"code"`       // 筛选条件——编码
	EnableNum  int         `json:"enableNum"`  // 启用数量
	DisableNum int         `json:"disableNum"` // 禁用数量
}

// 封装企业返回
type SysCompanyResponse struct {
	Company model.SysCompany `json:"company"` // 企业对象
}

// 企业作为筛选条件时，只需要名称和编码
type CompanyCondition struct {
	Name string `json:"name"` // 企业名称
	Code string `json:"code"` // 企业编码
}
