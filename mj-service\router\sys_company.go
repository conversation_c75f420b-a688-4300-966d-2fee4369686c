package router

import (
	v1 "auction-sys/api/v1"
	"auction-sys/middleware"

	"github.com/gin-gonic/gin"
)

func InitCompanyRouter(Router *gin.RouterGroup) {
	CompanyRouter := Router.Group("company").Use(middleware.JWTAuth())
	{
		CompanyRouter.POST("addCompany", v1.AddCompany)                   // 添加公司
		CompanyRouter.POST("deleteCompany", v1.DeleteCompany)             // 删除公司
		CompanyRouter.POST("getCompanyList", v1.GetCompanyList)           // 分页获取公司
		CompanyRouter.POST("updateCompanyStatus", v1.UpdateCompanyStatus) // 更新公司状态
		CompanyRouter.POST("uploadCompanyLogo", v1.UploadCompanyLogo)     // 上传公司logo
		CompanyRouter.POST("getCompany", v1.GetCompany)                   // 根据id查询公司
		CompanyRouter.POST("getCompanyCondition", v1.GetCompanyCondition) // 条件筛选栏企业数据
	}
}
