package constants

const (
	AUTHORITY_TYPE_SUPERADMIN = 0 // 超级管理员（系统开发人员）
	AUTHORITY_TYPE_ADMIN      = 1 // 公司管理员
	AUTHORITY_TYPE_NORMAL     = 2 // 普通用户
)

const (
	STATUS_ENABLED  = 1 // 启用
	STATUS_DISABLED = 0 // 禁用
)

const (
	PASSWORD_DEFAULT = "888888" // 默认密码
)

const (
	LIMIT = 1
)

const (
	CATEGORY_MENU = 0 // 菜单类型
	CATEGORY_BTN  = 1 // 按钮类型
)

// 登录方式
const (
	LOGIN_METHOD_APPLETS = 0 // 小程序
	LOGIN_METHOD_WEB     = 1 // web
)

// 小程序请求头
const APPLETS_HEADER = "MicroMessenger"

// 日志
const (
	LOG_LOGIN_LIST     = 0
	LOG_OPERATION_LIST = 1
)

// 是否Root用户
const (
	ROOT   = 1
	NOROOT = 0
)

// 辖区标识
const (
	ALL   = "all"   // 全国
	TEMP  = "temp"  // 温区
	AREA  = "area"  // 片区
	CITY  = "city"  // 城市
	SMALL = "small" // 小片区
)

// 时间格式
const (
	DATE_FORMAT         = "2006-01-02"
	MONTH_FORMAT        = "2006-01"
	YEAR_FORMAT         = "2006"
	TIME_FORMAT         = "2006-01-02 15:04:05"
	YYYY_MM_DD_HH_MM_SS = "2006-01-02 15:04:05"
	YYYYMMDDHHMMSS      = "20060102150405"
	DATE_FORMA_2 = "2006/01/02"
)

var FieldName = map[string]string{
	"ContrlShackDryBallTemp": "干球温度",
	"ContrlShackWetBallTemp": "湿球温度",
	"RtDryBallTemp":          "干球目标",
	"RtWetBallTemp":          "湿球目标",
	"CntrlModel":             "控制模式",
	"RunStep":                "运行状态",
	"Threshold1":             "阈值一",
	"Threshold2":             "阈值二",
	"Threshold3":             "阈值三",
	"GiveMaterialTime":       "进料时间",
	"GiveMaterialInterval":   "进料间隔",
	"Value1":                 "#1膨胀阀",
	"CleanDregTime":          "清渣时间",
	"Temp1":                  "#1温度",
	"CleanDregInterval":      "清渣间隔",
	"Status2":                "#2状态",
	"CleanDregCycle":         "清渣周期",
	"Value2":                 "#2膨胀阀",
	"FireCorrect":            "火力校正",
	"Temp2":                  "#2温度",
	"FanSpeed":               "鼓风风速",
	"OutputStatus":           "输出状态",
	"Status1":                "#1状态",
}

const (
	NoticeByTeam = 1
	NoticeByUser = 2
	NoticeAllUser= 3
)

var LandManagerActionFormat = []string{
	"",
	"整地",
	"播种",
	"施肥",
	"滴水",
	"灌溉",
	"打药",
	"打顶",
	"脱叶",
	"采收",
}



var WeatherType = map[string]string{
    "晴":"晴",
    "少云":"多云",
    "晴间多云":"多云",
    "多云":"多云",
    "阴":"阴天",
    "有风":"风",
    "平静":"风",
    "微风":"风",
    "和风":"风",
    "清风":"风",
    "强风/劲风":"风",
    "疾风":"风",
    "大风":"风",
    "烈风":"风",
    "风暴":"风",
    "狂爆风":"风",
    "飓风":"风",
    "热带风暴":"风",
    "霾":"雾霾",
    "中度霾":"雾霾",
    "重度霾":"雾霾",
    "严重霾":"雾霾",
    "阵雨":"阵雨",
    "雷阵雨":"阵雨",
    "雷阵雨并伴有冰雹":"阵雨",
    "小雨":"中雨",
    "中雨":"中雨",
    "大雨":"中雨",
    "暴雨":"暴雨",
    "大暴雨":"暴雨",
    "特大暴雨":"暴雨",
    "强阵雨":"暴雨",
    "强雷阵雨":"暴雨",
    "极端降雨":"暴雨",
    "毛毛雨/细雨":"中雨",
    "雨":"中雨",
    "小雨-中雨":"中雨",
    "中雨-大雨":"中雨",
    "大雨-暴雨":"中雨",
    "暴雨-大暴雨":"暴雨",
    "大暴雨-特大暴雨":"暴雨",
    "雨雪天气":"雨夹雪",
    "雨夹雪":"雨夹雪",
    "阵雨夹雪":"雨夹雪",
    "冻雨":"雨夹雪",
    "雪":"中雪",
    "阵雪":"中雪",
    "小雪":"中雪",
    "中雪":"中雪",
    "大雪":"中雪",
    "暴雪":"暴雪",
    "小雪-中雪":"暴雪",
    "中雪-大雪":"暴雪",
    "大雪-暴雪":"暴雪",
    "浮尘":"浮尘",
    "扬沙":"扬沙",
    "沙尘暴":"沙尘暴",
    "强沙尘暴":"沙尘暴",
    "龙卷风":"沙尘暴",
    "雾":"雾",
    "浓雾":"雾",
    "强浓雾":"雾",
    "轻雾":"雾",
    "大雾":"雾",
    "特强浓雾":"雾",
    "热":"热",
    "冷":"冷",
    "未知":"阴天",
}

var WeatherIconMap=map[string]string{
    "晴":"tianqi-1.png",
    "多云":"tianqi-2.png",
    "阴天":"tianqi-3.png",
    "风":"tianqi-4.png",
    "雾霾":"tianqi-5.png",
    "阵雨":"tianqi-6.png",
    "中雨":"tianqi-7.png",
    "暴雨":"tianqi-8.png",
    "雨夹雪":"tianqi-9.png",
    "中雪":"tianqi-10.png",
    "暴雪":"tianqi-11.png",
    "浮尘":"tianqi-12.png",
    "扬沙":"tianqi-13.png",
    "沙尘暴":"tianqi-14.png",
    "雾":"tianqi-15.png",
    "热":"tianqi-16.png",
    "冷":"tianqi-17.png",
}
