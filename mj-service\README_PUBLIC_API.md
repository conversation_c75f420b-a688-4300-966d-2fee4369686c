# 公开竞价项目接口说明

## 接口概述

新增了一个无需登录的公开竞价项目列表接口，该接口不返回价格信息，适用于展示竞价项目的基本信息。

## 接口详情

### 获取公开竞价项目列表

**接口地址**: `POST /auction/public/projects`

**接口说明**: 获取竞价项目列表，无需登录，不包含价格信息

**请求参数**:
```json
{
  "page": 1,
  "pageSize": 12,
  "title": "",
  "owner": "",
  "status": 0
}
```

**参数说明**:
- `page`: 页码，默认1
- `pageSize`: 每页条数，默认12
- `title`: 项目标题（可选，模糊搜索）
- `owner`: 货权人（可选，模糊搜索）
- `status`: 项目状态（可选，0=即将开始，1=竞价中，2=已结束，3=已终止）

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "title": "优质棉花竞价",
        "productCategory": "棉花",
        "quantity": 100.5,
        "unit": "吨",
        "startTime": "2025-01-15 09:00:00",
        "endTime": "2025-01-15 17:00:00",
        "owner": "某某贸易公司",
        "warehouseAddress": "新疆乌鲁木齐仓库",
        "status": 1,
        "bidCount": 15,
        "viewCount": 128,
        "lastBidTime": "2025-01-15 14:30:00",
        "remainingTime": 3600,
        "description": "运费自理，提货时间3天内"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 12
  },
  "msg": "操作成功"
}
```

**响应字段说明**:
- `id`: 项目ID
- `title`: 项目标题
- `productCategory`: 商品分类名称
- `quantity`: 商品数量
- `unit`: 计量单位
- `startTime`: 竞价开始时间
- `endTime`: 竞价结束时间
- `owner`: 货权人
- `warehouseAddress`: 仓库地址
- `status`: 项目状态（0=即将开始，1=竞价中，2=已结束，3=已终止）
- `bidCount`: 出价次数
- `viewCount`: 浏览次数
- `lastBidTime`: 最后出价时间
- `remainingTime`: 剩余时间（秒）
- `description`: 项目描述（费用说明）

**注意事项**:
1. 该接口不返回价格相关信息（起拍价、当前价、最小加价幅度等）
2. 无需登录即可访问
3. 不包含用户权限相关信息（canBid字段）
4. 适用于公开展示竞价项目基本信息

## 与会员接口的区别

| 字段 | 公开接口 | 会员接口 |
|------|----------|----------|
| 起拍价 | ❌ 不返回 | ✅ 返回 |
| 当前价 | ❌ 不返回 | ✅ 返回 |
| 最小加价幅度 | ❌ 不返回 | ✅ 返回 |
| 是否可出价 | ❌ 不返回 | ✅ 返回 |
| 权限控制 | ❌ 无权限控制 | ✅ 只显示有权限的项目 |
| 登录要求 | ❌ 无需登录 | ✅ 需要登录 |

## 前端使用示例

```javascript
// 调用公开接口
import { getPublicAuctionList } from '@/api/auction'

async function fetchPublicAuctions() {
  try {
    const response = await getPublicAuctionList({
      page: 1,
      pageSize: 12,
      status: 1 // 只获取竞价中的项目
    })
    
    if (response.data.code === 200) {
      const projects = response.data.data.list
      console.log('公开项目列表:', projects)
    }
  } catch (error) {
    console.error('获取失败:', error)
  }
}
```

## 使用场景

1. **首页展示**: 在网站首页展示热门竞价项目，吸引用户注册
2. **项目预览**: 让未注册用户了解平台的竞价项目类型和规模
3. **SEO优化**: 公开内容有利于搜索引擎收录
4. **营销推广**: 可以在外部渠道分享项目信息，无需登录即可查看

## 安全考虑

1. 不暴露敏感的价格信息
2. 不暴露用户权限信息
3. 保持与会员接口相同的数据验证和过滤逻辑
4. 支持相同的分页和搜索功能
