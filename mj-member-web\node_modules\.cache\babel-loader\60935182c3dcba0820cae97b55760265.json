{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\src\\store\\modules\\auth.js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\store\\modules\\auth.js", "mtime": 1757558735860}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["login", "register", "logout", "getUserInfo", "getToken", "setToken", "removeToken", "setUserInfo", "getUser<PERSON><PERSON><PERSON>", "removeUserInfo", "state", "token", "userInfo", "isLoggedIn", "mutations", "SET_TOKEN", "SET_USER_INFO", "CLEAR_AUTH", "actions", "commit", "loginForm", "response", "data", "code", "member", "success", "message", "msg", "error", "console", "registerForm", "log", "getters", "namespaced"], "sources": ["E:/pywsp/auction-sys/mj-member-web/src/store/modules/auth.js"], "sourcesContent": ["import { login, register, logout, getUserInfo } from '@/api/auth'\nimport { getToken, setToken, removeToken ,setUserInfo,getUserCookie,removeUserInfo} from '@/utils/auth'\n\nconst state = {\n  token: getToken(),\n  userInfo: getUserCookie(), \n  isLoggedIn: !!getToken()\n}\n\nconst mutations = {\n  SET_TOKEN(state, token) {\n    state.token = token\n    state.isLoggedIn = !!token\n  },\n  SET_USER_INFO(state, userInfo) {\n    state.userInfo = userInfo\n  },\n  CLEAR_AUTH(state) {\n    state.token = null\n    state.userInfo = null\n    state.isLoggedIn = false\n  }\n}\n\nconst actions = {\n  // 用户登录\n  async login({ commit }, loginForm) {\n    try {\n      const response = await login(loginForm)\n      if (response.data.code === 200) {\n        const { token, member } = response.data.data\n        commit('SET_TOKEN', token)\n        commit('SET_USER_INFO', member)\n        setToken(token) // 保存token到cookie\n        setUserInfo(member) // 保存用户信息到cookie\n        return { success: true, data: response.data.data }\n      } else {\n        return { success: false, message: response.data.msg }\n      }\n    } catch (error) {\n      console.error('登录失败:', error)\n      return { success: false, message: '登录失败，请稍后重试' }\n    }\n  },\n\n  // 用户注册\n  async register({ commit }, registerForm) {\n    try {\n      const response = await register(registerForm)\n      if (response.data.code === 200) {\n        return { success: true, message: '注册成功，请等待审核' }\n      } else {\n        return { success: false, message: response.data.msg }\n      }\n    } catch (error) {\n      console.error('注册失败:', error)\n      return { success: false, message: '注册失败，请稍后重试' }\n    }\n  },\n\n  // 获取用户信息\n  async getUserInfo({ commit, state }) {\n    try {\n      if (!state.token) return { success: false }\n      \n      const response = await getUserInfo()\n      if (response.data.code === 200) {\n        setUserInfo(response.data.data)\n        commit('SET_USER_INFO', response.data.data)\n        return { success: true, data: response.data.data }\n      } else {\n        return { success: false, message: response.data.msg }\n      }\n    } catch (error) {\n      console.error('获取用户信息失败:', error)\n      return { success: false, message: '获取用户信息失败' }\n    }\n  },\n\n  // 用户登出\n  async logout({ commit }) {\n    try {\n      commit('CLEAR_AUTH')\n      removeToken()\n      removeUserInfo()\n    } catch (error) {\n      console.log(error)\n    } finally {\n\n    }\n  }\n}\n\nconst getters = {\n  isLoggedIn: state => state.isLoggedIn,\n  userInfo: state => state.userInfo,\n  token: state => state.token\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,YAAY;AACjE,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAACC,aAAa,EAACC,cAAc,QAAO,cAAc;AAEvG,MAAMC,KAAK,GAAG;EACZC,KAAK,EAAEP,QAAQ,CAAC,CAAC;EACjBQ,QAAQ,EAAEJ,aAAa,CAAC,CAAC;EACzBK,UAAU,EAAE,CAAC,CAACT,QAAQ,CAAC;AACzB,CAAC;AAED,MAAMU,SAAS,GAAG;EAChBC,SAASA,CAACL,KAAK,EAAEC,KAAK,EAAE;IACtBD,KAAK,CAACC,KAAK,GAAGA,KAAK;IACnBD,KAAK,CAACG,UAAU,GAAG,CAAC,CAACF,KAAK;EAC5B,CAAC;EACDK,aAAaA,CAACN,KAAK,EAAEE,QAAQ,EAAE;IAC7BF,KAAK,CAACE,QAAQ,GAAGA,QAAQ;EAC3B,CAAC;EACDK,UAAUA,CAACP,KAAK,EAAE;IAChBA,KAAK,CAACC,KAAK,GAAG,IAAI;IAClBD,KAAK,CAACE,QAAQ,GAAG,IAAI;IACrBF,KAAK,CAACG,UAAU,GAAG,KAAK;EAC1B;AACF,CAAC;AAED,MAAMK,OAAO,GAAG;EACd;EACA,MAAMlB,KAAKA,CAAC;IAAEmB;EAAO,CAAC,EAAEC,SAAS,EAAE;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,KAAK,CAACoB,SAAS,CAAC;MACvC,IAAIC,QAAQ,CAACC,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;QAC9B,MAAM;UAAEZ,KAAK;UAAEa;QAAO,CAAC,GAAGH,QAAQ,CAACC,IAAI,CAACA,IAAI;QAC5CH,MAAM,CAAC,WAAW,EAAER,KAAK,CAAC;QAC1BQ,MAAM,CAAC,eAAe,EAAEK,MAAM,CAAC;QAC/BnB,QAAQ,CAACM,KAAK,CAAC,EAAC;QAChBJ,WAAW,CAACiB,MAAM,CAAC,EAAC;QACpB,OAAO;UAAEC,OAAO,EAAE,IAAI;UAAEH,IAAI,EAAED,QAAQ,CAACC,IAAI,CAACA;QAAK,CAAC;MACpD,CAAC,MAAM;QACL,OAAO;UAAEG,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAEL,QAAQ,CAACC,IAAI,CAACK;QAAI,CAAC;MACvD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAa,CAAC;IAClD;EACF,CAAC;EAED;EACA,MAAMzB,QAAQA,CAAC;IAAEkB;EAAO,CAAC,EAAEW,YAAY,EAAE;IACvC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMpB,QAAQ,CAAC6B,YAAY,CAAC;MAC7C,IAAIT,QAAQ,CAACC,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;QAC9B,OAAO;UAAEE,OAAO,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAa,CAAC;MACjD,CAAC,MAAM;QACL,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAEL,QAAQ,CAACC,IAAI,CAACK;QAAI,CAAC;MACvD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAa,CAAC;IAClD;EACF,CAAC;EAED;EACA,MAAMvB,WAAWA,CAAC;IAAEgB,MAAM;IAAET;EAAM,CAAC,EAAE;IACnC,IAAI;MACF,IAAI,CAACA,KAAK,CAACC,KAAK,EAAE,OAAO;QAAEc,OAAO,EAAE;MAAM,CAAC;MAE3C,MAAMJ,QAAQ,GAAG,MAAMlB,WAAW,CAAC,CAAC;MACpC,IAAIkB,QAAQ,CAACC,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;QAC9BhB,WAAW,CAACc,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC;QAC/BH,MAAM,CAAC,eAAe,EAAEE,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC;QAC3C,OAAO;UAAEG,OAAO,EAAE,IAAI;UAAEH,IAAI,EAAED,QAAQ,CAACC,IAAI,CAACA;QAAK,CAAC;MACpD,CAAC,MAAM;QACL,OAAO;UAAEG,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAEL,QAAQ,CAACC,IAAI,CAACK;QAAI,CAAC;MACvD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAW,CAAC;IAChD;EACF,CAAC;EAED;EACA,MAAMxB,MAAMA,CAAC;IAAEiB;EAAO,CAAC,EAAE;IACvB,IAAI;MACFA,MAAM,CAAC,YAAY,CAAC;MACpBb,WAAW,CAAC,CAAC;MACbG,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACE,GAAG,CAACH,KAAK,CAAC;IACpB,CAAC,SAAS,CAEV;EACF;AACF,CAAC;AAED,MAAMI,OAAO,GAAG;EACdnB,UAAU,EAAEH,KAAK,IAAIA,KAAK,CAACG,UAAU;EACrCD,QAAQ,EAAEF,KAAK,IAAIA,KAAK,CAACE,QAAQ;EACjCD,KAAK,EAAED,KAAK,IAAIA,KAAK,CAACC;AACxB,CAAC;AAED,eAAe;EACbsB,UAAU,EAAE,IAAI;EAChBvB,KAAK;EACLI,SAAS;EACTI,OAAO;EACPc;AACF,CAAC", "ignoreList": []}]}