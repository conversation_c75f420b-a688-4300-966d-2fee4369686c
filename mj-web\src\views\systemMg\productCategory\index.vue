<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="searchForm" class="demo-form-inline">
      <el-form-item label="类别名称">
        <el-input v-model="searchForm.name" placeholder="请输入类别名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <el-button type="danger" icon="el-icon-delete" :disabled="selectedRows.length === 0" @click="handleBatchDelete">批量删除</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="类别名称" min-width="120" />
      <el-table-column prop="description" label="类别描述" min-width="200" />
      <el-table-column prop="unit" label="计量单位" width="100" align="center" />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="small" @click="handleStatusChange(scope.row)">
            {{ scope.row.status === 1 ? '禁用' : '启用' }}
          </el-button>
          <el-button type="text" size="small" style="color: #f56c6c" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      :current-page="pagination.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form ref="categoryForm" :model="categoryForm" :rules="rules" label-width="80px">
        <el-form-item label="类别名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入类别名称" />
        </el-form-item>
        <el-form-item label="类别描述" prop="description">
          <el-input v-model="categoryForm.description" type="textarea" :rows="3" placeholder="请输入类别描述" />
        </el-form-item>
        <el-form-item label="计量单位" prop="unit">
          <el-input v-model="categoryForm.unit" placeholder="请输入计量单位" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="categoryForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProductCategoryList,
  addProductCategory,
  updateProductCategory,
  deleteProductCategory,
  updateProductCategoryStatus
} from '@/api/productCategory'

export default {
  name: 'ProductCategory',
  data() {
    return {
      loading: false,
      searchForm: {
        name: ''
      },
      tableData: [],
      selectedRows: [],
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: '',
      categoryForm: {
        id: null,
        name: '',
        description: '',
        unit: '',
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入类别名称', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }
        const { data } = await getProductCategoryList(params)
        console.log(data)
        this.tableData = data.data.list || []
        this.pagination.total = data.data.total || 0
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        name: ''
      }
      this.handleSearch()
    },

    // 分页相关
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getList()
    },

    // 选择行
    handleSelectionChange(val) {
      this.selectedRows = val
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增商品类别'
      this.categoryForm = {
        id: null,
        name: '',
        description: '',
        unit: '',
        status: 1
      }
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑商品类别'
      this.categoryForm = {
        id: row.id,
        name: row.name,
        description: row.description,
        unit: row.unit,
        status: row.status
      }
      this.dialogVisible = true
    },

    // 状态切换
    async handleStatusChange(row) {
      const newStatus = row.status === 1 ? 2 : 1
      const action = newStatus === 1 ? '启用' : '禁用'
      
      try {
        await this.$confirm(`确定要${action}该商品类别吗？`, '提示', {
          type: 'warning'
        })
        
        await updateProductCategoryStatus([{ id: row.id, status: newStatus }])
        this.$message.success(`${action}成功`)
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`${action}失败`)
        }
      }
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除该商品类别吗？', '提示', {
          type: 'warning'
        })
        
        await deleteProductCategory([{ id: row.id }])
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm('确定要删除选中的商品类别吗？', '提示', {
          type: 'warning'
        })
        
        const ids = this.selectedRows.map(item => ({ id: item.id }))
        await deleteProductCategory(ids)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.categoryForm.validate(async (valid) => {
        if (!valid) return
        
        try {
          if (this.categoryForm.id) {
            await updateProductCategory(this.categoryForm)
            this.$message.success('更新成功')
          } else {
            await addProductCategory(this.categoryForm)
            this.$message.success('新增成功')
          }
          
          this.dialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.categoryForm && this.$refs.categoryForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.demo-form-inline {
  margin-bottom: 20px;
}

.operation-bar {
  margin-bottom: 20px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>