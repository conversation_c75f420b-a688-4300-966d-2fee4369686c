// @Title 微信相关
// @Description 请填写文件描述
// <AUTHOR> 2020/11/27 17:20

package wechat

import (
	"auction-sys/global"
	"auction-sys/model/req"
	"auction-sys/utils"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

var (
	HOST_OPENID_NAME       = "https://api.weixin.qq.com/sns/jscode2session?"
	HOST_ACCESS_TOKEN_NAME = "https://api.weixin.qq.com/cgi-bin/token?"
	HOST_UNLIMITED_NAME    = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?"
	SEND_TEMPLATE_MSG      = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token="
	//JS2SESSION = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code"
	JS2SESSION = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code"
	USERINFO   = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN"
)

var (
	ErrAppIDNotMatch       = errors.New("app id not match")
	ErrInvalidBlockSize    = errors.New("invalid block size")
	ErrInvalidPKCS7Data    = errors.New("invalid PKCS7 data")
	ErrInvalidPKCS7Padding = errors.New("invalid padding on input")
)

// 线条颜色
type LineColor struct {
	R int
	G int
	B int
}

// 获取微信个人信息
type WxUserInfo struct {
	OpenID    string `json:"openId"`
	UnionID   string `json:"unionId"`
	NickName  string `json:"nickName"`
	Gender    int    `json:"gender"`
	City      string `json:"city"`
	Province  string `json:"province"`
	Country   string `json:"country"`
	AvatarURL string `json:"avatarUrl"`
	Language  string `json:"language"`
	Watermark struct {
		Timestamp int64  `json:"timestamp"`
		AppID     string `json:"appid"`
	} `json:"watermark"`
}

// 获取手机号
type WxUserPhone struct {
	PhoneNumber     string `json:"phoneNumber"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"`
	Watermark       struct {
		Timestamp int64  `json:"timestamp"`
		AppID     string `json:"appid"`
	} `json:"watermark"`
}

type WXUserDataCrypt struct {
	appID, sessionKey string
}

// 初始化请求信息
func NewWXUserDataCrypt(appID, sessionKey string) *WXUserDataCrypt {
	return &WXUserDataCrypt{
		appID:      appID,
		sessionKey: sessionKey,
	}
}

// pkcs7Unpad返回不带填充的原始数据片段
func pkcs7Unpad(data []byte, blockSize int) ([]byte, error) {
	if blockSize <= 0 {
		return nil, ErrInvalidBlockSize
	}
	if len(data)%blockSize != 0 || len(data) == 0 {
		return nil, ErrInvalidPKCS7Data
	}
	c := data[len(data)-1]
	n := int(c)
	if n == 0 || n > len(data) {
		return nil, ErrInvalidPKCS7Padding
	}
	for i := 0; i < n; i++ {
		if data[len(data)-n+i] != c {
			return nil, ErrInvalidPKCS7Padding
		}
	}
	return data[:len(data)-n], nil
}

// @Title 解密获取用户信息
// @Description 函数的详细描述
// <AUTHOR> 2020/11/27 18:10
// @Param
// @Return
func (w *WXUserDataCrypt) Decrypt(encryptedData, iv string) (*WxUserInfo, error) {
	aesKey, err := base64.StdEncoding.DecodeString(w.sessionKey)
	if err != nil {
		return nil, err
	}
	cipherText, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}
	ivBytes, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return nil, err
	}
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}
	mode := cipher.NewCBCDecrypter(block, ivBytes)
	mode.CryptBlocks(cipherText, cipherText)
	cipherText, err = pkcs7Unpad(cipherText, block.BlockSize())
	if err != nil {
		return nil, err
	}
	var userInfo WxUserInfo
	err = json.Unmarshal(cipherText, &userInfo)
	if err != nil {
		return nil, err
	}
	if userInfo.Watermark.AppID != w.appID {
		return nil, ErrAppIDNotMatch
	}
	return &userInfo, nil
}

// @Title 解密获取用户手机号
// @Description 函数的详细描述
// <AUTHOR> 2020/11/27 18:10
// @Param
// @Return
func (w *WXUserDataCrypt) DecryptPhone(encryptedData, iv string) (*WxUserPhone, error) {
	aesKey, err := base64.StdEncoding.DecodeString(w.sessionKey)
	if err != nil {
		return nil, err
	}
	cipherText, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}
	ivBytes, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return nil, err
	}
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}
	mode := cipher.NewCBCDecrypter(block, ivBytes)
	mode.CryptBlocks(cipherText, cipherText)
	cipherText, err = pkcs7Unpad(cipherText, block.BlockSize())
	if err != nil {
		return nil, err
	}
	var userPhone WxUserPhone
	err = json.Unmarshal(cipherText, &userPhone)
	if err != nil {
		return nil, err
	}
	if userPhone.Watermark.AppID != w.appID {
		return nil, ErrAppIDNotMatch
	}
	return &userPhone, nil
}

// @Title 获取用户openid
// @Description 函数的详细描述
// <AUTHOR> 2020/11/26 14:37
// @Param
// @Return
func GetUserOpenID(code string) (err error, param map[string]interface{}) {
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(HOST_OPENID_NAME + "appid=" + global.GVA_CONFIG.Wechat.Appid + "&secret=" + global.GVA_CONFIG.Wechat.AppSecret + "&js_code=" + code + "&grant_type=authorization_code")
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return errors.New("无法访问外部服务器"), param
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, param
	}
	param = make(map[string]interface{})
	_ = json.Unmarshal(body, &param)
	return err, param
}

// @Title 设置微信接口调用凭据
// @Description 函数的详细描述
// <AUTHOR> 2020/11/26 14:10
// @Param
// @Return
func SetWXAccessToken() {
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(HOST_ACCESS_TOKEN_NAME + "grant_type=client_credential&appid=" + global.GVA_CONFIG.Wechat.Appid + "&secret=" + global.GVA_CONFIG.Wechat.AppSecret)
	if err != nil {
		global.GVA_LOG.Error("无法访问外部服务器：", err)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}
	fmt.Println(string(body))
	param := make(map[string]interface{})
	_ = json.Unmarshal(body, &param)
	_, _ = global.GVA_REDIS.Set("access_token", param["access_token"], time.Duration(param["expires_in"].(float64))*time.Second).Result()
}

// 获取微信接口调用凭证
func GetWXAccessToken() string {
	for {
		val, err := global.GVA_REDIS.Get("access_token").Result()
		if err != nil {
			SetWXAccessToken()
		} else {
			return val
		}
	}
}

// @Title 生成小程序二维码
// @Description 函数的详细描述
// <AUTHOR> 2020/11/27 11:11
// @Param	secene		string		参数
// @Param	page		string		必须是已经发布的小程序存在的页面
// @Param	width		string		二维码的宽度，单位 px
// @Param	autoColor	string		自动配置线条颜色
// @Param	lineColor	string		auto_color 为 false 时生效，使用 rgb 设置颜色
// @Param	isHyaline	string		是否需要透明底色
// @Return	imgIO		[]byte		图片流
// @Return	errmsg		string		错误信息
// @Return	err			error		错误
func GetUnlimited(scene, page string, width int, autoColor bool, lineColor LineColor, isHyaline bool) (imgIO []byte, errmsg string, err error) {
	if scene == "" {
		return imgIO, errmsg, errors.New("scene不能为空")
	}
	// 获取access_token
	accessToken := GetWXAccessToken()
	url := HOST_UNLIMITED_NAME + "access_token=" + accessToken
	data := make(map[string]interface{})
	data["scene"] = scene
	if page != "" {
		data["page"] = page
	}
	if width != 0 {
		data["width"] = width
	}
	data["is_hyaline"] = isHyaline
	// 发送post请求
	res, err := utils.Post(url, data, nil, nil)
	if err != nil {
		global.GVA_LOG.Error("无法访问外部服务器：", err)
		return imgIO, errmsg, errors.New("二维码生成失败")
	}
	// byte转string
	jsonString := string(res[:])
	// 异常返回：返回errmsg,errcode形式
	if strings.Contains(jsonString, "errmsg") {
		result := make(map[string]interface{})
		// byte 转 map
		err = json.Unmarshal(res, &result)
		if err != nil {
			global.GVA_LOG.Error("格式转换失败：", err)
			return imgIO, errmsg, errors.New("二维码生成失败")
		}
		errmsg = result["errmsg"].(string)
	} else { // 正常返回，返回图片buffer
		return res, errmsg, err
	}
	return res, errmsg, nil
}

type SessionInfo struct {
	OpenId     string `json:"openid"`
	SessionKey string `json:"session_key"`
	Unionid    string `json:"unionid"`
	Errcode    int    `json:"errcode"`
	Errmsg     string `json:"errmsg"`

	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
}

func JsCode2Session(code string) (openId string) {
	var info SessionInfo
	uri := fmt.Sprintf(JS2SESSION, global.GVA_CONFIG.Wechat.Appid, global.GVA_CONFIG.Wechat.AppSecret, code)

	rs, err := utils.Get(uri, nil, nil)
	if err != nil {
		global.GVA_LOG.Error(err)
		return ""
	}
	fmt.Println(string(rs))
	if err = json.Unmarshal(rs, &info); err != nil {
		global.GVA_LOG.Error(err)
		return ""
	}
	accessToken := GetWXAccessToken()
	//检查是否关注了公众号
	uri1 := fmt.Sprintf(USERINFO, accessToken, info.OpenId)
	rs, err = utils.Get(uri1, nil, nil)
	if err != nil {
		global.GVA_LOG.Error(err)
		return ""
	}
	param := make(map[string]interface{})
	_ = json.Unmarshal(rs, &param)
	subscribe := param["subscribe"]
	errorCode, ok := param["errorcode"]
	if ok && errorCode != 0 {
		return ""
	}
	fmt.Println(string(rs), subscribe)

	if subscribe == 0 {
		return ""
	}

	return info.OpenId
}

// @Title       发送信息
// @Description
// <AUTHOR>
// @Param
// @Return
func SendPostMsgMore(openId []string, templateId string, params map[string]req.ItemValue) (err error) {

	for _, v := range openId {
		if err = SendPostMsg(v, templateId, params); err != nil {
			global.GVA_LOG.Error(err.Error())
		}
	}
	return nil
}

func SendPostMsg(openId string, templateId string, params map[string]req.ItemValue) (err error) {
	token := GetWXAccessToken()
	uri := SEND_TEMPLATE_MSG + token

	body := make(map[string]interface{})

	body["touser"] = openId
	body["template_id"] = templateId
	body["miniprogram"] = map[string]string{
		"appid": global.GVA_CONFIG.Wechat.AppidMini,
	}
	body["data"] = params

	rs, err := utils.Post(uri, body, nil, nil)
	if err != nil {
		return nil
	}
	global.GVA_LOG.Debug(string(rs))
	return nil
}
