package router

import (
	v1 "auction-sys/api/v1"
	"auction-sys/middleware"

	"github.com/gin-gonic/gin"
)

func InitMenuRouter(Router *gin.RouterGroup) (R gin.IRoutes) {
	MenuRouter := Router.Group("menu").Use(middleware.JWTAuth())
	{
		MenuRouter.POST("getMenu", v1.GetMenu)                           // 获取菜单树
		MenuRouter.POST("getMenuList", v1.GetMenuList)                   // 分页获取基础menu列表
		MenuRouter.POST("addBaseMenu", v1.AddBaseMenu)                   // 新增菜单
		MenuRouter.POST("deleteBaseMenu", v1.DeleteBaseMenu)             // 删除菜单
		MenuRouter.POST("updateBaseMenu", v1.UpdateBaseMenu)             // 更新菜单
		MenuRouter.POST("getBaseMenuById", v1.GetBaseMenuById)           // 根据id获取菜单
		MenuRouter.POST("getMenuAuthorityInit", v1.GetMenuAuthorityInit) //获取角色新增时初始化
		MenuRouter.POST("getAuthMenus", v1.GetAuthMenus)                 // 根据角色id，查询可用菜单
	}
	return MenuRouter
}
