{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\components\\AppHeader.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\components\\AppHeader.vue", "mtime": 1757555981677}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdBcHBIZWFkZXInLAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKCdhdXRoJywgWydpc0xvZ2dlZEluJywgJ3VzZXJJbmZvJ10pCiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQpIHsKICAgICAgc3dpdGNoIChjb21tYW5kKSB7CiAgICAgICAgY2FzZSAncHJvZmlsZSc6CiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3Byb2ZpbGUnKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ2JpZHMnOgogICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9wcm9maWxlL2JpZHMnKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ3Byb2plY3RzJzoKICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvcHJvZmlsZS9wcm9qZWN0cycpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAnbG9nb3V0JzoKICAgICAgICAgIHRoaXMuaGFuZGxlTG9nb3V0KCk7CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGhhbmRsZUxvZ291dCgpIHsKICAgICAgdHJ5IHsKICAgICAgICBhd2FpdCB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXV0aC9sb2dvdXQnKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mAgOWHuueZu+W9leaIkOWKnycpOwogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvaG9tZScpOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mAgOWHuueZu+W9leWksei0pTonLCBlcnJvcik7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["mapGetters", "name", "computed", "methods", "handleCommand", "command", "$router", "push", "handleLogout", "$store", "dispatch", "$message", "success", "error", "console"], "sources": ["src/components/AppHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"app-header\" v-if=\"!$route.meta.hideHeader\">\n    <div class=\"container\" style=\"height: 100%;\">\n      <div class=\"header-content\">\n        <!-- Logo -->\n        <div class=\"logo\" @click=\"$router.push('/')\">\n          <h2>新疆生产建设兵团第一师棉麻有限责任公司竞价平台</h2>\n        </div>\n\n        <!-- 用户信息 -->\n        <div class=\"user-info\">\n          <template v-if=\"isLoggedIn\">\n            <el-dropdown @command=\"handleCommand\">\n              <span class=\"user-name\">\n                {{ userInfo?.name || '用户' }}\n                <i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </span>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"profile\">个人中心</el-dropdown-item>\n                <el-dropdown-item command=\"bids\">我的出价</el-dropdown-item>\n                <el-dropdown-item command=\"projects\">我的项目</el-dropdown-item>\n                <el-dropdown-item divided command=\"logout\">退出登录</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </template>\n          <template v-else>\n            <el-button type=\"text\" @click=\"$router.push('/login')\">登录</el-button>\n            <el-button type=\"primary\" size=\"small\" @click=\"$router.push('/register')\">注册</el-button>\n          </template>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'AppHeader',\n  computed: {\n    ...mapGetters('auth', ['isLoggedIn', 'userInfo'])\n  },\n  methods: {\n    handleCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$router.push('/profile')\n          break\n        case 'bids':\n          this.$router.push('/profile/bids')\n          break\n        case 'projects':\n          this.$router.push('/profile/projects')\n          break\n        case 'logout':\n          this.handleLogout()\n          break\n      }\n    },\n    \n    async handleLogout() {\n      try {\n        await this.$store.dispatch('auth/logout')\n        this.$message.success('退出登录成功')\n        this.$router.push('/home')\n      } catch (error) {\n        console.error('退出登录失败:', error)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-header {\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  height: $header-height;\n\n  .header-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: 100%;\n  }\n\n  .logo {\n    cursor: pointer;\n    \n    h2 {\n      color: $primary-color;\n      font-size: 20px;\n      font-weight: 600;\n    }\n  }\n\n  .nav-menu {\n    display: flex;\n    align-items: center;\n    gap: 30px;\n\n    .nav-item {\n      color: $text-regular;\n      text-decoration: none;\n      font-weight: 500;\n      transition: color 0.3s;\n\n      &:hover,\n      &.router-link-active {\n        color: $primary-color;\n      }\n    }\n  }\n\n  .user-info {\n    display: flex;\n    align-items: center;\n    gap: 15px;\n\n    .user-name {\n      color: $text-primary;\n      cursor: pointer;\n      font-weight: 500;\n    }\n  }\n}\n\n@media (max-width: $mobile) {\n  .app-header {\n    .nav-menu {\n      display: none;\n    }\n    \n    .logo h2 {\n      font-size: 16px;\n    }\n  }\n}\n</style>\n"], "mappings": ";AAoCA,SAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,QAAA;IACA,GAAAF,UAAA;EACA;EACAG,OAAA;IACAC,cAAAC,OAAA;MACA,QAAAA,OAAA;QACA;UACA,KAAAC,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAC,YAAA;UACA;MACA;IACA;IAEA,MAAAA,aAAA;MACA;QACA,WAAAC,MAAA,CAAAC,QAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAAN,OAAA,CAAAC,IAAA;MACA,SAAAM,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}