package utils

import (
	"auction-sys/constants"
	"strconv"
	"time"
)

func TimeDiffWithNow(tStr string) time.Duration {

	t, err := time.ParseInLocation(constants.TIME_FORMAT, tStr, time.Local)
	if err != nil {
		return 0
	}
	return time.Now().Sub(t)
}

func FloatToString(f float64, n int) string {
	return strconv.FormatFloat(f, 'f', n, 64)

}
func StringToFloat(s string) float64 {
	f, _ := strconv.ParseFloat(s, 64)
	return f
}
func FormatFloat(f float64, n int) float64 {
	fStr := FloatToString(f, n)
	return StringToFloat(fStr)

}
