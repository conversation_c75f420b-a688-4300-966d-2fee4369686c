import Cookies from 'js-cookie'

const TokenKey = 'member_token'
const UserinfoKey = 'user_info'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: 7 }) // 7天过期
}

export function removeToken() {
  return Cookies.remove(Token<PERSON>ey)
}

export function setUserInfo(userinfo) {
  return Cookies.set(UserinfoKey, JSON.stringify(userinfo), { expires: 7 }) // 7天过期
}

export function getUserCookie() {
  try {
      const tempInfo = Cookies.get(UserinfoKey)
      const userInfo = JSON.parse(tempInfo)
      return userInfo
  } catch (error) {
    
  }

  return null
}

export function removeUserInfo(userinfo) {
  return Cookies.remove(UserinfoKey)
}
