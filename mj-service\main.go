package main

import (
	"auction-sys/core"
	"auction-sys/global"
	"auction-sys/initialize"
	"auction-sys/service"
	//"runtime"
)

const NAME = "go.micro.api.admin"

// @title Swagger Example API
// @version 0.0.1
// @description This is a sample Server pets
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name x-token
// @BasePath /
func main() {
	GinStart()
}

func GinStart() {
	switch global.GVA_CONFIG.System.DbType {

	case "mysql":
		initialize.Mysql()
	default:
		initialize.Mysql()
	}
	initialize.DBTables()

	// 初始化WebSocket Hub
	service.InitWebSocketHub()

	// 程序结束前关闭数据库链接
	defer global.GVA_DB.Close()
	core.RunWindowsServer()
}
