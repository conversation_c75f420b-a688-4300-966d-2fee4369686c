import Vue from 'vue'
import Vuex from 'vuex'
import auth from './modules/auth'
import auction from './modules/auction'
import user from './modules/user'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    auth,
    auction,
    user
  },
  getters: {
    // 全局getters，方便访问
    isLoggedIn: state => state.auth.isLoggedIn,
    userInfo: state => state.auth.userInfo,
    token: state => state.auth.token
  },
  strict: process.env.NODE_ENV !== 'production'
})
