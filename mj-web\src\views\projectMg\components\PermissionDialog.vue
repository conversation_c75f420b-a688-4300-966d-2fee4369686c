<template>
  <el-dialog
    title="权限管理"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    top='50px'
    @close="handleClose"
  >
    <!-- 项目信息 -->
    <el-card class="project-info-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>项目信息</span>
      </div>
      <div class="project-info">
        <span><strong>项目名称：</strong>{{ projectInfo.title }}</span>
        <span><strong>项目ID：</strong>{{ projectInfo.id }}</span>
      </div>
    </el-card>

    <!-- 权限管理区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 已授权会员 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>已授权会员 ({{ authorizedMembers.length }})</span>
            <el-button 
              style="float: right; padding: 3px 0" 
              type="text" 
              @click="batchRevoke"
              :disabled="authorizedSelection.length === 0">
              批量撤销
            </el-button>
          </div>
          
          <!-- 搜索 -->
          <div style="margin-bottom: 15px;">
            <el-input
              v-model="authorizedSearch"
              placeholder="搜索已授权会员"
              prefix-icon="el-icon-search"
              clearable
              @input="filterAuthorizedMembers"
            />
          </div>

          <!-- 已授权会员列表 -->
          <el-table 
            :data="filteredAuthorizedMembers" 
            height="350" 
            @selection-change="handleAuthorizedSelectionChange"
            border>
            <el-table-column type="selection" width="55" />
            <el-table-column label="姓名" prop="name" />
            <el-table-column label="手机号" prop="mobile" />
            <el-table-column label="企业名称" prop="companyName" show-overflow-tooltip />
            <el-table-column label="授权时间" prop="grantTime" width="160" />
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button 
                  type="danger" 
                  size="mini" 
                  @click="revokeSingle(scope.row)">
                  撤销
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 可授权会员 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>可授权会员 ({{ availableMembers.length }})</span>
            <el-button 
              style="float: right; padding: 3px 0" 
              type="text" 
              @click="batchGrant"
              :disabled="availableSelection.length === 0">
              批量授权
            </el-button>
          </div>
          
          <!-- 搜索 -->
          <div style="margin-bottom: 15px;">
            <el-input
              v-model="availableSearch"
              placeholder="搜索可授权会员"
              prefix-icon="el-icon-search"
              clearable
              @input="filterAvailableMembers"
            />
          </div>

          <!-- 可授权会员列表 -->
          <el-table 
            :data="filteredAvailableMembers" 
            height="350" 
            @selection-change="handleAvailableSelectionChange"
            border>
            <el-table-column type="selection" width="55" />
            <el-table-column label="姓名" prop="name" />
            <el-table-column label="手机号" prop="mobile" />
            <el-table-column label="企业名称" prop="companyName" show-overflow-tooltip />
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button 
                  type="primary" 
                  size="mini" 
                  @click="grantSingle(scope.row)">
                  授权
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { manageAuctionPermission,getAuctionProjectPermission, getApprovedMembers } from '@/api/auction'

export default {
  name: 'PermissionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      // 已授权会员
      authorizedMembers: [],
      filteredAuthorizedMembers: [],
      authorizedSelection: [],
      authorizedSearch: '',
      // 可授权会员
      availableMembers: [],
      filteredAvailableMembers: [],
      availableSelection: [],
      availableSearch: ''
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.projectInfo.id) {
        this.loadPermissionData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 加载权限数据
    async loadPermissionData() {
      await Promise.all([
        this.loadAuthorizedMembers(),
        this.loadAvailableMembers()
      ])
    },

    // 加载已授权会员
    async loadAuthorizedMembers() {
      try {
        const response = await getAuctionProjectPermission({
          action: 'list',
          projectId: this.projectInfo.id
        })
        if (response.data.code === 200) {
          this.authorizedMembers = response.data.data || []
          this.filteredAuthorizedMembers = [...this.authorizedMembers]
        }
      } catch (error) {
        console.error('获取已授权会员失败:', error)
      }
    },

    // 加载可授权会员
    async loadAvailableMembers() {
      try {
        const response = await getApprovedMembers({
          projectId: this.projectInfo.id,
          excludeAuthorized: true
        })
        if (response.data.code === 200) {
          this.availableMembers = response.data.data || []
          this.filteredAvailableMembers = [...this.availableMembers]
        }
      } catch (error) {
        console.error('获取可授权会员失败:', error)
      }
    },

    // 单个授权
    async grantSingle(member) {
      try {
        const response = await manageAuctionPermission({
          action: 1,
          projectId: this.projectInfo.id,
          memberIds: [member.id]
        })
        if (response.data.code === 200) {
          this.$message.success('授权成功')
          this.loadPermissionData()
        } else {
          this.$message.error(response.data.msg || '授权失败')
        }
      } catch (error) {
        console.error('授权失败:', error)
        this.$message.error('授权失败')
      }
    },

    // 单个撤销
    async revokeSingle(member) {
      this.$confirm('确定要撤销该会员的竞价权限吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await manageAuctionPermission({
            action: 2,
            projectId: this.projectInfo.id,
            memberIds: [member.memberId]
          })
          if (response.data.code === 200) {
            this.$message.success('撤销成功')
            this.loadPermissionData()
          } else {
            this.$message.error(response.data.msg || '撤销失败')
          }
        } catch (error) {
          console.error('撤销失败:', error)
          this.$message.error('撤销失败')
        }
      })
    },

    // 批量授权
    async batchGrant() {
      if (this.availableSelection.length === 0) {
        this.$message.warning('请选择要授权的会员')
        return
      }

      try {
        const memberIds = this.availableSelection.map(item => item.id)
        const response = await manageAuctionPermission({
          action: 1,
          projectId: this.projectInfo.id,
          memberIds: memberIds
        })
        if (response.data.code === 200) {
          this.$message.success('批量授权成功')
          this.loadPermissionData()
        } else {
          this.$message.error(response.data.msg || '批量授权失败')
        }
      } catch (error) {
        console.error('批量授权失败:', error)
        this.$message.error('批量授权失败')
      }
    },

    // 批量撤销
    async batchRevoke() {
      if (this.authorizedSelection.length === 0) {
        this.$message.warning('请选择要撤销的会员')
        return
      }

      this.$confirm('确定要批量撤销选中会员的竞价权限吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const memberIds = this.authorizedSelection.map(item => item.memberId)
          const response = await manageAuctionPermission({
            action: 2,
            projectId: this.projectInfo.id,
            memberIds: memberIds
          })
          if (response.data.code === 200) {
            this.$message.success('批量撤销成功')
            this.loadPermissionData()
          } else {
            this.$message.error(response.data.msg || '批量撤销失败')
          }
        } catch (error) {
          console.error('批量撤销失败:', error)
          this.$message.error('批量撤销失败')
        }
      })
    },

    // 过滤已授权会员
    filterAuthorizedMembers() {
      if (!this.authorizedSearch) {
        this.filteredAuthorizedMembers = [...this.authorizedMembers]
      } else {
        this.filteredAuthorizedMembers = this.authorizedMembers.filter(member =>
          member.name.includes(this.authorizedSearch) ||
          member.mobile.includes(this.authorizedSearch) ||
          member.companyName.includes(this.authorizedSearch)
        )
      }
    },

    // 过滤可授权会员
    filterAvailableMembers() {
      if (!this.availableSearch) {
        this.filteredAvailableMembers = [...this.availableMembers]
      } else {
        this.filteredAvailableMembers = this.availableMembers.filter(member =>
          member.name.includes(this.availableSearch) ||
          member.mobile.includes(this.availableSearch) ||
          member.companyName.includes(this.availableSearch)
        )
      }
    },

    // 已授权会员选择变化
    handleAuthorizedSelectionChange(val) {
      this.authorizedSelection = val
    },

    // 可授权会员选择变化
    handleAvailableSelectionChange(val) {
      this.availableSelection = val
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
      // 清空搜索和选择
      this.authorizedSearch = ''
      this.availableSearch = ''
      this.authorizedSelection = []
      this.availableSelection = []
    }
  }
}
</script>

<style scoped>
.project-info-card {
  margin-bottom: 20px;
}

.project-info {
  display: flex;
  gap: 30px;
}

.project-info span {
  color: #606266;
}

.dialog-footer {
  text-align: right;
}
</style>
