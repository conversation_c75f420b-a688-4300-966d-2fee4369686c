package req

// 列表页查询条件
type GetAuthorityList struct {
	PageInfo
	Name string `json:"name"`
}

// 绑定用户数列表页查询条件
type GetAuthAndUsers struct {
	PageInfo
	ID       int    `json:"id"`       // 角色id
	Username string `json:"username"` // 用户名
}

// 添加或更新角色
type SaveOrUpdateAuthority struct {
	ID            int    `json:"id"`
	AuthorityName string `json:"authorityName"` // 角色名
	CompanyCode   string `json:"companyCode"`   // 公司标识
	AuthorityType int    `json:"authorityType"` // 角色类别
	Description   string `json:"description"`   // 角色描述
	Status        int8   `json:"status"`        // 角色状态
	BindingUsers  int    `json:"bindingUsers"`  // 绑定用户数
	MenuIds       []int  `json:"menuIds"`       // 菜单 id 数组
}

// 更新角色绑定用户
type UpdateAuthBindingUsers struct {
	AuthId  int   `json:"authId"`  // 角色 id
	UserIds []int `json:"userIds"` // 用户 id 数组
}
