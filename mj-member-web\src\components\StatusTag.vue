<template>
  <span class="status-tag" :class="statusClass">
    {{ statusText }}
  </span>
</template>

<script>
export default {
  name: 'StatusTag',
  props: {
    status: {
      type: [Number, String],
      required: true
    },
    type: {
      type: String,
      default: 'auction' // auction, audit
    }
  },
  computed: {
    statusText() {
      if (this.type === 'auction') {
        const statusMap = {
          0: '即将开始',
          1: '竞价中',
          2: '已结束',
          3: '已终止'
        }
        return statusMap[this.status] || '未知状态'
      } else if (this.type === 'audit') {
        const statusMap = {
          0: '待审核',
          1: '审核通过',
          2: '审核拒绝'
        }
        return statusMap[this.status] || '未知状态'
      }
      return this.status
    },
    
    statusClass() {
      if (this.type === 'auction') {
        const classMap = {
          0: 'status-pending',
          1: 'status-active',
          2: 'status-success',
          3: 'status-danger'
        }
        return classMap[this.status] || 'status-default'
      } else if (this.type === 'audit') {
        const classMap = {
          0: 'status-pending',
          1: 'status-success',
          2: 'status-danger'
        }
        return classMap[this.status] || 'status-default'
      }
      return 'status-default'
    }
  }
}
</script>

<style lang="scss" scoped>
.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  
  &.status-pending {
    background: #fdf6ec;
    color: $warning-color;
    border: 1px solid #faecd8;
  }
  
  &.status-active {
    background: #f0f9ff;
    color: $primary-color;
    border: 1px solid #d1ecf1;
  }
  
  &.status-success {
    background: #f0f9f0;
    color: $success-color;
    border: 1px solid #d4edda;
  }
  
  &.status-danger {
    background: #fef0f0;
    color: $danger-color;
    border: 1px solid #fecaca;
  }
  
  &.status-default {
    background: #f5f7fa;
    color: $text-secondary;
    border: 1px solid #e4e7ed;
  }
}
</style>
