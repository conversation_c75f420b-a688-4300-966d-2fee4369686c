import { getDict } from "@/utils/dictionary";
export default {
    data() {
        return {
            page: 1,
            total: 10,
            pageSize: 10,
            tableData: [],
            searchInfo: {}
        }
    },
    methods: {
        filterDict(value, type) {
            const rowLabel = this[type + "Options"] && this[type + "Options"].filter(item => item.value == value)
            return rowLabel && rowLabel[0] && rowLabel[0].label
        },
        async getDict(type) {
            const dicts = await getDict(type)
            this[type + "Options"] = dicts
            return dicts
        },
        handleSizeChange(val) {
            this.pageSize = val
            this.getTableData()
        },
        handleCurrentChange(val) {
            this.page = val
            this.getTableData()
        },
        async getTableData() {
            let page = this.page
            let pageSize = this.pageSize
            const table = await this.listApi({ page, pageSize, ...this.searchInfo })
            if (table.data.code == 200) {
                this.tableData = table.data.data.list
                this.total = table.data.data.total
                this.page = table.data.data.page
                this.pageSize = table.data.data.pageSize
            }
        }
    }
}