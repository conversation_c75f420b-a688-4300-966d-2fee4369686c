package config

type Server struct {
	Mysql   Mysql   `mapstructure:"mysql" json:"mysql" yaml:"mysql"`
	<PERSON><PERSON>   `mapstructure:"qiniu" json:"qiniu" yaml:"qiniu"`
	<PERSON><PERSON><PERSON>  `mapstructure:"casbin" json:"casbin" yaml:"casbin"`
	Redis   Redis   `mapstructure:"redis" json:"redis" yaml:"redis"`
	System  System  `mapstructure:"system" json:"system" yaml:"system"`
	JWT     JWT     `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Captcha Captcha `mapstructure:"captcha" json:"captcha" yaml:"captcha"`
	Log     Log     `mapstructure:"log" json:"log" yaml:"log"`
	Logo    Logo    `mapstructure:"logo" json:"logo" yaml:"logo"`
	Admin   Admin   `mapstructure:"admin" json:"admin" yaml:"admin"`
	Sms     Sms     `mapstructure:"sms" json:"sms" yaml:"sms"`
	Wechat  Wechat  `mapstructure:"wechat" json:"wechat" yaml:"wechat"`
}

type System struct {
	UseMultipoint bool   `mapstructure:"use-multipoint" json:"useMultipoint" yaml:"use-multipoint"`
	Env           string `mapstructure:"env" json:"env" yaml:"env"`
	Addr          int    `mapstructure:"addr" json:"addr" yaml:"addr"`
	DbType        string `mapstructure:"db-type" json:"dbType" yaml:"db-type"`
	RsaLogin      bool   `mapstructure:"rsaLogin" json:"rsaLogin" yaml:"rsaLogin"`
	ParamsUrl     string `mapstructure:"params-url" json:"paramsUrl" yaml:"params-url"`
	LoginUrl      string `mapstructure:"login-url" json:"loginUrl" yaml:"login-url"`
	Username      string `mapstructure:"username" json:"username" yaml:"username"`
	Password      string `mapstructure:"password" json:"password" yaml:"password"`
}

type JWT struct {
	SigningKey string `mapstructure:"signing-key" json:"signingKey" yaml:"signing-key"`
}

type Casbin struct {
	ModelPath string `mapstructure:"model-path" json:"modelPath" yaml:"model-path"`
}

type Mysql struct {
	Username     string `mapstructure:"username" json:"username" yaml:"username"`
	Password     string `mapstructure:"password" json:"password" yaml:"password"`
	Path         string `mapstructure:"path" json:"path" yaml:"path"`
	Dbname       string `mapstructure:"db-name" json:"dbname" yaml:"db-name"`
	Config       string `mapstructure:"config" json:"config" yaml:"config"`
	MaxIdleConns int    `mapstructure:"max-idle-conns" json:"maxIdleConns" yaml:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns" json:"maxOpenConns" yaml:"max-open-conns"`
	LogMode      bool   `mapstructure:"log-mode" json:"logMode" yaml:"log-mode"`
	ParseTime    bool   `mapstructure:"parse-time" json:"parseTime" yaml:"parse-time"`
	TimeZone     string `mapstructure:"time-zone" json:"timeZone" yaml:"time-zone"`
}

type Redis struct {
	Addr     string `mapstructure:"addr" json:"addr" yaml:"addr"`
	Password string `mapstructure:"password" json:"password" yaml:"password"`
	DB       int    `mapstructure:"db" json:"db" yaml:"db"`
}

type Qiniu struct {
	AccessKey string `mapstructure:"access-key" json:"accessKey" yaml:"access-key"`
	SecretKey string `mapstructure:"secret-key" json:"secretKey" yaml:"secret-key"`
	Bucket    string `mapstructure:"bucket" json:"bucket" yaml:"bucket"`
	ImgPath   string `mapstructure:"img-path" json:"imgPath" yaml:"img-path"`
}

type Captcha struct {
	KeyLong   int `mapstructure:"key-long" json:"keyLong" yaml:"key-long"`
	ImgWidth  int `mapstructure:"img-width" json:"imgWidth" yaml:"img-width"`
	ImgHeight int `mapstructure:"img-height" json:"imgHeight" yaml:"img-height"`
}

type Log struct {
	Prefix  string `mapstructure:"prefix" json:"prefix" yaml:"prefix"`
	LogFile bool   `mapstructure:"log-file" json:"logFile" yaml:"log-file"`
	Stdout  string `mapstructure:"stdout" json:"stdout" yaml:"stdout"`
	File    string `mapstructure:"file" json:"file" yaml:"file"`
}

type Logo struct {
	Company string `mapstructure:"company" json:"company" yaml:"company"`
	User    string `mapstructure:"user" json:"user" yaml:"user"`
}

type Admin struct {
	Username string `mapstructure:"username" json:"username" yaml:"username"`
}

type Sms struct {
	CorpId string `mapstructure:"corp-id" json:"corpId" yaml:"corp-id"`
	Pwd    string `mapstructure:"pwd" json:"pwd" yaml:"pwd"`
}
type Wechat struct {
	Appid     string `mapstructure:"appid" json:"appid" yaml:"appid"`
	AppidMini string `mapstructure:"appid-mini" json:"appid" yaml:"appid-mini"`
	AppSecret string `mapstructure:"app-secret" json:"appSecret" yaml:"app-secret"`
}
