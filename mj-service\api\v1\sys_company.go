package v1

import (
	"auction-sys/constants"
	"auction-sys/global/response"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/service"
	"auction-sys/utils"
	"encoding/json"
	"fmt"

	"github.com/gin-gonic/gin"
)

// @Tags company ：企业管理
// @Summary 新增公司
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysCompany true "新增公司"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"创建成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"创建失败"}"
// @Router /company/addCompany [post]
func AddCompany(c *gin.Context) {
	// 获取请求头
	agent := c.<PERSON>eader("User-Agent")

	var company model.SysCompany
	_ = c.ShouldBindJSON(&company)
	if company.ID != 0 {
		CompanyVerify := utils.Rules{
			"Name":         {utils.NotEmpty()},
			"Code":         {utils.NotEmpty()},
			"PersonLiable": {utils.NotEmpty()},
		}
		CompanyVerifyErr := utils.Verify(company, CompanyVerify)
		if CompanyVerifyErr != nil {
			response.FailWithMessage(CompanyVerifyErr.Error(), c)
			return
		}
	} else {
		CompanyVerify := utils.Rules{
			"Name":         {utils.NotEmpty()},
			"Code":         {utils.NotEmpty(), utils.CheckUsci(company.Code)},
			"Telephone":    {utils.NotEmpty(), utils.Mobile(company.Telephone)},
			"PersonLiable": {utils.NotEmpty()},
		}
		CompanyVerifyErr := utils.Verify(company, CompanyVerify)
		if CompanyVerifyErr != nil {
			response.FailWithMessage(CompanyVerifyErr.Error(), c)
			return
		}
	}

	// 邮箱验证
	if company.Email != "" {
		EmailVerifyErr := utils.Verify(company, utils.Rules{"Email": {utils.Email(company.Email)}})
		if EmailVerifyErr != nil {
			response.FailWithMessage(EmailVerifyErr.Error(), c)
			return
		}
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(company)
	if company.ID != 0 {
		err := service.UpdateCompany(company)
		if err != nil {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-编辑企业", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
			response.FailWithMessage(fmt.Sprintf("更新失败:%v", err), c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-编辑企业", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
			response.OkWithMessage("更新成功", c)
		}
	} else {
		err := service.AddCompany(company)
		if err != nil {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-添加企业", c.ClientIP(), string(requestParam), "添加失败", response.ERROR, agent)
			response.FailWithMessage(err.Error(), c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-添加企业", c.ClientIP(), string(requestParam), "添加成功", response.SUCCESS, agent)
			response.OkWithMessage("添加成功", c)
		}
	}
}

// @Tags company ：企业管理
// @Summary 根据id查询公司
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "根据id查询公司"
// @Success 200 {string} string "{"code":200,"data":{SysCompanyResponse},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"查询失败"}"
// @Router /company/getCompany [post]
func GetCompany(c *gin.Context) {
	var idInfo req.GetById
	_ = c.ShouldBindJSON(&idInfo)
	CompanyVerify := utils.Rules{
		"Id": {"notEmpty"},
	}
	CompanyVerifyErr := utils.Verify(idInfo, CompanyVerify)
	if CompanyVerifyErr != nil {
		response.FailWithMessage(CompanyVerifyErr.Error(), c)
		return
	}
	err, company := service.GetCompany(idInfo.Id)
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("查询失败：%v", err), c)
	} else {
		response.OkWithData(resp.SysCompanyResponse{Company: company}, c)
	}
}

// @Tags company ：企业管理
// @Summary 删除公司
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []request.GetById true "删除公司"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"删除成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"删除失败"}"
// @Router /company/deleteCompany [post]
func DeleteCompany(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var reqIds []req.GetById
	_ = c.ShouldBindJSON(&reqIds)
	for _, reqId := range reqIds {
		IdVerifyErr := utils.Verify(reqId, utils.CustomizeMap["IdVerify"])
		if IdVerifyErr != nil {
			response.FailWithMessage(IdVerifyErr.Error(), c)
			return
		}
	}
	err := service.DeleteCompany(reqIds)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(reqIds)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "企业管理-删除企业", c.ClientIP(), string(requestParam), "删除失败", response.ERROR, agent)
		response.FailWithMessage("删除失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "企业管理-删除企业", c.ClientIP(), string(requestParam), "删除成功", response.SUCCESS, agent)
		response.OkWithMessage("删除成功", c)
	}
}

// @Tags company ：企业管理
// @Summary 获取公司
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetCompanyList true "获取公司"
// @Success 200 {string} string "{"code":200,"data":{GetCompanyList},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"后去数据失败"}"
// @Router /company/getCompanyList [post]
func GetCompanyList(c *gin.Context) {
	var company req.GetCompanyList
	_ = c.ShouldBindJSON(&company)
	CompanyVerifyErr := utils.Verify(company, utils.CustomizeMap["CompanyVerify"])
	if CompanyVerifyErr != nil {
		response.FailWithMessage(CompanyVerifyErr.Error(), c)
		return
	}
	claims, _ := c.Get("claims")
	// 这里我们通过断言获取 claims内的所有内容
	waitUse := claims.(*req.CustomClaims)
	uuid := waitUse.UUID
	err, list, total, enableNum, disableNum := service.GetCompanyList(company, uuid.String())
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取数据失败，%v", err), c)
	} else {
		response.OkWithData(resp.GetCompanyList{
			List:       list,
			Total:      total,
			Page:       company.Page,
			PageSize:   company.PageSize,
			Name:       company.Name,
			EnableNum:  enableNum,
			DisableNum: disableNum,
		}, c)
	}
}

// @Tags company ：企业管理
// @Summary 更新公司状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []model.SysCompany true "更新公司状态"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /company/updateCompanyStatus [post]
func UpdateCompanyStatus(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var companys []model.SysCompany
	_ = c.ShouldBindJSON(&companys)
	CompanyVerify := utils.Rules{
		"ID": {utils.NotEmpty()},
	}
	for _, company := range companys {
		CompanyVerifyErr := utils.Verify(company, CompanyVerify)
		if CompanyVerifyErr != nil {
			response.FailWithMessage(CompanyVerifyErr.Error(), c)
			return
		}
	}

	err := service.UpdateCompanyStatus(companys)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(companys)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "企业管理-更新状态", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage("更新失败", c)
	} else {
		if companys[0].Status == constants.STATUS_ENABLED {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-更新状态", c.ClientIP(), string(requestParam), "已启用", response.SUCCESS, agent)
			response.OkWithMessage("已启用", c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-更新状态", c.ClientIP(), string(requestParam), "已禁用", response.SUCCESS, agent)
			response.OkWithMessage("已禁用", c)
		}

	}
}

// @Tags company ：企业管理
// @Summary 上传公司Logo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":200,"data":{},"msg":"上传成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"上传失败"}"
// @Router /company/uploadCompanyLogo [post]
func UploadCompanyLogo(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	_, header, err := c.Request.FormFile("file")
	if err != nil {
		response.FailWithMessage("上传文件失败", c)
	} else {
		// 文件上传后拿到文件路径
		err, filePath, _ := utils.Upload(header)
		cl, _ := c.Get("claims")
		claims, _ := cl.(*req.CustomClaims)
		if err != nil {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-上传logo", c.ClientIP(), "", "上传失败", response.ERROR, agent)
			response.FailWithMessage("上传文件失败", c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "企业管理-上传logo", c.ClientIP(), "", filePath, response.SUCCESS, agent)
			data := make(map[string]string)
			data["imageUrl"] = filePath
			response.OkWithData(data, c)
		}
	}
}

// @Tags company ：企业管理
// @Summary 筛选条件栏企业数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":200,"data":{[]response.CompanyCondition},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"加载失败"}"
// @Router /company/getCompanyCondition [post]
func GetCompanyCondition(c *gin.Context) {
	claims, _ := c.Get("claims")
	// 这里我们通过断言获取 claims内的所有内容
	waitUse := claims.(*req.CustomClaims)
	uuid := waitUse.UUID
	// 获取企业信息
	err, companyList := service.GetCompanyCondition(uuid.String())
	if err != nil {
		response.FailWithMessage("加载失败", c)
	} else {
		response.OkWithData(companyList, c)
	}
}
