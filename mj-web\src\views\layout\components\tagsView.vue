<template>
  <div class="tags-view-container">
    <el-scrollbar
      ref="scrollContainer"
      :vertical="false"
      class="scroll-container"
      @wheel.native.prevent="handleScroll"
    >
      <!-- 此处使用最新滚动事件 wheel -->
      <router-link
        v-for="tag in visitedViews"
        ref="tag"
        :class="isActive(tag)?'active':''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        :key="tag.path"
        tag="span"
        class="tags-view-item"
      >
        {{ tag.title }}
        <span v-if="!isDefaultShowTags(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
      </router-link>
    </el-scrollbar>
  </div>
</template>

<script>
import path from "path";

export default {
  data() {
    return {
      defaultShowTags: []
    };
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews;
    },
    // 每次登录进来时，拿到最新的 routers（constantRouterMap 与 asyncRouterMap 合并后的最新结果）
    routers() {
      return this.$store.state.permission.routers;
    }
  },
  watch: {
    $route() {
      this.addViewTags();
    }
  },
  mounted() {
    this.initTags(); // 系统退出时会清空路由，每次进入系统时初始化判断要默认显示的路由页面
    this.addViewTags();
  },
  methods: {
    // 鼠标滚动事件
    handleScroll(e) {
      // wheelDelta：获取滚轮滚动方向，向上120，向下-120，但为常量，与滚轮速率无关
      // deltaY：垂直滚动幅度，正值向下滚动,与wheelDelta正好正负相反。电脑鼠标滚轮垂直行数默认值是3
      // wheelDelta只有部分浏览器支持，deltaY几乎所有浏览器都支持
      const eventDelta = e.wheelDelta || -e.deltaY * 40; //在不支持wheelDelta属性的浏览器上使用deltaY属性
      const $scrollWrapper = this.$refs.scrollContainer.$refs.wrap; //源码路径element/packages/scrollbar/src/main.js中对el-scrollbar的实现代码里有个ref="wrap"的div元素
      // 0到scrollLeft为滚动区域隐藏部分,即每次滚动在当前 scrollLeft 值得基础上正负移动 eventDelta / 4 的距离
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4;
    },
    isActive(route) {
      return route.path === this.$route.path;
    },
    // 如果默认显示，则不能删除，隐藏删除图标
    isDefaultShowTags(tag) {
      return tag.meta && tag.meta.defaultShow
    },
    // 鼠标点击关闭事件
    closeSelectedTag(view) {
      this.$store.dispatch("delView", view).then(({ visitedViews }) => {
        //关闭页面之后进一步判断关闭的页面是否是当前激活状态，如果是，则判断是否存在最后一个页面，如果存在，则跳转到最后一个路由打开页面，否则跳转到根路由页面
        if (this.isActive(view)) {
          const latestView = visitedViews.slice(-1)[0];
          if (latestView) {
            this.$router.push(latestView);
          } else {
            this.$router.push("/");
          }
        }
      });
    },
    // 过滤筛选出有默认显示设置的路由页面
    filterDefaultShowTags(routes, basePath = "/") {
      let tags = [];
      routes.forEach(route => {
        if (route.meta && route.meta.defaultShow) {
          const tagPath = path.resolve(basePath, route.path);
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          });
        }
        // 如果存在子节点，递归调用
        if (route.children) {
          const tempTags = this.filterDefaultShowTags(
            route.children,
            route.path
          );
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags];
          }
        }
      });
      return tags;
    },
    // 初始化，将默认显示路由页面添加到 visitedViews 中去 （会往 visitedViews 里面追加新的，visitedViews 中已经存在的，不会重复添加）
    initTags() {
      // 将最新 this.routers 传入 filterDefaultShowTags 函数进行过滤处理， 返回具有默认显示设置的页面路由
      const defaultShowTags = (this.defaultShowTags = this.filterDefaultShowTags(
        this.routers
      ));
      for (const tag of defaultShowTags) {
        // 必须要有name
        if (tag.name) {
          this.$store.dispatch("addVisitedView", tag);
        }
      }
    },
    addViewTags() {
      const { name } = this.$route;
      if (name) {
        this.$store.dispatch("addView", this.$route);
      }
      return false;
    }
  }
};
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 34px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  .scroll-container {
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    width: 100%;
    ::v-deep {
      .el-scrollbar__bar {
        bottom: 0;
      }
      .el-scrollbar__wrap {
        height: 49px;
      }
    }
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      border: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      padding: 0 8px;
      font-size: 12px;
      margin-left: 5px;
      margin-top: 4px;
      &:first-of-type {
        margin-left: 15px;
      }
      &:last-of-type {
        margin-right: 15px;
      }
      &.active {
        background-color: #42b983;
        color: #fff;
        border-color: #42b983;
        &::before {
          content: "";
          background: #fff;
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;
          margin-right: 2px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.scroll-container {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }
      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
