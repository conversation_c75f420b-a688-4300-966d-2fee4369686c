module auction-sys

go 1.14

require (
	github.com/Gre-Z/common v0.0.0-20191024025434-2dbc6bd196f9
	github.com/alecthomas/template v0.0.0-20190718012654-fb15b899a751
	github.com/axgle/mahonia v0.0.0-20180208002826-3358181d7394
	github.com/bluesky335/IDCheck v0.0.0-20200319021444-08ee85bfcb7b
	github.com/dchest/captcha v0.0.0-20170622155422-6a29415a8364
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/fastly/go-utils v0.0.0-20180712184237-d95a45783239 // indirect
	github.com/fsnotify/fsnotify v1.4.9
	github.com/gin-gonic/gin v1.6.3
	github.com/go-openapi/spec v0.19.7 // indirect
	github.com/go-openapi/swag v0.19.8 // indirect
	github.com/go-playground/validator/v10 v10.3.0 // indirect
	github.com/go-redis/redis v6.15.7+incompatible
	github.com/golang/protobuf v1.4.2 // indirect
	github.com/gorilla/websocket v1.4.1
	github.com/jehiah/go-strftime v0.0.0-20171201141054-1d33003b3869 // indirect
	github.com/jinzhu/gorm v1.9.12
	github.com/json-iterator/go v1.1.10 // indirect
	github.com/lestrrat/go-envload v0.0.0-20180220120943-6ed08b54a570 // indirect
	github.com/lestrrat/go-file-rotatelogs v0.0.0-20180223000712-d3151e2a480f
	github.com/lestrrat/go-strftime v0.0.0-20180220042222-ba3bf9c1d042 // indirect
	github.com/mailru/easyjson v0.7.1 // indirect
	github.com/micro/cli v0.2.0
	github.com/micro/go-micro v1.18.0
	github.com/micro/go-micro/v2 v2.9.1
	github.com/micro/micro v1.18.0
	github.com/mitchellh/mapstructure v1.2.2 // indirect
	github.com/mojocn/base64Captcha v1.3.1
	github.com/op/go-logging v0.0.0-20160315200505-970db520ece7
	github.com/pelletier/go-toml v1.6.0 // indirect
	github.com/qiniu/api.v7 v7.2.5+incompatible
	github.com/qiniu/x v7.0.8+incompatible // indirect
	github.com/satori/go.uuid v1.2.0
	github.com/spf13/afero v1.2.2 // indirect
	github.com/spf13/cast v1.3.1 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.6.2
	github.com/stretchr/testify v1.5.1 // indirect
	github.com/swaggo/gin-swagger v1.2.0
	github.com/swaggo/swag v1.6.5
	github.com/tealeg/xlsx v1.0.5
	github.com/tebeka/strftime v0.1.3 // indirect
	github.com/xinliangnote/go-util v0.0.0-20200323134426-527984dc34bf
	github.com/yangtizi/cz88 v1.0.3
	go.uber.org/zap v1.15.0
	golang.org/x/image v0.0.0-20210216034530-4410531fe030 // indirect
	golang.org/x/sys v0.0.0-20210304124612-50617c2ba197 // indirect
	golang.org/x/text v0.3.5 // indirect
	google.golang.org/protobuf v1.24.0 // indirect
	gopkg.in/ini.v1 v1.55.0 // indirect
	gopkg.in/yaml.v2 v2.3.0 // indirect
	qiniupkg.com/x v7.0.8+incompatible
)

replace google.golang.org/grpc => google.golang.org/grpc v1.26.0
