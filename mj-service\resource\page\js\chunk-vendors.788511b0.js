(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-vendors"],{"0192":function(t,e,s){var r=s("f240"),i=Math.max,n=Math.min;t.exports=function(t,e){var s=r(t);return s<0?i(s+e,0):n(s,e)}},"01d7":function(t,e,s){"use strict";var r=s("7dc7"),i=s("c223"),n=s("aec8");t.exports=function(t,e,s){var a=r(e);a in t?i.f(t,a,n(0,s)):t[a]=s}},"021b":function(t,e,s){"use strict";var r=s("407d").forEach,i=s("fb11"),n=s("6885"),a=i("forEach"),o=n("forEach");t.exports=a&&o?[].forEach:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}},"02d0":function(t,e,s){var r=s("f28d"),i=s("8c47"),n=s("6be9").indexOf,a=s("4888");t.exports=function(t,e){var s,o=i(t),c=0,h=[];for(s in o)!r(a,s)&&r(o,s)&&h.push(s);while(e.length>c)r(o,s=e[c++])&&(~n(h,s)||h.push(s));return h}},"032e":function(t,e,s){var r=s("d5dc"),i=s("d68d"),n=r.document,a=i(n)&&i(n.createElement);t.exports=function(t){return a?n.createElement(t):{}}},"0532":function(t,e,s){var r=s("57c4"),i=s("ed35"),n=r("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||a[n]===t)}},"0618":function(t,e,s){"use strict";var r=s("ac83");t.exports=function(){var t=r(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"09ee":function(t,e,s){"use strict";var r=s("91fe"),i=s("407d").find,n=s("5751"),a=s("6885"),o="find",c=!0,h=a(o);o in[]&&Array(1)[o]((function(){c=!1})),r({target:"Array",proto:!0,forced:c||!h},{find:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),n(o)},"0b29":function(t,e,s){var r=s("a9f2");t.exports=function(t,e,s){if(r(t),void 0===e)return t;switch(s){case 0:return function(){return t.call(e)};case 1:return function(s){return t.call(e,s)};case 2:return function(s,r){return t.call(e,s,r)};case 3:return function(s,r,i){return t.call(e,s,r,i)}}return function(){return t.apply(e,arguments)}}},1072:function(t,e){e.f=Object.getOwnPropertySymbols},"12d9":function(t,e,s){var r=s("f30e"),i=/#|\.prototype\./,n=function(t,e){var s=o[a(t)];return s==h||s!=c&&("function"==typeof e?r(e):!!e)},a=n.normalize=function(t){return String(t).replace(i,".").toLowerCase()},o=n.data={},c=n.NATIVE="N",h=n.POLYFILL="P";t.exports=n},"143b":function(t,e,s){"use strict";var r,i,n,a=s("90a7"),o=s("2ba5"),c=s("f28d"),h=s("57c4"),l=s("e17a"),p=h("iterator"),u=!1,d=function(){return this};[].keys&&(n=[].keys(),"next"in n?(i=a(a(n)),i!==Object.prototype&&(r=i)):u=!0),void 0==r&&(r={}),l||c(r,p)||o(r,p,d),t.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:u}},1544:function(t,e,s){var r=s("8c47"),i=s("65af").f,n={}.toString,a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],o=function(t){try{return i(t)}catch(e){return a.slice()}};t.exports.f=function(t){return a&&"[object Window]"==n.call(t)?o(t):i(r(t))}},"16e5":function(t,e,s){var r=s("02d0"),i=s("6807");t.exports=Object.keys||function(t){return r(t,i)}},"1a8c":function(t,e,s){"use strict";var r=s("91fe"),i=s("407d").map,n=s("b1a1"),a=s("6885"),o=n("map"),c=a("map");r({target:"Array",proto:!0,forced:!o||!c},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"1f53":function(t,e,s){var r=s("f30e");t.exports=!r((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},"200e":function(t,e,s){var r=s("d5dc"),i=s("2ba5");t.exports=function(t,e){try{i(r,t,e)}catch(s){r[t]=e}return e}},"21d4":function(t,e,s){"use strict";var r=s("0618"),i=s("dcb6"),n=RegExp.prototype.exec,a=String.prototype.replace,o=n,c=function(){var t=/a/,e=/b*/g;return n.call(t,"a"),n.call(e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),h=i.UNSUPPORTED_Y||i.BROKEN_CARET,l=void 0!==/()??/.exec("")[1],p=c||l||h;p&&(o=function(t){var e,s,i,o,p=this,u=h&&p.sticky,d=r.call(p),f=p.source,m=0,y=t;return u&&(d=d.replace("y",""),-1===d.indexOf("g")&&(d+="g"),y=String(t).slice(p.lastIndex),p.lastIndex>0&&(!p.multiline||p.multiline&&"\n"!==t[p.lastIndex-1])&&(f="(?: "+f+")",y=" "+y,m++),s=new RegExp("^(?:"+f+")",d)),l&&(s=new RegExp("^"+f+"$(?!\\s)",d)),c&&(e=p.lastIndex),i=n.call(u?s:p,y),u?i?(i.input=i.input.slice(m),i[0]=i[0].slice(m),i.index=p.lastIndex,p.lastIndex+=i[0].length):p.lastIndex=0:c&&i&&(p.lastIndex=p.global?i.index+i[0].length:e),l&&i&&i.length>1&&a.call(i[0],s,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(i[o]=void 0)})),i}),t.exports=o},"23c4":function(t,e,s){"use strict";function r(){return r=Object.assign||function(t){for(var e,s=1;s<arguments.length;s++)for(var r in e=arguments[s],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},r.apply(this,arguments)}var i=["attrs","props","domProps"],n=["class","style","directives"],a=["on","nativeOn"],o=function(t){return t.reduce((function(t,e){for(var s in e)if(t[s])if(-1!==i.indexOf(s))t[s]=r({},t[s],e[s]);else if(-1!==n.indexOf(s)){var o=t[s]instanceof Array?t[s]:[t[s]],h=e[s]instanceof Array?e[s]:[e[s]];t[s]=o.concat(h)}else if(-1!==a.indexOf(s))for(var l in e[s])if(t[s][l]){var p=t[s][l]instanceof Array?t[s][l]:[t[s][l]],u=e[s][l]instanceof Array?e[s][l]:[e[s][l]];t[s][l]=p.concat(u)}else t[s][l]=e[s][l];else if("hook"==s)for(var d in e[s])t[s][d]=t[s][d]?c(t[s][d],e[s][d]):e[s][d];else t[s]=e[s];else t[s]=e[s];return t}),{})},c=function(t,e){return function(){t&&t.apply(this,arguments),e&&e.apply(this,arguments)}};t.exports=o},2480:function(t,e,s){"use strict";
/**!
 * Sortable 1.10.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */
function r(t){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function i(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}function n(){return n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var s=arguments[e];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(t[r]=s[r])}return t},n.apply(this,arguments)}function a(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{},r=Object.keys(s);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(t){return Object.getOwnPropertyDescriptor(s,t).enumerable})))),r.forEach((function(e){i(t,e,s[e])}))}return t}function o(t,e){if(null==t)return{};var s,r,i={},n=Object.keys(t);for(r=0;r<n.length;r++)s=n[r],e.indexOf(s)>=0||(i[s]=t[s]);return i}function c(t,e){if(null==t)return{};var s,r,i=o(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(r=0;r<n.length;r++)s=n[r],e.indexOf(s)>=0||Object.prototype.propertyIsEnumerable.call(t,s)&&(i[s]=t[s])}return i}function h(t){return l(t)||p(t)||u()}function l(t){if(Array.isArray(t)){for(var e=0,s=new Array(t.length);e<t.length;e++)s[e]=t[e];return s}}function p(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance")}s.r(e),s.d(e,"MultiDrag",(function(){return Fe})),s.d(e,"Sortable",(function(){return Qt})),s.d(e,"Swap",(function(){return Se}));var d="1.10.2";function f(t){if("undefined"!==typeof window&&window.navigator)return!!navigator.userAgent.match(t)}var m=f(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),y=f(/Edge/i),g=f(/firefox/i),x=f(/safari/i)&&!f(/chrome/i)&&!f(/android/i),b=f(/iP(ad|od|hone)/i),v=f(/chrome/i)&&f(/android/i),w={capture:!1,passive:!1};function P(t,e,s){t.addEventListener(e,s,!m&&w)}function T(t,e,s){t.removeEventListener(e,s,!m&&w)}function E(t,e){if(e){if(">"===e[0]&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(s){return!1}return!1}}function A(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function S(t,e,s,r){if(t){s=s||document;do{if(null!=e&&(">"===e[0]?t.parentNode===s&&E(t,e):E(t,e))||r&&t===s)return t;if(t===s)break}while(t=A(t))}return null}var C,k=/\s+/g;function N(t,e,s){if(t&&e)if(t.classList)t.classList[s?"add":"remove"](e);else{var r=(" "+t.className+" ").replace(k," ").replace(" "+e+" "," ");t.className=(r+(s?" "+e:"")).replace(k," ")}}function I(t,e,s){var r=t&&t.style;if(r){if(void 0===s)return document.defaultView&&document.defaultView.getComputedStyle?s=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(s=t.currentStyle),void 0===e?s:s[e];e in r||-1!==e.indexOf("webkit")||(e="-webkit-"+e),r[e]=s+("string"===typeof s?"":"px")}}function O(t,e){var s="";if("string"===typeof t)s=t;else do{var r=I(t,"transform");r&&"none"!==r&&(s=r+" "+s)}while(!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(s)}function D(t,e,s){if(t){var r=t.getElementsByTagName(e),i=0,n=r.length;if(s)for(;i<n;i++)s(r[i],i);return r}return[]}function M(){var t=document.scrollingElement;return t||document.documentElement}function L(t,e,s,r,i){if(t.getBoundingClientRect||t===window){var n,a,o,c,h,l,p;if(t!==window&&t!==M()?(n=t.getBoundingClientRect(),a=n.top,o=n.left,c=n.bottom,h=n.right,l=n.height,p=n.width):(a=0,o=0,c=window.innerHeight,h=window.innerWidth,l=window.innerHeight,p=window.innerWidth),(e||s)&&t!==window&&(i=i||t.parentNode,!m))do{if(i&&i.getBoundingClientRect&&("none"!==I(i,"transform")||s&&"static"!==I(i,"position"))){var u=i.getBoundingClientRect();a-=u.top+parseInt(I(i,"border-top-width")),o-=u.left+parseInt(I(i,"border-left-width")),c=a+n.height,h=o+n.width;break}}while(i=i.parentNode);if(r&&t!==window){var d=O(i||t),f=d&&d.a,y=d&&d.d;d&&(a/=y,o/=f,p/=f,l/=y,c=a+l,h=o+p)}return{top:a,left:o,bottom:c,right:h,width:p,height:l}}}function _(t,e,s){var r=q(t,!0),i=L(t)[e];while(r){var n=L(r)[s],a=void 0;if(a="top"===s||"left"===s?i>=n:i<=n,!a)return r;if(r===M())break;r=q(r,!1)}return!1}function R(t,e,s){var r=0,i=0,n=t.children;while(i<n.length){if("none"!==n[i].style.display&&n[i]!==Qt.ghost&&n[i]!==Qt.dragged&&S(n[i],s.draggable,t,!1)){if(r===e)return n[i];r++}i++}return null}function j(t,e){var s=t.lastElementChild;while(s&&(s===Qt.ghost||"none"===I(s,"display")||e&&!E(s,e)))s=s.previousElementSibling;return s||null}function F(t,e){var s=0;if(!t||!t.parentNode)return-1;while(t=t.previousElementSibling)"TEMPLATE"===t.nodeName.toUpperCase()||t===Qt.clone||e&&!E(t,e)||s++;return s}function B(t){var e=0,s=0,r=M();if(t)do{var i=O(t),n=i.a,a=i.d;e+=t.scrollLeft*n,s+=t.scrollTop*a}while(t!==r&&(t=t.parentNode));return[e,s]}function U(t,e){for(var s in t)if(t.hasOwnProperty(s))for(var r in e)if(e.hasOwnProperty(r)&&e[r]===t[s][r])return Number(s);return-1}function q(t,e){if(!t||!t.getBoundingClientRect)return M();var s=t,r=!1;do{if(s.clientWidth<s.scrollWidth||s.clientHeight<s.scrollHeight){var i=I(s);if(s.clientWidth<s.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||s.clientHeight<s.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!s.getBoundingClientRect||s===document.body)return M();if(r||e)return s;r=!0}}}while(s=s.parentNode);return M()}function V(t,e){if(t&&e)for(var s in e)e.hasOwnProperty(s)&&(t[s]=e[s]);return t}function z(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}function H(t,e){return function(){if(!C){var s=arguments,r=this;1===s.length?t.call(r,s[0]):t.apply(r,s),C=setTimeout((function(){C=void 0}),e)}}}function W(){clearTimeout(C),C=void 0}function K(t,e,s){t.scrollLeft+=e,t.scrollTop+=s}function $(t){var e=window.Polymer,s=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):s?s(t).clone(!0)[0]:t.cloneNode(!0)}function X(t,e){I(t,"position","absolute"),I(t,"top",e.top),I(t,"left",e.left),I(t,"width",e.width),I(t,"height",e.height)}function G(t){I(t,"position",""),I(t,"top",""),I(t,"left",""),I(t,"width",""),I(t,"height","")}var Y="Sortable"+(new Date).getTime();function J(){var t,e=[];return{captureAnimationState:function(){if(e=[],this.options.animation){var t=[].slice.call(this.el.children);t.forEach((function(t){if("none"!==I(t,"display")&&t!==Qt.ghost){e.push({target:t,rect:L(t)});var s=a({},e[e.length-1].rect);if(t.thisAnimationDuration){var r=O(t,!0);r&&(s.top-=r.f,s.left-=r.e)}t.fromRect=s}}))}},addAnimationState:function(t){e.push(t)},removeAnimationState:function(t){e.splice(U(e,{target:t}),1)},animateAll:function(s){var r=this;if(!this.options.animation)return clearTimeout(t),void("function"===typeof s&&s());var i=!1,n=0;e.forEach((function(t){var e=0,s=t.target,a=s.fromRect,o=L(s),c=s.prevFromRect,h=s.prevToRect,l=t.rect,p=O(s,!0);p&&(o.top-=p.f,o.left-=p.e),s.toRect=o,s.thisAnimationDuration&&z(c,o)&&!z(a,o)&&(l.top-o.top)/(l.left-o.left)===(a.top-o.top)/(a.left-o.left)&&(e=Z(l,c,h,r.options)),z(o,a)||(s.prevFromRect=a,s.prevToRect=o,e||(e=r.options.animation),r.animate(s,l,o,e)),e&&(i=!0,n=Math.max(n,e),clearTimeout(s.animationResetTimer),s.animationResetTimer=setTimeout((function(){s.animationTime=0,s.prevFromRect=null,s.fromRect=null,s.prevToRect=null,s.thisAnimationDuration=null}),e),s.thisAnimationDuration=e)})),clearTimeout(t),i?t=setTimeout((function(){"function"===typeof s&&s()}),n):"function"===typeof s&&s(),e=[]},animate:function(t,e,s,r){if(r){I(t,"transition",""),I(t,"transform","");var i=O(this.el),n=i&&i.a,a=i&&i.d,o=(e.left-s.left)/(n||1),c=(e.top-s.top)/(a||1);t.animatingX=!!o,t.animatingY=!!c,I(t,"transform","translate3d("+o+"px,"+c+"px,0)"),Q(t),I(t,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),I(t,"transform","translate3d(0,0,0)"),"number"===typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout((function(){I(t,"transition",""),I(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1}),r)}}}}function Q(t){return t.offsetWidth}function Z(t,e,s,r){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-s.top,2)+Math.pow(e.left-s.left,2))*r.animation}var tt=[],et={initializeByDefault:!0},st={mount:function(t){for(var e in et)et.hasOwnProperty(e)&&!(e in t)&&(t[e]=et[e]);tt.push(t)},pluginEvent:function(t,e,s){var r=this;this.eventCanceled=!1,s.cancel=function(){r.eventCanceled=!0};var i=t+"Global";tt.forEach((function(r){e[r.pluginName]&&(e[r.pluginName][i]&&e[r.pluginName][i](a({sortable:e},s)),e.options[r.pluginName]&&e[r.pluginName][t]&&e[r.pluginName][t](a({sortable:e},s)))}))},initializePlugins:function(t,e,s,r){for(var i in tt.forEach((function(r){var i=r.pluginName;if(t.options[i]||r.initializeByDefault){var a=new r(t,e,t.options);a.sortable=t,a.options=t.options,t[i]=a,n(s,a.defaults)}})),t.options)if(t.options.hasOwnProperty(i)){var a=this.modifyOption(t,i,t.options[i]);"undefined"!==typeof a&&(t.options[i]=a)}},getEventProperties:function(t,e){var s={};return tt.forEach((function(r){"function"===typeof r.eventProperties&&n(s,r.eventProperties.call(e[r.pluginName],t))})),s},modifyOption:function(t,e,s){var r;return tt.forEach((function(i){t[i.pluginName]&&i.optionListeners&&"function"===typeof i.optionListeners[e]&&(r=i.optionListeners[e].call(t[i.pluginName],s))})),r}};function rt(t){var e=t.sortable,s=t.rootEl,r=t.name,i=t.targetEl,n=t.cloneEl,o=t.toEl,c=t.fromEl,h=t.oldIndex,l=t.newIndex,p=t.oldDraggableIndex,u=t.newDraggableIndex,d=t.originalEvent,f=t.putSortable,g=t.extraEventProperties;if(e=e||s&&s[Y],e){var x,b=e.options,v="on"+r.charAt(0).toUpperCase()+r.substr(1);!window.CustomEvent||m||y?(x=document.createEvent("Event"),x.initEvent(r,!0,!0)):x=new CustomEvent(r,{bubbles:!0,cancelable:!0}),x.to=o||s,x.from=c||s,x.item=i||s,x.clone=n,x.oldIndex=h,x.newIndex=l,x.oldDraggableIndex=p,x.newDraggableIndex=u,x.originalEvent=d,x.pullMode=f?f.lastPutMode:void 0;var w=a({},g,st.getEventProperties(r,e));for(var P in w)x[P]=w[P];s&&s.dispatchEvent(x),b[v]&&b[v].call(e,x)}}var it=function(t,e){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=s.evt,i=c(s,["evt"]);st.pluginEvent.bind(Qt)(t,e,a({dragEl:at,parentEl:ot,ghostEl:ct,rootEl:ht,nextEl:lt,lastDownEl:pt,cloneEl:ut,cloneHidden:dt,dragStarted:St,putSortable:bt,activeSortable:Qt.active,originalEvent:r,oldIndex:ft,oldDraggableIndex:yt,newIndex:mt,newDraggableIndex:gt,hideGhostForTarget:Xt,unhideGhostForTarget:Gt,cloneNowHidden:function(){dt=!0},cloneNowShown:function(){dt=!1},dispatchSortableEvent:function(t){nt({sortable:e,name:t,originalEvent:r})}},i))};function nt(t){rt(a({putSortable:bt,cloneEl:ut,targetEl:at,rootEl:ht,oldIndex:ft,oldDraggableIndex:yt,newIndex:mt,newDraggableIndex:gt},t))}var at,ot,ct,ht,lt,pt,ut,dt,ft,mt,yt,gt,xt,bt,vt,wt,Pt,Tt,Et,At,St,Ct,kt,Nt,It,Ot=!1,Dt=!1,Mt=[],Lt=!1,_t=!1,Rt=[],jt=!1,Ft=[],Bt="undefined"!==typeof document,Ut=b,qt=y||m?"cssFloat":"float",Vt=Bt&&!v&&!b&&"draggable"in document.createElement("div"),zt=function(){if(Bt){if(m)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto","auto"===t.style.pointerEvents}}(),Ht=function(t,e){var s=I(t),r=parseInt(s.width)-parseInt(s.paddingLeft)-parseInt(s.paddingRight)-parseInt(s.borderLeftWidth)-parseInt(s.borderRightWidth),i=R(t,0,e),n=R(t,1,e),a=i&&I(i),o=n&&I(n),c=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+L(i).width,h=o&&parseInt(o.marginLeft)+parseInt(o.marginRight)+L(n).width;if("flex"===s.display)return"column"===s.flexDirection||"column-reverse"===s.flexDirection?"vertical":"horizontal";if("grid"===s.display)return s.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&a["float"]&&"none"!==a["float"]){var l="left"===a["float"]?"left":"right";return!n||"both"!==o.clear&&o.clear!==l?"horizontal":"vertical"}return i&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||c>=r&&"none"===s[qt]||n&&"none"===s[qt]&&c+h>r)?"vertical":"horizontal"},Wt=function(t,e,s){var r=s?t.left:t.top,i=s?t.right:t.bottom,n=s?t.width:t.height,a=s?e.left:e.top,o=s?e.right:e.bottom,c=s?e.width:e.height;return r===a||i===o||r+n/2===a+c/2},Kt=function(t,e){var s;return Mt.some((function(r){if(!j(r)){var i=L(r),n=r[Y].options.emptyInsertThreshold,a=t>=i.left-n&&t<=i.right+n,o=e>=i.top-n&&e<=i.bottom+n;return n&&a&&o?s=r:void 0}})),s},$t=function(t){function e(t,s){return function(r,i,n,a){var o=r.options.group.name&&i.options.group.name&&r.options.group.name===i.options.group.name;if(null==t&&(s||o))return!0;if(null==t||!1===t)return!1;if(s&&"clone"===t)return t;if("function"===typeof t)return e(t(r,i,n,a),s)(r,i,n,a);var c=(s?r:i).options.group.name;return!0===t||"string"===typeof t&&t===c||t.join&&t.indexOf(c)>-1}}var s={},i=t.group;i&&"object"==r(i)||(i={name:i}),s.name=i.name,s.checkPull=e(i.pull,!0),s.checkPut=e(i.put),s.revertClone=i.revertClone,t.group=s},Xt=function(){!zt&&ct&&I(ct,"display","none")},Gt=function(){!zt&&ct&&I(ct,"display","")};Bt&&document.addEventListener("click",(function(t){if(Dt)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),Dt=!1,!1}),!0);var Yt=function(t){if(at){t=t.touches?t.touches[0]:t;var e=Kt(t.clientX,t.clientY);if(e){var s={};for(var r in t)t.hasOwnProperty(r)&&(s[r]=t[r]);s.target=s.rootEl=e,s.preventDefault=void 0,s.stopPropagation=void 0,e[Y]._onDragOver(s)}}},Jt=function(t){at&&at.parentNode[Y]._isOutsideThisEl(t.target)};function Qt(t,e){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=n({},e),t[Y]=this;var s={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Ht(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,e){t.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Qt.supportPointer&&"PointerEvent"in window,emptyInsertThreshold:5};for(var r in st.initializePlugins(this,t,s),s)!(r in e)&&(e[r]=s[r]);for(var i in $t(e),this)"_"===i.charAt(0)&&"function"===typeof this[i]&&(this[i]=this[i].bind(this));this.nativeDraggable=!e.forceFallback&&Vt,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?P(t,"pointerdown",this._onTapStart):(P(t,"mousedown",this._onTapStart),P(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(P(t,"dragover",this),P(t,"dragenter",this)),Mt.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),n(this,J())}function Zt(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function te(t,e,s,r,i,n,a,o){var c,h,l=t[Y],p=l.options.onMove;return!window.CustomEvent||m||y?(c=document.createEvent("Event"),c.initEvent("move",!0,!0)):c=new CustomEvent("move",{bubbles:!0,cancelable:!0}),c.to=e,c.from=t,c.dragged=s,c.draggedRect=r,c.related=i||e,c.relatedRect=n||L(e),c.willInsertAfter=o,c.originalEvent=a,t.dispatchEvent(c),p&&(h=p.call(l,c,a)),h}function ee(t){t.draggable=!1}function se(){jt=!1}function re(t,e,s){var r=L(j(s.el,s.options.draggable)),i=10;return e?t.clientX>r.right+i||t.clientX<=r.right&&t.clientY>r.bottom&&t.clientX>=r.left:t.clientX>r.right&&t.clientY>r.top||t.clientX<=r.right&&t.clientY>r.bottom+i}function ie(t,e,s,r,i,n,a,o){var c=r?t.clientY:t.clientX,h=r?s.height:s.width,l=r?s.top:s.left,p=r?s.bottom:s.right,u=!1;if(!a)if(o&&Nt<h*i){if(!Lt&&(1===kt?c>l+h*n/2:c<p-h*n/2)&&(Lt=!0),Lt)u=!0;else if(1===kt?c<l+Nt:c>p-Nt)return-kt}else if(c>l+h*(1-i)/2&&c<p-h*(1-i)/2)return ne(e);return u=u||a,u&&(c<l+h*n/2||c>p-h*n/2)?c>l+h/2?1:-1:0}function ne(t){return F(at)<F(t)?1:-1}function ae(t){var e=t.tagName+t.className+t.src+t.href+t.textContent,s=e.length,r=0;while(s--)r+=e.charCodeAt(s);return r.toString(36)}function oe(t){Ft.length=0;var e=t.getElementsByTagName("input"),s=e.length;while(s--){var r=e[s];r.checked&&Ft.push(r)}}function ce(t){return setTimeout(t,0)}function he(t){return clearTimeout(t)}Qt.prototype={constructor:Qt,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(Ct=null)},_getDirection:function(t,e){return"function"===typeof this.options.direction?this.options.direction.call(this,t,e,at):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,s=this.el,r=this.options,i=r.preventOnFilter,n=t.type,a=t.touches&&t.touches[0]||t.pointerType&&"touch"===t.pointerType&&t,o=(a||t).target,c=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||o,h=r.filter;if(oe(s),!at&&!(/mousedown|pointerdown/.test(n)&&0!==t.button||r.disabled)&&!c.isContentEditable&&(o=S(o,r.draggable,s,!1),(!o||!o.animated)&&pt!==o)){if(ft=F(o),yt=F(o,r.draggable),"function"===typeof h){if(h.call(this,t,o,this))return nt({sortable:e,rootEl:c,name:"filter",targetEl:o,toEl:s,fromEl:s}),it("filter",e,{evt:t}),void(i&&t.cancelable&&t.preventDefault())}else if(h&&(h=h.split(",").some((function(r){if(r=S(c,r.trim(),s,!1),r)return nt({sortable:e,rootEl:r,name:"filter",targetEl:o,fromEl:s,toEl:s}),it("filter",e,{evt:t}),!0})),h))return void(i&&t.cancelable&&t.preventDefault());r.handle&&!S(c,r.handle,s,!1)||this._prepareDragStart(t,a,o)}}},_prepareDragStart:function(t,e,s){var r,i=this,n=i.el,a=i.options,o=n.ownerDocument;if(s&&!at&&s.parentNode===n){var c=L(s);if(ht=n,at=s,ot=at.parentNode,lt=at.nextSibling,pt=s,xt=a.group,Qt.dragged=at,vt={target:at,clientX:(e||t).clientX,clientY:(e||t).clientY},Et=vt.clientX-c.left,At=vt.clientY-c.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,at.style["will-change"]="all",r=function(){it("delayEnded",i,{evt:t}),Qt.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!g&&i.nativeDraggable&&(at.draggable=!0),i._triggerDragStart(t,e),nt({sortable:i,name:"choose",originalEvent:t}),N(at,a.chosenClass,!0))},a.ignore.split(",").forEach((function(t){D(at,t.trim(),ee)})),P(o,"dragover",Yt),P(o,"mousemove",Yt),P(o,"touchmove",Yt),P(o,"mouseup",i._onDrop),P(o,"touchend",i._onDrop),P(o,"touchcancel",i._onDrop),g&&this.nativeDraggable&&(this.options.touchStartThreshold=4,at.draggable=!0),it("delayStart",this,{evt:t}),!a.delay||a.delayOnTouchOnly&&!e||this.nativeDraggable&&(y||m))r();else{if(Qt.eventCanceled)return void this._onDrop();P(o,"mouseup",i._disableDelayedDrag),P(o,"touchend",i._disableDelayedDrag),P(o,"touchcancel",i._disableDelayedDrag),P(o,"mousemove",i._delayedDragTouchMoveHandler),P(o,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&P(o,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(r,a.delay)}}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){at&&ee(at),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;T(t,"mouseup",this._disableDelayedDrag),T(t,"touchend",this._disableDelayedDrag),T(t,"touchcancel",this._disableDelayedDrag),T(t,"mousemove",this._delayedDragTouchMoveHandler),T(t,"touchmove",this._delayedDragTouchMoveHandler),T(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||"touch"==t.pointerType&&t,!this.nativeDraggable||e?this.options.supportPointer?P(document,"pointermove",this._onTouchMove):P(document,e?"touchmove":"mousemove",this._onTouchMove):(P(at,"dragend",this),P(ht,"dragstart",this._onDragStart));try{document.selection?ce((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(s){}},_dragStarted:function(t,e){if(Ot=!1,ht&&at){it("dragStarted",this,{evt:e}),this.nativeDraggable&&P(document,"dragover",Jt);var s=this.options;!t&&N(at,s.dragClass,!1),N(at,s.ghostClass,!0),Qt.active=this,t&&this._appendGhost(),nt({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(wt){this._lastX=wt.clientX,this._lastY=wt.clientY,Xt();var t=document.elementFromPoint(wt.clientX,wt.clientY),e=t;while(t&&t.shadowRoot){if(t=t.shadowRoot.elementFromPoint(wt.clientX,wt.clientY),t===e)break;e=t}if(at.parentNode[Y]._isOutsideThisEl(t),e)do{if(e[Y]){var s=void 0;if(s=e[Y]._onDragOver({clientX:wt.clientX,clientY:wt.clientY,target:t,rootEl:e}),s&&!this.options.dragoverBubble)break}t=e}while(e=e.parentNode);Gt()}},_onTouchMove:function(t){if(vt){var e=this.options,s=e.fallbackTolerance,r=e.fallbackOffset,i=t.touches?t.touches[0]:t,n=ct&&O(ct,!0),a=ct&&n&&n.a,o=ct&&n&&n.d,c=Ut&&It&&B(It),h=(i.clientX-vt.clientX+r.x)/(a||1)+(c?c[0]-Rt[0]:0)/(a||1),l=(i.clientY-vt.clientY+r.y)/(o||1)+(c?c[1]-Rt[1]:0)/(o||1);if(!Qt.active&&!Ot){if(s&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<s)return;this._onDragStart(t,!0)}if(ct){n?(n.e+=h-(Pt||0),n.f+=l-(Tt||0)):n={a:1,b:0,c:0,d:1,e:h,f:l};var p="matrix(".concat(n.a,",").concat(n.b,",").concat(n.c,",").concat(n.d,",").concat(n.e,",").concat(n.f,")");I(ct,"webkitTransform",p),I(ct,"mozTransform",p),I(ct,"msTransform",p),I(ct,"transform",p),Pt=h,Tt=l,wt=i}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!ct){var t=this.options.fallbackOnBody?document.body:ht,e=L(at,!0,Ut,!0,t),s=this.options;if(Ut){It=t;while("static"===I(It,"position")&&"none"===I(It,"transform")&&It!==document)It=It.parentNode;It!==document.body&&It!==document.documentElement?(It===document&&(It=M()),e.top+=It.scrollTop,e.left+=It.scrollLeft):It=M(),Rt=B(It)}ct=at.cloneNode(!0),N(ct,s.ghostClass,!1),N(ct,s.fallbackClass,!0),N(ct,s.dragClass,!0),I(ct,"transition",""),I(ct,"transform",""),I(ct,"box-sizing","border-box"),I(ct,"margin",0),I(ct,"top",e.top),I(ct,"left",e.left),I(ct,"width",e.width),I(ct,"height",e.height),I(ct,"opacity","0.8"),I(ct,"position",Ut?"absolute":"fixed"),I(ct,"zIndex","100000"),I(ct,"pointerEvents","none"),Qt.ghost=ct,t.appendChild(ct),I(ct,"transform-origin",Et/parseInt(ct.style.width)*100+"% "+At/parseInt(ct.style.height)*100+"%")}},_onDragStart:function(t,e){var s=this,r=t.dataTransfer,i=s.options;it("dragStart",this,{evt:t}),Qt.eventCanceled?this._onDrop():(it("setupClone",this),Qt.eventCanceled||(ut=$(at),ut.draggable=!1,ut.style["will-change"]="",this._hideClone(),N(ut,this.options.chosenClass,!1),Qt.clone=ut),s.cloneId=ce((function(){it("clone",s),Qt.eventCanceled||(s.options.removeCloneOnHide||ht.insertBefore(ut,at),s._hideClone(),nt({sortable:s,name:"clone"}))})),!e&&N(at,i.dragClass,!0),e?(Dt=!0,s._loopId=setInterval(s._emulateDragOver,50)):(T(document,"mouseup",s._onDrop),T(document,"touchend",s._onDrop),T(document,"touchcancel",s._onDrop),r&&(r.effectAllowed="move",i.setData&&i.setData.call(s,r,at)),P(document,"drop",s),I(at,"transform","translateZ(0)")),Ot=!0,s._dragStartId=ce(s._dragStarted.bind(s,e,t)),P(document,"selectstart",s),St=!0,x&&I(document.body,"user-select","none"))},_onDragOver:function(t){var e,s,r,i,n=this.el,o=t.target,c=this.options,h=c.group,l=Qt.active,p=xt===h,u=c.sort,d=bt||l,f=this,m=!1;if(!jt){if(void 0!==t.preventDefault&&t.cancelable&&t.preventDefault(),o=S(o,c.draggable,n,!0),D("dragOver"),Qt.eventCanceled)return m;if(at.contains(t.target)||o.animated&&o.animatingX&&o.animatingY||f._ignoreWhileAnimating===o)return R(!1);if(Dt=!1,l&&!c.disabled&&(p?u||(r=!ht.contains(at)):bt===this||(this.lastPutMode=xt.checkPull(this,l,at,t))&&h.checkPut(this,l,at,t))){if(i="vertical"===this._getDirection(t,o),e=L(at),D("dragOverValid"),Qt.eventCanceled)return m;if(r)return ot=ht,M(),this._hideClone(),D("revert"),Qt.eventCanceled||(lt?ht.insertBefore(at,lt):ht.appendChild(at)),R(!0);var y=j(n,c.draggable);if(!y||re(t,i,this)&&!y.animated){if(y===at)return R(!1);if(y&&n===t.target&&(o=y),o&&(s=L(o)),!1!==te(ht,n,at,e,o,s,t,!!o))return M(),n.appendChild(at),ot=n,B(),R(!0)}else if(o.parentNode===n){s=L(o);var g,x,b=0,v=at.parentNode!==n,w=!Wt(at.animated&&at.toRect||e,o.animated&&o.toRect||s,i),P=i?"top":"left",T=_(o,"top","top")||_(at,"top","top"),E=T?T.scrollTop:void 0;if(Ct!==o&&(g=s[P],Lt=!1,_t=!w&&c.invertSwap||v),b=ie(t,o,s,i,w?1:c.swapThreshold,null==c.invertedSwapThreshold?c.swapThreshold:c.invertedSwapThreshold,_t,Ct===o),0!==b){var A=F(at);do{A-=b,x=ot.children[A]}while(x&&("none"===I(x,"display")||x===ct))}if(0===b||x===o)return R(!1);Ct=o,kt=b;var C=o.nextElementSibling,k=!1;k=1===b;var O=te(ht,n,at,e,o,s,t,k);if(!1!==O)return 1!==O&&-1!==O||(k=1===O),jt=!0,setTimeout(se,30),M(),k&&!C?n.appendChild(at):o.parentNode.insertBefore(at,k?C:o),T&&K(T,0,E-T.scrollTop),ot=at.parentNode,void 0===g||_t||(Nt=Math.abs(g-L(o)[P])),B(),R(!0)}if(n.contains(at))return R(!1)}return!1}function D(c,h){it(c,f,a({evt:t,isOwner:p,axis:i?"vertical":"horizontal",revert:r,dragRect:e,targetRect:s,canSort:u,fromSortable:d,target:o,completed:R,onMove:function(s,r){return te(ht,n,at,e,s,L(s),t,r)},changed:B},h))}function M(){D("dragOverAnimationCapture"),f.captureAnimationState(),f!==d&&d.captureAnimationState()}function R(e){return D("dragOverCompleted",{insertion:e}),e&&(p?l._hideClone():l._showClone(f),f!==d&&(N(at,bt?bt.options.ghostClass:l.options.ghostClass,!1),N(at,c.ghostClass,!0)),bt!==f&&f!==Qt.active?bt=f:f===Qt.active&&bt&&(bt=null),d===f&&(f._ignoreWhileAnimating=o),f.animateAll((function(){D("dragOverAnimationComplete"),f._ignoreWhileAnimating=null})),f!==d&&(d.animateAll(),d._ignoreWhileAnimating=null)),(o===at&&!at.animated||o===n&&!o.animated)&&(Ct=null),c.dragoverBubble||t.rootEl||o===document||(at.parentNode[Y]._isOutsideThisEl(t.target),!e&&Yt(t)),!c.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),m=!0}function B(){mt=F(at),gt=F(at,c.draggable),nt({sortable:f,name:"change",toEl:n,newIndex:mt,newDraggableIndex:gt,originalEvent:t})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){T(document,"mousemove",this._onTouchMove),T(document,"touchmove",this._onTouchMove),T(document,"pointermove",this._onTouchMove),T(document,"dragover",Yt),T(document,"mousemove",Yt),T(document,"touchmove",Yt)},_offUpEvents:function(){var t=this.el.ownerDocument;T(t,"mouseup",this._onDrop),T(t,"touchend",this._onDrop),T(t,"pointerup",this._onDrop),T(t,"touchcancel",this._onDrop),T(document,"selectstart",this)},_onDrop:function(t){var e=this.el,s=this.options;mt=F(at),gt=F(at,s.draggable),it("drop",this,{evt:t}),ot=at&&at.parentNode,mt=F(at),gt=F(at,s.draggable),Qt.eventCanceled||(Ot=!1,_t=!1,Lt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),he(this.cloneId),he(this._dragStartId),this.nativeDraggable&&(T(document,"drop",this),T(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),x&&I(document.body,"user-select",""),I(at,"transform",""),t&&(St&&(t.cancelable&&t.preventDefault(),!s.dropBubble&&t.stopPropagation()),ct&&ct.parentNode&&ct.parentNode.removeChild(ct),(ht===ot||bt&&"clone"!==bt.lastPutMode)&&ut&&ut.parentNode&&ut.parentNode.removeChild(ut),at&&(this.nativeDraggable&&T(at,"dragend",this),ee(at),at.style["will-change"]="",St&&!Ot&&N(at,bt?bt.options.ghostClass:this.options.ghostClass,!1),N(at,this.options.chosenClass,!1),nt({sortable:this,name:"unchoose",toEl:ot,newIndex:null,newDraggableIndex:null,originalEvent:t}),ht!==ot?(mt>=0&&(nt({rootEl:ot,name:"add",toEl:ot,fromEl:ht,originalEvent:t}),nt({sortable:this,name:"remove",toEl:ot,originalEvent:t}),nt({rootEl:ot,name:"sort",toEl:ot,fromEl:ht,originalEvent:t}),nt({sortable:this,name:"sort",toEl:ot,originalEvent:t})),bt&&bt.save()):mt!==ft&&mt>=0&&(nt({sortable:this,name:"update",toEl:ot,originalEvent:t}),nt({sortable:this,name:"sort",toEl:ot,originalEvent:t})),Qt.active&&(null!=mt&&-1!==mt||(mt=ft,gt=yt),nt({sortable:this,name:"end",toEl:ot,originalEvent:t}),this.save())))),this._nulling()},_nulling:function(){it("nulling",this),ht=at=ot=ct=lt=ut=pt=dt=vt=wt=St=mt=gt=ft=yt=Ct=kt=bt=xt=Qt.dragged=Qt.ghost=Qt.clone=Qt.active=null,Ft.forEach((function(t){t.checked=!0})),Ft.length=Pt=Tt=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":at&&(this._onDragOver(t),Zt(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t,e=[],s=this.el.children,r=0,i=s.length,n=this.options;r<i;r++)t=s[r],S(t,n.draggable,this.el,!1)&&e.push(t.getAttribute(n.dataIdAttr)||ae(t));return e},sort:function(t){var e={},s=this.el;this.toArray().forEach((function(t,r){var i=s.children[r];S(i,this.options.draggable,s,!1)&&(e[t]=i)}),this),t.forEach((function(t){e[t]&&(s.removeChild(e[t]),s.appendChild(e[t]))}))},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return S(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var s=this.options;if(void 0===e)return s[t];var r=st.modifyOption(this,t,e);s[t]="undefined"!==typeof r?r:e,"group"===t&&$t(s)},destroy:function(){it("destroy",this);var t=this.el;t[Y]=null,T(t,"mousedown",this._onTapStart),T(t,"touchstart",this._onTapStart),T(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(T(t,"dragover",this),T(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),(function(t){t.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),Mt.splice(Mt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!dt){if(it("hideClone",this),Qt.eventCanceled)return;I(ut,"display","none"),this.options.removeCloneOnHide&&ut.parentNode&&ut.parentNode.removeChild(ut),dt=!0}},_showClone:function(t){if("clone"===t.lastPutMode){if(dt){if(it("showClone",this),Qt.eventCanceled)return;ht.contains(at)&&!this.options.group.revertClone?ht.insertBefore(ut,at):lt?ht.insertBefore(ut,lt):ht.appendChild(ut),this.options.group.revertClone&&this.animate(at,ut),I(ut,"display",""),dt=!1}}else this._hideClone()}},Bt&&P(document,"touchmove",(function(t){(Qt.active||Ot)&&t.cancelable&&t.preventDefault()})),Qt.utils={on:P,off:T,css:I,find:D,is:function(t,e){return!!S(t,e,t,!1)},extend:V,throttle:H,closest:S,toggleClass:N,clone:$,index:F,nextTick:ce,cancelNextTick:he,detectDirection:Ht,getChild:R},Qt.get=function(t){return t[Y]},Qt.mount=function(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];e[0].constructor===Array&&(e=e[0]),e.forEach((function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(Qt.utils=a({},Qt.utils,t.utils)),st.mount(t)}))},Qt.create=function(t,e){return new Qt(t,e)},Qt.version=d;var le,pe,ue,de,fe,me,ye=[],ge=!1;function xe(){function t(){for(var t in this.defaults={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"===typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?P(document,"dragover",this._handleAutoScroll):this.options.supportPointer?P(document,"pointermove",this._handleFallbackAutoScroll):e.touches?P(document,"touchmove",this._handleFallbackAutoScroll):P(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?T(document,"dragover",this._handleAutoScroll):(T(document,"pointermove",this._handleFallbackAutoScroll),T(document,"touchmove",this._handleFallbackAutoScroll),T(document,"mousemove",this._handleFallbackAutoScroll)),ve(),be(),W()},nulling:function(){fe=pe=le=ge=me=ue=de=null,ye.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var s=this,r=(t.touches?t.touches[0]:t).clientX,i=(t.touches?t.touches[0]:t).clientY,n=document.elementFromPoint(r,i);if(fe=t,e||y||m||x){Pe(t,this.options,n,e);var a=q(n,!0);!ge||me&&r===ue&&i===de||(me&&ve(),me=setInterval((function(){var n=q(document.elementFromPoint(r,i),!0);n!==a&&(a=n,be()),Pe(t,s.options,n,e)}),10),ue=r,de=i)}else{if(!this.options.bubbleScroll||q(n,!0)===M())return void be();Pe(t,this.options,q(n,!1),!1)}}},n(t,{pluginName:"scroll",initializeByDefault:!0})}function be(){ye.forEach((function(t){clearInterval(t.pid)})),ye=[]}function ve(){clearInterval(me)}var we,Pe=H((function(t,e,s,r){if(e.scroll){var i,n=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,o=e.scrollSensitivity,c=e.scrollSpeed,h=M(),l=!1;pe!==s&&(pe=s,be(),le=e.scroll,i=e.scrollFn,!0===le&&(le=q(s,!0)));var p=0,u=le;do{var d=u,f=L(d),m=f.top,y=f.bottom,g=f.left,x=f.right,b=f.width,v=f.height,w=void 0,P=void 0,T=d.scrollWidth,E=d.scrollHeight,A=I(d),S=d.scrollLeft,C=d.scrollTop;d===h?(w=b<T&&("auto"===A.overflowX||"scroll"===A.overflowX||"visible"===A.overflowX),P=v<E&&("auto"===A.overflowY||"scroll"===A.overflowY||"visible"===A.overflowY)):(w=b<T&&("auto"===A.overflowX||"scroll"===A.overflowX),P=v<E&&("auto"===A.overflowY||"scroll"===A.overflowY));var k=w&&(Math.abs(x-n)<=o&&S+b<T)-(Math.abs(g-n)<=o&&!!S),N=P&&(Math.abs(y-a)<=o&&C+v<E)-(Math.abs(m-a)<=o&&!!C);if(!ye[p])for(var O=0;O<=p;O++)ye[O]||(ye[O]={});ye[p].vx==k&&ye[p].vy==N&&ye[p].el===d||(ye[p].el=d,ye[p].vx=k,ye[p].vy=N,clearInterval(ye[p].pid),0==k&&0==N||(l=!0,ye[p].pid=setInterval(function(){r&&0===this.layer&&Qt.active._onTouchMove(fe);var e=ye[this.layer].vy?ye[this.layer].vy*c:0,s=ye[this.layer].vx?ye[this.layer].vx*c:0;"function"===typeof i&&"continue"!==i.call(Qt.dragged.parentNode[Y],s,e,t,fe,ye[this.layer].el)||K(ye[this.layer].el,s,e)}.bind({layer:p}),24))),p++}while(e.bubbleScroll&&u!==h&&(u=q(u,!1)));ge=l}}),30),Te=function(t){var e=t.originalEvent,s=t.putSortable,r=t.dragEl,i=t.activeSortable,n=t.dispatchSortableEvent,a=t.hideGhostForTarget,o=t.unhideGhostForTarget;if(e){var c=s||i;a();var h=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,l=document.elementFromPoint(h.clientX,h.clientY);o(),c&&!c.el.contains(l)&&(n("spill"),this.onSpill({dragEl:r,putSortable:s}))}};function Ee(){}function Ae(){}function Se(){function t(){this.defaults={swapClass:"sortable-swap-highlight"}}return t.prototype={dragStart:function(t){var e=t.dragEl;we=e},dragOverValid:function(t){var e=t.completed,s=t.target,r=t.onMove,i=t.activeSortable,n=t.changed,a=t.cancel;if(i.options.swap){var o=this.sortable.el,c=this.options;if(s&&s!==o){var h=we;!1!==r(s)?(N(s,c.swapClass,!0),we=s):we=null,h&&h!==we&&N(h,c.swapClass,!1)}n(),e(!0),a()}},drop:function(t){var e=t.activeSortable,s=t.putSortable,r=t.dragEl,i=s||this.sortable,n=this.options;we&&N(we,n.swapClass,!1),we&&(n.swap||s&&s.options.swap)&&r!==we&&(i.captureAnimationState(),i!==e&&e.captureAnimationState(),Ce(r,we),i.animateAll(),i!==e&&e.animateAll())},nulling:function(){we=null}},n(t,{pluginName:"swap",eventProperties:function(){return{swapItem:we}}})}function Ce(t,e){var s,r,i=t.parentNode,n=e.parentNode;i&&n&&!i.isEqualNode(e)&&!n.isEqualNode(t)&&(s=F(t),r=F(e),i.isEqualNode(n)&&s<r&&r++,i.insertBefore(e,i.children[s]),n.insertBefore(t,n.children[r]))}Ee.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,s=t.putSortable;this.sortable.captureAnimationState(),s&&s.captureAnimationState();var r=R(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(e,r):this.sortable.el.appendChild(e),this.sortable.animateAll(),s&&s.animateAll()},drop:Te},n(Ee,{pluginName:"revertOnSpill"}),Ae.prototype={onSpill:function(t){var e=t.dragEl,s=t.putSortable,r=s||this.sortable;r.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),r.animateAll()},drop:Te},n(Ae,{pluginName:"removeOnSpill"});var ke,Ne,Ie,Oe,De,Me=[],Le=[],_e=!1,Re=!1,je=!1;function Fe(){function t(t){for(var e in this)"_"===e.charAt(0)&&"function"===typeof this[e]&&(this[e]=this[e].bind(this));t.options.supportPointer?P(document,"pointerup",this._deselectMultiDrag):(P(document,"mouseup",this._deselectMultiDrag),P(document,"touchend",this._deselectMultiDrag)),P(document,"keydown",this._checkKeyDown),P(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(e,s){var r="";Me.length&&Ne===t?Me.forEach((function(t,e){r+=(e?", ":"")+t.textContent})):r=s.textContent,e.setData("Text",r)}}}return t.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var e=t.dragEl;Ie=e},delayEnded:function(){this.isMultiDrag=~Me.indexOf(Ie)},setupClone:function(t){var e=t.sortable,s=t.cancel;if(this.isMultiDrag){for(var r=0;r<Me.length;r++)Le.push($(Me[r])),Le[r].sortableIndex=Me[r].sortableIndex,Le[r].draggable=!1,Le[r].style["will-change"]="",N(Le[r],this.options.selectedClass,!1),Me[r]===Ie&&N(Le[r],this.options.chosenClass,!1);e._hideClone(),s()}},clone:function(t){var e=t.sortable,s=t.rootEl,r=t.dispatchSortableEvent,i=t.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||Me.length&&Ne===e&&(Ue(!0,s),r("clone"),i()))},showClone:function(t){var e=t.cloneNowShown,s=t.rootEl,r=t.cancel;this.isMultiDrag&&(Ue(!1,s),Le.forEach((function(t){I(t,"display","")})),e(),De=!1,r())},hideClone:function(t){var e=this,s=(t.sortable,t.cloneNowHidden),r=t.cancel;this.isMultiDrag&&(Le.forEach((function(t){I(t,"display","none"),e.options.removeCloneOnHide&&t.parentNode&&t.parentNode.removeChild(t)})),s(),De=!0,r())},dragStartGlobal:function(t){t.sortable;!this.isMultiDrag&&Ne&&Ne.multiDrag._deselectMultiDrag(),Me.forEach((function(t){t.sortableIndex=F(t)})),Me=Me.sort((function(t,e){return t.sortableIndex-e.sortableIndex})),je=!0},dragStarted:function(t){var e=this,s=t.sortable;if(this.isMultiDrag){if(this.options.sort&&(s.captureAnimationState(),this.options.animation)){Me.forEach((function(t){t!==Ie&&I(t,"position","absolute")}));var r=L(Ie,!1,!0,!0);Me.forEach((function(t){t!==Ie&&X(t,r)})),Re=!0,_e=!0}s.animateAll((function(){Re=!1,_e=!1,e.options.animation&&Me.forEach((function(t){G(t)})),e.options.sort&&qe()}))}},dragOver:function(t){var e=t.target,s=t.completed,r=t.cancel;Re&&~Me.indexOf(e)&&(s(!1),r())},revert:function(t){var e=t.fromSortable,s=t.rootEl,r=t.sortable,i=t.dragRect;Me.length>1&&(Me.forEach((function(t){r.addAnimationState({target:t,rect:Re?L(t):i}),G(t),t.fromRect=i,e.removeAnimationState(t)})),Re=!1,Be(!this.options.removeCloneOnHide,s))},dragOverCompleted:function(t){var e=t.sortable,s=t.isOwner,r=t.insertion,i=t.activeSortable,n=t.parentEl,a=t.putSortable,o=this.options;if(r){if(s&&i._hideClone(),_e=!1,o.animation&&Me.length>1&&(Re||!s&&!i.options.sort&&!a)){var c=L(Ie,!1,!0,!0);Me.forEach((function(t){t!==Ie&&(X(t,c),n.appendChild(t))})),Re=!0}if(!s)if(Re||qe(),Me.length>1){var h=De;i._showClone(e),i.options.animation&&!De&&h&&Le.forEach((function(t){i.addAnimationState({target:t,rect:Oe}),t.fromRect=Oe,t.thisAnimationDuration=null}))}else i._showClone(e)}},dragOverAnimationCapture:function(t){var e=t.dragRect,s=t.isOwner,r=t.activeSortable;if(Me.forEach((function(t){t.thisAnimationDuration=null})),r.options.animation&&!s&&r.multiDrag.isMultiDrag){Oe=n({},e);var i=O(Ie,!0);Oe.top-=i.f,Oe.left-=i.e}},dragOverAnimationComplete:function(){Re&&(Re=!1,qe())},drop:function(t){var e=t.originalEvent,s=t.rootEl,r=t.parentEl,i=t.sortable,n=t.dispatchSortableEvent,a=t.oldIndex,o=t.putSortable,c=o||this.sortable;if(e){var h=this.options,l=r.children;if(!je)if(h.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),N(Ie,h.selectedClass,!~Me.indexOf(Ie)),~Me.indexOf(Ie))Me.splice(Me.indexOf(Ie),1),ke=null,rt({sortable:i,rootEl:s,name:"deselect",targetEl:Ie,originalEvt:e});else{if(Me.push(Ie),rt({sortable:i,rootEl:s,name:"select",targetEl:Ie,originalEvt:e}),e.shiftKey&&ke&&i.el.contains(ke)){var p,u,d=F(ke),f=F(Ie);if(~d&&~f&&d!==f)for(f>d?(u=d,p=f):(u=f,p=d+1);u<p;u++)~Me.indexOf(l[u])||(N(l[u],h.selectedClass,!0),Me.push(l[u]),rt({sortable:i,rootEl:s,name:"select",targetEl:l[u],originalEvt:e}))}else ke=Ie;Ne=c}if(je&&this.isMultiDrag){if((r[Y].options.sort||r!==s)&&Me.length>1){var m=L(Ie),y=F(Ie,":not(."+this.options.selectedClass+")");if(!_e&&h.animation&&(Ie.thisAnimationDuration=null),c.captureAnimationState(),!_e&&(h.animation&&(Ie.fromRect=m,Me.forEach((function(t){if(t.thisAnimationDuration=null,t!==Ie){var e=Re?L(t):m;t.fromRect=e,c.addAnimationState({target:t,rect:e})}}))),qe(),Me.forEach((function(t){l[y]?r.insertBefore(t,l[y]):r.appendChild(t),y++})),a===F(Ie))){var g=!1;Me.forEach((function(t){t.sortableIndex===F(t)||(g=!0)})),g&&n("update")}Me.forEach((function(t){G(t)})),c.animateAll()}Ne=c}(s===r||o&&"clone"!==o.lastPutMode)&&Le.forEach((function(t){t.parentNode&&t.parentNode.removeChild(t)}))}},nullingGlobal:function(){this.isMultiDrag=je=!1,Le.length=0},destroyGlobal:function(){this._deselectMultiDrag(),T(document,"pointerup",this._deselectMultiDrag),T(document,"mouseup",this._deselectMultiDrag),T(document,"touchend",this._deselectMultiDrag),T(document,"keydown",this._checkKeyDown),T(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(("undefined"===typeof je||!je)&&Ne===this.sortable&&(!t||!S(t.target,this.options.draggable,this.sortable.el,!1))&&(!t||0===t.button))while(Me.length){var e=Me[0];N(e,this.options.selectedClass,!1),Me.shift(),rt({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:e,originalEvt:t})}},_checkKeyDown:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},n(t,{pluginName:"multiDrag",utils:{select:function(t){var e=t.parentNode[Y];e&&e.options.multiDrag&&!~Me.indexOf(t)&&(Ne&&Ne!==e&&(Ne.multiDrag._deselectMultiDrag(),Ne=e),N(t,e.options.selectedClass,!0),Me.push(t))},deselect:function(t){var e=t.parentNode[Y],s=Me.indexOf(t);e&&e.options.multiDrag&&~s&&(N(t,e.options.selectedClass,!1),Me.splice(s,1))}},eventProperties:function(){var t=this,e=[],s=[];return Me.forEach((function(r){var i;e.push({multiDragElement:r,index:r.sortableIndex}),i=Re&&r!==Ie?-1:Re?F(r,":not(."+t.options.selectedClass+")"):F(r),s.push({multiDragElement:r,index:i})})),{items:h(Me),clones:[].concat(Le),oldIndicies:e,newIndicies:s}},optionListeners:{multiDragKey:function(t){return t=t.toLowerCase(),"ctrl"===t?t="Control":t.length>1&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}function Be(t,e){Me.forEach((function(s,r){var i=e.children[s.sortableIndex+(t?Number(r):0)];i?e.insertBefore(s,i):e.appendChild(s)}))}function Ue(t,e){Le.forEach((function(s,r){var i=e.children[s.sortableIndex+(t?Number(r):0)];i?e.insertBefore(s,i):e.appendChild(s)}))}function qe(){Me.forEach((function(t){t!==Ie&&t.parentNode&&t.parentNode.removeChild(t)}))}Qt.mount(new xe),Qt.mount(Ae,Ee),e["default"]=Qt},"252a":function(t,e,s){var r=s("d5dc"),i=s("41f6"),n=s("d9a3"),a=s("2ba5"),o=s("57c4"),c=o("iterator"),h=o("toStringTag"),l=n.values;for(var p in i){var u=r[p],d=u&&u.prototype;if(d){if(d[c]!==l)try{a(d,c,l)}catch(m){d[c]=l}if(d[h]||a(d,h,p),i[p])for(var f in n)if(d[f]!==n[f])try{a(d,f,n[f])}catch(m){d[f]=n[f]}}}},"2a2f":function(t,e,s){var r=s("d5dc");t.exports=r},"2ba5":function(t,e,s){var r=s("7a23"),i=s("c223"),n=s("aec8");t.exports=r?function(t,e,s){return i.f(t,e,n(1,s))}:function(t,e,s){return t[e]=s,t}},"2bba":function(t,e,s){var r=s("ac83");t.exports=function(t,e,s,i){try{return i?e(r(s)[0],s[1]):e(s)}catch(a){var n=t["return"];throw void 0!==n&&r(n.call(t)),a}}},"30c9":function(t,e,s){var r=s("57c4"),i=r("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(s){try{return e[i]=!1,"/./"[t](e)}catch(r){}}return!1}},3109:function(t,e,s){var r=s("d5dc"),i=s("527d"),n=r.WeakMap;t.exports="function"===typeof n&&/native code/.test(i(n))},3132:function(t,e,s){var r=s("d68d"),i=s("a8c9"),n=s("57c4"),a=n("species");t.exports=function(t,e){var s;return i(t)&&(s=t.constructor,"function"!=typeof s||s!==Array&&!i(s.prototype)?r(s)&&(s=s[a],null===s&&(s=void 0)):s=void 0),new(void 0===s?Array:s)(0===e?0:e)}},3193:function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on "+t);return t}},3303:function(t,e,s){var r=s("f240"),i=s("3193"),n=function(t){return function(e,s){var n,a,o=String(i(e)),c=r(s),h=o.length;return c<0||c>=h?t?"":void 0:(n=o.charCodeAt(c),n<55296||n>56319||c+1===h||(a=o.charCodeAt(c+1))<56320||a>57343?t?o.charAt(c):n:t?o.slice(c,c+2):a-56320+(n-55296<<10)+65536)}};t.exports={codeAt:n(!1),charAt:n(!0)}},"33c4":function(t,e,s){"use strict";var r=s("91fe"),i=s("407d").findIndex,n=s("5751"),a=s("6885"),o="findIndex",c=!0,h=a(o);o in[]&&Array(1)[o]((function(){c=!1})),r({target:"Array",proto:!0,forced:c||!h},{findIndex:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),n(o)},"354c":function(t,e,s){"use strict";var r={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,n=i&&!r.call({1:2},1);e.f=n?function(t){var e=i(this,t);return!!e&&e.enumerable}:r},"3a20":function(t,e,s){var r=s("efd1"),i=s("3d8a"),n=s("3f8e");r||i(Object.prototype,"toString",n,{unsafe:!0})},"3d8a":function(t,e,s){var r=s("d5dc"),i=s("2ba5"),n=s("f28d"),a=s("200e"),o=s("527d"),c=s("d0e2"),h=c.get,l=c.enforce,p=String(String).split("String");(t.exports=function(t,e,s,o){var c=!!o&&!!o.unsafe,h=!!o&&!!o.enumerable,u=!!o&&!!o.noTargetGet;"function"==typeof s&&("string"!=typeof e||n(s,"name")||i(s,"name",e),l(s).source=p.join("string"==typeof e?e:"")),t!==r?(c?!u&&t[e]&&(h=!0):delete t[e],h?t[e]=s:i(t,e,s)):h?t[e]=s:a(e,s)})(Function.prototype,"toString",(function(){return"function"==typeof this&&h(this).source||o(this)}))},"3e5e":function(t,e,s){"use strict";var r=s("deaa"),i=s("e1dd"),n=s("ac83"),a=s("3193"),o=s("fb8e"),c=s("536c"),h=s("684e"),l=s("81a0"),p=s("21d4"),u=s("f30e"),d=[].push,f=Math.min,m=4294967295,y=!u((function(){return!RegExp(m,"y")}));r("split",2,(function(t,e,s){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,s){var r=String(a(this)),n=void 0===s?m:s>>>0;if(0===n)return[];if(void 0===t)return[r];if(!i(t))return e.call(r,t,n);var o,c,h,l=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),f=0,y=new RegExp(t.source,u+"g");while(o=p.call(y,r)){if(c=y.lastIndex,c>f&&(l.push(r.slice(f,o.index)),o.length>1&&o.index<r.length&&d.apply(l,o.slice(1)),h=o[0].length,f=c,l.length>=n))break;y.lastIndex===o.index&&y.lastIndex++}return f===r.length?!h&&y.test("")||l.push(""):l.push(r.slice(f)),l.length>n?l.slice(0,n):l}:"0".split(void 0,0).length?function(t,s){return void 0===t&&0===s?[]:e.call(this,t,s)}:e,[function(e,s){var i=a(this),n=void 0==e?void 0:e[t];return void 0!==n?n.call(e,i,s):r.call(String(i),e,s)},function(t,i){var a=s(r,t,this,i,r!==e);if(a.done)return a.value;var p=n(t),u=String(this),d=o(p,RegExp),g=p.unicode,x=(p.ignoreCase?"i":"")+(p.multiline?"m":"")+(p.unicode?"u":"")+(y?"y":"g"),b=new d(y?p:"^(?:"+p.source+")",x),v=void 0===i?m:i>>>0;if(0===v)return[];if(0===u.length)return null===l(b,u)?[u]:[];var w=0,P=0,T=[];while(P<u.length){b.lastIndex=y?P:0;var E,A=l(b,y?u:u.slice(P));if(null===A||(E=f(h(b.lastIndex+(y?0:P)),u.length))===w)P=c(u,P,g);else{if(T.push(u.slice(w,P)),T.length===v)return T;for(var S=1;S<=A.length-1;S++)if(T.push(A[S]),T.length===v)return T;P=w=E}}return T.push(u.slice(w)),T}]}),!y)},"3f36":function(t,e,s){var r=s("91fe"),i=s("f30e"),n=s("8c47"),a=s("4aef").f,o=s("7a23"),c=i((function(){a(1)})),h=!o||c;r({target:"Object",stat:!0,forced:h,sham:!o},{getOwnPropertyDescriptor:function(t,e){return a(n(t),e)}})},"3f8e":function(t,e,s){"use strict";var r=s("efd1"),i=s("9552");t.exports=r?{}.toString:function(){return"[object "+i(this)+"]"}},4045:function(t,e,s){"use strict";var r=s("91fe"),i=s("6be9").indexOf,n=s("fb11"),a=s("6885"),o=[].indexOf,c=!!o&&1/[1].indexOf(1,-0)<0,h=n("indexOf"),l=a("indexOf",{ACCESSORS:!0,1:0});r({target:"Array",proto:!0,forced:c||!h||!l},{indexOf:function(t){return c?o.apply(this,arguments)||0:i(this,t,arguments.length>1?arguments[1]:void 0)}})},"407d":function(t,e,s){var r=s("0b29"),i=s("fee7"),n=s("ee6f"),a=s("684e"),o=s("3132"),c=[].push,h=function(t){var e=1==t,s=2==t,h=3==t,l=4==t,p=6==t,u=5==t||p;return function(d,f,m,y){for(var g,x,b=n(d),v=i(b),w=r(f,m,3),P=a(v.length),T=0,E=y||o,A=e?E(d,P):s?E(d,0):void 0;P>T;T++)if((u||T in v)&&(g=v[T],x=w(g,T,b),t))if(e)A[T]=x;else if(x)switch(t){case 3:return!0;case 5:return g;case 6:return T;case 2:c.call(A,g)}else if(l)return!1;return p?-1:h||l?l:A}};t.exports={forEach:h(0),map:h(1),filter:h(2),some:h(3),every:h(4),find:h(5),findIndex:h(6)}},"40d4":function(t,e){t.exports=function(t,e,s){if(!(t instanceof e))throw TypeError("Incorrect "+(s?s+" ":"")+"invocation");return t}},"416d":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=!0,i=!0,n=!0,a=!0,o=!0,c=!0;class h{constructor(t,e={}){this.label=t,this.keyword=e.keyword,this.beforeExpr=!!e.beforeExpr,this.startsExpr=!!e.startsExpr,this.rightAssociative=!!e.rightAssociative,this.isLoop=!!e.isLoop,this.isAssign=!!e.isAssign,this.prefix=!!e.prefix,this.postfix=!!e.postfix,this.binop=null!=e.binop?e.binop:null,this.updateContext=null}}const l=new Map;function p(t,e={}){e.keyword=t;const s=new h(t,e);return l.set(t,s),s}function u(t,e){return new h(t,{beforeExpr:r,binop:e})}const d={num:new h("num",{startsExpr:i}),bigint:new h("bigint",{startsExpr:i}),regexp:new h("regexp",{startsExpr:i}),string:new h("string",{startsExpr:i}),name:new h("name",{startsExpr:i}),eof:new h("eof"),bracketL:new h("[",{beforeExpr:r,startsExpr:i}),bracketHashL:new h("#[",{beforeExpr:r,startsExpr:i}),bracketBarL:new h("[|",{beforeExpr:r,startsExpr:i}),bracketR:new h("]"),bracketBarR:new h("|]"),braceL:new h("{",{beforeExpr:r,startsExpr:i}),braceBarL:new h("{|",{beforeExpr:r,startsExpr:i}),braceHashL:new h("#{",{beforeExpr:r,startsExpr:i}),braceR:new h("}"),braceBarR:new h("|}"),parenL:new h("(",{beforeExpr:r,startsExpr:i}),parenR:new h(")"),comma:new h(",",{beforeExpr:r}),semi:new h(";",{beforeExpr:r}),colon:new h(":",{beforeExpr:r}),doubleColon:new h("::",{beforeExpr:r}),dot:new h("."),question:new h("?",{beforeExpr:r}),questionDot:new h("?."),arrow:new h("=>",{beforeExpr:r}),template:new h("template"),ellipsis:new h("...",{beforeExpr:r}),backQuote:new h("`",{startsExpr:i}),dollarBraceL:new h("${",{beforeExpr:r,startsExpr:i}),at:new h("@"),hash:new h("#",{startsExpr:i}),interpreterDirective:new h("#!..."),eq:new h("=",{beforeExpr:r,isAssign:a}),assign:new h("_=",{beforeExpr:r,isAssign:a}),incDec:new h("++/--",{prefix:o,postfix:c,startsExpr:i}),bang:new h("!",{beforeExpr:r,prefix:o,startsExpr:i}),tilde:new h("~",{beforeExpr:r,prefix:o,startsExpr:i}),pipeline:u("|>",0),nullishCoalescing:u("??",1),logicalOR:u("||",1),logicalAND:u("&&",2),bitwiseOR:u("|",3),bitwiseXOR:u("^",4),bitwiseAND:u("&",5),equality:u("==/!=/===/!==",6),relational:u("</>/<=/>=",7),bitShift:u("<</>>/>>>",8),plusMin:new h("+/-",{beforeExpr:r,binop:9,prefix:o,startsExpr:i}),modulo:new h("%",{beforeExpr:r,binop:10,startsExpr:i}),star:u("*",10),slash:u("/",10),exponent:new h("**",{beforeExpr:r,binop:11,rightAssociative:!0}),_break:p("break"),_case:p("case",{beforeExpr:r}),_catch:p("catch"),_continue:p("continue"),_debugger:p("debugger"),_default:p("default",{beforeExpr:r}),_do:p("do",{isLoop:n,beforeExpr:r}),_else:p("else",{beforeExpr:r}),_finally:p("finally"),_for:p("for",{isLoop:n}),_function:p("function",{startsExpr:i}),_if:p("if"),_return:p("return",{beforeExpr:r}),_switch:p("switch"),_throw:p("throw",{beforeExpr:r,prefix:o,startsExpr:i}),_try:p("try"),_var:p("var"),_const:p("const"),_while:p("while",{isLoop:n}),_with:p("with"),_new:p("new",{beforeExpr:r,startsExpr:i}),_this:p("this",{startsExpr:i}),_super:p("super",{startsExpr:i}),_class:p("class",{startsExpr:i}),_extends:p("extends",{beforeExpr:r}),_export:p("export"),_import:p("import",{startsExpr:i}),_null:p("null",{startsExpr:i}),_true:p("true",{startsExpr:i}),_false:p("false",{startsExpr:i}),_in:p("in",{beforeExpr:r,binop:7}),_instanceof:p("instanceof",{beforeExpr:r,binop:7}),_typeof:p("typeof",{beforeExpr:r,prefix:o,startsExpr:i}),_void:p("void",{beforeExpr:r,prefix:o,startsExpr:i}),_delete:p("delete",{beforeExpr:r,prefix:o,startsExpr:i})},f=0,m=1,y=2,g=4,x=8,b=16,v=32,w=64,P=128,T=m|y|P,E=1,A=2,S=4,C=8,k=16,N=64,I=128,O=256,D=512,M=1024,L=E|A|C|I,_=0|E|C|0,R=0|E|S|0,j=0|E|k|0,F=0|A|I,B=0|A,U=E|A|C|O,q=0|M,V=0|N,z=0|E|N,H=U|D,W=0|M,K=4,$=2,X=1,G=$|X,Y=$|K,J=X|K,Q=$,Z=X,tt=0,et=/\r\n?|[\n\u2028\u2029]/,st=new RegExp(et.source,"g");function rt(t){switch(t){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}const it=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g;function nt(t){switch(t){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}class at{constructor(t,e){this.line=t,this.column=e}}class ot{constructor(t,e){this.start=t,this.end=e}}function ct(t,e){let s,r=1,i=0;st.lastIndex=0;while((s=st.exec(t))&&s.index<e)r++,i=st.lastIndex;return new at(r,e-i)}class ht{constructor(){this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}hasPlugin(t){return this.plugins.has(t)}getPluginOption(t,e){if(this.hasPlugin(t))return this.plugins.get(t)[e]}}function lt(t){return t[t.length-1]}class pt extends ht{addComment(t){this.filename&&(t.loc.filename=this.filename),this.state.trailingComments.push(t),this.state.leadingComments.push(t)}adjustCommentsAfterTrailingComma(t,e,s){if(0===this.state.leadingComments.length)return;let r=null,i=e.length;while(null===r&&i>0)r=e[--i];if(null===r)return;for(let a=0;a<this.state.leadingComments.length;a++)this.state.leadingComments[a].end<this.state.commentPreviousNode.end&&(this.state.leadingComments.splice(a,1),a--);const n=[];for(let a=0;a<this.state.leadingComments.length;a++){const e=this.state.leadingComments[a];e.end<t.end?(n.push(e),s||(this.state.leadingComments.splice(a,1),a--)):(void 0===t.trailingComments&&(t.trailingComments=[]),t.trailingComments.push(e))}s&&(this.state.leadingComments=[]),n.length>0?r.trailingComments=n:void 0!==r.trailingComments&&(r.trailingComments=[])}processComment(t){if("Program"===t.type&&t.body.length>0)return;const e=this.state.commentStack;let s,r,i,n,a;if(this.state.trailingComments.length>0)this.state.trailingComments[0].start>=t.end?(i=this.state.trailingComments,this.state.trailingComments=[]):this.state.trailingComments.length=0;else if(e.length>0){const s=lt(e);s.trailingComments&&s.trailingComments[0].start>=t.end&&(i=s.trailingComments,delete s.trailingComments)}e.length>0&&lt(e).start>=t.start&&(s=e.pop());while(e.length>0&&lt(e).start>=t.start)r=e.pop();if(!r&&s&&(r=s),s)switch(t.type){case"ObjectExpression":this.adjustCommentsAfterTrailingComma(t,t.properties);break;case"ObjectPattern":this.adjustCommentsAfterTrailingComma(t,t.properties,!0);break;case"CallExpression":this.adjustCommentsAfterTrailingComma(t,t.arguments);break;case"ArrayExpression":this.adjustCommentsAfterTrailingComma(t,t.elements);break;case"ArrayPattern":this.adjustCommentsAfterTrailingComma(t,t.elements,!0);break}else this.state.commentPreviousNode&&("ImportSpecifier"===this.state.commentPreviousNode.type&&"ImportSpecifier"!==t.type||"ExportSpecifier"===this.state.commentPreviousNode.type&&"ExportSpecifier"!==t.type)&&this.adjustCommentsAfterTrailingComma(t,[this.state.commentPreviousNode]);if(r){if(r.leadingComments)if(r!==t&&r.leadingComments.length>0&&lt(r.leadingComments).end<=t.start)t.leadingComments=r.leadingComments,delete r.leadingComments;else for(n=r.leadingComments.length-2;n>=0;--n)if(r.leadingComments[n].end<=t.start){t.leadingComments=r.leadingComments.splice(0,n+1);break}}else if(this.state.leadingComments.length>0)if(lt(this.state.leadingComments).end<=t.start){if(this.state.commentPreviousNode)for(a=0;a<this.state.leadingComments.length;a++)this.state.leadingComments[a].end<this.state.commentPreviousNode.end&&(this.state.leadingComments.splice(a,1),a--);this.state.leadingComments.length>0&&(t.leadingComments=this.state.leadingComments,this.state.leadingComments=[])}else{for(n=0;n<this.state.leadingComments.length;n++)if(this.state.leadingComments[n].end>t.start)break;const e=this.state.leadingComments.slice(0,n);e.length&&(t.leadingComments=e),i=this.state.leadingComments.slice(n),0===i.length&&(i=null)}this.state.commentPreviousNode=t,i&&(i.length&&i[0].start>=t.start&&lt(i).end<=t.end?t.innerComments=i:t.trailingComments=i),e.push(t)}}const ut=Object.freeze({ArgumentsDisallowedInInitializer:"'arguments' is not allowed in class field initializer",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function",AwaitExpressionFormalParameter:"await is not allowed in async function parameters",AwaitNotInAsyncFunction:"Can not use keyword 'await' outside an async function",BadGetterArity:"getter must not have any formal parameters",BadSetterArity:"setter must have exactly one formal parameter",BadSetterRestParameter:"setter function argument must not be a rest parameter",ConstructorClassField:"Classes may not have a field named 'constructor'",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'",ConstructorIsAccessor:"Class constructor may not be an accessor",ConstructorIsAsync:"Constructor can't be an async function",ConstructorIsGenerator:"Constructor can't be a generator",DeclarationMissingInitializer:"%0 require an initialization value",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. You can set the 'decoratorsBeforeExport' option to false to use the 'export @decorator class {}' syntax",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Using the export keyword between a decorator and a class is not allowed. Please use `export @dec class` instead.",DecoratorSemicolon:"Decorators must not be followed by a semicolon",DeletePrivateField:"Deleting a private field is not allowed",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:"`%0` has already been exported. Exported identifiers must be unique.",DuplicateProto:"Redefinition of __proto__ property",DuplicateRegExpFlags:"Duplicate regular expression flag",ElementAfterRest:"Rest element must be last element",EscapedCharNotAnIdentifier:"Invalid Unicode escape",ForInOfLoopInitializer:"%0 loop variable declaration may not have an initializer",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block",IllegalBreakContinue:"Unsyntactic %0",IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list",IllegalReturn:"'return' outside of function",ImportCallArgumentTrailingComma:"Trailing comma is disallowed inside import(...) arguments",ImportCallArity:"import() requires exactly one argument",ImportCallArityLtOne:"Dynamic imports require a parameter: import('a.js')",ImportCallNotNewExpression:"Cannot use new with import(...)",ImportCallSpreadArgument:"... is not allowed in import()",ImportMetaOutsideModule:"import.meta may appear only with 'sourceType: \"module\"'",ImportOutsideModule:"'import' and 'export' may appear only with 'sourceType: \"module\"'",InvalidCodePoint:"Code point out of bounds",InvalidDigit:"Expected number in radix %0",InvalidEscapeSequence:"Bad character escape sequence",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template",InvalidEscapedReservedWord:"Escape sequence in keyword %0",InvalidIdentifier:"Invalid identifier %0",InvalidLhs:"Invalid left-hand side in %0",InvalidLhsBinding:"Binding invalid left-hand side in %0",InvalidNumber:"Invalid number",InvalidOrUnexpectedToken:"Unexpected character '%0'",InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern",InvalidPrivateFieldResolution:"Private name #%0 is not defined",InvalidPropertyBindingPattern:"Binding member expression",InvalidRestAssignmentPattern:"Invalid rest operator's argument",LabelRedeclaration:"Label '%0' is already declared",LetInLexicalBinding:"'let' is not allowed to be used as a name in 'let' or 'const' declarations.",MalformedRegExpFlags:"Invalid regular expression flag",MissingClassName:"A class name is required",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators",ModuleExportUndefined:"Export '%0' is not defined",MultipleDefaultsInSwitch:"Multiple default clauses",NewlineAfterThrow:"Illegal newline after throw",NoCatchOrFinally:"Missing catch or finally clause",NumberIdentifier:"Identifier directly after number",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences",ObsoleteAwaitStar:"await* has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"constructors in/after an Optional Chain are not allowed",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain",ParamDupe:"Argument name clash",PatternHasAccessor:"Object pattern can't contain getter or setter",PatternHasMethod:"Object pattern can't contain methods",PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding",PrimaryTopicRequiresSmartPipeline:"Primary Topic Reference found but pipelineOperator not passed 'smart' for 'proposal' option.",PrivateNameRedeclaration:"Duplicate private name #%0",RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'",RestTrailingComma:"Unexpected trailing comma after rest element",SloppyFunction:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement",StaticPrototype:"Classes may not have static property named prototype",StrictDelete:"Deleting local variable in strict mode",StrictEvalArguments:"Assigning to '%0' in strict mode",StrictEvalArgumentsBinding:"Binding '%0' in strict mode",StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode",StrictWith:"'with' in strict mode",SuperNotAllowed:"super() is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super",TrailingDecorator:"Decorators must be attached to a class element",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal',UnexpectedDigitAfterHash:"Unexpected digit after hash token",UnexpectedImportExport:"'import' and 'export' may only appear at the top level",UnexpectedKeyword:"Unexpected keyword '%0'",UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context",UnexpectedNewTarget:"new.target can only be used in functions",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits",UnexpectedPrivateField:"Private names can only be used as the name of a class element (i.e. class C { #p = 42; #m() {} } )\n or a property of member expression (i.e. this.#p).",UnexpectedReservedWord:"Unexpected reserved word '%0'",UnexpectedSuper:"super is only allowed in object methods and classes",UnexpectedToken:"Unexpected token '%'",UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"import can only be used in import() or import.meta",UnsupportedMetaProperty:"The only valid meta property for %0 is %0.%1",UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties",UnsupportedSuper:"super can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop])",UnterminatedComment:"Unterminated comment",UnterminatedRegExp:"Unterminated regular expression",UnterminatedString:"Unterminated string constant",UnterminatedTemplate:"Unterminated template",VarRedeclaration:"Identifier '%0' has already been declared",YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator",YieldInParameter:"yield is not allowed in generator parameters",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0"});class dt extends pt{getLocationForPosition(t){let e;return e=t===this.state.start?this.state.startLoc:t===this.state.lastTokStart?this.state.lastTokStartLoc:t===this.state.end?this.state.endLoc:t===this.state.lastTokEnd?this.state.lastTokEndLoc:ct(this.input,t),e}raise(t,e,...s){return this.raiseWithData(t,void 0,e,...s)}raiseWithData(t,e,s,...r){const i=this.getLocationForPosition(t),n=s.replace(/%(\d+)/g,(t,e)=>r[e])+` (${i.line}:${i.column})`;return this._raise(Object.assign({loc:i,pos:t},e),n)}_raise(t,e){const s=new SyntaxError(e);if(Object.assign(s,t),this.options.errorRecovery)return this.isLookahead||this.state.errors.push(s),s;throw s}}function ft(t){return null!=t&&"Property"===t.type&&"init"===t.kind&&!1===t.method}var mt=t=>class extends t{estreeParseRegExpLiteral({pattern:t,flags:e}){let s=null;try{s=new RegExp(t,e)}catch(i){}const r=this.estreeParseLiteral(s);return r.regex={pattern:t,flags:e},r}estreeParseBigIntLiteral(t){const e="undefined"!==typeof BigInt?BigInt(t):null,s=this.estreeParseLiteral(e);return s.bigint=String(s.value||t),s}estreeParseLiteral(t){return this.parseLiteral(t,"Literal")}directiveToStmt(t){const e=t.value,s=this.startNodeAt(t.start,t.loc.start),r=this.startNodeAt(e.start,e.loc.start);return r.value=e.value,r.raw=e.extra.raw,s.expression=this.finishNodeAt(r,"Literal",e.end,e.loc.end),s.directive=e.extra.raw.slice(1,-1),this.finishNodeAt(s,"ExpressionStatement",t.end,t.loc.end)}initFunction(t,e){super.initFunction(t,e),t.expression=!1}checkDeclaration(t){ft(t)?this.checkDeclaration(t.value):super.checkDeclaration(t)}checkGetterSetterParams(t){const e=t,s="get"===e.kind?0:1,r=e.start;e.value.params.length!==s?"get"===t.kind?this.raise(r,ut.BadGetterArity):this.raise(r,ut.BadSetterArity):"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raise(r,ut.BadSetterRestParameter)}checkLVal(t,e=V,s,r,i){switch(t.type){case"ObjectPattern":t.properties.forEach(t=>{this.checkLVal("Property"===t.type?t.value:t,e,s,"object destructuring pattern",i)});break;default:super.checkLVal(t,e,s,r,i)}}checkDuplicatedProto(t,e,s){if("SpreadElement"===t.type||t.computed||t.method||t.shorthand)return;const r=t.key,i="Identifier"===r.type?r.name:String(r.value);"__proto__"===i&&"init"===t.kind&&(e.used&&(s&&-1===s.doubleProto?s.doubleProto=r.start:this.raise(r.start,ut.DuplicateProto)),e.used=!0)}isValidDirective(t){return"ExpressionStatement"===t.type&&"Literal"===t.expression.type&&"string"===typeof t.expression.value&&(!t.expression.extra||!t.expression.extra.parenthesized)}stmtToDirective(t){const e=super.stmtToDirective(t),s=t.expression.value;return e.value.value=s,e}parseBlockBody(t,e,s,r){super.parseBlockBody(t,e,s,r);const i=t.directives.map(t=>this.directiveToStmt(t));t.body=i.concat(t.body),delete t.directives}pushClassMethod(t,e,s,r,i,n){this.parseMethod(e,s,r,i,n,"ClassMethod",!0),e.typeParameters&&(e.value.typeParameters=e.typeParameters,delete e.typeParameters),t.body.push(e)}parseExprAtom(t){switch(this.state.type){case d.num:case d.string:return this.estreeParseLiteral(this.state.value);case d.regexp:return this.estreeParseRegExpLiteral(this.state.value);case d.bigint:return this.estreeParseBigIntLiteral(this.state.value);case d._null:return this.estreeParseLiteral(null);case d._true:return this.estreeParseLiteral(!0);case d._false:return this.estreeParseLiteral(!1);default:return super.parseExprAtom(t)}}parseLiteral(t,e,s,r){const i=super.parseLiteral(t,e,s,r);return i.raw=i.extra.raw,delete i.extra,i}parseFunctionBody(t,e,s=!1){super.parseFunctionBody(t,e,s),t.expression="BlockStatement"!==t.body.type}parseMethod(t,e,s,r,i,n,a=!1){let o=this.startNode();return o.kind=t.kind,o=super.parseMethod(o,e,s,r,i,n,a),o.type="FunctionExpression",delete o.kind,t.value=o,n="ClassMethod"===n?"MethodDefinition":n,this.finishNode(t,n)}parseObjectMethod(t,e,s,r,i){const n=super.parseObjectMethod(t,e,s,r,i);return n&&(n.type="Property","method"===n.kind&&(n.kind="init"),n.shorthand=!1),n}parseObjectProperty(t,e,s,r,i){const n=super.parseObjectProperty(t,e,s,r,i);return n&&(n.kind="init",n.type="Property"),n}toAssignable(t){return ft(t)?(this.toAssignable(t.value),t):super.toAssignable(t)}toAssignableObjectExpressionProp(t,e){if("get"===t.kind||"set"===t.kind)throw this.raise(t.key.start,ut.PatternHasAccessor);if(t.method)throw this.raise(t.key.start,ut.PatternHasMethod);super.toAssignableObjectExpressionProp(t,e)}finishCallExpression(t,e){return super.finishCallExpression(t,e),"Import"===t.callee.type&&(t.type="ImportExpression",t.source=t.arguments[0],delete t.arguments,delete t.callee),t}toReferencedListDeep(t,e){t&&super.toReferencedListDeep(t,e)}parseExport(t){switch(super.parseExport(t),t.type){case"ExportAllDeclaration":t.exported=null;break;case"ExportNamedDeclaration":1===t.specifiers.length&&"ExportNamespaceSpecifier"===t.specifiers[0].type&&(t.type="ExportAllDeclaration",t.exported=t.specifiers[0].exported,delete t.specifiers);break}return t}};class yt{constructor(t,e,s,r){this.token=t,this.isExpr=!!e,this.preserveSpace=!!s,this.override=r}}const gt={braceStatement:new yt("{",!1),braceExpression:new yt("{",!0),templateQuasi:new yt("${",!1),parenStatement:new yt("(",!1),parenExpression:new yt("(",!0),template:new yt("`",!0,!0,t=>t.readTmplToken()),functionExpression:new yt("function",!0),functionStatement:new yt("function",!1)};d.parenR.updateContext=d.braceR.updateContext=function(){if(1===this.state.context.length)return void(this.state.exprAllowed=!0);let t=this.state.context.pop();t===gt.braceStatement&&"function"===this.curContext().token&&(t=this.state.context.pop()),this.state.exprAllowed=!t.isExpr},d.name.updateContext=function(t){let e=!1;t!==d.dot&&("of"===this.state.value&&!this.state.exprAllowed||"yield"===this.state.value&&this.prodParam.hasYield)&&(e=!0),this.state.exprAllowed=e,this.state.isIterator&&(this.state.isIterator=!1)},d.braceL.updateContext=function(t){this.state.context.push(this.braceIsBlock(t)?gt.braceStatement:gt.braceExpression),this.state.exprAllowed=!0},d.dollarBraceL.updateContext=function(){this.state.context.push(gt.templateQuasi),this.state.exprAllowed=!0},d.parenL.updateContext=function(t){const e=t===d._if||t===d._for||t===d._with||t===d._while;this.state.context.push(e?gt.parenStatement:gt.parenExpression),this.state.exprAllowed=!0},d.incDec.updateContext=function(){},d._function.updateContext=d._class.updateContext=function(t){!t.beforeExpr||t===d.semi||t===d._else||t===d._return&&et.test(this.input.slice(this.state.lastTokEnd,this.state.start))||(t===d.colon||t===d.braceL)&&this.curContext()===gt.b_stat?this.state.context.push(gt.functionStatement):this.state.context.push(gt.functionExpression),this.state.exprAllowed=!1},d.backQuote.updateContext=function(){this.curContext()===gt.template?this.state.context.pop():this.state.context.push(gt.template),this.state.exprAllowed=!1};let xt="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",bt="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿";const vt=new RegExp("["+xt+"]"),wt=new RegExp("["+xt+bt+"]");xt=bt=null;const Pt=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,107,20,28,22,13,52,76,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8952,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42717,35,4148,12,221,3,5761,15,7472,3104,541,1507,4938],Tt=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,4759,9,787719,239];function Et(t,e){let s=65536;for(let r=0,i=e.length;r<i;r+=2){if(s+=e[r],s>t)return!1;if(s+=e[r+1],s>=t)return!0}return!1}function At(t){return t<65?36===t:t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&vt.test(String.fromCharCode(t)):Et(t,Pt)))}function St(t){return t<48?36===t:t<58||!(t<65)&&(t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&wt.test(String.fromCharCode(t)):Et(t,Pt)||Et(t,Tt))))}const Ct={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},kt=new Set(Ct.keyword),Nt=new Set(Ct.strict),It=new Set(Ct.strictBind);function Ot(t,e){return e&&"await"===t||"enum"===t}function Dt(t,e){return Ot(t,e)||Nt.has(t)}function Mt(t){return It.has(t)}function Lt(t,e){return Dt(t,e)||Mt(t)}function _t(t){return kt.has(t)}const Rt=/^in(stanceof)?$/;function jt(t,e){return 64===t&&64===e}const Ft=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),Bt=Object.freeze({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module",AssignReservedType:"Cannot overwrite reserved type %0",DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement",EnumBooleanMemberNotInitialized:"Boolean enum members need to be initialized. Use either `%0 = true,` or `%0 = false,` in enum `%1`.",EnumDuplicateMemberName:"Enum member names need to be unique, but the name `%0` has already been used before in enum `%1`.",EnumInconsistentMemberValues:"Enum `%0` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.",EnumInvalidExplicitType:"Enum type `%1` is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `%0`.",EnumInvalidExplicitTypeUnknownSupplied:"Supplied enum type is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `%0`.",EnumInvalidMemberInitializerPrimaryType:"Enum `%0` has type `%2`, so the initializer of `%1` needs to be a %2 literal.",EnumInvalidMemberInitializerSymbolType:"Symbol enum members cannot be initialized. Use `%1,` in enum `%0`.",EnumInvalidMemberInitializerUnknownType:"The enum member initializer for `%1` needs to be a literal (either a boolean, number, or string) in enum `%0`.",EnumInvalidMemberName:"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using `%0`, consider using `%1`, in enum `%2`.",EnumNumberMemberNotInitialized:"Number enum members need to be initialized, e.g. `%1 = 1` in enum `%0`.",EnumStringMemberInconsistentlyInitailized:"String enum members need to consistently either all use initializers, or use no initializers, in enum `%0`.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions",InexactVariance:"Explicit inexact syntax cannot have variance",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`",NestedFlowComment:"Cannot have a flow comment inside another flow comment",OptionalBindingPattern:"A binding pattern parameter cannot be optional in an implementation signature.",SpreadVariance:"Spread properties cannot have variance",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object",UnexpectedReservedType:"Unexpected reserved type %0",UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint"',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration",UnsupportedDeclareExportKind:"`declare export %0` is not supported. Use `%1` instead",UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module",UnterminatedFlowComment:"Unterminated flow-comment"});function Ut(t){return"DeclareExportAllDeclaration"===t.type||"DeclareExportDeclaration"===t.type&&(!t.declaration||"TypeAlias"!==t.declaration.type&&"InterfaceDeclaration"!==t.declaration.type)}function qt(t){return"type"===t.importKind||"typeof"===t.importKind}function Vt(t){return(t.type===d.name||!!t.type.keyword)&&"from"!==t.value}const zt={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"};function Ht(t,e){const s=[],r=[];for(let i=0;i<t.length;i++)(e(t[i],i,t)?s:r).push(t[i]);return[s,r]}const Wt=/\*?\s*@((?:no)?flow)\b/;var Kt=t=>class extends t{constructor(t,e){super(t,e),this.flowPragma=void 0}shouldParseTypes(){return this.getPluginOption("flow","all")||"flow"===this.flowPragma}shouldParseEnums(){return!!this.getPluginOption("flow","enums")}finishToken(t,e){return t!==d.string&&t!==d.semi&&t!==d.interpreterDirective&&void 0===this.flowPragma&&(this.flowPragma=null),super.finishToken(t,e)}addComment(t){if(void 0===this.flowPragma){const e=Wt.exec(t.value);if(e)if("flow"===e[1])this.flowPragma="flow";else{if("noflow"!==e[1])throw new Error("Unexpected flow pragma");this.flowPragma="noflow"}else;}return super.addComment(t)}flowParseTypeInitialiser(t){const e=this.state.inType;this.state.inType=!0,this.expect(t||d.colon);const s=this.flowParseType();return this.state.inType=e,s}flowParsePredicate(){const t=this.startNode(),e=this.state.startLoc,s=this.state.start;this.expect(d.modulo);const r=this.state.startLoc;return this.expectContextual("checks"),e.line===r.line&&e.column===r.column-1||this.raise(s,Bt.UnexpectedSpaceBetweenModuloChecks),this.eat(d.parenL)?(t.value=this.parseExpression(),this.expect(d.parenR),this.finishNode(t,"DeclaredPredicate")):this.finishNode(t,"InferredPredicate")}flowParseTypeAndPredicateInitialiser(){const t=this.state.inType;this.state.inType=!0,this.expect(d.colon);let e=null,s=null;return this.match(d.modulo)?(this.state.inType=t,s=this.flowParsePredicate()):(e=this.flowParseType(),this.state.inType=t,this.match(d.modulo)&&(s=this.flowParsePredicate())),[e,s]}flowParseDeclareClass(t){return this.next(),this.flowParseInterfaceish(t,!0),this.finishNode(t,"DeclareClass")}flowParseDeclareFunction(t){this.next();const e=t.id=this.parseIdentifier(),s=this.startNode(),r=this.startNode();this.isRelational("<")?s.typeParameters=this.flowParseTypeParameterDeclaration():s.typeParameters=null,this.expect(d.parenL);const i=this.flowParseFunctionTypeParams();return s.params=i.params,s.rest=i.rest,this.expect(d.parenR),[s.returnType,t.predicate]=this.flowParseTypeAndPredicateInitialiser(),r.typeAnnotation=this.finishNode(s,"FunctionTypeAnnotation"),e.typeAnnotation=this.finishNode(r,"TypeAnnotation"),this.resetEndLocation(e),this.semicolon(),this.finishNode(t,"DeclareFunction")}flowParseDeclare(t,e){if(this.match(d._class))return this.flowParseDeclareClass(t);if(this.match(d._function))return this.flowParseDeclareFunction(t);if(this.match(d._var))return this.flowParseDeclareVariable(t);if(this.eatContextual("module"))return this.match(d.dot)?this.flowParseDeclareModuleExports(t):(e&&this.raise(this.state.lastTokStart,Bt.NestedDeclareModule),this.flowParseDeclareModule(t));if(this.isContextual("type"))return this.flowParseDeclareTypeAlias(t);if(this.isContextual("opaque"))return this.flowParseDeclareOpaqueType(t);if(this.isContextual("interface"))return this.flowParseDeclareInterface(t);if(this.match(d._export))return this.flowParseDeclareExportDeclaration(t,e);throw this.unexpected()}flowParseDeclareVariable(t){return this.next(),t.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(t.id.name,R,t.id.start),this.semicolon(),this.finishNode(t,"DeclareVariable")}flowParseDeclareModule(t){this.scope.enter(f),this.match(d.string)?t.id=this.parseExprAtom():t.id=this.parseIdentifier();const e=t.body=this.startNode(),s=e.body=[];this.expect(d.braceL);while(!this.match(d.braceR)){let t=this.startNode();this.match(d._import)?(this.next(),this.isContextual("type")||this.match(d._typeof)||this.raise(this.state.lastTokStart,Bt.InvalidNonTypeImportInDeclareModule),this.parseImport(t)):(this.expectContextual("declare",Bt.UnsupportedStatementInDeclareModule),t=this.flowParseDeclare(t,!0)),s.push(t)}this.scope.exit(),this.expect(d.braceR),this.finishNode(e,"BlockStatement");let r=null,i=!1;return s.forEach(t=>{Ut(t)?("CommonJS"===r&&this.raise(t.start,Bt.AmbiguousDeclareModuleKind),r="ES"):"DeclareModuleExports"===t.type&&(i&&this.raise(t.start,Bt.DuplicateDeclareModuleExports),"ES"===r&&this.raise(t.start,Bt.AmbiguousDeclareModuleKind),r="CommonJS",i=!0)}),t.kind=r||"CommonJS",this.finishNode(t,"DeclareModule")}flowParseDeclareExportDeclaration(t,e){if(this.expect(d._export),this.eat(d._default))return this.match(d._function)||this.match(d._class)?t.declaration=this.flowParseDeclare(this.startNode()):(t.declaration=this.flowParseType(),this.semicolon()),t.default=!0,this.finishNode(t,"DeclareExportDeclaration");if(this.match(d._const)||this.isLet()||(this.isContextual("type")||this.isContextual("interface"))&&!e){const t=this.state.value,e=zt[t];throw this.raise(this.state.start,Bt.UnsupportedDeclareExportKind,t,e)}if(this.match(d._var)||this.match(d._function)||this.match(d._class)||this.isContextual("opaque"))return t.declaration=this.flowParseDeclare(this.startNode()),t.default=!1,this.finishNode(t,"DeclareExportDeclaration");if(this.match(d.star)||this.match(d.braceL)||this.isContextual("interface")||this.isContextual("type")||this.isContextual("opaque"))return t=this.parseExport(t),"ExportNamedDeclaration"===t.type&&(t.type="ExportDeclaration",t.default=!1,delete t.exportKind),t.type="Declare"+t.type,t;throw this.unexpected()}flowParseDeclareModuleExports(t){return this.next(),this.expectContextual("exports"),t.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(t,"DeclareModuleExports")}flowParseDeclareTypeAlias(t){return this.next(),this.flowParseTypeAlias(t),t.type="DeclareTypeAlias",t}flowParseDeclareOpaqueType(t){return this.next(),this.flowParseOpaqueType(t,!0),t.type="DeclareOpaqueType",t}flowParseDeclareInterface(t){return this.next(),this.flowParseInterfaceish(t),this.finishNode(t,"DeclareInterface")}flowParseInterfaceish(t,e=!1){if(t.id=this.flowParseRestrictedIdentifier(!e,!0),this.scope.declareName(t.id.name,e?j:_,t.id.start),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.extends=[],t.implements=[],t.mixins=[],this.eat(d._extends))do{t.extends.push(this.flowParseInterfaceExtends())}while(!e&&this.eat(d.comma));if(this.isContextual("mixins")){this.next();do{t.mixins.push(this.flowParseInterfaceExtends())}while(this.eat(d.comma))}if(this.isContextual("implements")){this.next();do{t.implements.push(this.flowParseInterfaceExtends())}while(this.eat(d.comma))}t.body=this.flowParseObjectType({allowStatic:e,allowExact:!1,allowSpread:!1,allowProto:e,allowInexact:!1})}flowParseInterfaceExtends(){const t=this.startNode();return t.id=this.flowParseQualifiedTypeIdentifier(),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterInstantiation():t.typeParameters=null,this.finishNode(t,"InterfaceExtends")}flowParseInterface(t){return this.flowParseInterfaceish(t),this.finishNode(t,"InterfaceDeclaration")}checkNotUnderscore(t){"_"===t&&this.raise(this.state.start,Bt.UnexpectedReservedUnderscore)}checkReservedType(t,e,s){Ft.has(t)&&this.raise(e,s?Bt.AssignReservedType:Bt.UnexpectedReservedType,t)}flowParseRestrictedIdentifier(t,e){return this.checkReservedType(this.state.value,this.state.start,e),this.parseIdentifier(t)}flowParseTypeAlias(t){return t.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(t.id.name,_,t.id.start),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.right=this.flowParseTypeInitialiser(d.eq),this.semicolon(),this.finishNode(t,"TypeAlias")}flowParseOpaqueType(t,e){return this.expectContextual("type"),t.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(t.id.name,_,t.id.start),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.supertype=null,this.match(d.colon)&&(t.supertype=this.flowParseTypeInitialiser(d.colon)),t.impltype=null,e||(t.impltype=this.flowParseTypeInitialiser(d.eq)),this.semicolon(),this.finishNode(t,"OpaqueType")}flowParseTypeParameter(t=!1){const e=this.state.start,s=this.startNode(),r=this.flowParseVariance(),i=this.flowParseTypeAnnotatableIdentifier();return s.name=i.name,s.variance=r,s.bound=i.typeAnnotation,this.match(d.eq)?(this.eat(d.eq),s.default=this.flowParseType()):t&&this.raise(e,Bt.MissingTypeParamDefault),this.finishNode(s,"TypeParameter")}flowParseTypeParameterDeclaration(){const t=this.state.inType,e=this.startNode();e.params=[],this.state.inType=!0,this.isRelational("<")||this.match(d.jsxTagStart)?this.next():this.unexpected();let s=!1;do{const t=this.flowParseTypeParameter(s);e.params.push(t),t.default&&(s=!0),this.isRelational(">")||this.expect(d.comma)}while(!this.isRelational(">"));return this.expectRelational(">"),this.state.inType=t,this.finishNode(e,"TypeParameterDeclaration")}flowParseTypeParameterInstantiation(){const t=this.startNode(),e=this.state.inType;t.params=[],this.state.inType=!0,this.expectRelational("<");const s=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!1;while(!this.isRelational(">"))t.params.push(this.flowParseType()),this.isRelational(">")||this.expect(d.comma);return this.state.noAnonFunctionType=s,this.expectRelational(">"),this.state.inType=e,this.finishNode(t,"TypeParameterInstantiation")}flowParseTypeParameterInstantiationCallOrNew(){const t=this.startNode(),e=this.state.inType;t.params=[],this.state.inType=!0,this.expectRelational("<");while(!this.isRelational(">"))t.params.push(this.flowParseTypeOrImplicitInstantiation()),this.isRelational(">")||this.expect(d.comma);return this.expectRelational(">"),this.state.inType=e,this.finishNode(t,"TypeParameterInstantiation")}flowParseInterfaceType(){const t=this.startNode();if(this.expectContextual("interface"),t.extends=[],this.eat(d._extends))do{t.extends.push(this.flowParseInterfaceExtends())}while(this.eat(d.comma));return t.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(t,"InterfaceTypeAnnotation")}flowParseObjectPropertyKey(){return this.match(d.num)||this.match(d.string)?this.parseExprAtom():this.parseIdentifier(!0)}flowParseObjectTypeIndexer(t,e,s){return t.static=e,this.lookahead().type===d.colon?(t.id=this.flowParseObjectPropertyKey(),t.key=this.flowParseTypeInitialiser()):(t.id=null,t.key=this.flowParseType()),this.expect(d.bracketR),t.value=this.flowParseTypeInitialiser(),t.variance=s,this.finishNode(t,"ObjectTypeIndexer")}flowParseObjectTypeInternalSlot(t,e){return t.static=e,t.id=this.flowParseObjectPropertyKey(),this.expect(d.bracketR),this.expect(d.bracketR),this.isRelational("<")||this.match(d.parenL)?(t.method=!0,t.optional=!1,t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.start,t.loc.start))):(t.method=!1,this.eat(d.question)&&(t.optional=!0),t.value=this.flowParseTypeInitialiser()),this.finishNode(t,"ObjectTypeInternalSlot")}flowParseObjectTypeMethodish(t){t.params=[],t.rest=null,t.typeParameters=null,this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(d.parenL);while(!this.match(d.parenR)&&!this.match(d.ellipsis))t.params.push(this.flowParseFunctionTypeParam()),this.match(d.parenR)||this.expect(d.comma);return this.eat(d.ellipsis)&&(t.rest=this.flowParseFunctionTypeParam()),this.expect(d.parenR),t.returnType=this.flowParseTypeInitialiser(),this.finishNode(t,"FunctionTypeAnnotation")}flowParseObjectTypeCallProperty(t,e){const s=this.startNode();return t.static=e,t.value=this.flowParseObjectTypeMethodish(s),this.finishNode(t,"ObjectTypeCallProperty")}flowParseObjectType({allowStatic:t,allowExact:e,allowSpread:s,allowProto:r,allowInexact:i}){const n=this.state.inType;this.state.inType=!0;const a=this.startNode();let o,c;a.callProperties=[],a.properties=[],a.indexers=[],a.internalSlots=[];let h=!1;e&&this.match(d.braceBarL)?(this.expect(d.braceBarL),o=d.braceBarR,c=!0):(this.expect(d.braceL),o=d.braceR,c=!1),a.exact=c;while(!this.match(o)){let e=!1,n=null,o=null;const l=this.startNode();if(r&&this.isContextual("proto")){const e=this.lookahead();e.type!==d.colon&&e.type!==d.question&&(this.next(),n=this.state.start,t=!1)}if(t&&this.isContextual("static")){const t=this.lookahead();t.type!==d.colon&&t.type!==d.question&&(this.next(),e=!0)}const p=this.flowParseVariance();if(this.eat(d.bracketL))null!=n&&this.unexpected(n),this.eat(d.bracketL)?(p&&this.unexpected(p.start),a.internalSlots.push(this.flowParseObjectTypeInternalSlot(l,e))):a.indexers.push(this.flowParseObjectTypeIndexer(l,e,p));else if(this.match(d.parenL)||this.isRelational("<"))null!=n&&this.unexpected(n),p&&this.unexpected(p.start),a.callProperties.push(this.flowParseObjectTypeCallProperty(l,e));else{let t="init";if(this.isContextual("get")||this.isContextual("set")){const e=this.lookahead();e.type!==d.name&&e.type!==d.string&&e.type!==d.num||(t=this.state.value,this.next())}const r=this.flowParseObjectTypeProperty(l,e,n,p,t,s,null!=i?i:!c);null===r?(h=!0,o=this.state.lastTokStart):a.properties.push(r)}this.flowObjectTypeSemicolon(),!o||this.match(d.braceR)||this.match(d.braceBarR)||this.raise(o,Bt.UnexpectedExplicitInexactInObject)}this.expect(o),s&&(a.inexact=h);const l=this.finishNode(a,"ObjectTypeAnnotation");return this.state.inType=n,l}flowParseObjectTypeProperty(t,e,s,r,i,n,a){if(this.eat(d.ellipsis)){const e=this.match(d.comma)||this.match(d.semi)||this.match(d.braceR)||this.match(d.braceBarR);return e?(n?a||this.raise(this.state.lastTokStart,Bt.InexactInsideExact):this.raise(this.state.lastTokStart,Bt.InexactInsideNonObject),r&&this.raise(r.start,Bt.InexactVariance),null):(n||this.raise(this.state.lastTokStart,Bt.UnexpectedSpreadType),null!=s&&this.unexpected(s),r&&this.raise(r.start,Bt.SpreadVariance),t.argument=this.flowParseType(),this.finishNode(t,"ObjectTypeSpreadProperty"))}{t.key=this.flowParseObjectPropertyKey(),t.static=e,t.proto=null!=s,t.kind=i;let n=!1;return this.isRelational("<")||this.match(d.parenL)?(t.method=!0,null!=s&&this.unexpected(s),r&&this.unexpected(r.start),t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.start,t.loc.start)),"get"!==i&&"set"!==i||this.flowCheckGetterSetterParams(t)):("init"!==i&&this.unexpected(),t.method=!1,this.eat(d.question)&&(n=!0),t.value=this.flowParseTypeInitialiser(),t.variance=r),t.optional=n,this.finishNode(t,"ObjectTypeProperty")}}flowCheckGetterSetterParams(t){const e="get"===t.kind?0:1,s=t.start,r=t.value.params.length+(t.value.rest?1:0);r!==e&&("get"===t.kind?this.raise(s,ut.BadGetterArity):this.raise(s,ut.BadSetterArity)),"set"===t.kind&&t.value.rest&&this.raise(s,ut.BadSetterRestParameter)}flowObjectTypeSemicolon(){this.eat(d.semi)||this.eat(d.comma)||this.match(d.braceR)||this.match(d.braceBarR)||this.unexpected()}flowParseQualifiedTypeIdentifier(t,e,s){t=t||this.state.start,e=e||this.state.startLoc;let r=s||this.flowParseRestrictedIdentifier(!0);while(this.eat(d.dot)){const s=this.startNodeAt(t,e);s.qualification=r,s.id=this.flowParseRestrictedIdentifier(!0),r=this.finishNode(s,"QualifiedTypeIdentifier")}return r}flowParseGenericType(t,e,s){const r=this.startNodeAt(t,e);return r.typeParameters=null,r.id=this.flowParseQualifiedTypeIdentifier(t,e,s),this.isRelational("<")&&(r.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(r,"GenericTypeAnnotation")}flowParseTypeofType(){const t=this.startNode();return this.expect(d._typeof),t.argument=this.flowParsePrimaryType(),this.finishNode(t,"TypeofTypeAnnotation")}flowParseTupleType(){const t=this.startNode();t.types=[],this.expect(d.bracketL);while(this.state.pos<this.length&&!this.match(d.bracketR)){if(t.types.push(this.flowParseType()),this.match(d.bracketR))break;this.expect(d.comma)}return this.expect(d.bracketR),this.finishNode(t,"TupleTypeAnnotation")}flowParseFunctionTypeParam(){let t=null,e=!1,s=null;const r=this.startNode(),i=this.lookahead();return i.type===d.colon||i.type===d.question?(t=this.parseIdentifier(),this.eat(d.question)&&(e=!0),s=this.flowParseTypeInitialiser()):s=this.flowParseType(),r.name=t,r.optional=e,r.typeAnnotation=s,this.finishNode(r,"FunctionTypeParam")}reinterpretTypeAsFunctionTypeParam(t){const e=this.startNodeAt(t.start,t.loc.start);return e.name=null,e.optional=!1,e.typeAnnotation=t,this.finishNode(e,"FunctionTypeParam")}flowParseFunctionTypeParams(t=[]){let e=null;while(!this.match(d.parenR)&&!this.match(d.ellipsis))t.push(this.flowParseFunctionTypeParam()),this.match(d.parenR)||this.expect(d.comma);return this.eat(d.ellipsis)&&(e=this.flowParseFunctionTypeParam()),{params:t,rest:e}}flowIdentToTypeAnnotation(t,e,s,r){switch(r.name){case"any":return this.finishNode(s,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(s,"BooleanTypeAnnotation");case"mixed":return this.finishNode(s,"MixedTypeAnnotation");case"empty":return this.finishNode(s,"EmptyTypeAnnotation");case"number":return this.finishNode(s,"NumberTypeAnnotation");case"string":return this.finishNode(s,"StringTypeAnnotation");case"symbol":return this.finishNode(s,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(r.name),this.flowParseGenericType(t,e,r)}}flowParsePrimaryType(){const t=this.state.start,e=this.state.startLoc,s=this.startNode();let r,i,n=!1;const a=this.state.noAnonFunctionType;switch(this.state.type){case d.name:return this.isContextual("interface")?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(t,e,s,this.parseIdentifier());case d.braceL:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case d.braceBarL:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case d.bracketL:return this.state.noAnonFunctionType=!1,i=this.flowParseTupleType(),this.state.noAnonFunctionType=a,i;case d.relational:if("<"===this.state.value)return s.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(d.parenL),r=this.flowParseFunctionTypeParams(),s.params=r.params,s.rest=r.rest,this.expect(d.parenR),this.expect(d.arrow),s.returnType=this.flowParseType(),this.finishNode(s,"FunctionTypeAnnotation");break;case d.parenL:if(this.next(),!this.match(d.parenR)&&!this.match(d.ellipsis))if(this.match(d.name)){const t=this.lookahead().type;n=t!==d.question&&t!==d.colon}else n=!0;if(n){if(this.state.noAnonFunctionType=!1,i=this.flowParseType(),this.state.noAnonFunctionType=a,this.state.noAnonFunctionType||!(this.match(d.comma)||this.match(d.parenR)&&this.lookahead().type===d.arrow))return this.expect(d.parenR),i;this.eat(d.comma)}return r=i?this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(i)]):this.flowParseFunctionTypeParams(),s.params=r.params,s.rest=r.rest,this.expect(d.parenR),this.expect(d.arrow),s.returnType=this.flowParseType(),s.typeParameters=null,this.finishNode(s,"FunctionTypeAnnotation");case d.string:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case d._true:case d._false:return s.value=this.match(d._true),this.next(),this.finishNode(s,"BooleanLiteralTypeAnnotation");case d.plusMin:if("-"===this.state.value){if(this.next(),this.match(d.num))return this.parseLiteral(-this.state.value,"NumberLiteralTypeAnnotation",s.start,s.loc.start);if(this.match(d.bigint))return this.parseLiteral(-this.state.value,"BigIntLiteralTypeAnnotation",s.start,s.loc.start);throw this.raise(this.state.start,Bt.UnexpectedSubtractionOperand)}throw this.unexpected();case d.num:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case d.bigint:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case d._void:return this.next(),this.finishNode(s,"VoidTypeAnnotation");case d._null:return this.next(),this.finishNode(s,"NullLiteralTypeAnnotation");case d._this:return this.next(),this.finishNode(s,"ThisTypeAnnotation");case d.star:return this.next(),this.finishNode(s,"ExistsTypeAnnotation");default:if("typeof"===this.state.type.keyword)return this.flowParseTypeofType();if(this.state.type.keyword){const t=this.state.type.label;return this.next(),super.createIdentifier(s,t)}}throw this.unexpected()}flowParsePostfixType(){const t=this.state.start,e=this.state.startLoc;let s=this.flowParsePrimaryType();while(this.match(d.bracketL)&&!this.canInsertSemicolon()){const r=this.startNodeAt(t,e);r.elementType=s,this.expect(d.bracketL),this.expect(d.bracketR),s=this.finishNode(r,"ArrayTypeAnnotation")}return s}flowParsePrefixType(){const t=this.startNode();return this.eat(d.question)?(t.typeAnnotation=this.flowParsePrefixType(),this.finishNode(t,"NullableTypeAnnotation")):this.flowParsePostfixType()}flowParseAnonFunctionWithoutParens(){const t=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(d.arrow)){const e=this.startNodeAt(t.start,t.loc.start);return e.params=[this.reinterpretTypeAsFunctionTypeParam(t)],e.rest=null,e.returnType=this.flowParseType(),e.typeParameters=null,this.finishNode(e,"FunctionTypeAnnotation")}return t}flowParseIntersectionType(){const t=this.startNode();this.eat(d.bitwiseAND);const e=this.flowParseAnonFunctionWithoutParens();t.types=[e];while(this.eat(d.bitwiseAND))t.types.push(this.flowParseAnonFunctionWithoutParens());return 1===t.types.length?e:this.finishNode(t,"IntersectionTypeAnnotation")}flowParseUnionType(){const t=this.startNode();this.eat(d.bitwiseOR);const e=this.flowParseIntersectionType();t.types=[e];while(this.eat(d.bitwiseOR))t.types.push(this.flowParseIntersectionType());return 1===t.types.length?e:this.finishNode(t,"UnionTypeAnnotation")}flowParseType(){const t=this.state.inType;this.state.inType=!0;const e=this.flowParseUnionType();return this.state.inType=t,this.state.exprAllowed=this.state.exprAllowed||this.state.noAnonFunctionType,e}flowParseTypeOrImplicitInstantiation(){if(this.state.type===d.name&&"_"===this.state.value){const t=this.state.start,e=this.state.startLoc,s=this.parseIdentifier();return this.flowParseGenericType(t,e,s)}return this.flowParseType()}flowParseTypeAnnotation(){const t=this.startNode();return t.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(t,"TypeAnnotation")}flowParseTypeAnnotatableIdentifier(t){const e=t?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(d.colon)&&(e.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e)),e}typeCastToParameter(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.end,t.typeAnnotation.loc.end),t.expression}flowParseVariance(){let t=null;return this.match(d.plusMin)&&(t=this.startNode(),"+"===this.state.value?t.kind="plus":t.kind="minus",this.next(),this.finishNode(t,"Variance")),t}parseFunctionBody(t,e,s=!1){return e?this.forwardNoArrowParamsConversionAt(t,()=>super.parseFunctionBody(t,!0,s)):super.parseFunctionBody(t,!1,s)}parseFunctionBodyAndFinish(t,e,s=!1){if(this.match(d.colon)){const e=this.startNode();[e.typeAnnotation,t.predicate]=this.flowParseTypeAndPredicateInitialiser(),t.returnType=e.typeAnnotation?this.finishNode(e,"TypeAnnotation"):null}super.parseFunctionBodyAndFinish(t,e,s)}parseStatement(t,e){if(this.state.strict&&this.match(d.name)&&"interface"===this.state.value){const t=this.startNode();return this.next(),this.flowParseInterface(t)}if(this.shouldParseEnums()&&this.isContextual("enum")){const t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}{const s=super.parseStatement(t,e);return void 0!==this.flowPragma||this.isValidDirective(s)||(this.flowPragma=null),s}}parseExpressionStatement(t,e){if("Identifier"===e.type)if("declare"===e.name){if(this.match(d._class)||this.match(d.name)||this.match(d._function)||this.match(d._var)||this.match(d._export))return this.flowParseDeclare(t)}else if(this.match(d.name)){if("interface"===e.name)return this.flowParseInterface(t);if("type"===e.name)return this.flowParseTypeAlias(t);if("opaque"===e.name)return this.flowParseOpaqueType(t,!1)}return super.parseExpressionStatement(t,e)}shouldParseExportDeclaration(){return this.isContextual("type")||this.isContextual("interface")||this.isContextual("opaque")||this.shouldParseEnums()&&this.isContextual("enum")||super.shouldParseExportDeclaration()}isExportDefaultSpecifier(){return(!this.match(d.name)||!("type"===this.state.value||"interface"===this.state.value||"opaque"===this.state.value||this.shouldParseEnums()&&"enum"===this.state.value))&&super.isExportDefaultSpecifier()}parseExportDefaultExpression(){if(this.shouldParseEnums()&&this.isContextual("enum")){const t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}return super.parseExportDefaultExpression()}parseConditional(t,e,s,r,i){if(!this.match(d.question))return t;if(i){const n=this.tryParse(()=>super.parseConditional(t,e,s,r));return n.node?(n.error&&(this.state=n.failState),n.node):(i.start=n.error.pos||this.state.start,t)}this.expect(d.question);const n=this.state.clone(),a=this.state.noArrowAt,o=this.startNodeAt(s,r);let{consequent:c,failed:h}=this.tryParseConditionalConsequent(),[l,p]=this.getArrowLikeExpressions(c);if(h||p.length>0){const t=[...a];if(p.length>0){this.state=n,this.state.noArrowAt=t;for(let e=0;e<p.length;e++)t.push(p[e].start);({consequent:c,failed:h}=this.tryParseConditionalConsequent()),[l,p]=this.getArrowLikeExpressions(c)}h&&l.length>1&&this.raise(n.start,Bt.AmbiguousConditionalArrow),h&&1===l.length&&(this.state=n,this.state.noArrowAt=t.concat(l[0].start),({consequent:c,failed:h}=this.tryParseConditionalConsequent()))}return this.getArrowLikeExpressions(c,!0),this.state.noArrowAt=a,this.expect(d.colon),o.test=t,o.consequent=c,o.alternate=this.forwardNoArrowParamsConversionAt(o,()=>this.parseMaybeAssign(e,void 0,void 0,void 0)),this.finishNode(o,"ConditionalExpression")}tryParseConditionalConsequent(){this.state.noArrowParamsConversionAt.push(this.state.start);const t=this.parseMaybeAssign(),e=!this.match(d.colon);return this.state.noArrowParamsConversionAt.pop(),{consequent:t,failed:e}}getArrowLikeExpressions(t,e){const s=[t],r=[];while(0!==s.length){const t=s.pop();"ArrowFunctionExpression"===t.type?(t.typeParameters||!t.returnType?this.finishArrowValidation(t):r.push(t),s.push(t.body)):"ConditionalExpression"===t.type&&(s.push(t.consequent),s.push(t.alternate))}return e?(r.forEach(t=>this.finishArrowValidation(t)),[r,[]]):Ht(r,t=>t.params.every(t=>this.isAssignable(t,!0)))}finishArrowValidation(t){var e;this.toAssignableList(t.params,null==(e=t.extra)?void 0:e.trailingComma),this.scope.enter(y|g),super.checkParams(t,!1,!0),this.scope.exit()}forwardNoArrowParamsConversionAt(t,e){let s;return-1!==this.state.noArrowParamsConversionAt.indexOf(t.start)?(this.state.noArrowParamsConversionAt.push(this.state.start),s=e(),this.state.noArrowParamsConversionAt.pop()):s=e(),s}parseParenItem(t,e,s){if(t=super.parseParenItem(t,e,s),this.eat(d.question)&&(t.optional=!0,this.resetEndLocation(t)),this.match(d.colon)){const r=this.startNodeAt(e,s);return r.expression=t,r.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(r,"TypeCastExpression")}return t}assertModuleNodeAllowed(t){"ImportDeclaration"===t.type&&("type"===t.importKind||"typeof"===t.importKind)||"ExportNamedDeclaration"===t.type&&"type"===t.exportKind||"ExportAllDeclaration"===t.type&&"type"===t.exportKind||super.assertModuleNodeAllowed(t)}parseExport(t){const e=super.parseExport(t);return"ExportNamedDeclaration"!==e.type&&"ExportAllDeclaration"!==e.type||(e.exportKind=e.exportKind||"value"),e}parseExportDeclaration(t){if(this.isContextual("type")){t.exportKind="type";const e=this.startNode();return this.next(),this.match(d.braceL)?(t.specifiers=this.parseExportSpecifiers(),this.parseExportFrom(t),null):this.flowParseTypeAlias(e)}if(this.isContextual("opaque")){t.exportKind="type";const e=this.startNode();return this.next(),this.flowParseOpaqueType(e,!1)}if(this.isContextual("interface")){t.exportKind="type";const e=this.startNode();return this.next(),this.flowParseInterface(e)}if(this.shouldParseEnums()&&this.isContextual("enum")){t.exportKind="value";const e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}return super.parseExportDeclaration(t)}eatExportStar(t){return!!super.eatExportStar(...arguments)||!(!this.isContextual("type")||this.lookahead().type!==d.star)&&(t.exportKind="type",this.next(),this.next(),!0)}maybeParseExportNamespaceSpecifier(t){const e=this.state.start,s=super.maybeParseExportNamespaceSpecifier(t);return s&&"type"===t.exportKind&&this.unexpected(e),s}parseClassId(t,e,s){super.parseClassId(t,e,s),this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration())}parseClassMember(t,e,s,r){const i=this.state.start;if(this.isContextual("declare")){if(this.parseClassMemberFromModifier(t,e))return;e.declare=!0}super.parseClassMember(t,e,s,r),e.declare&&("ClassProperty"!==e.type&&"ClassPrivateProperty"!==e.type?this.raise(i,Bt.DeclareClassElement):e.value&&this.raise(e.value.start,Bt.DeclareClassFieldInitializer))}getTokenFromCode(t){const e=this.input.charCodeAt(this.state.pos+1);return 123===t&&124===e?this.finishOp(d.braceBarL,2):!this.state.inType||62!==t&&60!==t?jt(t,e)?(this.state.isIterator=!0,super.readWord()):super.getTokenFromCode(t):this.finishOp(d.relational,1)}isAssignable(t,e){switch(t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":return!0;case"ObjectExpression":{const e=t.properties.length-1;return t.properties.every((t,s)=>"ObjectMethod"!==t.type&&(s===e||"SpreadElement"===t.type)&&this.isAssignable(t))}case"ObjectProperty":return this.isAssignable(t.value);case"SpreadElement":return this.isAssignable(t.argument);case"ArrayExpression":return t.elements.every(t=>this.isAssignable(t));case"AssignmentExpression":return"="===t.operator;case"ParenthesizedExpression":case"TypeCastExpression":return this.isAssignable(t.expression);case"MemberExpression":case"OptionalMemberExpression":return!e;default:return!1}}toAssignable(t){return"TypeCastExpression"===t.type?super.toAssignable(this.typeCastToParameter(t)):super.toAssignable(t)}toAssignableList(t,e){for(let s=0;s<t.length;s++){const e=t[s];e&&"TypeCastExpression"===e.type&&(t[s]=this.typeCastToParameter(e))}return super.toAssignableList(t,e)}toReferencedList(t,e){for(let s=0;s<t.length;s++){const r=t[s];!r||"TypeCastExpression"!==r.type||r.extra&&r.extra.parenthesized||!(t.length>1)&&e||this.raise(r.typeAnnotation.start,Bt.TypeCastInPattern)}return t}checkLVal(t,e=V,s,r){if("TypeCastExpression"!==t.type)return super.checkLVal(t,e,s,r)}parseClassProperty(t){return this.match(d.colon)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassProperty(t)}parseClassPrivateProperty(t){return this.match(d.colon)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassPrivateProperty(t)}isClassMethod(){return this.isRelational("<")||super.isClassMethod()}isClassProperty(){return this.match(d.colon)||super.isClassProperty()}isNonstaticConstructor(t){return!this.match(d.colon)&&super.isNonstaticConstructor(t)}pushClassMethod(t,e,s,r,i,n){e.variance&&this.unexpected(e.variance.start),delete e.variance,this.isRelational("<")&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassMethod(t,e,s,r,i,n)}pushClassPrivateMethod(t,e,s,r){e.variance&&this.unexpected(e.variance.start),delete e.variance,this.isRelational("<")&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassPrivateMethod(t,e,s,r)}parseClassSuper(t){if(super.parseClassSuper(t),t.superClass&&this.isRelational("<")&&(t.superTypeParameters=this.flowParseTypeParameterInstantiation()),this.isContextual("implements")){this.next();const e=t.implements=[];do{const t=this.startNode();t.id=this.flowParseRestrictedIdentifier(!0),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterInstantiation():t.typeParameters=null,e.push(this.finishNode(t,"ClassImplements"))}while(this.eat(d.comma))}}parsePropertyName(t,e){const s=this.flowParseVariance(),r=super.parsePropertyName(t,e);return t.variance=s,r}parseObjPropValue(t,e,s,r,i,n,a,o){let c;t.variance&&this.unexpected(t.variance.start),delete t.variance,this.isRelational("<")&&(c=this.flowParseTypeParameterDeclaration(),this.match(d.parenL)||this.unexpected()),super.parseObjPropValue(t,e,s,r,i,n,a,o),c&&((t.value||t).typeParameters=c)}parseAssignableListItemTypes(t){return this.eat(d.question)&&("Identifier"!==t.type&&this.raise(t.start,Bt.OptionalBindingPattern),t.optional=!0),this.match(d.colon)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),this.resetEndLocation(t),t}parseMaybeDefault(t,e,s){const r=super.parseMaybeDefault(t,e,s);return"AssignmentPattern"===r.type&&r.typeAnnotation&&r.right.start<r.typeAnnotation.start&&this.raise(r.typeAnnotation.start,Bt.TypeBeforeInitializer),r}shouldParseDefaultImport(t){return qt(t)?Vt(this.state):super.shouldParseDefaultImport(t)}parseImportSpecifierLocal(t,e,s,r){e.local=qt(t)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),this.checkLVal(e.local,_,void 0,r),t.specifiers.push(this.finishNode(e,s))}maybeParseDefaultImportSpecifier(t){t.importKind="value";let e=null;if(this.match(d._typeof)?e="typeof":this.isContextual("type")&&(e="type"),e){const s=this.lookahead();"type"===e&&s.type===d.star&&this.unexpected(s.start),(Vt(s)||s.type===d.braceL||s.type===d.star)&&(this.next(),t.importKind=e)}return super.maybeParseDefaultImportSpecifier(t)}parseImportSpecifier(t){const e=this.startNode(),s=this.state.start,r=this.parseIdentifier(!0);let i=null;"type"===r.name?i="type":"typeof"===r.name&&(i="typeof");let n=!1;if(this.isContextual("as")&&!this.isLookaheadContextual("as")){const t=this.parseIdentifier(!0);null===i||this.match(d.name)||this.state.type.keyword?(e.imported=r,e.importKind=null,e.local=this.parseIdentifier()):(e.imported=t,e.importKind=i,e.local=t.__clone())}else null!==i&&(this.match(d.name)||this.state.type.keyword)?(e.imported=this.parseIdentifier(!0),e.importKind=i,this.eatContextual("as")?e.local=this.parseIdentifier():(n=!0,e.local=e.imported.__clone())):(n=!0,e.imported=r,e.importKind=null,e.local=e.imported.__clone());const a=qt(t),o=qt(e);a&&o&&this.raise(s,Bt.ImportTypeShorthandOnlyInPureImport),(a||o)&&this.checkReservedType(e.local.name,e.local.start,!0),!n||a||o||this.checkReservedWord(e.local.name,e.start,!0,!0),this.checkLVal(e.local,_,void 0,"import specifier"),t.specifiers.push(this.finishNode(e,"ImportSpecifier"))}parseFunctionParams(t,e){const s=t.kind;"get"!==s&&"set"!==s&&this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),super.parseFunctionParams(t,e)}parseVarId(t,e){super.parseVarId(t,e),this.match(d.colon)&&(t.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(t.id))}parseAsyncArrowFromCallExpression(t,e){if(this.match(d.colon)){const e=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,t.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=e}return super.parseAsyncArrowFromCallExpression(t,e)}shouldParseAsyncArrow(){return this.match(d.colon)||super.shouldParseAsyncArrow()}parseMaybeAssign(t,e,s,r){let i,n=null;if(this.hasPlugin("jsx")&&(this.match(d.jsxTagStart)||this.isRelational("<"))){if(n=this.state.clone(),i=this.tryParse(()=>super.parseMaybeAssign(t,e,s,r),n),!i.error)return i.node;const{context:a}=this.state;a[a.length-1]===gt.j_oTag?a.length-=2:a[a.length-1]===gt.j_expr&&(a.length-=1)}if(i&&i.error||this.isRelational("<")){let a;n=n||this.state.clone();const o=this.tryParse(()=>{a=this.flowParseTypeParameterDeclaration();const i=this.forwardNoArrowParamsConversionAt(a,()=>super.parseMaybeAssign(t,e,s,r));return i.typeParameters=a,this.resetStartLocationFromNode(i,a),i},n),c=o.node&&"ArrowFunctionExpression"===o.node.type?o.node:null;if(!o.error&&c)return c;if(i&&i.node)return this.state=i.failState,i.node;if(c)return this.state=o.failState,c;if(i&&i.thrown)throw i.error;if(o.thrown)throw o.error;throw this.raise(a.start,Bt.UnexpectedTokenAfterTypeParameter)}return super.parseMaybeAssign(t,e,s,r)}parseArrow(t){if(this.match(d.colon)){const e=this.tryParse(()=>{const e=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0;const s=this.startNode();return[s.typeAnnotation,t.predicate]=this.flowParseTypeAndPredicateInitialiser(),this.state.noAnonFunctionType=e,this.canInsertSemicolon()&&this.unexpected(),this.match(d.arrow)||this.unexpected(),s});if(e.thrown)return null;e.error&&(this.state=e.failState),t.returnType=e.node.typeAnnotation?this.finishNode(e.node,"TypeAnnotation"):null}return super.parseArrow(t)}shouldParseArrow(){return this.match(d.colon)||super.shouldParseArrow()}setArrowFunctionParameters(t,e){-1!==this.state.noArrowParamsConversionAt.indexOf(t.start)?t.params=e:super.setArrowFunctionParameters(t,e)}checkParams(t,e,s){if(!s||-1===this.state.noArrowParamsConversionAt.indexOf(t.start))return super.checkParams(...arguments)}parseParenAndDistinguishExpression(t){return super.parseParenAndDistinguishExpression(t&&-1===this.state.noArrowAt.indexOf(this.state.start))}parseSubscripts(t,e,s,r){if("Identifier"===t.type&&"async"===t.name&&-1!==this.state.noArrowAt.indexOf(e)){this.next();const r=this.startNodeAt(e,s);r.callee=t,r.arguments=this.parseCallExpressionArguments(d.parenR,!1),t=this.finishNode(r,"CallExpression")}else if("Identifier"===t.type&&"async"===t.name&&this.isRelational("<")){const i=this.state.clone(),n=this.tryParse(t=>this.parseAsyncArrowWithTypeParameters(e,s)||t(),i);if(!n.error&&!n.aborted)return n.node;const a=this.tryParse(()=>super.parseSubscripts(t,e,s,r),i);if(a.node&&!a.error)return a.node;if(n.node)return this.state=n.failState,n.node;if(a.node)return this.state=a.failState,a.node;throw n.error||a.error}return super.parseSubscripts(t,e,s,r)}parseSubscript(t,e,s,r,i){if(this.match(d.questionDot)&&this.isLookaheadRelational("<")){if(i.optionalChainMember=!0,r)return i.stop=!0,t;this.next();const n=this.startNodeAt(e,s);return n.callee=t,n.typeArguments=this.flowParseTypeParameterInstantiation(),this.expect(d.parenL),n.arguments=this.parseCallExpressionArguments(d.parenR,!1),n.optional=!0,this.finishCallExpression(n,!0)}if(!r&&this.shouldParseTypes()&&this.isRelational("<")){const r=this.startNodeAt(e,s);r.callee=t;const n=this.tryParse(()=>(r.typeArguments=this.flowParseTypeParameterInstantiationCallOrNew(),this.expect(d.parenL),r.arguments=this.parseCallExpressionArguments(d.parenR,!1),i.optionalChainMember&&(r.optional=!1),this.finishCallExpression(r,i.optionalChainMember)));if(n.node)return n.error&&(this.state=n.failState),n.node}return super.parseSubscript(t,e,s,r,i)}parseNewArguments(t){let e=null;this.shouldParseTypes()&&this.isRelational("<")&&(e=this.tryParse(()=>this.flowParseTypeParameterInstantiationCallOrNew()).node),t.typeArguments=e,super.parseNewArguments(t)}parseAsyncArrowWithTypeParameters(t,e){const s=this.startNodeAt(t,e);if(this.parseFunctionParams(s),this.parseArrow(s))return this.parseArrowExpression(s,void 0,!0)}readToken_mult_modulo(t){const e=this.input.charCodeAt(this.state.pos+1);if(42===t&&47===e&&this.state.hasFlowComment)return this.state.hasFlowComment=!1,this.state.pos+=2,void this.nextToken();super.readToken_mult_modulo(t)}readToken_pipe_amp(t){const e=this.input.charCodeAt(this.state.pos+1);124!==t||125!==e?super.readToken_pipe_amp(t):this.finishOp(d.braceBarR,2)}parseTopLevel(t,e){const s=super.parseTopLevel(t,e);return this.state.hasFlowComment&&this.raise(this.state.pos,Bt.UnterminatedFlowComment),s}skipBlockComment(){if(this.hasPlugin("flowComments")&&this.skipFlowComment())return this.state.hasFlowComment&&this.unexpected(null,Bt.NestedFlowComment),this.hasFlowCommentCompletion(),this.state.pos+=this.skipFlowComment(),void(this.state.hasFlowComment=!0);if(this.state.hasFlowComment){const t=this.input.indexOf("*-/",this.state.pos+=2);if(-1===t)throw this.raise(this.state.pos-2,ut.UnterminatedComment);this.state.pos=t+3}else super.skipBlockComment()}skipFlowComment(){const{pos:t}=this.state;let e=2;while([32,9].includes(this.input.charCodeAt(t+e)))e++;const s=this.input.charCodeAt(e+t),r=this.input.charCodeAt(e+t+1);return 58===s&&58===r?e+2:"flow-include"===this.input.slice(e+t,e+t+12)?e+12:58===s&&58!==r&&e}hasFlowCommentCompletion(){const t=this.input.indexOf("*/",this.state.pos);if(-1===t)throw this.raise(this.state.pos,ut.UnterminatedComment)}flowEnumErrorBooleanMemberNotInitialized(t,{enumName:e,memberName:s}){this.raise(t,Bt.EnumBooleanMemberNotInitialized,s,e)}flowEnumErrorInvalidMemberName(t,{enumName:e,memberName:s}){const r=s[0].toUpperCase()+s.slice(1);this.raise(t,Bt.EnumInvalidMemberName,s,r,e)}flowEnumErrorDuplicateMemberName(t,{enumName:e,memberName:s}){this.raise(t,Bt.EnumDuplicateMemberName,s,e)}flowEnumErrorInconsistentMemberValues(t,{enumName:e}){this.raise(t,Bt.EnumInconsistentMemberValues,e)}flowEnumErrorInvalidExplicitType(t,{enumName:e,suppliedType:s}){return this.raise(t,null===s?Bt.EnumInvalidExplicitTypeUnknownSupplied:Bt.EnumInvalidExplicitType,e,s)}flowEnumErrorInvalidMemberInitializer(t,{enumName:e,explicitType:s,memberName:r}){let i=null;switch(s){case"boolean":case"number":case"string":i=Bt.EnumInvalidMemberInitializerPrimaryType;break;case"symbol":i=Bt.EnumInvalidMemberInitializerSymbolType;break;default:i=Bt.EnumInvalidMemberInitializerUnknownType}return this.raise(t,i,e,r,s)}flowEnumErrorNumberMemberNotInitialized(t,{enumName:e,memberName:s}){this.raise(t,Bt.EnumNumberMemberNotInitialized,e,s)}flowEnumErrorStringMemberInconsistentlyInitailized(t,{enumName:e}){this.raise(t,Bt.EnumStringMemberInconsistentlyInitailized,e)}flowEnumMemberInit(){const t=this.state.start,e=()=>this.match(d.comma)||this.match(d.braceR);switch(this.state.type){case d.num:{const s=this.parseLiteral(this.state.value,"NumericLiteral");return e()?{type:"number",pos:s.start,value:s}:{type:"invalid",pos:t}}case d.string:{const s=this.parseLiteral(this.state.value,"StringLiteral");return e()?{type:"string",pos:s.start,value:s}:{type:"invalid",pos:t}}case d._true:case d._false:{const s=this.parseBooleanLiteral();return e()?{type:"boolean",pos:s.start,value:s}:{type:"invalid",pos:t}}default:return{type:"invalid",pos:t}}}flowEnumMemberRaw(){const t=this.state.start,e=this.parseIdentifier(!0),s=this.eat(d.eq)?this.flowEnumMemberInit():{type:"none",pos:t};return{id:e,init:s}}flowEnumCheckExplicitTypeMismatch(t,e,s){const{explicitType:r}=e;null!==r&&r!==s&&this.flowEnumErrorInvalidMemberInitializer(t,e)}flowEnumMembers({enumName:t,explicitType:e}){const s=new Set,r={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]};while(!this.match(d.braceR)){const i=this.startNode(),{id:n,init:a}=this.flowEnumMemberRaw(),o=n.name;if(""===o)continue;/^[a-z]/.test(o)&&this.flowEnumErrorInvalidMemberName(n.start,{enumName:t,memberName:o}),s.has(o)&&this.flowEnumErrorDuplicateMemberName(n.start,{enumName:t,memberName:o}),s.add(o);const c={enumName:t,explicitType:e,memberName:o};switch(i.id=n,a.type){case"boolean":this.flowEnumCheckExplicitTypeMismatch(a.pos,c,"boolean"),i.init=a.value,r.booleanMembers.push(this.finishNode(i,"EnumBooleanMember"));break;case"number":this.flowEnumCheckExplicitTypeMismatch(a.pos,c,"number"),i.init=a.value,r.numberMembers.push(this.finishNode(i,"EnumNumberMember"));break;case"string":this.flowEnumCheckExplicitTypeMismatch(a.pos,c,"string"),i.init=a.value,r.stringMembers.push(this.finishNode(i,"EnumStringMember"));break;case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(a.pos,c);case"none":switch(e){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(a.pos,c);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(a.pos,c);break;default:r.defaultedMembers.push(this.finishNode(i,"EnumDefaultedMember"))}}this.match(d.braceR)||this.expect(d.comma)}return r}flowEnumStringMembers(t,e,{enumName:s}){if(0===t.length)return e;if(0===e.length)return t;if(e.length>t.length){for(let e=0;e<t.length;e++){const r=t[e];this.flowEnumErrorStringMemberInconsistentlyInitailized(r.start,{enumName:s})}return e}for(let r=0;r<e.length;r++){const t=e[r];this.flowEnumErrorStringMemberInconsistentlyInitailized(t.start,{enumName:s})}return t}flowEnumParseExplicitType({enumName:t}){if(this.eatContextual("of")){if(!this.match(d.name))throw this.flowEnumErrorInvalidExplicitType(this.state.start,{enumName:t,suppliedType:null});const{value:e}=this.state;return this.next(),"boolean"!==e&&"number"!==e&&"string"!==e&&"symbol"!==e&&this.flowEnumErrorInvalidExplicitType(this.state.start,{enumName:t,suppliedType:e}),e}return null}flowEnumBody(t,{enumName:e,nameLoc:s}){const r=this.flowEnumParseExplicitType({enumName:e});this.expect(d.braceL);const i=this.flowEnumMembers({enumName:e,explicitType:r});switch(r){case"boolean":return t.explicitType=!0,t.members=i.booleanMembers,this.expect(d.braceR),this.finishNode(t,"EnumBooleanBody");case"number":return t.explicitType=!0,t.members=i.numberMembers,this.expect(d.braceR),this.finishNode(t,"EnumNumberBody");case"string":return t.explicitType=!0,t.members=this.flowEnumStringMembers(i.stringMembers,i.defaultedMembers,{enumName:e}),this.expect(d.braceR),this.finishNode(t,"EnumStringBody");case"symbol":return t.members=i.defaultedMembers,this.expect(d.braceR),this.finishNode(t,"EnumSymbolBody");default:{const r=()=>(t.members=[],this.expect(d.braceR),this.finishNode(t,"EnumStringBody"));t.explicitType=!1;const n=i.booleanMembers.length,a=i.numberMembers.length,o=i.stringMembers.length,c=i.defaultedMembers.length;if(n||a||o||c){if(n||a){if(!a&&!o&&n>=c){for(let t=0,s=i.defaultedMembers;t<s.length;t++){const r=s[t];this.flowEnumErrorBooleanMemberNotInitialized(r.start,{enumName:e,memberName:r.id.name})}return t.members=i.booleanMembers,this.expect(d.braceR),this.finishNode(t,"EnumBooleanBody")}if(!n&&!o&&a>=c){for(let t=0,s=i.defaultedMembers;t<s.length;t++){const r=s[t];this.flowEnumErrorNumberMemberNotInitialized(r.start,{enumName:e,memberName:r.id.name})}return t.members=i.numberMembers,this.expect(d.braceR),this.finishNode(t,"EnumNumberBody")}return this.flowEnumErrorInconsistentMemberValues(s,{enumName:e}),r()}return t.members=this.flowEnumStringMembers(i.stringMembers,i.defaultedMembers,{enumName:e}),this.expect(d.braceR),this.finishNode(t,"EnumStringBody")}return r()}}}flowParseEnumDeclaration(t){const e=this.parseIdentifier();return t.id=e,t.body=this.flowEnumBody(this.startNode(),{enumName:e.name,nameLoc:e.start}),this.finishNode(t,"EnumDeclaration")}};const $t={quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"},Xt=/^[\da-fA-F]+$/,Gt=/^\d+$/,Yt=Object.freeze({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression",MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>",MissingClosingTagElement:"Expected corresponding JSX closing tag for <%0>",UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text",UnterminatedJsxContent:"Unterminated JSX contents",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"});function Jt(t){return!!t&&("JSXOpeningFragment"===t.type||"JSXClosingFragment"===t.type)}function Qt(t){if("JSXIdentifier"===t.type)return t.name;if("JSXNamespacedName"===t.type)return t.namespace.name+":"+t.name.name;if("JSXMemberExpression"===t.type)return Qt(t.object)+"."+Qt(t.property);throw new Error("Node had unexpected type: "+t.type)}gt.j_oTag=new yt("<tag",!1),gt.j_cTag=new yt("</tag",!1),gt.j_expr=new yt("<tag>...</tag>",!0,!0),d.jsxName=new h("jsxName"),d.jsxText=new h("jsxText",{beforeExpr:!0}),d.jsxTagStart=new h("jsxTagStart",{startsExpr:!0}),d.jsxTagEnd=new h("jsxTagEnd"),d.jsxTagStart.updateContext=function(){this.state.context.push(gt.j_expr),this.state.context.push(gt.j_oTag),this.state.exprAllowed=!1},d.jsxTagEnd.updateContext=function(t){const e=this.state.context.pop();e===gt.j_oTag&&t===d.slash||e===gt.j_cTag?(this.state.context.pop(),this.state.exprAllowed=this.curContext()===gt.j_expr):this.state.exprAllowed=!0};var Zt=t=>class extends t{jsxReadToken(){let t="",e=this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,Yt.UnterminatedJsxContent);const s=this.input.charCodeAt(this.state.pos);switch(s){case 60:case 123:return this.state.pos===this.state.start?60===s&&this.state.exprAllowed?(++this.state.pos,this.finishToken(d.jsxTagStart)):super.getTokenFromCode(s):(t+=this.input.slice(e,this.state.pos),this.finishToken(d.jsxText,t));case 38:t+=this.input.slice(e,this.state.pos),t+=this.jsxReadEntity(),e=this.state.pos;break;default:rt(s)?(t+=this.input.slice(e,this.state.pos),t+=this.jsxReadNewLine(!0),e=this.state.pos):++this.state.pos}}}jsxReadNewLine(t){const e=this.input.charCodeAt(this.state.pos);let s;return++this.state.pos,13===e&&10===this.input.charCodeAt(this.state.pos)?(++this.state.pos,s=t?"\n":"\r\n"):s=String.fromCharCode(e),++this.state.curLine,this.state.lineStart=this.state.pos,s}jsxReadString(t){let e="",s=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,ut.UnterminatedString);const r=this.input.charCodeAt(this.state.pos);if(r===t)break;38===r?(e+=this.input.slice(s,this.state.pos),e+=this.jsxReadEntity(),s=this.state.pos):rt(r)?(e+=this.input.slice(s,this.state.pos),e+=this.jsxReadNewLine(!1),s=this.state.pos):++this.state.pos}return e+=this.input.slice(s,this.state.pos++),this.finishToken(d.string,e)}jsxReadEntity(){let t,e="",s=0,r=this.input[this.state.pos];const i=++this.state.pos;while(this.state.pos<this.length&&s++<10){if(r=this.input[this.state.pos++],";"===r){"#"===e[0]?"x"===e[1]?(e=e.substr(2),Xt.test(e)&&(t=String.fromCodePoint(parseInt(e,16)))):(e=e.substr(1),Gt.test(e)&&(t=String.fromCodePoint(parseInt(e,10)))):t=$t[e];break}e+=r}return t||(this.state.pos=i,"&")}jsxReadWord(){let t;const e=this.state.pos;do{t=this.input.charCodeAt(++this.state.pos)}while(St(t)||45===t);return this.finishToken(d.jsxName,this.input.slice(e,this.state.pos))}jsxParseIdentifier(){const t=this.startNode();return this.match(d.jsxName)?t.name=this.state.value:this.state.type.keyword?t.name=this.state.type.keyword:this.unexpected(),this.next(),this.finishNode(t,"JSXIdentifier")}jsxParseNamespacedName(){const t=this.state.start,e=this.state.startLoc,s=this.jsxParseIdentifier();if(!this.eat(d.colon))return s;const r=this.startNodeAt(t,e);return r.namespace=s,r.name=this.jsxParseIdentifier(),this.finishNode(r,"JSXNamespacedName")}jsxParseElementName(){const t=this.state.start,e=this.state.startLoc;let s=this.jsxParseNamespacedName();if("JSXNamespacedName"===s.type)return s;while(this.eat(d.dot)){const r=this.startNodeAt(t,e);r.object=s,r.property=this.jsxParseIdentifier(),s=this.finishNode(r,"JSXMemberExpression")}return s}jsxParseAttributeValue(){let t;switch(this.state.type){case d.braceL:return t=this.startNode(),this.next(),t=this.jsxParseExpressionContainer(t),"JSXEmptyExpression"===t.expression.type&&this.raise(t.start,Yt.AttributeIsEmpty),t;case d.jsxTagStart:case d.string:return this.parseExprAtom();default:throw this.raise(this.state.start,Yt.UnsupportedJsxValue)}}jsxParseEmptyExpression(){const t=this.startNodeAt(this.state.lastTokEnd,this.state.lastTokEndLoc);return this.finishNodeAt(t,"JSXEmptyExpression",this.state.start,this.state.startLoc)}jsxParseSpreadChild(t){return this.next(),t.expression=this.parseExpression(),this.expect(d.braceR),this.finishNode(t,"JSXSpreadChild")}jsxParseExpressionContainer(t){return this.match(d.braceR)?t.expression=this.jsxParseEmptyExpression():t.expression=this.parseExpression(),this.expect(d.braceR),this.finishNode(t,"JSXExpressionContainer")}jsxParseAttribute(){const t=this.startNode();return this.eat(d.braceL)?(this.expect(d.ellipsis),t.argument=this.parseMaybeAssign(),this.expect(d.braceR),this.finishNode(t,"JSXSpreadAttribute")):(t.name=this.jsxParseNamespacedName(),t.value=this.eat(d.eq)?this.jsxParseAttributeValue():null,this.finishNode(t,"JSXAttribute"))}jsxParseOpeningElementAt(t,e){const s=this.startNodeAt(t,e);return this.match(d.jsxTagEnd)?(this.expect(d.jsxTagEnd),this.finishNode(s,"JSXOpeningFragment")):(s.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(s))}jsxParseOpeningElementAfterName(t){const e=[];while(!this.match(d.slash)&&!this.match(d.jsxTagEnd))e.push(this.jsxParseAttribute());return t.attributes=e,t.selfClosing=this.eat(d.slash),this.expect(d.jsxTagEnd),this.finishNode(t,"JSXOpeningElement")}jsxParseClosingElementAt(t,e){const s=this.startNodeAt(t,e);return this.match(d.jsxTagEnd)?(this.expect(d.jsxTagEnd),this.finishNode(s,"JSXClosingFragment")):(s.name=this.jsxParseElementName(),this.expect(d.jsxTagEnd),this.finishNode(s,"JSXClosingElement"))}jsxParseElementAt(t,e){const s=this.startNodeAt(t,e),r=[],i=this.jsxParseOpeningElementAt(t,e);let n=null;if(!i.selfClosing){t:for(;;)switch(this.state.type){case d.jsxTagStart:if(t=this.state.start,e=this.state.startLoc,this.next(),this.eat(d.slash)){n=this.jsxParseClosingElementAt(t,e);break t}r.push(this.jsxParseElementAt(t,e));break;case d.jsxText:r.push(this.parseExprAtom());break;case d.braceL:{const t=this.startNode();this.next(),this.match(d.ellipsis)?r.push(this.jsxParseSpreadChild(t)):r.push(this.jsxParseExpressionContainer(t));break}default:throw this.unexpected()}Jt(i)&&!Jt(n)?this.raise(n.start,Yt.MissingClosingTagFragment):!Jt(i)&&Jt(n)?this.raise(n.start,Yt.MissingClosingTagElement,Qt(i.name)):Jt(i)||Jt(n)||Qt(n.name)!==Qt(i.name)&&this.raise(n.start,Yt.MissingClosingTagElement,Qt(i.name))}if(Jt(i)?(s.openingFragment=i,s.closingFragment=n):(s.openingElement=i,s.closingElement=n),s.children=r,this.isRelational("<"))throw this.raise(this.state.start,Yt.UnwrappedAdjacentJSXElements);return Jt(i)?this.finishNode(s,"JSXFragment"):this.finishNode(s,"JSXElement")}jsxParseElement(){const t=this.state.start,e=this.state.startLoc;return this.next(),this.jsxParseElementAt(t,e)}parseExprAtom(t){return this.match(d.jsxText)?this.parseLiteral(this.state.value,"JSXText"):this.match(d.jsxTagStart)?this.jsxParseElement():this.isRelational("<")&&33!==this.input.charCodeAt(this.state.pos)?(this.finishToken(d.jsxTagStart),this.jsxParseElement()):super.parseExprAtom(t)}getTokenFromCode(t){if(this.state.inPropertyName)return super.getTokenFromCode(t);const e=this.curContext();if(e===gt.j_expr)return this.jsxReadToken();if(e===gt.j_oTag||e===gt.j_cTag){if(At(t))return this.jsxReadWord();if(62===t)return++this.state.pos,this.finishToken(d.jsxTagEnd);if((34===t||39===t)&&e===gt.j_oTag)return this.jsxReadString(t)}return 60===t&&this.state.exprAllowed&&33!==this.input.charCodeAt(this.state.pos+1)?(++this.state.pos,this.finishToken(d.jsxTagStart)):super.getTokenFromCode(t)}updateContext(t){if(this.match(d.braceL)){const e=this.curContext();e===gt.j_oTag?this.state.context.push(gt.braceExpression):e===gt.j_expr?this.state.context.push(gt.templateQuasi):super.updateContext(t),this.state.exprAllowed=!0}else{if(!this.match(d.slash)||t!==d.jsxTagStart)return super.updateContext(t);this.state.context.length-=2,this.state.context.push(gt.j_cTag),this.state.exprAllowed=!1}}};class te{constructor(t){this.var=[],this.lexical=[],this.functions=[],this.flags=t}}class ee{constructor(t,e){this.scopeStack=[],this.undefinedExports=new Map,this.undefinedPrivateNames=new Map,this.raise=t,this.inModule=e}get inFunction(){return(this.currentVarScope().flags&y)>0}get allowSuper(){return(this.currentThisScope().flags&b)>0}get allowDirectSuper(){return(this.currentThisScope().flags&v)>0}get inClass(){return(this.currentThisScope().flags&w)>0}get inNonArrowFunction(){return(this.currentThisScope().flags&y)>0}get treatFunctionsAsVar(){return this.treatFunctionsAsVarInScope(this.currentScope())}createScope(t){return new te(t)}enter(t){this.scopeStack.push(this.createScope(t))}exit(){this.scopeStack.pop()}treatFunctionsAsVarInScope(t){return!!(t.flags&y||!this.inModule&&t.flags&m)}declareName(t,e,s){let r=this.currentScope();if(e&C||e&k)this.checkRedeclarationInScope(r,t,e,s),e&k?r.functions.push(t):r.lexical.push(t),e&C&&this.maybeExportDefined(r,t);else if(e&S)for(let i=this.scopeStack.length-1;i>=0;--i)if(r=this.scopeStack[i],this.checkRedeclarationInScope(r,t,e,s),r.var.push(t),this.maybeExportDefined(r,t),r.flags&T)break;this.inModule&&r.flags&m&&this.undefinedExports.delete(t)}maybeExportDefined(t,e){this.inModule&&t.flags&m&&this.undefinedExports.delete(e)}checkRedeclarationInScope(t,e,s,r){this.isRedeclaredInScope(t,e,s)&&this.raise(r,ut.VarRedeclaration,e)}isRedeclaredInScope(t,e,s){return!!(s&E)&&(s&C?t.lexical.indexOf(e)>-1||t.functions.indexOf(e)>-1||t.var.indexOf(e)>-1:s&k?t.lexical.indexOf(e)>-1||!this.treatFunctionsAsVarInScope(t)&&t.var.indexOf(e)>-1:t.lexical.indexOf(e)>-1&&!(t.flags&x&&t.lexical[0]===e)||!this.treatFunctionsAsVarInScope(t)&&t.functions.indexOf(e)>-1)}checkLocalExport(t){-1===this.scopeStack[0].lexical.indexOf(t.name)&&-1===this.scopeStack[0].var.indexOf(t.name)&&-1===this.scopeStack[0].functions.indexOf(t.name)&&this.undefinedExports.set(t.name,t.start)}currentScope(){return this.scopeStack[this.scopeStack.length-1]}currentVarScope(){for(let t=this.scopeStack.length-1;;t--){const e=this.scopeStack[t];if(e.flags&T)return e}}currentThisScope(){for(let t=this.scopeStack.length-1;;t--){const e=this.scopeStack[t];if((e.flags&T||e.flags&w)&&!(e.flags&g))return e}}}class se extends te{constructor(...t){super(...t),this.types=[],this.enums=[],this.constEnums=[],this.classes=[],this.exportOnlyBindings=[]}}class re extends ee{createScope(t){return new se(t)}declareName(t,e,s){const r=this.currentScope();if(e&M)return this.maybeExportDefined(r,t),void r.exportOnlyBindings.push(t);super.declareName(...arguments),e&A&&(e&E||(this.checkRedeclarationInScope(r,t,e,s),this.maybeExportDefined(r,t)),r.types.push(t)),e&O&&r.enums.push(t),e&D&&r.constEnums.push(t),e&I&&r.classes.push(t)}isRedeclaredInScope(t,e,s){if(t.enums.indexOf(e)>-1){if(s&O){const r=!!(s&D),i=t.constEnums.indexOf(e)>-1;return r!==i}return!0}return s&I&&t.classes.indexOf(e)>-1?t.lexical.indexOf(e)>-1&&!!(s&E):!!(s&A&&t.types.indexOf(e)>-1)||super.isRedeclaredInScope(...arguments)}checkLocalExport(t){-1===this.scopeStack[0].types.indexOf(t.name)&&-1===this.scopeStack[0].exportOnlyBindings.indexOf(t.name)&&super.checkLocalExport(t)}}const ie=0,ne=1,ae=2,oe=4;class ce{constructor(){this.stacks=[]}enter(t){this.stacks.push(t)}exit(){this.stacks.pop()}currentFlags(){return this.stacks[this.stacks.length-1]}get hasAwait(){return(this.currentFlags()&ae)>0}get hasYield(){return(this.currentFlags()&ne)>0}get hasReturn(){return(this.currentFlags()&oe)>0}}function he(t,e){return(t?ae:0)|(e?ne:0)}function le(t){if(null==t)throw new Error(`Unexpected ${t} value.`);return t}function pe(t){if(!t)throw new Error("Assert fail")}const ue=Object.freeze({ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier",DeclareClassFieldHasInitializer:"'declare' class fields cannot have an initializer",DuplicateModifier:"Duplicate modifier: '%0'",EmptyHeritageClauseType:"'%0' list cannot be empty.",IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier",IndexSignatureHasAccessibility:"Index signatures cannot have an accessibility modifier ('%0')",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:"Private elements cannot have an accessibility modifier ('%0')",TemplateTypeHasSubstitution:"Template literal types cannot have any substitution",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:"Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got %0"});function de(t){switch(t){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}var fe=t=>class extends t{getScopeHandler(){return re}tsIsIdentifier(){return this.match(d.name)}tsNextTokenCanFollowModifier(){return this.next(),!this.hasPrecedingLineBreak()&&!this.match(d.parenL)&&!this.match(d.parenR)&&!this.match(d.colon)&&!this.match(d.eq)&&!this.match(d.question)&&!this.match(d.bang)}tsParseModifier(t){if(!this.match(d.name))return;const e=this.state.value;return-1!==t.indexOf(e)&&this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this))?e:void 0}tsParseModifiers(t,e){for(;;){const s=this.state.start,r=this.tsParseModifier(e);if(!r)break;Object.hasOwnProperty.call(t,r)&&this.raise(s,ue.DuplicateModifier,r),t[r]=!0}}tsIsListTerminator(t){switch(t){case"EnumMembers":case"TypeMembers":return this.match(d.braceR);case"HeritageClauseElement":return this.match(d.braceL);case"TupleElementTypes":return this.match(d.bracketR);case"TypeParametersOrArguments":return this.isRelational(">")}throw new Error("Unreachable")}tsParseList(t,e){const s=[];while(!this.tsIsListTerminator(t))s.push(e());return s}tsParseDelimitedList(t,e){return le(this.tsParseDelimitedListWorker(t,e,!0))}tsParseDelimitedListWorker(t,e,s){const r=[];for(;;){if(this.tsIsListTerminator(t))break;const i=e();if(null==i)return;if(r.push(i),!this.eat(d.comma)){if(this.tsIsListTerminator(t))break;return void(s&&this.expect(d.comma))}}return r}tsParseBracketedList(t,e,s,r){r||(s?this.expect(d.bracketL):this.expectRelational("<"));const i=this.tsParseDelimitedList(t,e);return s?this.expect(d.bracketR):this.expectRelational(">"),i}tsParseImportType(){const t=this.startNode();return this.expect(d._import),this.expect(d.parenL),this.match(d.string)||this.raise(this.state.start,ue.UnsupportedImportTypeArgument),t.argument=this.parseExprAtom(),this.expect(d.parenR),this.eat(d.dot)&&(t.qualifier=this.tsParseEntityName(!0)),this.isRelational("<")&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSImportType")}tsParseEntityName(t){let e=this.parseIdentifier();while(this.eat(d.dot)){const s=this.startNodeAtNode(e);s.left=e,s.right=this.parseIdentifier(t),e=this.finishNode(s,"TSQualifiedName")}return e}tsParseTypeReference(){const t=this.startNode();return t.typeName=this.tsParseEntityName(!1),!this.hasPrecedingLineBreak()&&this.isRelational("<")&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSTypeReference")}tsParseThisTypePredicate(t){this.next();const e=this.startNodeAtNode(t);return e.parameterName=t,e.typeAnnotation=this.tsParseTypeAnnotation(!1),this.finishNode(e,"TSTypePredicate")}tsParseThisTypeNode(){const t=this.startNode();return this.next(),this.finishNode(t,"TSThisType")}tsParseTypeQuery(){const t=this.startNode();return this.expect(d._typeof),this.match(d._import)?t.exprName=this.tsParseImportType():t.exprName=this.tsParseEntityName(!0),this.finishNode(t,"TSTypeQuery")}tsParseTypeParameter(){const t=this.startNode();return t.name=this.parseIdentifierName(t.start),t.constraint=this.tsEatThenParseType(d._extends),t.default=this.tsEatThenParseType(d.eq),this.finishNode(t,"TSTypeParameter")}tsTryParseTypeParameters(){if(this.isRelational("<"))return this.tsParseTypeParameters()}tsParseTypeParameters(){const t=this.startNode();return this.isRelational("<")||this.match(d.jsxTagStart)?this.next():this.unexpected(),t.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this),!1,!0),this.finishNode(t,"TSTypeParameterDeclaration")}tsTryNextParseConstantContext(){return this.lookahead().type===d._const?(this.next(),this.tsParseTypeReference()):null}tsFillSignature(t,e){const s=t===d.arrow;e.typeParameters=this.tsTryParseTypeParameters(),this.expect(d.parenL),e.parameters=this.tsParseBindingListForSignature(),(s||this.match(t))&&(e.typeAnnotation=this.tsParseTypeOrTypePredicateAnnotation(t))}tsParseBindingListForSignature(){return this.parseBindingList(d.parenR,41).map(t=>("Identifier"!==t.type&&"RestElement"!==t.type&&"ObjectPattern"!==t.type&&"ArrayPattern"!==t.type&&this.raise(t.start,ue.UnsupportedSignatureParameterKind,t.type),t))}tsParseTypeMemberSemicolon(){this.eat(d.comma)||this.semicolon()}tsParseSignatureMember(t,e){return this.tsFillSignature(d.colon,e),this.tsParseTypeMemberSemicolon(),this.finishNode(e,t)}tsIsUnambiguouslyIndexSignature(){return this.next(),this.eat(d.name)&&this.match(d.colon)}tsTryParseIndexSignature(t){if(!this.match(d.bracketL)||!this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this)))return;this.expect(d.bracketL);const e=this.parseIdentifier();e.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(e),this.expect(d.bracketR),t.parameters=[e];const s=this.tsTryParseTypeAnnotation();return s&&(t.typeAnnotation=s),this.tsParseTypeMemberSemicolon(),this.finishNode(t,"TSIndexSignature")}tsParsePropertyOrMethodSignature(t,e){this.eat(d.question)&&(t.optional=!0);const s=t;if(e||!this.match(d.parenL)&&!this.isRelational("<")){const t=s;e&&(t.readonly=!0);const r=this.tsTryParseTypeAnnotation();return r&&(t.typeAnnotation=r),this.tsParseTypeMemberSemicolon(),this.finishNode(t,"TSPropertySignature")}{const t=s;return this.tsFillSignature(d.colon,t),this.tsParseTypeMemberSemicolon(),this.finishNode(t,"TSMethodSignature")}}tsParseTypeMember(){const t=this.startNode();if(this.match(d.parenL)||this.isRelational("<"))return this.tsParseSignatureMember("TSCallSignatureDeclaration",t);if(this.match(d._new)){const e=this.startNode();return this.next(),this.match(d.parenL)||this.isRelational("<")?this.tsParseSignatureMember("TSConstructSignatureDeclaration",t):(t.key=this.createIdentifier(e,"new"),this.tsParsePropertyOrMethodSignature(t,!1))}const e=!!this.tsParseModifier(["readonly"]),s=this.tsTryParseIndexSignature(t);return s?(e&&(t.readonly=!0),s):(this.parsePropertyName(t,!1),this.tsParsePropertyOrMethodSignature(t,e))}tsParseTypeLiteral(){const t=this.startNode();return t.members=this.tsParseObjectTypeMembers(),this.finishNode(t,"TSTypeLiteral")}tsParseObjectTypeMembers(){this.expect(d.braceL);const t=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(d.braceR),t}tsIsStartOfMappedType(){return this.next(),this.eat(d.plusMin)?this.isContextual("readonly"):(this.isContextual("readonly")&&this.next(),!!this.match(d.bracketL)&&(this.next(),!!this.tsIsIdentifier()&&(this.next(),this.match(d._in))))}tsParseMappedTypeParameter(){const t=this.startNode();return t.name=this.parseIdentifierName(t.start),t.constraint=this.tsExpectThenParseType(d._in),this.finishNode(t,"TSTypeParameter")}tsParseMappedType(){const t=this.startNode();return this.expect(d.braceL),this.match(d.plusMin)?(t.readonly=this.state.value,this.next(),this.expectContextual("readonly")):this.eatContextual("readonly")&&(t.readonly=!0),this.expect(d.bracketL),t.typeParameter=this.tsParseMappedTypeParameter(),this.expect(d.bracketR),this.match(d.plusMin)?(t.optional=this.state.value,this.next(),this.expect(d.question)):this.eat(d.question)&&(t.optional=!0),t.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(d.braceR),this.finishNode(t,"TSMappedType")}tsParseTupleType(){const t=this.startNode();t.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);let e=!1;return t.elementTypes.forEach(t=>{"TSOptionalType"===t.type?e=!0:e&&"TSRestType"!==t.type&&this.raise(t.start,ue.OptionalTypeBeforeRequired)}),this.finishNode(t,"TSTupleType")}tsParseTupleElementType(){if(this.match(d.ellipsis)){const t=this.startNode();return this.next(),t.typeAnnotation=this.tsParseType(),this.match(d.comma)&&93!==this.lookaheadCharCode()&&this.raiseRestNotLast(this.state.start),this.finishNode(t,"TSRestType")}const t=this.tsParseType();if(this.eat(d.question)){const e=this.startNodeAtNode(t);return e.typeAnnotation=t,this.finishNode(e,"TSOptionalType")}return t}tsParseParenthesizedType(){const t=this.startNode();return this.expect(d.parenL),t.typeAnnotation=this.tsParseType(),this.expect(d.parenR),this.finishNode(t,"TSParenthesizedType")}tsParseFunctionOrConstructorType(t){const e=this.startNode();return"TSConstructorType"===t&&this.expect(d._new),this.tsFillSignature(d.arrow,e),this.finishNode(e,t)}tsParseLiteralTypeNode(){const t=this.startNode();return t.literal=(()=>{switch(this.state.type){case d.num:case d.string:case d._true:case d._false:return this.parseExprAtom();default:throw this.unexpected()}})(),this.finishNode(t,"TSLiteralType")}tsParseTemplateLiteralType(){const t=this.startNode(),e=this.parseTemplate(!1);return e.expressions.length>0&&this.raise(e.expressions[0].start,ue.TemplateTypeHasSubstitution),t.literal=e,this.finishNode(t,"TSLiteralType")}tsParseThisTypeOrThisTypePredicate(){const t=this.tsParseThisTypeNode();return this.isContextual("is")&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(t):t}tsParseNonArrayType(){switch(this.state.type){case d.name:case d._void:case d._null:{const t=this.match(d._void)?"TSVoidKeyword":this.match(d._null)?"TSNullKeyword":de(this.state.value);if(void 0!==t&&46!==this.lookaheadCharCode()){const e=this.startNode();return this.next(),this.finishNode(e,t)}return this.tsParseTypeReference()}case d.string:case d.num:case d._true:case d._false:return this.tsParseLiteralTypeNode();case d.plusMin:if("-"===this.state.value){const t=this.startNode();if(this.lookahead().type!==d.num)throw this.unexpected();return t.literal=this.parseMaybeUnary(),this.finishNode(t,"TSLiteralType")}break;case d._this:return this.tsParseThisTypeOrThisTypePredicate();case d._typeof:return this.tsParseTypeQuery();case d._import:return this.tsParseImportType();case d.braceL:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case d.bracketL:return this.tsParseTupleType();case d.parenL:return this.tsParseParenthesizedType();case d.backQuote:return this.tsParseTemplateLiteralType()}throw this.unexpected()}tsParseArrayTypeOrHigher(){let t=this.tsParseNonArrayType();while(!this.hasPrecedingLineBreak()&&this.eat(d.bracketL))if(this.match(d.bracketR)){const e=this.startNodeAtNode(t);e.elementType=t,this.expect(d.bracketR),t=this.finishNode(e,"TSArrayType")}else{const e=this.startNodeAtNode(t);e.objectType=t,e.indexType=this.tsParseType(),this.expect(d.bracketR),t=this.finishNode(e,"TSIndexedAccessType")}return t}tsParseTypeOperator(t){const e=this.startNode();return this.expectContextual(t),e.operator=t,e.typeAnnotation=this.tsParseTypeOperatorOrHigher(),"readonly"===t&&this.tsCheckTypeAnnotationForReadOnly(e),this.finishNode(e,"TSTypeOperator")}tsCheckTypeAnnotationForReadOnly(t){switch(t.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(t.start,ue.UnexpectedReadonly)}}tsParseInferType(){const t=this.startNode();this.expectContextual("infer");const e=this.startNode();return e.name=this.parseIdentifierName(e.start),t.typeParameter=this.finishNode(e,"TSTypeParameter"),this.finishNode(t,"TSInferType")}tsParseTypeOperatorOrHigher(){const t=["keyof","unique","readonly"].find(t=>this.isContextual(t));return t?this.tsParseTypeOperator(t):this.isContextual("infer")?this.tsParseInferType():this.tsParseArrayTypeOrHigher()}tsParseUnionOrIntersectionType(t,e,s){this.eat(s);let r=e();if(this.match(s)){const i=[r];while(this.eat(s))i.push(e());const n=this.startNodeAtNode(r);n.types=i,r=this.finishNode(n,t)}return r}tsParseIntersectionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),d.bitwiseAND)}tsParseUnionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),d.bitwiseOR)}tsIsStartOfFunctionType(){return!!this.isRelational("<")||this.match(d.parenL)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}tsSkipParameterStart(){if(this.match(d.name)||this.match(d._this))return this.next(),!0;if(this.match(d.braceL)){let t=1;this.next();while(t>0)this.match(d.braceL)?++t:this.match(d.braceR)&&--t,this.next();return!0}if(this.match(d.bracketL)){let t=1;this.next();while(t>0)this.match(d.bracketL)?++t:this.match(d.bracketR)&&--t,this.next();return!0}return!1}tsIsUnambiguouslyStartOfFunctionType(){if(this.next(),this.match(d.parenR)||this.match(d.ellipsis))return!0;if(this.tsSkipParameterStart()){if(this.match(d.colon)||this.match(d.comma)||this.match(d.question)||this.match(d.eq))return!0;if(this.match(d.parenR)&&(this.next(),this.match(d.arrow)))return!0}return!1}tsParseTypeOrTypePredicateAnnotation(t){return this.tsInType(()=>{const e=this.startNode();this.expect(t);const s=this.tsTryParse(this.tsParseTypePredicateAsserts.bind(this));if(s&&this.match(d._this)){let t=this.tsParseThisTypeOrThisTypePredicate();if("TSThisType"===t.type){const s=this.startNodeAtNode(e);s.parameterName=t,s.asserts=!0,t=this.finishNode(s,"TSTypePredicate")}else t.asserts=!0;return e.typeAnnotation=t,this.finishNode(e,"TSTypeAnnotation")}const r=this.tsIsIdentifier()&&this.tsTryParse(this.tsParseTypePredicatePrefix.bind(this));if(!r){if(!s)return this.tsParseTypeAnnotation(!1,e);const t=this.startNodeAtNode(e);return t.parameterName=this.parseIdentifier(),t.asserts=s,e.typeAnnotation=this.finishNode(t,"TSTypePredicate"),this.finishNode(e,"TSTypeAnnotation")}const i=this.tsParseTypeAnnotation(!1),n=this.startNodeAtNode(e);return n.parameterName=r,n.typeAnnotation=i,n.asserts=s,e.typeAnnotation=this.finishNode(n,"TSTypePredicate"),this.finishNode(e,"TSTypeAnnotation")})}tsTryParseTypeOrTypePredicateAnnotation(){return this.match(d.colon)?this.tsParseTypeOrTypePredicateAnnotation(d.colon):void 0}tsTryParseTypeAnnotation(){return this.match(d.colon)?this.tsParseTypeAnnotation():void 0}tsTryParseType(){return this.tsEatThenParseType(d.colon)}tsParseTypePredicatePrefix(){const t=this.parseIdentifier();if(this.isContextual("is")&&!this.hasPrecedingLineBreak())return this.next(),t}tsParseTypePredicateAsserts(){if(!this.match(d.name)||"asserts"!==this.state.value||this.hasPrecedingLineBreak())return!1;const t=this.state.containsEsc;return this.next(),!(!this.match(d.name)&&!this.match(d._this))&&(t&&this.raise(this.state.lastTokStart,ut.InvalidEscapedReservedWord,"asserts"),!0)}tsParseTypeAnnotation(t=!0,e=this.startNode()){return this.tsInType(()=>{t&&this.expect(d.colon),e.typeAnnotation=this.tsParseType()}),this.finishNode(e,"TSTypeAnnotation")}tsParseType(){pe(this.state.inType);const t=this.tsParseNonConditionalType();if(this.hasPrecedingLineBreak()||!this.eat(d._extends))return t;const e=this.startNodeAtNode(t);return e.checkType=t,e.extendsType=this.tsParseNonConditionalType(),this.expect(d.question),e.trueType=this.tsParseType(),this.expect(d.colon),e.falseType=this.tsParseType(),this.finishNode(e,"TSConditionalType")}tsParseNonConditionalType(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(d._new)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.tsParseUnionTypeOrHigher()}tsParseTypeAssertion(){const t=this.startNode(),e=this.tsTryNextParseConstantContext();return t.typeAnnotation=e||this.tsNextThenParseType(),this.expectRelational(">"),t.expression=this.parseMaybeUnary(),this.finishNode(t,"TSTypeAssertion")}tsParseHeritageClause(t){const e=this.state.start,s=this.tsParseDelimitedList("HeritageClauseElement",this.tsParseExpressionWithTypeArguments.bind(this));return s.length||this.raise(e,ue.EmptyHeritageClauseType,t),s}tsParseExpressionWithTypeArguments(){const t=this.startNode();return t.expression=this.tsParseEntityName(!1),this.isRelational("<")&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSExpressionWithTypeArguments")}tsParseInterfaceDeclaration(t){t.id=this.parseIdentifier(),this.checkLVal(t.id,F,void 0,"typescript interface declaration"),t.typeParameters=this.tsTryParseTypeParameters(),this.eat(d._extends)&&(t.extends=this.tsParseHeritageClause("extends"));const e=this.startNode();return e.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),t.body=this.finishNode(e,"TSInterfaceBody"),this.finishNode(t,"TSInterfaceDeclaration")}tsParseTypeAliasDeclaration(t){return t.id=this.parseIdentifier(),this.checkLVal(t.id,B,void 0,"typescript type alias"),t.typeParameters=this.tsTryParseTypeParameters(),t.typeAnnotation=this.tsExpectThenParseType(d.eq),this.semicolon(),this.finishNode(t,"TSTypeAliasDeclaration")}tsInNoContext(t){const e=this.state.context;this.state.context=[e[0]];try{return t()}finally{this.state.context=e}}tsInType(t){const e=this.state.inType;this.state.inType=!0;try{return t()}finally{this.state.inType=e}}tsEatThenParseType(t){return this.match(t)?this.tsNextThenParseType():void 0}tsExpectThenParseType(t){return this.tsDoThenParseType(()=>this.expect(t))}tsNextThenParseType(){return this.tsDoThenParseType(()=>this.next())}tsDoThenParseType(t){return this.tsInType(()=>(t(),this.tsParseType()))}tsParseEnumMember(){const t=this.startNode();return t.id=this.match(d.string)?this.parseExprAtom():this.parseIdentifier(!0),this.eat(d.eq)&&(t.initializer=this.parseMaybeAssign()),this.finishNode(t,"TSEnumMember")}tsParseEnumDeclaration(t,e){return e&&(t.const=!0),t.id=this.parseIdentifier(),this.checkLVal(t.id,e?H:U,void 0,"typescript enum declaration"),this.expect(d.braceL),t.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(d.braceR),this.finishNode(t,"TSEnumDeclaration")}tsParseModuleBlock(){const t=this.startNode();return this.scope.enter(f),this.expect(d.braceL),this.parseBlockOrModuleBlockBody(t.body=[],void 0,!0,d.braceR),this.scope.exit(),this.finishNode(t,"TSModuleBlock")}tsParseModuleOrNamespaceDeclaration(t,e=!1){if(t.id=this.parseIdentifier(),e||this.checkLVal(t.id,W,null,"module or namespace declaration"),this.eat(d.dot)){const e=this.startNode();this.tsParseModuleOrNamespaceDeclaration(e,!0),t.body=e}else this.scope.enter(P),this.prodParam.enter(ie),t.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(t,"TSModuleDeclaration")}tsParseAmbientExternalModuleDeclaration(t){return this.isContextual("global")?(t.global=!0,t.id=this.parseIdentifier()):this.match(d.string)?t.id=this.parseExprAtom():this.unexpected(),this.match(d.braceL)?(this.scope.enter(P),this.prodParam.enter(ie),t.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(t,"TSModuleDeclaration")}tsParseImportEqualsDeclaration(t,e){return t.isExport=e||!1,t.id=this.parseIdentifier(),this.checkLVal(t.id,_,void 0,"import equals declaration"),this.expect(d.eq),t.moduleReference=this.tsParseModuleReference(),this.semicolon(),this.finishNode(t,"TSImportEqualsDeclaration")}tsIsExternalModuleReference(){return this.isContextual("require")&&40===this.lookaheadCharCode()}tsParseModuleReference(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(!1)}tsParseExternalModuleReference(){const t=this.startNode();if(this.expectContextual("require"),this.expect(d.parenL),!this.match(d.string))throw this.unexpected();return t.expression=this.parseExprAtom(),this.expect(d.parenR),this.finishNode(t,"TSExternalModuleReference")}tsLookAhead(t){const e=this.state.clone(),s=t();return this.state=e,s}tsTryParseAndCatch(t){const e=this.tryParse(e=>t()||e());if(!e.aborted&&e.node)return e.error&&(this.state=e.failState),e.node}tsTryParse(t){const e=this.state.clone(),s=t();return void 0!==s&&!1!==s?s:void(this.state=e)}tsTryParseDeclare(t){if(this.isLineTerminator())return;let e,s=this.state.type;switch(this.isContextual("let")&&(s=d._var,e="let"),s){case d._function:return this.parseFunctionStatement(t,!1,!0);case d._class:return t.declare=!0,this.parseClass(t,!0,!1);case d._const:if(this.match(d._const)&&this.isLookaheadContextual("enum"))return this.expect(d._const),this.expectContextual("enum"),this.tsParseEnumDeclaration(t,!0);case d._var:return e=e||this.state.value,this.parseVarStatement(t,e);case d.name:{const e=this.state.value;return"global"===e?this.tsParseAmbientExternalModuleDeclaration(t):this.tsParseDeclaration(t,e,!0)}}}tsTryParseExportDeclaration(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0)}tsParseExpressionStatement(t,e){switch(e.name){case"declare":{const e=this.tsTryParseDeclare(t);if(e)return e.declare=!0,e;break}case"global":if(this.match(d.braceL)){this.scope.enter(P),this.prodParam.enter(ie);const s=t;return s.global=!0,s.id=e,s.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(s,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(t,e.name,!1)}}tsParseDeclaration(t,e,s){switch(e){case"abstract":if(this.tsCheckLineTerminatorAndMatch(d._class,s)){const e=t;return e.abstract=!0,s&&(this.next(),this.match(d._class)||this.unexpected(null,d._class)),this.parseClass(e,!0,!1)}break;case"enum":if(s||this.match(d.name))return s&&this.next(),this.tsParseEnumDeclaration(t,!1);break;case"interface":if(this.tsCheckLineTerminatorAndMatch(d.name,s))return s&&this.next(),this.tsParseInterfaceDeclaration(t);break;case"module":if(s&&this.next(),this.match(d.string))return this.tsParseAmbientExternalModuleDeclaration(t);if(this.tsCheckLineTerminatorAndMatch(d.name,s))return this.tsParseModuleOrNamespaceDeclaration(t);break;case"namespace":if(this.tsCheckLineTerminatorAndMatch(d.name,s))return s&&this.next(),this.tsParseModuleOrNamespaceDeclaration(t);break;case"type":if(this.tsCheckLineTerminatorAndMatch(d.name,s))return s&&this.next(),this.tsParseTypeAliasDeclaration(t);break}}tsCheckLineTerminatorAndMatch(t,e){return(e||this.match(t))&&!this.isLineTerminator()}tsTryParseGenericAsyncArrowFunction(t,e){if(!this.isRelational("<"))return;const s=this.state.maybeInArrowParameters,r=this.state.yieldPos,i=this.state.awaitPos;this.state.maybeInArrowParameters=!0,this.state.yieldPos=-1,this.state.awaitPos=-1;const n=this.tsTryParseAndCatch(()=>{const s=this.startNodeAt(t,e);return s.typeParameters=this.tsParseTypeParameters(),super.parseFunctionParams(s),s.returnType=this.tsTryParseTypeOrTypePredicateAnnotation(),this.expect(d.arrow),s});return this.state.maybeInArrowParameters=s,this.state.yieldPos=r,this.state.awaitPos=i,n?this.parseArrowExpression(n,null,!0):void 0}tsParseTypeArguments(){const t=this.startNode();return t.params=this.tsInType(()=>this.tsInNoContext(()=>(this.expectRelational("<"),this.tsParseDelimitedList("TypeParametersOrArguments",this.tsParseType.bind(this))))),this.state.exprAllowed=!1,this.expectRelational(">"),this.finishNode(t,"TSTypeParameterInstantiation")}tsIsDeclarationStart(){if(this.match(d.name))switch(this.state.value){case"abstract":case"declare":case"enum":case"interface":case"module":case"namespace":case"type":return!0}return!1}isExportDefaultSpecifier(){return!this.tsIsDeclarationStart()&&super.isExportDefaultSpecifier()}parseAssignableListItem(t,e){const s=this.state.start,r=this.state.startLoc;let i,n=!1;t&&(i=this.parseAccessModifier(),n=!!this.tsParseModifier(["readonly"]));const a=this.parseMaybeDefault();this.parseAssignableListItemTypes(a);const o=this.parseMaybeDefault(a.start,a.loc.start,a);if(i||n){const t=this.startNodeAt(s,r);return e.length&&(t.decorators=e),i&&(t.accessibility=i),n&&(t.readonly=n),"Identifier"!==o.type&&"AssignmentPattern"!==o.type&&this.raise(t.start,ue.UnsupportedParameterPropertyKind),t.parameter=o,this.finishNode(t,"TSParameterProperty")}return e.length&&(a.decorators=e),o}parseFunctionBodyAndFinish(t,e,s=!1){this.match(d.colon)&&(t.returnType=this.tsParseTypeOrTypePredicateAnnotation(d.colon));const r="FunctionDeclaration"===e?"TSDeclareFunction":"ClassMethod"===e?"TSDeclareMethod":void 0;r&&!this.match(d.braceL)&&this.isLineTerminator()?this.finishNode(t,r):super.parseFunctionBodyAndFinish(t,e,s)}registerFunctionStatementId(t){!t.body&&t.id?this.checkLVal(t.id,q,null,"function name"):super.registerFunctionStatementId(...arguments)}parseSubscript(t,e,s,r,i){if(!this.hasPrecedingLineBreak()&&this.match(d.bang)){this.state.exprAllowed=!1,this.next();const r=this.startNodeAt(e,s);return r.expression=t,this.finishNode(r,"TSNonNullExpression")}if(this.isRelational("<")){const n=this.tsTryParseAndCatch(()=>{if(!r&&this.atPossibleAsyncArrow(t)){const t=this.tsTryParseGenericAsyncArrowFunction(e,s);if(t)return t}const n=this.startNodeAt(e,s);n.callee=t;const a=this.tsParseTypeArguments();if(a){if(!r&&this.eat(d.parenL))return n.arguments=this.parseCallExpressionArguments(d.parenR,!1),n.typeParameters=a,this.finishCallExpression(n,i.optionalChainMember);if(this.match(d.backQuote))return this.parseTaggedTemplateExpression(e,s,t,i,a)}this.unexpected()});if(n)return n}return super.parseSubscript(t,e,s,r,i)}parseNewArguments(t){if(this.isRelational("<")){const e=this.tsTryParseAndCatch(()=>{const t=this.tsParseTypeArguments();return this.match(d.parenL)||this.unexpected(),t});e&&(t.typeParameters=e)}super.parseNewArguments(t)}parseExprOp(t,e,s,r,i){if(le(d._in.binop)>r&&!this.hasPrecedingLineBreak()&&this.isContextual("as")){const n=this.startNodeAt(e,s);n.expression=t;const a=this.tsTryNextParseConstantContext();return n.typeAnnotation=a||this.tsNextThenParseType(),this.finishNode(n,"TSAsExpression"),this.parseExprOp(n,e,s,r,i)}return super.parseExprOp(t,e,s,r,i)}checkReservedWord(t,e,s,r){}checkDuplicateExports(){}parseImport(t){if(this.match(d.name)||this.match(d.star)||this.match(d.braceL)){const e=this.lookahead();if(this.match(d.name)&&e.type===d.eq)return this.tsParseImportEqualsDeclaration(t);!this.isContextual("type")||e.type===d.comma||e.type===d.name&&"from"===e.value?t.importKind="value":(t.importKind="type",this.next())}const e=super.parseImport(t);return"type"===e.importKind&&e.specifiers.length>1&&"ImportDefaultSpecifier"===e.specifiers[0].type&&this.raise(e.start,"A type-only import can specify a default import or named bindings, but not both."),e}parseExport(t){if(this.match(d._import))return this.expect(d._import),this.tsParseImportEqualsDeclaration(t,!0);if(this.eat(d.eq)){const e=t;return e.expression=this.parseExpression(),this.semicolon(),this.finishNode(e,"TSExportAssignment")}if(this.eatContextual("as")){const e=t;return this.expectContextual("namespace"),e.id=this.parseIdentifier(),this.semicolon(),this.finishNode(e,"TSNamespaceExportDeclaration")}return this.isContextual("type")&&this.lookahead().type===d.braceL?(this.next(),t.exportKind="type"):t.exportKind="value",super.parseExport(t)}isAbstractClass(){return this.isContextual("abstract")&&this.lookahead().type===d._class}parseExportDefaultExpression(){if(this.isAbstractClass()){const t=this.startNode();return this.next(),this.parseClass(t,!0,!0),t.abstract=!0,t}if("interface"===this.state.value){const t=this.tsParseDeclaration(this.startNode(),this.state.value,!0);if(t)return t}return super.parseExportDefaultExpression()}parseStatementContent(t,e){if(this.state.type===d._const){const t=this.lookahead();if(t.type===d.name&&"enum"===t.value){const t=this.startNode();return this.expect(d._const),this.expectContextual("enum"),this.tsParseEnumDeclaration(t,!0)}}return super.parseStatementContent(t,e)}parseAccessModifier(){return this.tsParseModifier(["public","protected","private"])}parseClassMember(t,e,s,r){this.tsParseModifiers(e,["declare"]);const i=this.parseAccessModifier();i&&(e.accessibility=i),this.tsParseModifiers(e,["declare"]),super.parseClassMember(t,e,s,r)}parseClassMemberWithIsStatic(t,e,s,r,i){this.tsParseModifiers(e,["abstract","readonly","declare"]);const n=this.tsTryParseIndexSignature(e);if(n)return t.body.push(n),e.abstract&&this.raise(e.start,ue.IndexSignatureHasAbstract),r&&this.raise(e.start,ue.IndexSignatureHasStatic),void(e.accessibility&&this.raise(e.start,ue.IndexSignatureHasAccessibility,e.accessibility));super.parseClassMemberWithIsStatic(t,e,s,r,i)}parsePostMemberNameModifiers(t){const e=this.eat(d.question);e&&(t.optional=!0),t.readonly&&this.match(d.parenL)&&this.raise(t.start,ue.ClassMethodHasReadonly),t.declare&&this.match(d.parenL)&&this.raise(t.start,ue.ClassMethodHasDeclare)}parseExpressionStatement(t,e){const s="Identifier"===e.type?this.tsParseExpressionStatement(t,e):void 0;return s||super.parseExpressionStatement(t,e)}shouldParseExportDeclaration(){return!!this.tsIsDeclarationStart()||super.shouldParseExportDeclaration()}parseConditional(t,e,s,r,i){if(!i||!this.match(d.question))return super.parseConditional(t,e,s,r,i);const n=this.tryParse(()=>super.parseConditional(t,e,s,r));return n.node?(n.error&&(this.state=n.failState),n.node):(i.start=n.error.pos||this.state.start,t)}parseParenItem(t,e,s){if(t=super.parseParenItem(t,e,s),this.eat(d.question)&&(t.optional=!0,this.resetEndLocation(t)),this.match(d.colon)){const r=this.startNodeAt(e,s);return r.expression=t,r.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(r,"TSTypeCastExpression")}return t}parseExportDeclaration(t){const e=this.state.start,s=this.state.startLoc,r=this.eatContextual("declare");let i;return this.match(d.name)&&(i=this.tsTryParseExportDeclaration()),i||(i=super.parseExportDeclaration(t)),i&&("TSInterfaceDeclaration"===i.type||"TSTypeAliasDeclaration"===i.type||r)&&(t.exportKind="type"),i&&r&&(this.resetStartLocation(i,e,s),i.declare=!0),i}parseClassId(t,e,s){if((!e||s)&&this.isContextual("implements"))return;super.parseClassId(t,e,s,t.declare?q:L);const r=this.tsTryParseTypeParameters();r&&(t.typeParameters=r)}parseClassPropertyAnnotation(t){!t.optional&&this.eat(d.bang)&&(t.definite=!0);const e=this.tsTryParseTypeAnnotation();e&&(t.typeAnnotation=e)}parseClassProperty(t){return this.parseClassPropertyAnnotation(t),t.declare&&this.match(d.equal)&&this.raise(this.state.start,ue.DeclareClassFieldHasInitializer),super.parseClassProperty(t)}parseClassPrivateProperty(t){return t.abstract&&this.raise(t.start,ue.PrivateElementHasAbstract),t.accessibility&&this.raise(t.start,ue.PrivateElementHasAccessibility,t.accessibility),this.parseClassPropertyAnnotation(t),super.parseClassPrivateProperty(t)}pushClassMethod(t,e,s,r,i,n){const a=this.tsTryParseTypeParameters();a&&(e.typeParameters=a),super.pushClassMethod(t,e,s,r,i,n)}pushClassPrivateMethod(t,e,s,r){const i=this.tsTryParseTypeParameters();i&&(e.typeParameters=i),super.pushClassPrivateMethod(t,e,s,r)}parseClassSuper(t){super.parseClassSuper(t),t.superClass&&this.isRelational("<")&&(t.superTypeParameters=this.tsParseTypeArguments()),this.eatContextual("implements")&&(t.implements=this.tsParseHeritageClause("implements"))}parseObjPropValue(t,...e){const s=this.tsTryParseTypeParameters();s&&(t.typeParameters=s),super.parseObjPropValue(t,...e)}parseFunctionParams(t,e){const s=this.tsTryParseTypeParameters();s&&(t.typeParameters=s),super.parseFunctionParams(t,e)}parseVarId(t,e){super.parseVarId(t,e),"Identifier"===t.id.type&&this.eat(d.bang)&&(t.definite=!0);const s=this.tsTryParseTypeAnnotation();s&&(t.id.typeAnnotation=s,this.resetEndLocation(t.id))}parseAsyncArrowFromCallExpression(t,e){return this.match(d.colon)&&(t.returnType=this.tsParseTypeAnnotation()),super.parseAsyncArrowFromCallExpression(t,e)}parseMaybeAssign(...t){let e,s,r,i;if(this.match(d.jsxTagStart)){if(e=this.state.clone(),s=this.tryParse(()=>super.parseMaybeAssign(...t),e),!s.error)return s.node;const{context:r}=this.state;r[r.length-1]===gt.j_oTag?r.length-=2:r[r.length-1]===gt.j_expr&&(r.length-=1)}if((!s||!s.error)&&!this.isRelational("<"))return super.parseMaybeAssign(...t);e=e||this.state.clone();const n=this.tryParse(e=>{i=this.tsParseTypeParameters();const s=super.parseMaybeAssign(...t);return("ArrowFunctionExpression"!==s.type||s.extra&&s.extra.parenthesized)&&e(),i&&0!==i.params.length&&this.resetStartLocationFromNode(s,i),s.typeParameters=i,s},e);if(!n.error&&!n.aborted)return n.node;if(!s&&(pe(!this.hasPlugin("jsx")),r=this.tryParse(()=>super.parseMaybeAssign(...t),e),!r.error))return r.node;if(s&&s.node)return this.state=s.failState,s.node;if(n.node)return this.state=n.failState,n.node;if(r&&r.node)return this.state=r.failState,r.node;if(s&&s.thrown)throw s.error;if(n.thrown)throw n.error;if(r&&r.thrown)throw r.error;throw s&&s.error||n.error||r&&r.error}parseMaybeUnary(t){return!this.hasPlugin("jsx")&&this.isRelational("<")?this.tsParseTypeAssertion():super.parseMaybeUnary(t)}parseArrow(t){if(this.match(d.colon)){const e=this.tryParse(t=>{const e=this.tsParseTypeOrTypePredicateAnnotation(d.colon);return!this.canInsertSemicolon()&&this.match(d.arrow)||t(),e});if(e.aborted)return;e.thrown||(e.error&&(this.state=e.failState),t.returnType=e.node)}return super.parseArrow(t)}parseAssignableListItemTypes(t){this.eat(d.question)&&("Identifier"!==t.type&&this.raise(t.start,ue.PatternIsOptional),t.optional=!0);const e=this.tsTryParseTypeAnnotation();return e&&(t.typeAnnotation=e),this.resetEndLocation(t),t}toAssignable(t){switch(t.type){case"TSTypeCastExpression":return super.toAssignable(this.typeCastToParameter(t));case"TSParameterProperty":return super.toAssignable(t);case"TSAsExpression":case"TSNonNullExpression":case"TSTypeAssertion":return t.expression=this.toAssignable(t.expression),t;default:return super.toAssignable(t)}}checkLVal(t,e=V,s,r){switch(t.type){case"TSTypeCastExpression":return;case"TSParameterProperty":return void this.checkLVal(t.parameter,e,s,"parameter property");case"TSAsExpression":case"TSNonNullExpression":case"TSTypeAssertion":return void this.checkLVal(t.expression,e,s,r);default:return void super.checkLVal(t,e,s,r)}}parseBindingAtom(){switch(this.state.type){case d._this:return this.parseIdentifier(!0);default:return super.parseBindingAtom()}}parseMaybeDecoratorArguments(t){if(this.isRelational("<")){const e=this.tsParseTypeArguments();if(this.match(d.parenL)){const s=super.parseMaybeDecoratorArguments(t);return s.typeParameters=e,s}this.unexpected(this.state.start,d.parenL)}return super.parseMaybeDecoratorArguments(t)}isClassMethod(){return this.isRelational("<")||super.isClassMethod()}isClassProperty(){return this.match(d.bang)||this.match(d.colon)||super.isClassProperty()}parseMaybeDefault(...t){const e=super.parseMaybeDefault(...t);return"AssignmentPattern"===e.type&&e.typeAnnotation&&e.right.start<e.typeAnnotation.start&&this.raise(e.typeAnnotation.start,ue.TypeAnnotationAfterAssign),e}getTokenFromCode(t){return!this.state.inType||62!==t&&60!==t?super.getTokenFromCode(t):this.finishOp(d.relational,1)}toAssignableList(t){for(let e=0;e<t.length;e++){const s=t[e];if(s)switch(s.type){case"TSTypeCastExpression":t[e]=this.typeCastToParameter(s);break;case"TSAsExpression":case"TSTypeAssertion":this.state.maybeInArrowParameters?this.raise(s.start,ue.UnexpectedTypeCastInParameter):t[e]=this.typeCastToParameter(s);break}}return super.toAssignableList(...arguments)}typeCastToParameter(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.end,t.typeAnnotation.loc.end),t.expression}toReferencedList(t,e){for(let s=0;s<t.length;s++){const e=t[s];e&&"TSTypeCastExpression"===e.type&&this.raise(e.start,ue.UnexpectedTypeAnnotation)}return t}shouldParseArrow(){return this.match(d.colon)||super.shouldParseArrow()}shouldParseAsyncArrow(){return this.match(d.colon)||super.shouldParseAsyncArrow()}canHaveLeadingDecorator(){return super.canHaveLeadingDecorator()||this.isAbstractClass()}jsxParseOpeningElementAfterName(t){if(this.isRelational("<")){const e=this.tsTryParseAndCatch(()=>this.tsParseTypeArguments());e&&(t.typeParameters=e)}return super.jsxParseOpeningElementAfterName(t)}getGetterSetterExpectedParamCount(t){const e=super.getGetterSetterExpectedParamCount(t),s=t.params[0],r=s&&"Identifier"===s.type&&"this"===s.name;return r?e+1:e}};d.placeholder=new h("%%",{startsExpr:!0});var me=t=>class extends t{parsePlaceholder(t){if(this.match(d.placeholder)){const e=this.startNode();return this.next(),this.assertNoSpace("Unexpected space in placeholder."),e.name=super.parseIdentifier(!0),this.assertNoSpace("Unexpected space in placeholder."),this.expect(d.placeholder),this.finishPlaceholder(e,t)}}finishPlaceholder(t,e){const s=!(!t.expectedNode||"Placeholder"!==t.type);return t.expectedNode=e,s?t:this.finishNode(t,"Placeholder")}getTokenFromCode(t){return 37===t&&37===this.input.charCodeAt(this.state.pos+1)?this.finishOp(d.placeholder,2):super.getTokenFromCode(...arguments)}parseExprAtom(){return this.parsePlaceholder("Expression")||super.parseExprAtom(...arguments)}parseIdentifier(){return this.parsePlaceholder("Identifier")||super.parseIdentifier(...arguments)}checkReservedWord(t){void 0!==t&&super.checkReservedWord(...arguments)}parseBindingAtom(){return this.parsePlaceholder("Pattern")||super.parseBindingAtom(...arguments)}checkLVal(t){"Placeholder"!==t.type&&super.checkLVal(...arguments)}toAssignable(t){return t&&"Placeholder"===t.type&&"Expression"===t.expectedNode?(t.expectedNode="Pattern",t):super.toAssignable(...arguments)}verifyBreakContinue(t){t.label&&"Placeholder"===t.label.type||super.verifyBreakContinue(...arguments)}parseExpressionStatement(t,e){if("Placeholder"!==e.type||e.extra&&e.extra.parenthesized)return super.parseExpressionStatement(...arguments);if(this.match(d.colon)){const s=t;return s.label=this.finishPlaceholder(e,"Identifier"),this.next(),s.body=this.parseStatement("label"),this.finishNode(s,"LabeledStatement")}return this.semicolon(),t.name=e.name,this.finishPlaceholder(t,"Statement")}parseBlock(){return this.parsePlaceholder("BlockStatement")||super.parseBlock(...arguments)}parseFunctionId(){return this.parsePlaceholder("Identifier")||super.parseFunctionId(...arguments)}parseClass(t,e,s){const r=e?"ClassDeclaration":"ClassExpression";this.next(),this.takeDecorators(t);const i=this.parsePlaceholder("Identifier");if(i)if(this.match(d._extends)||this.match(d.placeholder)||this.match(d.braceL))t.id=i;else{if(s||!e)return t.id=null,t.body=this.finishPlaceholder(i,"ClassBody"),this.finishNode(t,r);this.unexpected(null,"A class name is required")}else this.parseClassId(t,e,s);return this.parseClassSuper(t),t.body=this.parsePlaceholder("ClassBody")||this.parseClassBody(!!t.superClass),this.finishNode(t,r)}parseExport(t){const e=this.parsePlaceholder("Identifier");if(!e)return super.parseExport(...arguments);if(!this.isContextual("from")&&!this.match(d.comma))return t.specifiers=[],t.source=null,t.declaration=this.finishPlaceholder(e,"Declaration"),this.finishNode(t,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");const s=this.startNode();return s.exported=e,t.specifiers=[this.finishNode(s,"ExportDefaultSpecifier")],super.parseExport(t)}maybeParseExportDefaultSpecifier(t){return!!(t.specifiers&&t.specifiers.length>0)||super.maybeParseExportDefaultSpecifier(...arguments)}checkExport(t){const{specifiers:e}=t;e&&e.length&&(t.specifiers=e.filter(t=>"Placeholder"===t.exported.type)),super.checkExport(t),t.specifiers=e}parseImport(t){const e=this.parsePlaceholder("Identifier");if(!e)return super.parseImport(...arguments);if(t.specifiers=[],!this.isContextual("from")&&!this.match(d.comma))return t.source=this.finishPlaceholder(e,"StringLiteral"),this.semicolon(),this.finishNode(t,"ImportDeclaration");const s=this.startNodeAtNode(e);if(s.local=e,this.finishNode(s,"ImportDefaultSpecifier"),t.specifiers.push(s),this.eat(d.comma)){const e=this.maybeParseStarImportSpecifier(t);e||this.parseNamedImportSpecifiers(t)}return this.expectContextual("from"),t.source=this.parseImportSource(),this.semicolon(),this.finishNode(t,"ImportDeclaration")}parseImportSource(){return this.parsePlaceholder("StringLiteral")||super.parseImportSource(...arguments)}},ye=t=>class extends t{parseV8Intrinsic(){if(this.match(d.modulo)){const t=this.state.start,e=this.startNode();if(this.eat(d.modulo),this.match(d.name)){const t=this.parseIdentifierName(this.state.start),s=this.createIdentifier(e,t);if(s.type="V8IntrinsicIdentifier",this.match(d.parenL))return s}this.unexpected(t)}}parseExprAtom(){return this.parseV8Intrinsic()||super.parseExprAtom(...arguments)}};function ge(t,e){return t.some(t=>Array.isArray(t)?t[0]===e:t===e)}function xe(t,e,s){const r=t.find(t=>Array.isArray(t)?t[0]===e:t===e);return r&&Array.isArray(r)?r[1][s]:null}const be=["minimal","smart","fsharp"],ve=["hash","bar"];function we(t){if(ge(t,"decorators")){if(ge(t,"decorators-legacy"))throw new Error("Cannot use the decorators and decorators-legacy plugin together");const e=xe(t,"decorators","decoratorsBeforeExport");if(null==e)throw new Error("The 'decorators' plugin requires a 'decoratorsBeforeExport' option, whose value must be a boolean. If you are migrating from Babylon/Babel 6 or want to use the old decorators proposal, you should use the 'decorators-legacy' plugin instead of 'decorators'.");if("boolean"!==typeof e)throw new Error("'decoratorsBeforeExport' must be a boolean.")}if(ge(t,"flow")&&ge(t,"typescript"))throw new Error("Cannot combine flow and typescript plugins.");if(ge(t,"placeholders")&&ge(t,"v8intrinsic"))throw new Error("Cannot combine placeholders and v8intrinsic plugins.");if(ge(t,"pipelineOperator")&&!be.includes(xe(t,"pipelineOperator","proposal")))throw new Error("'pipelineOperator' requires 'proposal' option whose value should be one of: "+be.map(t=>`'${t}'`).join(", "));if(ge(t,"recordAndTuple")&&!ve.includes(xe(t,"recordAndTuple","syntaxType")))throw new Error("'recordAndTuple' requires 'syntaxType' option whose value should be one of: "+ve.map(t=>`'${t}'`).join(", "))}const Pe={estree:mt,jsx:Zt,flow:Kt,typescript:fe,v8intrinsic:ye,placeholders:me},Te=Object.keys(Pe),Ee={sourceType:"script",sourceFilename:void 0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createParenthesizedExpressions:!1,errorRecovery:!1};function Ae(t){const e={};for(let s=0,r=Object.keys(Ee);s<r.length;s++){const i=r[s];e[i]=t&&null!=t[i]?t[i]:Ee[i]}return e}class Se{constructor(){this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.inParameters=!1,this.maybeInArrowParameters=!1,this.maybeInAsyncArrowHead=!1,this.inPipeline=!1,this.inType=!1,this.noAnonFunctionType=!1,this.inPropertyName=!1,this.hasFlowComment=!1,this.isIterator=!1,this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.soloAwait=!1,this.inFSharpPipelineDirectBody=!1,this.labels=[],this.decoratorStack=[[]],this.yieldPos=-1,this.awaitPos=-1,this.comments=[],this.trailingComments=[],this.leadingComments=[],this.commentStack=[],this.commentPreviousNode=null,this.pos=0,this.lineStart=0,this.type=d.eof,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.lastTokStart=0,this.lastTokEnd=0,this.context=[gt.braceStatement],this.exprAllowed=!0,this.containsEsc=!1,this.octalPositions=[],this.exportedIdentifiers=[],this.tokensLength=0}init(t){this.strict=!1!==t.strictMode&&"module"===t.sourceType,this.curLine=t.startLine,this.startLoc=this.endLoc=this.curPosition()}curPosition(){return new at(this.curLine,this.pos-this.lineStart)}clone(t){const e=new Se,s=Object.keys(this);for(let r=0,i=s.length;r<i;r++){const i=s[r];let n=this[i];!t&&Array.isArray(n)&&(n=n.slice()),e[i]=n}return e}}var Ce=function(t){return t>=48&&t<=57};const ke=new Set(["g","m","s","i","y","u"]),Ne={decBinOct:[46,66,69,79,95,98,101,111],hex:[46,88,95,120]},Ie={bin:[48,49]};Ie.oct=[...Ie.bin,50,51,52,53,54,55],Ie.dec=[...Ie.oct,56,57],Ie.hex=[...Ie.dec,65,66,67,68,69,70,97,98,99,100,101,102];class Oe{constructor(t){this.type=t.type,this.value=t.value,this.start=t.start,this.end=t.end,this.loc=new ot(t.startLoc,t.endLoc)}}class De extends dt{constructor(t,e){super(),this.tokens=[],this.state=new Se,this.state.init(t),this.input=e,this.length=e.length,this.isLookahead=!1}pushToken(t){this.tokens.length=this.state.tokensLength,this.tokens.push(t),++this.state.tokensLength}next(){this.isLookahead||(this.checkKeywordEscapes(),this.options.tokens&&this.pushToken(new Oe(this.state))),this.state.lastTokEnd=this.state.end,this.state.lastTokStart=this.state.start,this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}eat(t){return!!this.match(t)&&(this.next(),!0)}match(t){return this.state.type===t}lookahead(){const t=this.state;this.state=t.clone(!0),this.isLookahead=!0,this.next(),this.isLookahead=!1;const e=this.state;return this.state=t,e}nextTokenStart(){const t=this.state.pos;it.lastIndex=t;const e=it.exec(this.input);return t+e[0].length}lookaheadCharCode(){return this.input.charCodeAt(this.nextTokenStart())}setStrict(t){if(this.state.strict=t,this.match(d.num)||this.match(d.string)){this.state.pos=this.state.start;while(this.state.pos<this.state.lineStart)this.state.lineStart=this.input.lastIndexOf("\n",this.state.lineStart-2)+1,--this.state.curLine;this.nextToken()}}curContext(){return this.state.context[this.state.context.length-1]}nextToken(){const t=this.curContext();if(t&&t.preserveSpace||this.skipSpace(),this.state.octalPositions=[],this.state.start=this.state.pos,this.state.startLoc=this.state.curPosition(),this.state.pos>=this.length)return void this.finishToken(d.eof);const e=null==t?void 0:t.override;e?e(this):this.getTokenFromCode(this.input.codePointAt(this.state.pos))}pushComment(t,e,s,r,i,n){const a={type:t?"CommentBlock":"CommentLine",value:e,start:s,end:r,loc:new ot(i,n)};this.options.tokens&&this.pushToken(a),this.state.comments.push(a),this.addComment(a)}skipBlockComment(){const t=this.state.curPosition(),e=this.state.pos,s=this.input.indexOf("*/",this.state.pos+2);if(-1===s)throw this.raise(e,ut.UnterminatedComment);let r;this.state.pos=s+2,st.lastIndex=e;while((r=st.exec(this.input))&&r.index<this.state.pos)++this.state.curLine,this.state.lineStart=r.index+r[0].length;this.isLookahead||this.pushComment(!0,this.input.slice(e+2,s),e,this.state.pos,t,this.state.curPosition())}skipLineComment(t){const e=this.state.pos,s=this.state.curPosition();let r=this.input.charCodeAt(this.state.pos+=t);if(this.state.pos<this.length)while(!rt(r)&&++this.state.pos<this.length)r=this.input.charCodeAt(this.state.pos);this.isLookahead||this.pushComment(!1,this.input.slice(e+t,this.state.pos),e,this.state.pos,s,this.state.curPosition())}skipSpace(){t:while(this.state.pos<this.length){const t=this.input.charCodeAt(this.state.pos);switch(t){case 32:case 160:case 9:++this.state.pos;break;case 13:10===this.input.charCodeAt(this.state.pos+1)&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break t}break;default:if(!nt(t))break t;++this.state.pos}}}finishToken(t,e){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();const s=this.state.type;this.state.type=t,this.state.value=e,this.isLookahead||this.updateContext(s)}readToken_numberSign(){if(0===this.state.pos&&this.readToken_interpreter())return;const t=this.state.pos+1,e=this.input.charCodeAt(t);if(e>=48&&e<=57)throw this.raise(this.state.pos,ut.UnexpectedDigitAfterHash);if(!this.hasPlugin("recordAndTuple")||123!==e&&91!==e){if(!this.hasPlugin("classPrivateProperties")&&!this.hasPlugin("classPrivateMethods")&&"smart"!==this.getPluginOption("pipelineOperator","proposal"))throw this.raise(this.state.pos,ut.InvalidOrUnexpectedToken,"#");this.finishOp(d.hash,1)}else{if("hash"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,123===e?ut.RecordExpressionHashIncorrectStartSyntaxType:ut.TupleExpressionHashIncorrectStartSyntaxType);123===e?this.finishToken(d.braceHashL):this.finishToken(d.bracketHashL),this.state.pos+=2}}readToken_dot(){const t=this.input.charCodeAt(this.state.pos+1);t>=48&&t<=57?this.readNumber(!0):46===t&&46===this.input.charCodeAt(this.state.pos+2)?(this.state.pos+=3,this.finishToken(d.ellipsis)):(++this.state.pos,this.finishToken(d.dot))}readToken_slash(){if(this.state.exprAllowed&&!this.state.inType)return++this.state.pos,void this.readRegexp();const t=this.input.charCodeAt(this.state.pos+1);61===t?this.finishOp(d.assign,2):this.finishOp(d.slash,1)}readToken_interpreter(){if(0!==this.state.pos||this.length<2)return!1;let t=this.input.charCodeAt(this.state.pos+1);if(33!==t)return!1;const e=this.state.pos;this.state.pos+=1;while(!rt(t)&&++this.state.pos<this.length)t=this.input.charCodeAt(this.state.pos);const s=this.input.slice(e+2,this.state.pos);return this.finishToken(d.interpreterDirective,s),!0}readToken_mult_modulo(t){let e=42===t?d.star:d.modulo,s=1,r=this.input.charCodeAt(this.state.pos+1);const i=this.state.exprAllowed;42===t&&42===r&&(s++,r=this.input.charCodeAt(this.state.pos+2),e=d.exponent),61!==r||i||(s++,e=d.assign),this.finishOp(e,s)}readToken_pipe_amp(t){const e=this.input.charCodeAt(this.state.pos+1);if(e!==t){if(124===t){if(62===e)return void this.finishOp(d.pipeline,2);if(this.hasPlugin("recordAndTuple")&&125===e){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,ut.RecordExpressionBarIncorrectEndSyntaxType);return void this.finishOp(d.braceBarR,2)}if(this.hasPlugin("recordAndTuple")&&93===e){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,ut.TupleExpressionBarIncorrectEndSyntaxType);return void this.finishOp(d.bracketBarR,2)}}61!==e?this.finishOp(124===t?d.bitwiseOR:d.bitwiseAND,1):this.finishOp(d.assign,2)}else 61===this.input.charCodeAt(this.state.pos+2)?this.finishOp(d.assign,3):this.finishOp(124===t?d.logicalOR:d.logicalAND,2)}readToken_caret(){const t=this.input.charCodeAt(this.state.pos+1);61===t?this.finishOp(d.assign,2):this.finishOp(d.bitwiseXOR,1)}readToken_plus_min(t){const e=this.input.charCodeAt(this.state.pos+1);if(e===t)return 45!==e||this.inModule||62!==this.input.charCodeAt(this.state.pos+2)||0!==this.state.lastTokEnd&&!et.test(this.input.slice(this.state.lastTokEnd,this.state.pos))?void this.finishOp(d.incDec,2):(this.skipLineComment(3),this.skipSpace(),void this.nextToken());61===e?this.finishOp(d.assign,2):this.finishOp(d.plusMin,1)}readToken_lt_gt(t){const e=this.input.charCodeAt(this.state.pos+1);let s=1;return e===t?(s=62===t&&62===this.input.charCodeAt(this.state.pos+2)?3:2,61===this.input.charCodeAt(this.state.pos+s)?void this.finishOp(d.assign,s+1):void this.finishOp(d.bitShift,s)):33!==e||60!==t||this.inModule||45!==this.input.charCodeAt(this.state.pos+2)||45!==this.input.charCodeAt(this.state.pos+3)?(61===e&&(s=2),void this.finishOp(d.relational,s)):(this.skipLineComment(4),this.skipSpace(),void this.nextToken())}readToken_eq_excl(t){const e=this.input.charCodeAt(this.state.pos+1);if(61!==e)return 61===t&&62===e?(this.state.pos+=2,void this.finishToken(d.arrow)):void this.finishOp(61===t?d.eq:d.bang,1);this.finishOp(d.equality,61===this.input.charCodeAt(this.state.pos+2)?3:2)}readToken_question(){const t=this.input.charCodeAt(this.state.pos+1),e=this.input.charCodeAt(this.state.pos+2);63!==t||this.state.inType?46!==t||e>=48&&e<=57?(++this.state.pos,this.finishToken(d.question)):(this.state.pos+=2,this.finishToken(d.questionDot)):61===e?this.finishOp(d.assign,3):this.finishOp(d.nullishCoalescing,2)}getTokenFromCode(t){switch(t){case 46:return void this.readToken_dot();case 40:return++this.state.pos,void this.finishToken(d.parenL);case 41:return++this.state.pos,void this.finishToken(d.parenR);case 59:return++this.state.pos,void this.finishToken(d.semi);case 44:return++this.state.pos,void this.finishToken(d.comma);case 91:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,ut.TupleExpressionBarIncorrectStartSyntaxType);this.finishToken(d.bracketBarL),this.state.pos+=2}else++this.state.pos,this.finishToken(d.bracketL);return;case 93:return++this.state.pos,void this.finishToken(d.bracketR);case 123:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,ut.RecordExpressionBarIncorrectStartSyntaxType);this.finishToken(d.braceBarL),this.state.pos+=2}else++this.state.pos,this.finishToken(d.braceL);return;case 125:return++this.state.pos,void this.finishToken(d.braceR);case 58:return void(this.hasPlugin("functionBind")&&58===this.input.charCodeAt(this.state.pos+1)?this.finishOp(d.doubleColon,2):(++this.state.pos,this.finishToken(d.colon)));case 63:return void this.readToken_question();case 96:return++this.state.pos,void this.finishToken(d.backQuote);case 48:{const t=this.input.charCodeAt(this.state.pos+1);if(120===t||88===t)return void this.readRadixNumber(16);if(111===t||79===t)return void this.readRadixNumber(8);if(98===t||66===t)return void this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return void this.readNumber(!1);case 34:case 39:return void this.readString(t);case 47:return void this.readToken_slash();case 37:case 42:return void this.readToken_mult_modulo(t);case 124:case 38:return void this.readToken_pipe_amp(t);case 94:return void this.readToken_caret();case 43:case 45:return void this.readToken_plus_min(t);case 60:case 62:return void this.readToken_lt_gt(t);case 61:case 33:return void this.readToken_eq_excl(t);case 126:return void this.finishOp(d.tilde,1);case 64:return++this.state.pos,void this.finishToken(d.at);case 35:return void this.readToken_numberSign();case 92:return void this.readWord();default:if(At(t))return void this.readWord()}throw this.raise(this.state.pos,ut.InvalidOrUnexpectedToken,String.fromCodePoint(t))}finishOp(t,e){const s=this.input.slice(this.state.pos,this.state.pos+e);this.state.pos+=e,this.finishToken(t,s)}readRegexp(){const t=this.state.pos;let e,s;for(;;){if(this.state.pos>=this.length)throw this.raise(t,ut.UnterminatedRegExp);const r=this.input.charAt(this.state.pos);if(et.test(r))throw this.raise(t,ut.UnterminatedRegExp);if(e)e=!1;else{if("["===r)s=!0;else if("]"===r&&s)s=!1;else if("/"===r&&!s)break;e="\\"===r}++this.state.pos}const r=this.input.slice(t,this.state.pos);++this.state.pos;let i="";while(this.state.pos<this.length){const t=this.input[this.state.pos],e=this.input.codePointAt(this.state.pos);if(ke.has(t))i.indexOf(t)>-1&&this.raise(this.state.pos+1,ut.DuplicateRegExpFlags);else{if(!St(e)&&92!==e)break;this.raise(this.state.pos+1,ut.MalformedRegExpFlags)}++this.state.pos,i+=t}this.finishToken(d.regexp,{pattern:r,flags:i})}readInt(t,e,s,r=!0){const i=this.state.pos,n=16===t?Ne.hex:Ne.decBinOct,a=16===t?Ie.hex:10===t?Ie.dec:8===t?Ie.oct:Ie.bin;let o=!1,c=0;for(let h=0,l=null==e?1/0:e;h<l;++h){const e=this.input.charCodeAt(this.state.pos);let i;if(this.hasPlugin("numericSeparator")&&95===e){const t=this.input.charCodeAt(this.state.pos-1),e=this.input.charCodeAt(this.state.pos+1);(-1===a.indexOf(e)||n.indexOf(t)>-1||n.indexOf(e)>-1||Number.isNaN(e))&&this.raise(this.state.pos,ut.UnexpectedNumericSeparator),r||this.raise(this.state.pos,ut.NumericSeparatorInEscapeSequence),++this.state.pos}else{if(i=e>=97?e-97+10:e>=65?e-65+10:Ce(e)?e-48:1/0,i>=t)if(this.options.errorRecovery&&i<=9)i=0,this.raise(this.state.start+h+2,ut.InvalidDigit,t);else{if(!s)break;i=0,o=!0}++this.state.pos,c=c*t+i}}return this.state.pos===i||null!=e&&this.state.pos-i!==e||o?null:c}readRadixNumber(t){const e=this.state.pos;let s=!1;this.state.pos+=2;const r=this.readInt(t);if(null==r&&this.raise(this.state.start+2,ut.InvalidDigit,t),110===this.input.charCodeAt(this.state.pos)&&(++this.state.pos,s=!0),At(this.input.codePointAt(this.state.pos)))throw this.raise(this.state.pos,ut.NumberIdentifier);if(s){const t=this.input.slice(e,this.state.pos).replace(/[_n]/g,"");this.finishToken(d.bigint,t)}else this.finishToken(d.num,r)}readNumber(t){const e=this.state.pos;let s=!1,r=!1,i=!1;t||null!==this.readInt(10)||this.raise(e,ut.InvalidNumber);let n=this.state.pos-e>=2&&48===this.input.charCodeAt(e);n&&(this.state.strict&&this.raise(e,ut.StrictOctalLiteral),/[89]/.test(this.input.slice(e,this.state.pos))&&(n=!1,i=!0));let a=this.input.charCodeAt(this.state.pos);if(46!==a||n||(++this.state.pos,this.readInt(10),s=!0,a=this.input.charCodeAt(this.state.pos)),69!==a&&101!==a||n||(a=this.input.charCodeAt(++this.state.pos),43!==a&&45!==a||++this.state.pos,null===this.readInt(10)&&this.raise(e,"Invalid number"),s=!0,a=this.input.charCodeAt(this.state.pos)),this.hasPlugin("numericSeparator")&&(n||i)){const t=this.input.slice(e,this.state.pos).indexOf("_");t>0&&this.raise(t+e,ut.ZeroDigitNumericSeparator)}if(110===a&&((s||n||i)&&this.raise(e,"Invalid BigIntLiteral"),++this.state.pos,r=!0),At(this.input.codePointAt(this.state.pos)))throw this.raise(this.state.pos,ut.NumberIdentifier);const o=this.input.slice(e,this.state.pos).replace(/[_n]/g,"");if(r)return void this.finishToken(d.bigint,o);const c=n?parseInt(o,8):parseFloat(o);this.finishToken(d.num,c)}readCodePoint(t){const e=this.input.charCodeAt(this.state.pos);let s;if(123===e){const e=++this.state.pos;if(s=this.readHexChar(this.input.indexOf("}",this.state.pos)-this.state.pos,!0,t),++this.state.pos,null!==s&&s>1114111){if(!t)return null;this.raise(e,ut.InvalidCodePoint)}}else s=this.readHexChar(4,!1,t);return s}readString(t){let e="",s=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,ut.UnterminatedString);const r=this.input.charCodeAt(this.state.pos);if(r===t)break;if(92===r)e+=this.input.slice(s,this.state.pos),e+=this.readEscapedChar(!1),s=this.state.pos;else if(8232===r||8233===r)++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;else{if(rt(r))throw this.raise(this.state.start,ut.UnterminatedString);++this.state.pos}}e+=this.input.slice(s,this.state.pos++),this.finishToken(d.string,e)}readTmplToken(){let t="",e=this.state.pos,s=!1;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,ut.UnterminatedTemplate);const r=this.input.charCodeAt(this.state.pos);if(96===r||36===r&&123===this.input.charCodeAt(this.state.pos+1))return this.state.pos===this.state.start&&this.match(d.template)?36===r?(this.state.pos+=2,void this.finishToken(d.dollarBraceL)):(++this.state.pos,void this.finishToken(d.backQuote)):(t+=this.input.slice(e,this.state.pos),void this.finishToken(d.template,s?null:t));if(92===r){t+=this.input.slice(e,this.state.pos);const r=this.readEscapedChar(!0);null===r?s=!0:t+=r,e=this.state.pos}else if(rt(r)){switch(t+=this.input.slice(e,this.state.pos),++this.state.pos,r){case 13:10===this.input.charCodeAt(this.state.pos)&&++this.state.pos;case 10:t+="\n";break;default:t+=String.fromCharCode(r);break}++this.state.curLine,this.state.lineStart=this.state.pos,e=this.state.pos}else++this.state.pos}}readEscapedChar(t){const e=!t,s=this.input.charCodeAt(++this.state.pos);switch(++this.state.pos,s){case 110:return"\n";case 114:return"\r";case 120:{const t=this.readHexChar(2,!1,e);return null===t?null:String.fromCharCode(t)}case 117:{const t=this.readCodePoint(e);return null===t?null:String.fromCodePoint(t)}case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.state.pos)&&++this.state.pos;case 10:this.state.lineStart=this.state.pos,++this.state.curLine;case 8232:case 8233:return"";case 56:case 57:if(t)return null;default:if(s>=48&&s<=55){const e=this.state.pos-1;let s=this.input.substr(this.state.pos-1,3).match(/^[0-7]+/)[0],r=parseInt(s,8);r>255&&(s=s.slice(0,-1),r=parseInt(s,8)),this.state.pos+=s.length-1;const i=this.input.charCodeAt(this.state.pos);if("0"!==s||56===i||57===i){if(t)return null;this.state.strict?this.raise(e,ut.StrictOctalLiteral):this.state.octalPositions.push(e)}return String.fromCharCode(r)}return String.fromCharCode(s)}}readHexChar(t,e,s){const r=this.state.pos,i=this.readInt(16,t,e,!1);return null===i&&(s?this.raise(r,ut.InvalidEscapeSequence):this.state.pos=r-1),i}readWord1(){let t="";this.state.containsEsc=!1;const e=this.state.pos;let s=this.state.pos;while(this.state.pos<this.length){const r=this.input.codePointAt(this.state.pos);if(St(r))this.state.pos+=r<=65535?1:2;else if(this.state.isIterator&&64===r)++this.state.pos;else{if(92!==r)break;{this.state.containsEsc=!0,t+=this.input.slice(s,this.state.pos);const r=this.state.pos,i=this.state.pos===e?At:St;if(117!==this.input.charCodeAt(++this.state.pos)){this.raise(this.state.pos,ut.MissingUnicodeEscape);continue}++this.state.pos;const n=this.readCodePoint(!0);null!==n&&(i(n)||this.raise(r,ut.EscapedCharNotAnIdentifier),t+=String.fromCodePoint(n)),s=this.state.pos}}}return t+this.input.slice(s,this.state.pos)}isIterator(t){return"@@iterator"===t||"@@asyncIterator"===t}readWord(){const t=this.readWord1(),e=l.get(t)||d.name;!this.state.isIterator||this.isIterator(t)&&this.state.inType||this.raise(this.state.pos,ut.InvalidIdentifier,t),this.finishToken(e,t)}checkKeywordEscapes(){const t=this.state.type.keyword;t&&this.state.containsEsc&&this.raise(this.state.start,ut.InvalidEscapedReservedWord,t)}braceIsBlock(t){const e=this.curContext();return e===gt.functionExpression||e===gt.functionStatement||(t!==d.colon||e!==gt.braceStatement&&e!==gt.braceExpression?t===d._return||t===d.name&&this.state.exprAllowed?et.test(this.input.slice(this.state.lastTokEnd,this.state.start)):t===d._else||t===d.semi||t===d.eof||t===d.parenR||t===d.arrow||(t===d.braceL?e===gt.braceStatement:t!==d._var&&t!==d._const&&t!==d.name&&(t===d.relational||!this.state.exprAllowed)):!e.isExpr)}updateContext(t){const e=this.state.type;let s;!e.keyword||t!==d.dot&&t!==d.questionDot?(s=e.updateContext)?s.call(this,t):this.state.exprAllowed=e.beforeExpr:this.state.exprAllowed=!1}}class Me extends De{addExtra(t,e,s){if(!t)return;const r=t.extra=t.extra||{};r[e]=s}isRelational(t){return this.match(d.relational)&&this.state.value===t}isLookaheadRelational(t){const e=this.nextTokenStart();if(this.input.charAt(e)===t){if(e+1===this.input.length)return!0;const s=this.input.charCodeAt(e+1);return s!==t.charCodeAt(0)&&61!==s}return!1}expectRelational(t){this.isRelational(t)?this.next():this.unexpected(null,d.relational)}isContextual(t){return this.match(d.name)&&this.state.value===t&&!this.state.containsEsc}isUnparsedContextual(t,e){const s=t+e.length;return this.input.slice(t,s)===e&&(s===this.input.length||!St(this.input.charCodeAt(s)))}isLookaheadContextual(t){const e=this.nextTokenStart();return this.isUnparsedContextual(e,t)}eatContextual(t){return this.isContextual(t)&&this.eat(d.name)}expectContextual(t,e){this.eatContextual(t)||this.unexpected(null,e)}canInsertSemicolon(){return this.match(d.eof)||this.match(d.braceR)||this.hasPrecedingLineBreak()}hasPrecedingLineBreak(){return et.test(this.input.slice(this.state.lastTokEnd,this.state.start))}isLineTerminator(){return this.eat(d.semi)||this.canInsertSemicolon()}semicolon(){this.isLineTerminator()||this.unexpected(null,d.semi)}expect(t,e){this.eat(t)||this.unexpected(e,t)}assertNoSpace(t="Unexpected space."){this.state.start>this.state.lastTokEnd&&this.raise(this.state.lastTokEnd,t)}unexpected(t,e="Unexpected token"){throw"string"!==typeof e&&(e=`Unexpected token, expected "${e.label}"`),this.raise(null!=t?t:this.state.start,e)}expectPlugin(t,e){if(!this.hasPlugin(t))throw this.raiseWithData(null!=e?e:this.state.start,{missingPlugin:[t]},`This experimental syntax requires enabling the parser plugin: '${t}'`);return!0}expectOnePlugin(t,e){if(!t.some(t=>this.hasPlugin(t)))throw this.raiseWithData(null!=e?e:this.state.start,{missingPlugin:t},`This experimental syntax requires enabling one of the following parser plugin(s): '${t.join(", ")}'`)}checkYieldAwaitInDefaultParams(){-1!==this.state.yieldPos&&(-1===this.state.awaitPos||this.state.yieldPos<this.state.awaitPos)&&this.raise(this.state.yieldPos,"Yield cannot be used as name inside a generator function"),-1!==this.state.awaitPos&&this.raise(this.state.awaitPos,"Await cannot be used as name inside an async function")}tryParse(t,e=this.state.clone()){const s={node:null};try{const r=t((t=null)=>{throw s.node=t,s});if(this.state.errors.length>e.errors.length){const t=this.state;return this.state=e,{node:r,error:t.errors[e.errors.length],thrown:!1,aborted:!1,failState:t}}return{node:r,error:null,thrown:!1,aborted:!1,failState:null}}catch(r){const t=this.state;if(this.state=e,r instanceof SyntaxError)return{node:null,error:r,thrown:!0,aborted:!1,failState:t};if(r===s)return{node:s.node,error:null,thrown:!1,aborted:!0,failState:t};throw r}}checkExpressionErrors(t,e){if(!t)return!1;const{shorthandAssign:s,doubleProto:r}=t;if(!e)return s>=0||r>=0;s>=0&&this.unexpected(s),r>=0&&this.raise(r,ut.DuplicateProto)}}class Le{constructor(){this.shorthandAssign=-1,this.doubleProto=-1}}class _e{constructor(t,e,s){this.type="",this.start=e,this.end=0,this.loc=new ot(s),t&&t.options.ranges&&(this.range=[e,0]),t&&t.filename&&(this.loc.filename=t.filename)}__clone(){const t=new _e,e=Object.keys(this);for(let s=0,r=e.length;s<r;s++){const r=e[s];"leadingComments"!==r&&"trailingComments"!==r&&"innerComments"!==r&&(t[r]=this[r])}return t}}class Re extends Me{startNode(){return new _e(this,this.state.start,this.state.startLoc)}startNodeAt(t,e){return new _e(this,t,e)}startNodeAtNode(t){return this.startNodeAt(t.start,t.loc.start)}finishNode(t,e){return this.finishNodeAt(t,e,this.state.lastTokEnd,this.state.lastTokEndLoc)}finishNodeAt(t,e,s,r){return t.type=e,t.end=s,t.loc.end=r,this.options.ranges&&(t.range[1]=s),this.processComment(t),t}resetStartLocation(t,e,s){t.start=e,t.loc.start=s,this.options.ranges&&(t.range[0]=e)}resetEndLocation(t,e=this.state.lastTokEnd,s=this.state.lastTokEndLoc){t.end=e,t.loc.end=s,this.options.ranges&&(t.range[1]=e)}resetStartLocationFromNode(t,e){this.resetStartLocation(t,e.start,e.loc.start)}}const je=t=>"ParenthesizedExpression"===t.type?je(t.expression):t;class Fe extends Re{toAssignable(t){var e,s;let r=void 0;switch(("ParenthesizedExpression"===t.type||(null==(e=t.extra)?void 0:e.parenthesized))&&(r=je(t),"Identifier"!==r.type&&"MemberExpression"!==r.type&&this.raise(t.start,ut.InvalidParenthesizedAssignment)),t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":break;case"ObjectExpression":t.type="ObjectPattern";for(let e=0,s=t.properties.length,r=s-1;e<s;e++){var i;const s=t.properties[e],n=e===r;this.toAssignableObjectExpressionProp(s,n),n&&"RestElement"===s.type&&(null==(i=t.extra)?void 0:i.trailingComma)&&this.raiseRestNotLast(t.extra.trailingComma)}break;case"ObjectProperty":this.toAssignable(t.value);break;case"SpreadElement":{this.checkToRestConversion(t),t.type="RestElement";const e=t.argument;this.toAssignable(e);break}case"ArrayExpression":t.type="ArrayPattern",this.toAssignableList(t.elements,null==(s=t.extra)?void 0:s.trailingComma);break;case"AssignmentExpression":"="!==t.operator&&this.raise(t.left.end,ut.MissingEqInAssignment),t.type="AssignmentPattern",delete t.operator,this.toAssignable(t.left);break;case"ParenthesizedExpression":this.toAssignable(r);break}return t}toAssignableObjectExpressionProp(t,e){if("ObjectMethod"===t.type){const e="get"===t.kind||"set"===t.kind?ut.PatternHasAccessor:ut.PatternHasMethod;this.raise(t.key.start,e)}else"SpreadElement"!==t.type||e?this.toAssignable(t):this.raiseRestNotLast(t.start)}toAssignableList(t,e){let s=t.length;if(s){const r=t[s-1];if(r&&"RestElement"===r.type)--s;else if(r&&"SpreadElement"===r.type){r.type="RestElement";const t=r.argument;this.toAssignable(t),"Identifier"!==t.type&&"MemberExpression"!==t.type&&"ArrayPattern"!==t.type&&"ObjectPattern"!==t.type&&this.unexpected(t.start),e&&this.raiseTrailingCommaAfterRest(e),--s}}for(let r=0;r<s;r++){const e=t[r];e&&(this.toAssignable(e),"RestElement"===e.type&&this.raiseRestNotLast(e.start))}return t}toReferencedList(t,e){return t}toReferencedListDeep(t,e){this.toReferencedList(t,e);for(let s=0;s<t.length;s++){const e=t[s];e&&"ArrayExpression"===e.type&&this.toReferencedListDeep(e.elements)}}parseSpread(t,e){const s=this.startNode();return this.next(),s.argument=this.parseMaybeAssign(!1,t,void 0,e),this.finishNode(s,"SpreadElement")}parseRestBinding(){const t=this.startNode();return this.next(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")}parseBindingAtom(){switch(this.state.type){case d.bracketL:{const t=this.startNode();return this.next(),t.elements=this.parseBindingList(d.bracketR,93,!0),this.finishNode(t,"ArrayPattern")}case d.braceL:return this.parseObj(d.braceR,!0)}return this.parseIdentifier()}parseBindingList(t,e,s,r){const i=[];let n=!0;while(!this.eat(t))if(n?n=!1:this.expect(d.comma),s&&this.match(d.comma))i.push(null);else{if(this.eat(t))break;if(this.match(d.ellipsis)){i.push(this.parseAssignableListItemTypes(this.parseRestBinding())),this.checkCommaAfterRest(e),this.expect(t);break}{const t=[];this.match(d.at)&&this.hasPlugin("decorators")&&this.raise(this.state.start,ut.UnsupportedParameterDecorator);while(this.match(d.at))t.push(this.parseDecorator());i.push(this.parseAssignableListItem(r,t))}}return i}parseAssignableListItem(t,e){const s=this.parseMaybeDefault();this.parseAssignableListItemTypes(s);const r=this.parseMaybeDefault(s.start,s.loc.start,s);return e.length&&(s.decorators=e),r}parseAssignableListItemTypes(t){return t}parseMaybeDefault(t,e,s){if(e=e||this.state.startLoc,t=t||this.state.start,s=s||this.parseBindingAtom(),!this.eat(d.eq))return s;const r=this.startNodeAt(t,e);return r.left=s,r.right=this.parseMaybeAssign(),this.finishNode(r,"AssignmentPattern")}checkLVal(t,e=V,s,r,i,n=!1){switch(t.type){case"Identifier":if(this.state.strict&&(n?Lt(t.name,this.inModule):Mt(t.name))&&this.raise(t.start,e===V?ut.StrictEvalArguments:ut.StrictEvalArgumentsBinding,t.name),s){const e=`_${t.name}`;s[e]?this.raise(t.start,ut.ParamDupe):s[e]=!0}i&&"let"===t.name&&this.raise(t.start,ut.LetInLexicalBinding),e&V||this.scope.declareName(t.name,e,t.start);break;case"MemberExpression":e!==V&&this.raise(t.start,ut.InvalidPropertyBindingPattern);break;case"ObjectPattern":for(let r=0,n=t.properties;r<n.length;r++){let t=n[r];if("ObjectProperty"===t.type)t=t.value;else if("ObjectMethod"===t.type)continue;this.checkLVal(t,e,s,"object destructuring pattern",i)}break;case"ArrayPattern":for(let r=0,n=t.elements;r<n.length;r++){const t=n[r];t&&this.checkLVal(t,e,s,"array destructuring pattern",i)}break;case"AssignmentPattern":this.checkLVal(t.left,e,s,"assignment pattern");break;case"RestElement":this.checkLVal(t.argument,e,s,"rest element");break;case"ParenthesizedExpression":this.checkLVal(t.expression,e,s,"parenthesized expression");break;default:this.raise(t.start,e===V?ut.InvalidLhs:ut.InvalidLhsBinding,r)}}checkToRestConversion(t){"Identifier"!==t.argument.type&&"MemberExpression"!==t.argument.type&&this.raise(t.argument.start,ut.InvalidRestAssignmentPattern)}checkCommaAfterRest(t){this.match(d.comma)&&(this.lookaheadCharCode()===t?this.raiseTrailingCommaAfterRest(this.state.start):this.raiseRestNotLast(this.state.start))}raiseRestNotLast(t){throw this.raise(t,ut.ElementAfterRest)}raiseTrailingCommaAfterRest(t){this.raise(t,ut.RestTrailingComma)}}class Be extends Fe{checkDuplicatedProto(t,e,s){if("SpreadElement"===t.type||t.computed||t.kind||t.shorthand)return;const r=t.key,i="Identifier"===r.type?r.name:String(r.value);"__proto__"===i&&(e.used&&(s?-1===s.doubleProto&&(s.doubleProto=r.start):this.raise(r.start,ut.DuplicateProto)),e.used=!0)}getExpression(){let t=ie;this.hasPlugin("topLevelAwait")&&this.inModule&&(t|=ae),this.scope.enter(m),this.prodParam.enter(t),this.nextToken();const e=this.parseExpression();return this.match(d.eof)||this.unexpected(),e.comments=this.state.comments,e.errors=this.state.errors,e}parseExpression(t,e){const s=this.state.start,r=this.state.startLoc,i=this.parseMaybeAssign(t,e);if(this.match(d.comma)){const n=this.startNodeAt(s,r);n.expressions=[i];while(this.eat(d.comma))n.expressions.push(this.parseMaybeAssign(t,e));return this.toReferencedList(n.expressions),this.finishNode(n,"SequenceExpression")}return i}parseMaybeAssign(t,e,s,r){const i=this.state.start,n=this.state.startLoc;if(this.isContextual("yield")){if(this.prodParam.hasYield){let e=this.parseYield(t);return s&&(e=s.call(this,e,i,n)),e}this.state.exprAllowed=!1}let a;e?a=!1:(e=new Le,a=!0),(this.match(d.parenL)||this.match(d.name))&&(this.state.potentialArrowAt=this.state.start);let o=this.parseMaybeConditional(t,e,r);if(s&&(o=s.call(this,o,i,n)),this.state.type.isAssign){const s=this.startNodeAt(i,n),r=this.state.value;return s.operator=r,"??="===r&&this.expectPlugin("logicalAssignment"),"||="!==r&&"&&="!==r||this.expectPlugin("logicalAssignment"),this.match(d.eq)?(s.left=this.toAssignable(o),e.doubleProto=-1):s.left=o,e.shorthandAssign>=s.left.start&&(e.shorthandAssign=-1),this.checkLVal(o,void 0,void 0,"assignment expression"),this.next(),s.right=this.parseMaybeAssign(t),this.finishNode(s,"AssignmentExpression")}return a&&this.checkExpressionErrors(e,!0),o}parseMaybeConditional(t,e,s){const r=this.state.start,i=this.state.startLoc,n=this.state.potentialArrowAt,a=this.parseExprOps(t,e);return"ArrowFunctionExpression"===a.type&&a.start===n||this.checkExpressionErrors(e,!1)?a:this.parseConditional(a,t,r,i,s)}parseConditional(t,e,s,r,i){if(this.eat(d.question)){const i=this.startNodeAt(s,r);return i.test=t,i.consequent=this.parseMaybeAssign(),this.expect(d.colon),i.alternate=this.parseMaybeAssign(e),this.finishNode(i,"ConditionalExpression")}return t}parseExprOps(t,e){const s=this.state.start,r=this.state.startLoc,i=this.state.potentialArrowAt,n=this.parseMaybeUnary(e);return"ArrowFunctionExpression"===n.type&&n.start===i||this.checkExpressionErrors(e,!1)?n:this.parseExprOp(n,s,r,-1,t)}parseExprOp(t,e,s,r,i){let n=this.state.type.binop;if(null!=n&&(!i||!this.match(d._in))&&n>r){const a=this.state.value;if("|>"===a&&this.state.inFSharpPipelineDirectBody)return t;const o=this.startNodeAt(e,s);o.left=t,o.operator=a,"**"!==a||"UnaryExpression"!==t.type||!this.options.createParenthesizedExpressions&&t.extra&&t.extra.parenthesized||this.raise(t.argument.start,ut.UnexpectedTokenUnaryExponentiation);const c=this.state.type,h=c===d.logicalOR||c===d.logicalAND,l=c===d.nullishCoalescing;if(c===d.pipeline?(this.expectPlugin("pipelineOperator"),this.state.inPipeline=!0,this.checkPipelineAtInfixOperator(t,e)):l&&(n=d.logicalAND.binop),this.next(),c===d.pipeline&&"minimal"===this.getPluginOption("pipelineOperator","proposal")&&this.match(d.name)&&"await"===this.state.value&&this.prodParam.hasAwait)throw this.raise(this.state.start,ut.UnexpectedAwaitAfterPipelineBody);o.right=this.parseExprOpRightExpr(c,n,i),this.finishNode(o,h||l?"LogicalExpression":"BinaryExpression");const p=this.state.type;if(l&&(p===d.logicalOR||p===d.logicalAND)||h&&p===d.nullishCoalescing)throw this.raise(this.state.start,ut.MixingCoalesceWithLogical);return this.parseExprOp(o,e,s,r,i)}return t}parseExprOpRightExpr(t,e,s){const r=this.state.start,i=this.state.startLoc;switch(t){case d.pipeline:switch(this.getPluginOption("pipelineOperator","proposal")){case"smart":return this.withTopicPermittingContext(()=>this.parseSmartPipelineBody(this.parseExprOpBaseRightExpr(t,e,s),r,i));case"fsharp":return this.withSoloAwaitPermittingContext(()=>this.parseFSharpPipelineBody(e,s))}default:return this.parseExprOpBaseRightExpr(t,e,s)}}parseExprOpBaseRightExpr(t,e,s){const r=this.state.start,i=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnary(),r,i,t.rightAssociative?e-1:e,s)}parseMaybeUnary(t){if(this.isContextual("await")&&this.isAwaitAllowed())return this.parseAwait();if(this.state.type.prefix){const e=this.startNode(),s=this.match(d.incDec);if(e.operator=this.state.value,e.prefix=!0,"throw"===e.operator&&this.expectPlugin("throwExpressions"),this.next(),e.argument=this.parseMaybeUnary(),this.checkExpressionErrors(t,!0),s)this.checkLVal(e.argument,void 0,void 0,"prefix operation");else if(this.state.strict&&"delete"===e.operator){const t=e.argument;"Identifier"===t.type?this.raise(e.start,ut.StrictDelete):"MemberExpression"===t.type&&"PrivateName"===t.property.type&&this.raise(e.start,ut.DeletePrivateField)}return this.finishNode(e,s?"UpdateExpression":"UnaryExpression")}const e=this.state.start,s=this.state.startLoc;let r=this.parseExprSubscripts(t);if(this.checkExpressionErrors(t,!1))return r;while(this.state.type.postfix&&!this.canInsertSemicolon()){const t=this.startNodeAt(e,s);t.operator=this.state.value,t.prefix=!1,t.argument=r,this.checkLVal(r,void 0,void 0,"postfix operation"),this.next(),r=this.finishNode(t,"UpdateExpression")}return r}parseExprSubscripts(t){const e=this.state.start,s=this.state.startLoc,r=this.state.potentialArrowAt,i=this.parseExprAtom(t);return"ArrowFunctionExpression"===i.type&&i.start===r?i:this.parseSubscripts(i,e,s)}parseSubscripts(t,e,s,r){const i={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsyncArrow(t),stop:!1};do{const n=this.state.maybeInAsyncArrowHead;i.maybeAsyncArrow&&(this.state.maybeInAsyncArrowHead=!0),t=this.parseSubscript(t,e,s,r,i),i.maybeAsyncArrow=!1,this.state.maybeInAsyncArrowHead=n}while(!i.stop);return t}parseSubscript(t,e,s,r,i){if(!r&&this.eat(d.doubleColon)){const n=this.startNodeAt(e,s);return n.object=t,n.callee=this.parseNoCallExpr(),i.stop=!0,this.parseSubscripts(this.finishNode(n,"BindExpression"),e,s,r)}let n=!1;if(this.match(d.questionDot)){if(i.optionalChainMember=n=!0,r&&40===this.lookaheadCharCode())return i.stop=!0,t;this.next()}const a=this.eat(d.bracketL);if(n&&!this.match(d.parenL)&&!this.match(d.backQuote)||a||this.eat(d.dot)){const r=this.startNodeAt(e,s);return r.object=t,r.property=a?this.parseExpression():n?this.parseIdentifier(!0):this.parseMaybePrivateName(!0),r.computed=a,"PrivateName"===r.property.type&&("Super"===r.object.type&&this.raise(e,ut.SuperPrivateField),this.classScope.usePrivateName(r.property.id.name,r.property.start)),a&&this.expect(d.bracketR),i.optionalChainMember?(r.optional=n,this.finishNode(r,"OptionalMemberExpression")):this.finishNode(r,"MemberExpression")}if(!r&&this.match(d.parenL)){const r=this.state.maybeInArrowParameters,a=this.state.yieldPos,o=this.state.awaitPos;this.state.maybeInArrowParameters=!0,this.state.yieldPos=-1,this.state.awaitPos=-1,this.next();let c=this.startNodeAt(e,s);return c.callee=t,n?(c.optional=!0,c.arguments=this.parseCallExpressionArguments(d.parenR,!1)):c.arguments=this.parseCallExpressionArguments(d.parenR,i.maybeAsyncArrow,"Import"===t.type,"Super"!==t.type,c),this.finishCallExpression(c,i.optionalChainMember),i.maybeAsyncArrow&&this.shouldParseAsyncArrow()&&!n?(i.stop=!0,c=this.parseAsyncArrowFromCallExpression(this.startNodeAt(e,s),c),this.checkYieldAwaitInDefaultParams(),this.state.yieldPos=a,this.state.awaitPos=o):(this.toReferencedListDeep(c.arguments),-1!==a&&(this.state.yieldPos=a),(this.isAwaitAllowed()||r)&&-1===o||(this.state.awaitPos=o)),this.state.maybeInArrowParameters=r,c}return this.match(d.backQuote)?this.parseTaggedTemplateExpression(e,s,t,i):(i.stop=!0,t)}parseTaggedTemplateExpression(t,e,s,r,i){const n=this.startNodeAt(t,e);return n.tag=s,n.quasi=this.parseTemplate(!0),i&&(n.typeParameters=i),r.optionalChainMember&&this.raise(t,ut.OptionalChainingNoTemplate),this.finishNode(n,"TaggedTemplateExpression")}atPossibleAsyncArrow(t){return"Identifier"===t.type&&"async"===t.name&&this.state.lastTokEnd===t.end&&!this.canInsertSemicolon()&&t.end-t.start===5&&t.start===this.state.potentialArrowAt}finishCallExpression(t,e){if("Import"===t.callee.type)if(1!==t.arguments.length)this.raise(t.start,ut.ImportCallArity);else{const e=t.arguments[0];e&&"SpreadElement"===e.type&&this.raise(e.start,ut.ImportCallSpreadArgument)}return this.finishNode(t,e?"OptionalCallExpression":"CallExpression")}parseCallExpressionArguments(t,e,s,r,i){const n=[];let a,o=!0;const c=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;while(!this.eat(t)){if(o)o=!1;else if(this.expect(d.comma),this.match(t)){s&&this.raise(this.state.lastTokStart,ut.ImportCallArgumentTrailingComma),i&&this.addExtra(i,"trailingComma",this.state.lastTokStart),this.next();break}this.match(d.parenL)&&!a&&(a=this.state.start),n.push(this.parseExprListItem(!1,e?new Le:void 0,e?{start:0}:void 0,r))}return e&&a&&this.shouldParseAsyncArrow()&&this.unexpected(),this.state.inFSharpPipelineDirectBody=c,n}shouldParseAsyncArrow(){return this.match(d.arrow)&&!this.canInsertSemicolon()}parseAsyncArrowFromCallExpression(t,e){var s;return this.expect(d.arrow),this.parseArrowExpression(t,e.arguments,!0,null==(s=e.extra)?void 0:s.trailingComma),t}parseNoCallExpr(){const t=this.state.start,e=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),t,e,!0)}parseExprAtom(t){this.state.type===d.slash&&this.readRegexp();const e=this.state.potentialArrowAt===this.state.start;let s;switch(this.state.type){case d._super:return s=this.startNode(),this.next(),!this.match(d.parenL)||this.scope.allowDirectSuper||this.options.allowSuperOutsideMethod?this.scope.allowSuper||this.options.allowSuperOutsideMethod||this.raise(s.start,ut.UnexpectedSuper):this.raise(s.start,ut.SuperNotAllowed),this.match(d.parenL)||this.match(d.bracketL)||this.match(d.dot)||this.raise(s.start,ut.UnsupportedSuper),this.finishNode(s,"Super");case d._import:return s=this.startNode(),this.next(),this.match(d.dot)?this.parseImportMetaProperty(s):(this.match(d.parenL)||this.raise(this.state.lastTokStart,ut.UnsupportedImport),this.finishNode(s,"Import"));case d._this:return s=this.startNode(),this.next(),this.finishNode(s,"ThisExpression");case d.name:{s=this.startNode();const t=this.state.containsEsc,r=this.parseIdentifier();if(!t&&"async"===r.name&&this.match(d._function)&&!this.canInsertSemicolon()){const t=this.state.context.length-1;if(this.state.context[t]!==gt.functionStatement)throw new Error("Internal error");return this.state.context[t]=gt.functionExpression,this.next(),this.parseFunction(s,void 0,!0)}if(e&&!t&&"async"===r.name&&this.match(d.name)&&!this.canInsertSemicolon()){const t=this.state.maybeInArrowParameters,e=this.state.maybeInAsyncArrowHead,r=this.state.yieldPos,i=this.state.awaitPos;this.state.maybeInArrowParameters=!0,this.state.maybeInAsyncArrowHead=!0,this.state.yieldPos=-1,this.state.awaitPos=-1;const n=[this.parseIdentifier()];return this.expect(d.arrow),this.checkYieldAwaitInDefaultParams(),this.state.maybeInArrowParameters=t,this.state.maybeInAsyncArrowHead=e,this.state.yieldPos=r,this.state.awaitPos=i,this.parseArrowExpression(s,n,!0),s}return e&&this.match(d.arrow)&&!this.canInsertSemicolon()?(this.next(),this.parseArrowExpression(s,[r],!1),s):r}case d._do:{this.expectPlugin("doExpressions");const t=this.startNode();this.next();const e=this.state.labels;return this.state.labels=[],t.body=this.parseBlock(),this.state.labels=e,this.finishNode(t,"DoExpression")}case d.regexp:{const t=this.state.value;return s=this.parseLiteral(t.value,"RegExpLiteral"),s.pattern=t.pattern,s.flags=t.flags,s}case d.num:return this.parseLiteral(this.state.value,"NumericLiteral");case d.bigint:return this.parseLiteral(this.state.value,"BigIntLiteral");case d.string:return this.parseLiteral(this.state.value,"StringLiteral");case d._null:return s=this.startNode(),this.next(),this.finishNode(s,"NullLiteral");case d._true:case d._false:return this.parseBooleanLiteral();case d.parenL:return this.parseParenAndDistinguishExpression(e);case d.bracketBarL:case d.bracketHashL:{this.expectPlugin("recordAndTuple");const e=this.state.inFSharpPipelineDirectBody,r=this.state.type===d.bracketBarL?d.bracketBarR:d.bracketR;return this.state.inFSharpPipelineDirectBody=!1,s=this.startNode(),this.next(),s.elements=this.parseExprList(r,!0,t,s),this.state.inFSharpPipelineDirectBody=e,this.finishNode(s,"TupleExpression")}case d.bracketL:{const e=this.state.inFSharpPipelineDirectBody;return this.state.inFSharpPipelineDirectBody=!1,s=this.startNode(),this.next(),s.elements=this.parseExprList(d.bracketR,!0,t,s),this.state.maybeInArrowParameters||this.toReferencedList(s.elements),this.state.inFSharpPipelineDirectBody=e,this.finishNode(s,"ArrayExpression")}case d.braceBarL:case d.braceHashL:{this.expectPlugin("recordAndTuple");const e=this.state.inFSharpPipelineDirectBody,s=this.state.type===d.braceBarL?d.braceBarR:d.braceR;this.state.inFSharpPipelineDirectBody=!1;const r=this.parseObj(s,!1,!0,t);return this.state.inFSharpPipelineDirectBody=e,r}case d.braceL:{const e=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;const s=this.parseObj(d.braceR,!1,!1,t);return this.state.inFSharpPipelineDirectBody=e,s}case d._function:return this.parseFunctionExpression();case d.at:this.parseDecorators();case d._class:return s=this.startNode(),this.takeDecorators(s),this.parseClass(s,!1);case d._new:return this.parseNew();case d.backQuote:return this.parseTemplate(!1);case d.doubleColon:{s=this.startNode(),this.next(),s.object=null;const t=s.callee=this.parseNoCallExpr();if("MemberExpression"===t.type)return this.finishNode(s,"BindExpression");throw this.raise(t.start,ut.UnsupportedBind)}case d.hash:if(this.state.inPipeline)return s=this.startNode(),"smart"!==this.getPluginOption("pipelineOperator","proposal")&&this.raise(s.start,ut.PrimaryTopicRequiresSmartPipeline),this.next(),this.primaryTopicReferenceIsAllowedInCurrentTopicContext()||this.raise(s.start,ut.PrimaryTopicNotAllowed),this.registerTopicReference(),this.finishNode(s,"PipelinePrimaryTopicReference");default:throw this.unexpected()}}parseBooleanLiteral(){const t=this.startNode();return t.value=this.match(d._true),this.next(),this.finishNode(t,"BooleanLiteral")}parseMaybePrivateName(t){const e=this.match(d.hash);if(e){this.expectOnePlugin(["classPrivateProperties","classPrivateMethods"]),t||this.raise(this.state.pos,ut.UnexpectedPrivateField);const e=this.startNode();return this.next(),this.assertNoSpace("Unexpected space between # and identifier"),e.id=this.parseIdentifier(!0),this.finishNode(e,"PrivateName")}return this.parseIdentifier(!0)}parseFunctionExpression(){const t=this.startNode();let e=this.startNode();return this.next(),e=this.createIdentifier(e,"function"),this.prodParam.hasYield&&this.eat(d.dot)?this.parseMetaProperty(t,e,"sent"):this.parseFunction(t)}parseMetaProperty(t,e,s){t.meta=e,"function"===e.name&&"sent"===s&&(this.isContextual(s)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected());const r=this.state.containsEsc;return t.property=this.parseIdentifier(!0),(t.property.name!==s||r)&&this.raise(t.property.start,ut.UnsupportedMetaProperty,e.name,s),this.finishNode(t,"MetaProperty")}parseImportMetaProperty(t){const e=this.createIdentifier(this.startNodeAtNode(t),"import");return this.expect(d.dot),this.isContextual("meta")?(this.expectPlugin("importMeta"),this.inModule||this.raiseWithData(e.start,{code:"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"},ut.ImportMetaOutsideModule),this.sawUnambiguousESM=!0):this.hasPlugin("importMeta")||this.raise(e.start,ut.ImportCallArityLtOne),this.parseMetaProperty(t,e,"meta")}parseLiteral(t,e,s,r){s=s||this.state.start,r=r||this.state.startLoc;const i=this.startNodeAt(s,r);return this.addExtra(i,"rawValue",t),this.addExtra(i,"raw",this.input.slice(s,this.state.end)),i.value=t,this.next(),this.finishNode(i,e)}parseParenAndDistinguishExpression(t){const e=this.state.start,s=this.state.startLoc;let r;this.expect(d.parenL);const i=this.state.maybeInArrowParameters,n=this.state.yieldPos,a=this.state.awaitPos,o=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.yieldPos=-1,this.state.awaitPos=-1,this.state.inFSharpPipelineDirectBody=!1;const c=this.state.start,h=this.state.startLoc,l=[],p=new Le,u={start:0};let f,m,y=!0;while(!this.match(d.parenR)){if(y)y=!1;else if(this.expect(d.comma,u.start||null),this.match(d.parenR)){m=this.state.start;break}if(this.match(d.ellipsis)){const t=this.state.start,e=this.state.startLoc;f=this.state.start,l.push(this.parseParenItem(this.parseRestBinding(),t,e)),this.checkCommaAfterRest(41);break}l.push(this.parseMaybeAssign(!1,p,this.parseParenItem,u))}const g=this.state.start,x=this.state.startLoc;this.expect(d.parenR),this.state.maybeInArrowParameters=i,this.state.inFSharpPipelineDirectBody=o;let b=this.startNodeAt(e,s);if(t&&this.shouldParseArrow()&&(b=this.parseArrow(b))){this.isAwaitAllowed()||this.state.maybeInAsyncArrowHead||(this.state.awaitPos=a),this.checkYieldAwaitInDefaultParams(),this.state.yieldPos=n,this.state.awaitPos=a;for(let t=0;t<l.length;t++){const e=l[t];e.extra&&e.extra.parenthesized&&this.unexpected(e.extra.parenStart)}return this.parseArrowExpression(b,l,!1),b}if(-1!==n&&(this.state.yieldPos=n),-1!==a&&(this.state.awaitPos=a),l.length||this.unexpected(this.state.lastTokStart),m&&this.unexpected(m),f&&this.unexpected(f),this.checkExpressionErrors(p,!0),u.start&&this.unexpected(u.start),this.toReferencedListDeep(l,!0),l.length>1?(r=this.startNodeAt(c,h),r.expressions=l,this.finishNodeAt(r,"SequenceExpression",g,x)):r=l[0],!this.options.createParenthesizedExpressions)return this.addExtra(r,"parenthesized",!0),this.addExtra(r,"parenStart",e),r;const v=this.startNodeAt(e,s);return v.expression=r,this.finishNode(v,"ParenthesizedExpression"),v}shouldParseArrow(){return!this.canInsertSemicolon()}parseArrow(t){if(this.eat(d.arrow))return t}parseParenItem(t,e,s){return t}parseNew(){const t=this.startNode();let e=this.startNode();if(this.next(),e=this.createIdentifier(e,"new"),this.eat(d.dot)){const s=this.parseMetaProperty(t,e,"target");if(!this.scope.inNonArrowFunction&&!this.scope.inClass){let t=ut.UnexpectedNewTarget;this.hasPlugin("classProperties")&&(t+=" or class properties"),this.raise(s.start,t)}return s}return t.callee=this.parseNoCallExpr(),"Import"===t.callee.type?this.raise(t.callee.start,ut.ImportCallNotNewExpression):"OptionalMemberExpression"===t.callee.type||"OptionalCallExpression"===t.callee.type?this.raise(this.state.lastTokEnd,ut.OptionalChainingNoNew):this.eat(d.questionDot)&&this.raise(this.state.start,ut.OptionalChainingNoNew),this.parseNewArguments(t),this.finishNode(t,"NewExpression")}parseNewArguments(t){if(this.eat(d.parenL)){const e=this.parseExprList(d.parenR);this.toReferencedList(e),t.arguments=e}else t.arguments=[]}parseTemplateElement(t){const e=this.startNode();return null===this.state.value&&(t||this.raise(this.state.start+1,ut.InvalidEscapeSequenceTemplate)),e.value={raw:this.input.slice(this.state.start,this.state.end).replace(/\r\n?/g,"\n"),cooked:this.state.value},this.next(),e.tail=this.match(d.backQuote),this.finishNode(e,"TemplateElement")}parseTemplate(t){const e=this.startNode();this.next(),e.expressions=[];let s=this.parseTemplateElement(t);e.quasis=[s];while(!s.tail)this.expect(d.dollarBraceL),e.expressions.push(this.parseExpression()),this.expect(d.braceR),e.quasis.push(s=this.parseTemplateElement(t));return this.next(),this.finishNode(e,"TemplateLiteral")}parseObj(t,e,s,r){const i=Object.create(null);let n=!0;const a=this.startNode();a.properties=[],this.next();while(!this.eat(t)){if(n)n=!1;else if(this.expect(d.comma),this.match(t)){this.addExtra(a,"trailingComma",this.state.lastTokStart),this.next();break}const s=this.parseObjectMember(e,r);e||this.checkDuplicatedProto(s,i,r),s.shorthand&&this.addExtra(s,"shorthand",!0),a.properties.push(s)}let o="ObjectExpression";return e?o="ObjectPattern":s&&(o="RecordExpression"),this.finishNode(a,o)}isAsyncProp(t){return!t.computed&&"Identifier"===t.key.type&&"async"===t.key.name&&(this.match(d.name)||this.match(d.num)||this.match(d.string)||this.match(d.bracketL)||this.state.type.keyword||this.match(d.star))&&!this.hasPrecedingLineBreak()}parseObjectMember(t,e){let s=[];if(this.match(d.at)){this.hasPlugin("decorators")&&this.raise(this.state.start,ut.UnsupportedPropertyDecorator);while(this.match(d.at))s.push(this.parseDecorator())}const r=this.startNode();let i,n,a=!1,o=!1;if(this.match(d.ellipsis))return s.length&&this.unexpected(),t?(this.next(),r.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(r,"RestElement")):this.parseSpread();s.length&&(r.decorators=s,s=[]),r.method=!1,(t||e)&&(i=this.state.start,n=this.state.startLoc),t||(a=this.eat(d.star));const c=this.state.containsEsc;return this.parsePropertyName(r,!1),t||c||a||!this.isAsyncProp(r)?o=!1:(o=!0,a=this.eat(d.star),this.parsePropertyName(r,!1)),this.parseObjPropValue(r,i,n,a,o,t,e,c),r}isGetterOrSetterMethod(t,e){return!e&&!t.computed&&"Identifier"===t.key.type&&("get"===t.key.name||"set"===t.key.name)&&(this.match(d.string)||this.match(d.num)||this.match(d.bracketL)||this.match(d.name)||!!this.state.type.keyword)}getGetterSetterExpectedParamCount(t){return"get"===t.kind?0:1}checkGetterSetterParams(t){const e=this.getGetterSetterExpectedParamCount(t),s=t.start;t.params.length!==e&&("get"===t.kind?this.raise(s,ut.BadGetterArity):this.raise(s,ut.BadSetterArity)),"set"===t.kind&&"RestElement"===t.params[t.params.length-1].type&&this.raise(s,ut.BadSetterRestParameter)}parseObjectMethod(t,e,s,r,i){return s||e||this.match(d.parenL)?(r&&this.unexpected(),t.kind="method",t.method=!0,this.parseMethod(t,e,s,!1,!1,"ObjectMethod")):!i&&this.isGetterOrSetterMethod(t,r)?((e||s)&&this.unexpected(),t.kind=t.key.name,this.parsePropertyName(t,!1),this.parseMethod(t,!1,!1,!1,!1,"ObjectMethod"),this.checkGetterSetterParams(t),t):void 0}parseObjectProperty(t,e,s,r,i){return t.shorthand=!1,this.eat(d.colon)?(t.value=r?this.parseMaybeDefault(this.state.start,this.state.startLoc):this.parseMaybeAssign(!1,i),this.finishNode(t,"ObjectProperty")):t.computed||"Identifier"!==t.key.type?void 0:(this.checkReservedWord(t.key.name,t.key.start,!0,!0),r?t.value=this.parseMaybeDefault(e,s,t.key.__clone()):this.match(d.eq)&&i?(-1===i.shorthandAssign&&(i.shorthandAssign=this.state.start),t.value=this.parseMaybeDefault(e,s,t.key.__clone())):t.value=t.key.__clone(),t.shorthand=!0,this.finishNode(t,"ObjectProperty"))}parseObjPropValue(t,e,s,r,i,n,a,o){const c=this.parseObjectMethod(t,r,i,n,o)||this.parseObjectProperty(t,e,s,n,a);return c||this.unexpected(),c}parsePropertyName(t,e){if(this.eat(d.bracketL))t.computed=!0,t.key=this.parseMaybeAssign(),this.expect(d.bracketR);else{const s=this.state.inPropertyName;this.state.inPropertyName=!0,t.key=this.match(d.num)||this.match(d.string)||this.match(d.bigint)?this.parseExprAtom():this.parseMaybePrivateName(e),"PrivateName"!==t.key.type&&(t.computed=!1),this.state.inPropertyName=s}return t.key}initFunction(t,e){t.id=null,t.generator=!1,t.async=!!e}parseMethod(t,e,s,r,i,n,a=!1){const o=this.state.yieldPos,c=this.state.awaitPos;this.state.yieldPos=-1,this.state.awaitPos=-1,this.initFunction(t,s),t.generator=!!e;const h=r;return this.scope.enter(y|b|(a?w:0)|(i?v:0)),this.prodParam.enter(he(s,t.generator)),this.parseFunctionParams(t,h),this.parseFunctionBodyAndFinish(t,n,!0),this.prodParam.exit(),this.scope.exit(),this.state.yieldPos=o,this.state.awaitPos=c,t}parseArrowExpression(t,e,s,r){this.scope.enter(y|g),this.prodParam.enter(he(s,!1)),this.initFunction(t,s);const i=this.state.maybeInArrowParameters,n=this.state.yieldPos,a=this.state.awaitPos;return e&&(this.state.maybeInArrowParameters=!0,this.setArrowFunctionParameters(t,e,r)),this.state.maybeInArrowParameters=!1,this.state.yieldPos=-1,this.state.awaitPos=-1,this.parseFunctionBody(t,!0),this.prodParam.exit(),this.scope.exit(),this.state.maybeInArrowParameters=i,this.state.yieldPos=n,this.state.awaitPos=a,this.finishNode(t,"ArrowFunctionExpression")}setArrowFunctionParameters(t,e,s){t.params=this.toAssignableList(e,s)}parseFunctionBodyAndFinish(t,e,s=!1){this.parseFunctionBody(t,!1,s),this.finishNode(t,e)}parseFunctionBody(t,e,s=!1){const r=e&&!this.match(d.braceL),i=this.state.inParameters;if(this.state.inParameters=!1,r)t.body=this.parseMaybeAssign(),this.checkParams(t,!1,e,!1);else{const r=this.state.strict,i=this.state.labels;this.state.labels=[],this.prodParam.enter(this.prodParam.currentFlags()|oe),t.body=this.parseBlock(!0,!1,i=>{const n=!this.isSimpleParamList(t.params);if(i&&n){const e="method"!==t.kind&&"constructor"!==t.kind||!t.key?t.start:t.key.end;this.raise(e,ut.IllegalLanguageModeDirective)}const a=!r&&this.state.strict;this.checkParams(t,!this.state.strict&&!e&&!s&&!n,e,a),this.state.strict&&t.id&&this.checkLVal(t.id,z,void 0,"function name",void 0,a)}),this.prodParam.exit(),this.state.labels=i}this.state.inParameters=i}isSimpleParamList(t){for(let e=0,s=t.length;e<s;e++)if("Identifier"!==t[e].type)return!1;return!0}checkParams(t,e,s,r=!0){const i=Object.create(null);for(let n=0;n<t.params.length;n++)this.checkLVal(t.params[n],R,e?null:i,"function parameter list",void 0,r)}parseExprList(t,e,s,r){const i=[];let n=!0;while(!this.eat(t)){if(n)n=!1;else if(this.expect(d.comma),this.match(t)){r&&this.addExtra(r,"trailingComma",this.state.lastTokStart),this.next();break}i.push(this.parseExprListItem(e,s))}return i}parseExprListItem(t,e,s,r){let i;if(t&&this.match(d.comma))i=null;else if(this.match(d.ellipsis)){const t=this.state.start,r=this.state.startLoc;i=this.parseParenItem(this.parseSpread(e,s),t,r)}else if(this.match(d.question)){this.expectPlugin("partialApplication"),r||this.raise(this.state.start,ut.UnexpectedArgumentPlaceholder);const t=this.startNode();this.next(),i=this.finishNode(t,"ArgumentPlaceholder")}else i=this.parseMaybeAssign(!1,e,this.parseParenItem,s);return i}parseIdentifier(t){const e=this.startNode(),s=this.parseIdentifierName(e.start,t);return this.createIdentifier(e,s)}createIdentifier(t,e){return t.name=e,t.loc.identifierName=e,this.finishNode(t,"Identifier")}parseIdentifierName(t,e){let s;if(this.match(d.name))s=this.state.value;else{if(!this.state.type.keyword)throw this.unexpected();s=this.state.type.keyword,"class"!==s&&"function"!==s||this.state.lastTokEnd===this.state.lastTokStart+1&&46===this.input.charCodeAt(this.state.lastTokStart)||this.state.context.pop()}return e?this.state.type=d.name:this.checkReservedWord(s,this.state.start,!!this.state.type.keyword,!1),this.next(),s}checkReservedWord(t,e,s,r){if(this.prodParam.hasYield&&"yield"===t)return void this.raise(e,ut.YieldBindingIdentifier);if("await"===t){if(this.prodParam.hasAwait)return void this.raise(e,ut.AwaitBindingIdentifier);-1===this.state.awaitPos&&(this.state.maybeInAsyncArrowHead||this.isAwaitAllowed())&&(this.state.awaitPos=this.state.start)}if(this.scope.inClass&&!this.scope.inNonArrowFunction&&"arguments"===t)return void this.raise(e,ut.ArgumentsDisallowedInInitializer);if(s&&_t(t))return void this.raise(e,ut.UnexpectedKeyword,t);const i=this.state.strict?r?Lt:Dt:Ot;i(t,this.inModule)&&(this.prodParam.hasAwait||"await"!==t?this.raise(e,ut.UnexpectedReservedWord,t):this.raise(e,ut.AwaitNotInAsyncFunction))}isAwaitAllowed(){return this.scope.inFunction?this.prodParam.hasAwait:!!this.options.allowAwaitOutsideFunction||!!this.hasPlugin("topLevelAwait")&&(this.inModule&&this.prodParam.hasAwait)}parseAwait(){const t=this.startNode();return this.next(),this.state.inParameters?this.raise(t.start,ut.AwaitExpressionFormalParameter):-1===this.state.awaitPos&&(this.state.awaitPos=t.start),this.eat(d.star)&&this.raise(t.start,ut.ObsoleteAwaitStar),this.scope.inFunction||this.options.allowAwaitOutsideFunction||(this.hasPrecedingLineBreak()||this.match(d.plusMin)||this.match(d.parenL)||this.match(d.bracketL)||this.match(d.backQuote)||this.match(d.regexp)||this.match(d.slash)||this.hasPlugin("v8intrinsic")&&this.match(d.modulo)?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(t.argument=this.parseMaybeUnary()),this.finishNode(t,"AwaitExpression")}parseYield(t){const e=this.startNode();return this.state.inParameters?this.raise(e.start,ut.YieldInParameter):-1===this.state.yieldPos&&(this.state.yieldPos=e.start),this.next(),this.match(d.semi)||!this.match(d.star)&&!this.state.type.startsExpr||this.hasPrecedingLineBreak()?(e.delegate=!1,e.argument=null):(e.delegate=this.eat(d.star),e.argument=this.parseMaybeAssign(t)),this.finishNode(e,"YieldExpression")}checkPipelineAtInfixOperator(t,e){"smart"===this.getPluginOption("pipelineOperator","proposal")&&"SequenceExpression"===t.type&&this.raise(e,ut.PipelineHeadSequenceExpression)}parseSmartPipelineBody(t,e,s){const r=this.checkSmartPipelineBodyStyle(t);return this.checkSmartPipelineBodyEarlyErrors(t,r,e),this.parseSmartPipelineBodyInStyle(t,r,e,s)}checkSmartPipelineBodyEarlyErrors(t,e,s){if(this.match(d.arrow))throw this.raise(this.state.start,ut.PipelineBodyNoArrow);"PipelineTopicExpression"===e&&"SequenceExpression"===t.type&&this.raise(s,ut.PipelineBodySequenceExpression)}parseSmartPipelineBodyInStyle(t,e,s,r){const i=this.startNodeAt(s,r);switch(e){case"PipelineBareFunction":i.callee=t;break;case"PipelineBareConstructor":i.callee=t.callee;break;case"PipelineBareAwaitedFunction":i.callee=t.argument;break;case"PipelineTopicExpression":this.topicReferenceWasUsedInCurrentTopicContext()||this.raise(s,ut.PipelineTopicUnused),i.expression=t;break;default:throw new Error(`Internal @babel/parser error: Unknown pipeline style (${e})`)}return this.finishNode(i,e)}checkSmartPipelineBodyStyle(t){switch(t.type){default:return this.isSimpleReference(t)?"PipelineBareFunction":"PipelineTopicExpression"}}isSimpleReference(t){switch(t.type){case"MemberExpression":return!t.computed&&this.isSimpleReference(t.object);case"Identifier":return!0;default:return!1}}withTopicPermittingContext(t){const e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}withTopicForbiddingContext(t){const e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}withSoloAwaitPermittingContext(t){const e=this.state.soloAwait;this.state.soloAwait=!0;try{return t()}finally{this.state.soloAwait=e}}registerTopicReference(){this.state.topicContext.maxTopicIndex=0}primaryTopicReferenceIsAllowedInCurrentTopicContext(){return this.state.topicContext.maxNumOfResolvableTopics>=1}topicReferenceWasUsedInCurrentTopicContext(){return null!=this.state.topicContext.maxTopicIndex&&this.state.topicContext.maxTopicIndex>=0}parseFSharpPipelineBody(t,e){const s=this.state.start,r=this.state.startLoc;this.state.potentialArrowAt=this.state.start;const i=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;const n=this.parseExprOp(this.parseMaybeUnary(),s,r,t,e);return this.state.inFSharpPipelineDirectBody=i,n}}const Ue={kind:"loop"},qe={kind:"switch"},Ve=0,ze=1,He=2,We=4;class Ke extends Be{parseTopLevel(t,e){if(e.sourceType=this.options.sourceType,e.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(e,!0,!0,d.eof),this.inModule&&!this.options.allowUndeclaredExports&&this.scope.undefinedExports.size>0)for(let s=0,r=Array.from(this.scope.undefinedExports);s<r.length;s++){const[t]=r[s],e=this.scope.undefinedExports.get(t);this.raise(e,ut.ModuleExportUndefined,t)}return t.program=this.finishNode(e,"Program"),t.comments=this.state.comments,this.options.tokens&&(t.tokens=this.tokens),this.finishNode(t,"File")}stmtToDirective(t){const e=t.expression,s=this.startNodeAt(e.start,e.loc.start),r=this.startNodeAt(t.start,t.loc.start),i=this.input.slice(e.start,e.end),n=s.value=i.slice(1,-1);return this.addExtra(s,"raw",i),this.addExtra(s,"rawValue",n),r.value=this.finishNodeAt(s,"DirectiveLiteral",e.end,e.loc.end),this.finishNodeAt(r,"Directive",t.end,t.loc.end)}parseInterpreterDirective(){if(!this.match(d.interpreterDirective))return null;const t=this.startNode();return t.value=this.state.value,this.next(),this.finishNode(t,"InterpreterDirective")}isLet(t){if(!this.isContextual("let"))return!1;const e=this.nextTokenStart(),s=this.input.charCodeAt(e);if(91===s)return!0;if(t)return!1;if(123===s)return!0;if(At(s)){let t=e+1;while(St(this.input.charCodeAt(t)))++t;const s=this.input.slice(e,t);if(!Rt.test(s))return!0}return!1}parseStatement(t,e){return this.match(d.at)&&this.parseDecorators(!0),this.parseStatementContent(t,e)}parseStatementContent(t,e){let s=this.state.type;const r=this.startNode();let i;switch(this.isLet(t)&&(s=d._var,i="let"),s){case d._break:case d._continue:return this.parseBreakContinueStatement(r,s.keyword);case d._debugger:return this.parseDebuggerStatement(r);case d._do:return this.parseDoStatement(r);case d._for:return this.parseForStatement(r);case d._function:if(46===this.lookaheadCharCode())break;return t&&(this.state.strict?this.raise(this.state.start,ut.StrictFunction):"if"!==t&&"label"!==t&&this.raise(this.state.start,ut.SloppyFunction)),this.parseFunctionStatement(r,!1,!t);case d._class:return t&&this.unexpected(),this.parseClass(r,!0);case d._if:return this.parseIfStatement(r);case d._return:return this.parseReturnStatement(r);case d._switch:return this.parseSwitchStatement(r);case d._throw:return this.parseThrowStatement(r);case d._try:return this.parseTryStatement(r);case d._const:case d._var:return i=i||this.state.value,t&&"var"!==i&&this.raise(this.state.start,ut.UnexpectedLexicalDeclaration),this.parseVarStatement(r,i);case d._while:return this.parseWhileStatement(r);case d._with:return this.parseWithStatement(r);case d.braceL:return this.parseBlock();case d.semi:return this.parseEmptyStatement(r);case d._export:case d._import:{const t=this.lookaheadCharCode();if(40===t||46===t)break;let i;return this.options.allowImportExportEverywhere||e||this.raise(this.state.start,ut.UnexpectedImportExport),this.next(),s===d._import?(i=this.parseImport(r),"ImportDeclaration"!==i.type||i.importKind&&"value"!==i.importKind||(this.sawUnambiguousESM=!0)):(i=this.parseExport(r),("ExportNamedDeclaration"!==i.type||i.exportKind&&"value"!==i.exportKind)&&("ExportAllDeclaration"!==i.type||i.exportKind&&"value"!==i.exportKind)&&"ExportDefaultDeclaration"!==i.type||(this.sawUnambiguousESM=!0)),this.assertModuleNodeAllowed(r),i}default:if(this.isAsyncFunction())return t&&this.raise(this.state.start,ut.AsyncFunctionInSingleStatementContext),this.next(),this.parseFunctionStatement(r,!0,!t)}const n=this.state.value,a=this.parseExpression();return s===d.name&&"Identifier"===a.type&&this.eat(d.colon)?this.parseLabeledStatement(r,n,a,t):this.parseExpressionStatement(r,a)}assertModuleNodeAllowed(t){this.options.allowImportExportEverywhere||this.inModule||this.raiseWithData(t.start,{code:"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"},ut.ImportOutsideModule)}takeDecorators(t){const e=this.state.decoratorStack[this.state.decoratorStack.length-1];e.length&&(t.decorators=e,this.resetStartLocationFromNode(t,e[0]),this.state.decoratorStack[this.state.decoratorStack.length-1]=[])}canHaveLeadingDecorator(){return this.match(d._class)}parseDecorators(t){const e=this.state.decoratorStack[this.state.decoratorStack.length-1];while(this.match(d.at)){const t=this.parseDecorator();e.push(t)}if(this.match(d._export))t||this.unexpected(),this.hasPlugin("decorators")&&!this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(this.state.start,ut.DecoratorExportClass);else if(!this.canHaveLeadingDecorator())throw this.raise(this.state.start,ut.UnexpectedLeadingDecorator)}parseDecorator(){this.expectOnePlugin(["decorators-legacy","decorators"]);const t=this.startNode();if(this.next(),this.hasPlugin("decorators")){this.state.decoratorStack.push([]);const e=this.state.start,s=this.state.startLoc;let r;if(this.eat(d.parenL))r=this.parseExpression(),this.expect(d.parenR);else{r=this.parseIdentifier(!1);while(this.eat(d.dot)){const t=this.startNodeAt(e,s);t.object=r,t.property=this.parseIdentifier(!0),t.computed=!1,r=this.finishNode(t,"MemberExpression")}}t.expression=this.parseMaybeDecoratorArguments(r),this.state.decoratorStack.pop()}else t.expression=this.parseExprSubscripts();return this.finishNode(t,"Decorator")}parseMaybeDecoratorArguments(t){if(this.eat(d.parenL)){const e=this.startNodeAtNode(t);return e.callee=t,e.arguments=this.parseCallExpressionArguments(d.parenR,!1),this.toReferencedList(e.arguments),this.finishNode(e,"CallExpression")}return t}parseBreakContinueStatement(t,e){const s="break"===e;return this.next(),this.isLineTerminator()?t.label=null:(t.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(t,e),this.finishNode(t,s?"BreakStatement":"ContinueStatement")}verifyBreakContinue(t,e){const s="break"===e;let r;for(r=0;r<this.state.labels.length;++r){const e=this.state.labels[r];if(null==t.label||e.name===t.label.name){if(null!=e.kind&&(s||"loop"===e.kind))break;if(t.label&&s)break}}r===this.state.labels.length&&this.raise(t.start,ut.IllegalBreakContinue,e)}parseDebuggerStatement(t){return this.next(),this.semicolon(),this.finishNode(t,"DebuggerStatement")}parseHeaderExpression(){this.expect(d.parenL);const t=this.parseExpression();return this.expect(d.parenR),t}parseDoStatement(t){return this.next(),this.state.labels.push(Ue),t.body=this.withTopicForbiddingContext(()=>this.parseStatement("do")),this.state.labels.pop(),this.expect(d._while),t.test=this.parseHeaderExpression(),this.eat(d.semi),this.finishNode(t,"DoWhileStatement")}parseForStatement(t){this.next(),this.state.labels.push(Ue);let e=-1;if(this.isAwaitAllowed()&&this.eatContextual("await")&&(e=this.state.lastTokStart),this.scope.enter(f),this.expect(d.parenL),this.match(d.semi))return e>-1&&this.unexpected(e),this.parseFor(t,null);const s=this.isLet();if(this.match(d._var)||this.match(d._const)||s){const r=this.startNode(),i=s?"let":this.state.value;return this.next(),this.parseVar(r,!0,i),this.finishNode(r,"VariableDeclaration"),(this.match(d._in)||this.isContextual("of"))&&1===r.declarations.length?this.parseForIn(t,r,e):(e>-1&&this.unexpected(e),this.parseFor(t,r))}const r=new Le,i=this.parseExpression(!0,r);if(this.match(d._in)||this.isContextual("of")){this.toAssignable(i);const s=this.isContextual("of")?"for-of statement":"for-in statement";return this.checkLVal(i,void 0,void 0,s),this.parseForIn(t,i,e)}return this.checkExpressionErrors(r,!0),e>-1&&this.unexpected(e),this.parseFor(t,i)}parseFunctionStatement(t,e,s){return this.next(),this.parseFunction(t,ze|(s?0:He),e)}parseIfStatement(t){return this.next(),t.test=this.parseHeaderExpression(),t.consequent=this.parseStatement("if"),t.alternate=this.eat(d._else)?this.parseStatement("if"):null,this.finishNode(t,"IfStatement")}parseReturnStatement(t){return this.prodParam.hasReturn||this.options.allowReturnOutsideFunction||this.raise(this.state.start,ut.IllegalReturn),this.next(),this.isLineTerminator()?t.argument=null:(t.argument=this.parseExpression(),this.semicolon()),this.finishNode(t,"ReturnStatement")}parseSwitchStatement(t){this.next(),t.discriminant=this.parseHeaderExpression();const e=t.cases=[];let s,r;for(this.expect(d.braceL),this.state.labels.push(qe),this.scope.enter(f);!this.match(d.braceR);)if(this.match(d._case)||this.match(d._default)){const t=this.match(d._case);s&&this.finishNode(s,"SwitchCase"),e.push(s=this.startNode()),s.consequent=[],this.next(),t?s.test=this.parseExpression():(r&&this.raise(this.state.lastTokStart,ut.MultipleDefaultsInSwitch),r=!0,s.test=null),this.expect(d.colon)}else s?s.consequent.push(this.parseStatement(null)):this.unexpected();return this.scope.exit(),s&&this.finishNode(s,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(t,"SwitchStatement")}parseThrowStatement(t){return this.next(),et.test(this.input.slice(this.state.lastTokEnd,this.state.start))&&this.raise(this.state.lastTokEnd,ut.NewlineAfterThrow),t.argument=this.parseExpression(),this.semicolon(),this.finishNode(t,"ThrowStatement")}parseTryStatement(t){if(this.next(),t.block=this.parseBlock(),t.handler=null,this.match(d._catch)){const e=this.startNode();if(this.next(),this.match(d.parenL)){this.expect(d.parenL),e.param=this.parseBindingAtom();const t="Identifier"===e.param.type;this.scope.enter(t?x:0),this.checkLVal(e.param,_,null,"catch clause"),this.expect(d.parenR)}else e.param=null,this.scope.enter(f);e.body=this.withTopicForbiddingContext(()=>this.parseBlock(!1,!1)),this.scope.exit(),t.handler=this.finishNode(e,"CatchClause")}return t.finalizer=this.eat(d._finally)?this.parseBlock():null,t.handler||t.finalizer||this.raise(t.start,ut.NoCatchOrFinally),this.finishNode(t,"TryStatement")}parseVarStatement(t,e){return this.next(),this.parseVar(t,!1,e),this.semicolon(),this.finishNode(t,"VariableDeclaration")}parseWhileStatement(t){return this.next(),t.test=this.parseHeaderExpression(),this.state.labels.push(Ue),t.body=this.withTopicForbiddingContext(()=>this.parseStatement("while")),this.state.labels.pop(),this.finishNode(t,"WhileStatement")}parseWithStatement(t){return this.state.strict&&this.raise(this.state.start,ut.StrictWith),this.next(),t.object=this.parseHeaderExpression(),t.body=this.withTopicForbiddingContext(()=>this.parseStatement("with")),this.finishNode(t,"WithStatement")}parseEmptyStatement(t){return this.next(),this.finishNode(t,"EmptyStatement")}parseLabeledStatement(t,e,s,r){for(let n=0,a=this.state.labels;n<a.length;n++){const t=a[n];t.name===e&&this.raise(s.start,ut.LabelRedeclaration,e)}const i=this.state.type.isLoop?"loop":this.match(d._switch)?"switch":null;for(let n=this.state.labels.length-1;n>=0;n--){const e=this.state.labels[n];if(e.statementStart!==t.start)break;e.statementStart=this.state.start,e.kind=i}return this.state.labels.push({name:e,kind:i,statementStart:this.state.start}),t.body=this.parseStatement(r?-1===r.indexOf("label")?r+"label":r:"label"),this.state.labels.pop(),t.label=s,this.finishNode(t,"LabeledStatement")}parseExpressionStatement(t,e){return t.expression=e,this.semicolon(),this.finishNode(t,"ExpressionStatement")}parseBlock(t=!1,e=!0,s){const r=this.startNode();return this.expect(d.braceL),e&&this.scope.enter(f),this.parseBlockBody(r,t,!1,d.braceR,s),e&&this.scope.exit(),this.finishNode(r,"BlockStatement")}isValidDirective(t){return"ExpressionStatement"===t.type&&"StringLiteral"===t.expression.type&&!t.expression.extra.parenthesized}parseBlockBody(t,e,s,r,i){const n=t.body=[],a=t.directives=[];this.parseBlockOrModuleBlockBody(n,e?a:void 0,s,r,i)}parseBlockOrModuleBlockBody(t,e,s,r,i){const n=[],a=this.state.strict;let o=!1,c=!1;while(!this.match(r)){!c&&this.state.octalPositions.length&&n.push(...this.state.octalPositions);const r=this.parseStatement(null,s);if(e&&!c&&this.isValidDirective(r)){const t=this.stmtToDirective(r);e.push(t),o||"use strict"!==t.value.value||(o=!0,this.setStrict(!0))}else c=!0,t.push(r)}if(this.state.strict&&n.length)for(let h=0;h<n.length;h++){const t=n[h];this.raise(t,ut.StrictOctalLiteral)}i&&i.call(this,o),a||this.setStrict(!1),this.next()}parseFor(t,e){return t.init=e,this.expect(d.semi),t.test=this.match(d.semi)?null:this.parseExpression(),this.expect(d.semi),t.update=this.match(d.parenR)?null:this.parseExpression(),this.expect(d.parenR),t.body=this.withTopicForbiddingContext(()=>this.parseStatement("for")),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,"ForStatement")}parseForIn(t,e,s){const r=this.match(d._in);return this.next(),r?s>-1&&this.unexpected(s):t.await=s>-1,"VariableDeclaration"!==e.type||null==e.declarations[0].init||r&&!this.state.strict&&"var"===e.kind&&"Identifier"===e.declarations[0].id.type?"AssignmentPattern"===e.type&&this.raise(e.start,ut.InvalidLhs,"for-loop"):this.raise(e.start,ut.ForInOfLoopInitializer,r?"for-in":"for-of"),t.left=e,t.right=r?this.parseExpression():this.parseMaybeAssign(),this.expect(d.parenR),t.body=this.withTopicForbiddingContext(()=>this.parseStatement("for")),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,r?"ForInStatement":"ForOfStatement")}parseVar(t,e,s){const r=t.declarations=[],i=this.hasPlugin("typescript");for(t.kind=s;;){const t=this.startNode();if(this.parseVarId(t,s),this.eat(d.eq)?t.init=this.parseMaybeAssign(e):("const"!==s||this.match(d._in)||this.isContextual("of")?"Identifier"===t.id.type||e&&(this.match(d._in)||this.isContextual("of"))||this.raise(this.state.lastTokEnd,ut.DeclarationMissingInitializer,"Complex binding patterns"):i||this.unexpected(),t.init=null),r.push(this.finishNode(t,"VariableDeclarator")),!this.eat(d.comma))break}return t}parseVarId(t,e){t.id=this.parseBindingAtom(),this.checkLVal(t.id,"var"===e?R:_,void 0,"variable declaration","var"!==e)}parseFunction(t,e=Ve,s=!1){const r=e&ze,i=e&He,n=!!r&&!(e&We);this.initFunction(t,s),this.match(d.star)&&i&&this.raise(this.state.start,ut.GeneratorInSingleStatementContext),t.generator=this.eat(d.star),r&&(t.id=this.parseFunctionId(n));const a=this.state.maybeInArrowParameters,o=this.state.yieldPos,c=this.state.awaitPos;return this.state.maybeInArrowParameters=!1,this.state.yieldPos=-1,this.state.awaitPos=-1,this.scope.enter(y),this.prodParam.enter(he(s,t.generator)),r||(t.id=this.parseFunctionId()),this.parseFunctionParams(t),this.withTopicForbiddingContext(()=>{this.parseFunctionBodyAndFinish(t,r?"FunctionDeclaration":"FunctionExpression")}),this.prodParam.exit(),this.scope.exit(),r&&!i&&this.registerFunctionStatementId(t),this.state.maybeInArrowParameters=a,this.state.yieldPos=o,this.state.awaitPos=c,t}parseFunctionId(t){return t||this.match(d.name)?this.parseIdentifier():null}parseFunctionParams(t,e){const s=this.state.inParameters;this.state.inParameters=!0,this.expect(d.parenL),t.params=this.parseBindingList(d.parenR,41,!1,e),this.state.inParameters=s,this.checkYieldAwaitInDefaultParams()}registerFunctionStatementId(t){t.id&&this.scope.declareName(t.id.name,this.state.strict||t.generator||t.async?this.scope.treatFunctionsAsVar?R:_:j,t.id.start)}parseClass(t,e,s){this.next(),this.takeDecorators(t);const r=this.state.strict;return this.state.strict=!0,this.parseClassId(t,e,s),this.parseClassSuper(t),t.body=this.parseClassBody(!!t.superClass,r),this.state.strict=r,this.finishNode(t,e?"ClassDeclaration":"ClassExpression")}isClassProperty(){return this.match(d.eq)||this.match(d.semi)||this.match(d.braceR)}isClassMethod(){return this.match(d.parenL)}isNonstaticConstructor(t){return!t.computed&&!t.static&&("constructor"===t.key.name||"constructor"===t.key.value)}parseClassBody(t,e){this.classScope.enter();const s={hadConstructor:!1};let r=[];const i=this.startNode();if(i.body=[],this.expect(d.braceL),this.withTopicForbiddingContext(()=>{while(!this.match(d.braceR)){if(this.eat(d.semi)){if(r.length>0)throw this.raise(this.state.lastTokEnd,ut.DecoratorSemicolon);continue}if(this.match(d.at)){r.push(this.parseDecorator());continue}const e=this.startNode();r.length&&(e.decorators=r,this.resetStartLocationFromNode(e,r[0]),r=[]),this.parseClassMember(i,e,s,t),"constructor"===e.kind&&e.decorators&&e.decorators.length>0&&this.raise(e.start,ut.DecoratorConstructor)}}),e||(this.state.strict=!1),this.next(),r.length)throw this.raise(this.state.start,ut.TrailingDecorator);return this.classScope.exit(),this.finishNode(i,"ClassBody")}parseClassMemberFromModifier(t,e){const s=this.state.containsEsc,r=this.parseIdentifier(!0);if(this.isClassMethod()){const s=e;return s.kind="method",s.computed=!1,s.key=r,s.static=!1,this.pushClassMethod(t,s,!1,!1,!1,!1),!0}if(this.isClassProperty()){const s=e;return s.computed=!1,s.key=r,s.static=!1,t.body.push(this.parseClassProperty(s)),!0}if(s)throw this.unexpected();return!1}parseClassMember(t,e,s,r){const i=this.isContextual("static");i&&this.parseClassMemberFromModifier(t,e)||this.parseClassMemberWithIsStatic(t,e,s,i,r)}parseClassMemberWithIsStatic(t,e,s,r,i){const n=e,a=e,o=e,c=e,h=n,l=n;if(e.static=r,this.eat(d.star))return h.kind="method",this.parseClassPropertyName(h),"PrivateName"===h.key.type?void this.pushClassPrivateMethod(t,a,!0,!1):(this.isNonstaticConstructor(n)&&this.raise(n.key.start,ut.ConstructorIsGenerator),void this.pushClassMethod(t,n,!0,!1,!1,!1));const p=this.state.containsEsc,u=this.parseClassPropertyName(e),f="PrivateName"===u.type,m="Identifier"===u.type,y=this.state.start;if(this.parsePostMemberNameModifiers(l),this.isClassMethod()){if(h.kind="method",f)return void this.pushClassPrivateMethod(t,a,!1,!1);const e=this.isNonstaticConstructor(n);let r=!1;e&&(n.kind="constructor",s.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(u.start,ut.DuplicateConstructor),s.hadConstructor=!0,r=i),this.pushClassMethod(t,n,!1,!1,e,r)}else if(this.isClassProperty())f?this.pushClassPrivateProperty(t,c):this.pushClassProperty(t,o);else if(!m||"async"!==u.name||p||this.isLineTerminator())!m||"get"!==u.name&&"set"!==u.name||p||this.match(d.star)&&this.isLineTerminator()?this.isLineTerminator()?f?this.pushClassPrivateProperty(t,c):this.pushClassProperty(t,o):this.unexpected():(h.kind=u.name,this.parseClassPropertyName(n),"PrivateName"===h.key.type?this.pushClassPrivateMethod(t,a,!1,!1):(this.isNonstaticConstructor(n)&&this.raise(n.key.start,ut.ConstructorIsAccessor),this.pushClassMethod(t,n,!1,!1,!1,!1)),this.checkGetterSetterParams(n));else{const e=this.eat(d.star);l.optional&&this.unexpected(y),h.kind="method",this.parseClassPropertyName(h),this.parsePostMemberNameModifiers(l),"PrivateName"===h.key.type?this.pushClassPrivateMethod(t,a,e,!0):(this.isNonstaticConstructor(n)&&this.raise(n.key.start,ut.ConstructorIsAsync),this.pushClassMethod(t,n,e,!0,!1,!1))}}parseClassPropertyName(t){const e=this.parsePropertyName(t,!0);return t.computed||!t.static||"prototype"!==e.name&&"prototype"!==e.value||this.raise(e.start,ut.StaticPrototype),"PrivateName"===e.type&&"constructor"===e.id.name&&this.raise(e.start,ut.ConstructorClassPrivateField),e}pushClassProperty(t,e){e.computed||"constructor"!==e.key.name&&"constructor"!==e.key.value||this.raise(e.key.start,ut.ConstructorClassField),t.body.push(this.parseClassProperty(e))}pushClassPrivateProperty(t,e){this.expectPlugin("classPrivateProperties",e.key.start);const s=this.parseClassPrivateProperty(e);t.body.push(s),this.classScope.declarePrivateName(s.key.id.name,tt,s.key.start)}pushClassMethod(t,e,s,r,i,n){t.body.push(this.parseMethod(e,s,r,i,n,"ClassMethod",!0))}pushClassPrivateMethod(t,e,s,r){this.expectPlugin("classPrivateMethods",e.key.start);const i=this.parseMethod(e,s,r,!1,!1,"ClassPrivateMethod",!0);t.body.push(i);const n="get"===i.kind?i.static?Y:Q:"set"===i.kind?i.static?J:Z:tt;this.classScope.declarePrivateName(i.key.id.name,n,i.key.start)}parsePostMemberNameModifiers(t){}parseAccessModifier(){}parseClassPrivateProperty(t){return this.scope.enter(w|b),this.prodParam.enter(ie),t.value=this.eat(d.eq)?this.parseMaybeAssign():null,this.semicolon(),this.prodParam.exit(),this.scope.exit(),this.finishNode(t,"ClassPrivateProperty")}parseClassProperty(t){return t.typeAnnotation||this.expectPlugin("classProperties"),this.scope.enter(w|b),this.prodParam.enter(ie),this.match(d.eq)?(this.expectPlugin("classProperties"),this.next(),t.value=this.parseMaybeAssign()):t.value=null,this.semicolon(),this.prodParam.exit(),this.scope.exit(),this.finishNode(t,"ClassProperty")}parseClassId(t,e,s,r=L){this.match(d.name)?(t.id=this.parseIdentifier(),e&&this.checkLVal(t.id,r,void 0,"class name")):s||!e?t.id=null:this.unexpected(null,ut.MissingClassName)}parseClassSuper(t){t.superClass=this.eat(d._extends)?this.parseExprSubscripts():null}parseExport(t){const e=this.maybeParseExportDefaultSpecifier(t),s=!e||this.eat(d.comma),r=s&&this.eatExportStar(t),i=r&&this.maybeParseExportNamespaceSpecifier(t),n=s&&(!i||this.eat(d.comma)),a=e||r;if(r&&!i)return e&&this.unexpected(),this.parseExportFrom(t,!0),this.finishNode(t,"ExportAllDeclaration");const o=this.maybeParseExportNamedSpecifiers(t);if(e&&s&&!r&&!o||i&&n&&!o)throw this.unexpected(null,d.braceL);let c;if(a||o?(c=!1,this.parseExportFrom(t,a)):c=this.maybeParseExportDeclaration(t),a||o||c)return this.checkExport(t,!0,!1,!!t.source),this.finishNode(t,"ExportNamedDeclaration");if(this.eat(d._default))return t.declaration=this.parseExportDefaultExpression(),this.checkExport(t,!0,!0),this.finishNode(t,"ExportDefaultDeclaration");throw this.unexpected(null,d.braceL)}eatExportStar(t){return this.eat(d.star)}maybeParseExportDefaultSpecifier(t){if(this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom");const e=this.startNode();return e.exported=this.parseIdentifier(!0),t.specifiers=[this.finishNode(e,"ExportDefaultSpecifier")],!0}return!1}maybeParseExportNamespaceSpecifier(t){if(this.isContextual("as")){t.specifiers||(t.specifiers=[]);const e=this.startNodeAt(this.state.lastTokStart,this.state.lastTokStartLoc);return this.next(),e.exported=this.parseIdentifier(!0),t.specifiers.push(this.finishNode(e,"ExportNamespaceSpecifier")),!0}return!1}maybeParseExportNamedSpecifiers(t){return!!this.match(d.braceL)&&(t.specifiers||(t.specifiers=[]),t.specifiers.push(...this.parseExportSpecifiers()),t.source=null,t.declaration=null,!0)}maybeParseExportDeclaration(t){if(this.shouldParseExportDeclaration()){if(this.isContextual("async")){const t=this.nextTokenStart();this.isUnparsedContextual(t,"function")||this.unexpected(t,d._function)}return t.specifiers=[],t.source=null,t.declaration=this.parseExportDeclaration(t),!0}return!1}isAsyncFunction(){if(!this.isContextual("async"))return!1;const t=this.nextTokenStart();return!et.test(this.input.slice(this.state.pos,t))&&this.isUnparsedContextual(t,"function")}parseExportDefaultExpression(){const t=this.startNode(),e=this.isAsyncFunction();if(this.match(d._function)||e)return this.next(),e&&this.next(),this.parseFunction(t,ze|We,e);if(this.match(d._class))return this.parseClass(t,!0,!0);if(this.match(d.at))return this.hasPlugin("decorators")&&this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(this.state.start,ut.DecoratorBeforeExport),this.parseDecorators(!1),this.parseClass(t,!0,!0);if(this.match(d._const)||this.match(d._var)||this.isLet())throw this.raise(this.state.start,ut.UnsupportedDefaultExport);{const t=this.parseMaybeAssign();return this.semicolon(),t}}parseExportDeclaration(t){return this.parseStatement(null)}isExportDefaultSpecifier(){if(this.match(d.name))return"async"!==this.state.value&&"let"!==this.state.value;if(!this.match(d._default))return!1;const t=this.nextTokenStart();return 44===this.input.charCodeAt(t)||this.isUnparsedContextual(t,"from")}parseExportFrom(t,e){this.eatContextual("from")?(t.source=this.parseImportSource(),this.checkExport(t)):e?this.unexpected():t.source=null,this.semicolon()}shouldParseExportDeclaration(){if(this.match(d.at)&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))){if(!this.getPluginOption("decorators","decoratorsBeforeExport"))return!0;this.unexpected(this.state.start,ut.DecoratorBeforeExport)}return"var"===this.state.type.keyword||"const"===this.state.type.keyword||"function"===this.state.type.keyword||"class"===this.state.type.keyword||this.isLet()||this.isAsyncFunction()}checkExport(t,e,s,r){if(e)if(s)this.checkDuplicateExports(t,"default");else if(t.specifiers&&t.specifiers.length)for(let n=0,a=t.specifiers;n<a.length;n++){const t=a[n];this.checkDuplicateExports(t,t.exported.name),!r&&t.local&&(this.checkReservedWord(t.local.name,t.local.start,!0,!1),this.scope.checkLocalExport(t.local))}else if(t.declaration)if("FunctionDeclaration"===t.declaration.type||"ClassDeclaration"===t.declaration.type){const e=t.declaration.id;if(!e)throw new Error("Assertion failure");this.checkDuplicateExports(t,e.name)}else if("VariableDeclaration"===t.declaration.type)for(let n=0,a=t.declaration.declarations;n<a.length;n++){const t=a[n];this.checkDeclaration(t.id)}const i=this.state.decoratorStack[this.state.decoratorStack.length-1];if(i.length){const e=t.declaration&&("ClassDeclaration"===t.declaration.type||"ClassExpression"===t.declaration.type);if(!t.declaration||!e)throw this.raise(t.start,ut.UnsupportedDecoratorExport);this.takeDecorators(t.declaration)}}checkDeclaration(t){if("Identifier"===t.type)this.checkDuplicateExports(t,t.name);else if("ObjectPattern"===t.type)for(let e=0,s=t.properties;e<s.length;e++){const t=s[e];this.checkDeclaration(t)}else if("ArrayPattern"===t.type)for(let e=0,s=t.elements;e<s.length;e++){const t=s[e];t&&this.checkDeclaration(t)}else"ObjectProperty"===t.type?this.checkDeclaration(t.value):"RestElement"===t.type?this.checkDeclaration(t.argument):"AssignmentPattern"===t.type&&this.checkDeclaration(t.left)}checkDuplicateExports(t,e){this.state.exportedIdentifiers.indexOf(e)>-1&&this.raise(t.start,"default"===e?ut.DuplicateDefaultExport:ut.DuplicateExport,e),this.state.exportedIdentifiers.push(e)}parseExportSpecifiers(){const t=[];let e=!0;this.expect(d.braceL);while(!this.eat(d.braceR)){if(e)e=!1;else if(this.expect(d.comma),this.eat(d.braceR))break;const s=this.startNode();s.local=this.parseIdentifier(!0),s.exported=this.eatContextual("as")?this.parseIdentifier(!0):s.local.__clone(),t.push(this.finishNode(s,"ExportSpecifier"))}return t}parseImport(t){if(t.specifiers=[],!this.match(d.string)){const e=this.maybeParseDefaultImportSpecifier(t),s=!e||this.eat(d.comma),r=s&&this.maybeParseStarImportSpecifier(t);s&&!r&&this.parseNamedImportSpecifiers(t),this.expectContextual("from")}return t.source=this.parseImportSource(),this.semicolon(),this.finishNode(t,"ImportDeclaration")}parseImportSource(){return this.match(d.string)||this.unexpected(),this.parseExprAtom()}shouldParseDefaultImport(t){return this.match(d.name)}parseImportSpecifierLocal(t,e,s,r){e.local=this.parseIdentifier(),this.checkLVal(e.local,_,void 0,r),t.specifiers.push(this.finishNode(e,s))}maybeParseDefaultImportSpecifier(t){return!!this.shouldParseDefaultImport(t)&&(this.parseImportSpecifierLocal(t,this.startNode(),"ImportDefaultSpecifier","default import specifier"),!0)}maybeParseStarImportSpecifier(t){if(this.match(d.star)){const e=this.startNode();return this.next(),this.expectContextual("as"),this.parseImportSpecifierLocal(t,e,"ImportNamespaceSpecifier","import namespace specifier"),!0}return!1}parseNamedImportSpecifiers(t){let e=!0;this.expect(d.braceL);while(!this.eat(d.braceR)){if(e)e=!1;else{if(this.eat(d.colon))throw this.raise(this.state.start,ut.DestructureNamedImport);if(this.expect(d.comma),this.eat(d.braceR))break}this.parseImportSpecifier(t)}}parseImportSpecifier(t){const e=this.startNode();e.imported=this.parseIdentifier(!0),this.eatContextual("as")?e.local=this.parseIdentifier():(this.checkReservedWord(e.imported.name,e.start,!0,!0),e.local=e.imported.__clone()),this.checkLVal(e.local,_,void 0,"import specifier"),t.specifiers.push(this.finishNode(e,"ImportSpecifier"))}}class $e{constructor(){this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map}}class Xe{constructor(t){this.stack=[],this.undefinedPrivateNames=new Map,this.raise=t}current(){return this.stack[this.stack.length-1]}enter(){this.stack.push(new $e)}exit(){const t=this.stack.pop(),e=this.current();for(let s=0,r=Array.from(t.undefinedPrivateNames);s<r.length;s++){const[t,i]=r[s];e?e.undefinedPrivateNames.has(t)||e.undefinedPrivateNames.set(t,i):this.raise(i,ut.InvalidPrivateFieldResolution,t)}}declarePrivateName(t,e,s){const r=this.current();let i=r.privateNames.has(t);if(e&G){const s=i&&r.loneAccessors.get(t);if(s){const n=s&K,a=e&K,o=s&G,c=e&G;i=o===c||n!==a,i||r.loneAccessors.delete(t)}else i||r.loneAccessors.set(t,e)}i&&this.raise(s,ut.PrivateNameRedeclaration,t),r.privateNames.add(t),r.undefinedPrivateNames.delete(t)}usePrivateName(t,e){let s;for(let r=0,i=this.stack;r<i.length;r++)if(s=i[r],s.privateNames.has(t))return;s?s.undefinedPrivateNames.set(t,e):this.raise(e,ut.InvalidPrivateFieldResolution,t)}}class Ge extends Ke{constructor(t,e){t=Ae(t),super(t,e);const s=this.getScopeHandler();this.options=t,this.inModule="module"===this.options.sourceType,this.scope=new s(this.raise.bind(this),this.inModule),this.prodParam=new ce,this.classScope=new Xe(this.raise.bind(this)),this.plugins=Ye(this.options.plugins),this.filename=t.sourceFilename}getScopeHandler(){return ee}parse(){let t=ie;this.hasPlugin("topLevelAwait")&&this.inModule&&(t|=ae),this.scope.enter(m),this.prodParam.enter(t);const e=this.startNode(),s=this.startNode();return this.nextToken(),e.errors=null,this.parseTopLevel(e,s),e.errors=this.state.errors,e}}function Ye(t){const e=new Map;for(let s=0;s<t.length;s++){const r=t[s],[i,n]=Array.isArray(r)?r:[r,{}];e.has(i)||e.set(i,n||{})}return e}function Je(t,e){if(!e||"unambiguous"!==e.sourceType)return Ze(e,t).parse();e=Object.assign({},e);try{e.sourceType="module";const r=Ze(e,t),i=r.parse();if(r.sawUnambiguousESM)return i;if(r.ambiguousScriptDifferentAst)try{return e.sourceType="script",Ze(e,t).parse()}catch(s){}else i.program.sourceType="script";return i}catch(r){try{return e.sourceType="script",Ze(e,t).parse()}catch(i){}throw r}}function Qe(t,e){const s=Ze(e,t);return s.options.strictMode&&(s.state.strict=!0),s.getExpression()}function Ze(t,e){let s=Ge;return t&&t.plugins&&(we(t.plugins),s=es(t.plugins)),new s(t,e)}const ts={};function es(t){const e=Te.filter(e=>ge(t,e)),s=e.join("/");let r=ts[s];if(!r){r=Ge;for(let t=0;t<e.length;t++){const s=e[t];r=Pe[s](r)}ts[s]=r}return r}e.parse=Je,e.parseExpression=Qe,e.tokTypes=d},4178:function(t,e,s){"use strict";var r=s("91fe"),i=s("d5dc"),n=s("df50"),a=s("e17a"),o=s("7a23"),c=s("4ccd"),h=s("4445"),l=s("f30e"),p=s("f28d"),u=s("a8c9"),d=s("d68d"),f=s("ac83"),m=s("ee6f"),y=s("8c47"),g=s("7dc7"),x=s("aec8"),b=s("641d"),v=s("16e5"),w=s("65af"),P=s("1544"),T=s("1072"),E=s("4aef"),A=s("c223"),S=s("354c"),C=s("2ba5"),k=s("3d8a"),N=s("f880"),I=s("4d52"),O=s("4888"),D=s("9db6"),M=s("57c4"),L=s("7287"),_=s("c0aa"),R=s("94d7"),j=s("d0e2"),F=s("407d").forEach,B=I("hidden"),U="Symbol",q="prototype",V=M("toPrimitive"),z=j.set,H=j.getterFor(U),W=Object[q],K=i.Symbol,$=n("JSON","stringify"),X=E.f,G=A.f,Y=P.f,J=S.f,Q=N("symbols"),Z=N("op-symbols"),tt=N("string-to-symbol-registry"),et=N("symbol-to-string-registry"),st=N("wks"),rt=i.QObject,it=!rt||!rt[q]||!rt[q].findChild,nt=o&&l((function(){return 7!=b(G({},"a",{get:function(){return G(this,"a",{value:7}).a}})).a}))?function(t,e,s){var r=X(W,e);r&&delete W[e],G(t,e,s),r&&t!==W&&G(W,e,r)}:G,at=function(t,e){var s=Q[t]=b(K[q]);return z(s,{type:U,tag:t,description:e}),o||(s.description=e),s},ot=h?function(t){return"symbol"==typeof t}:function(t){return Object(t)instanceof K},ct=function(t,e,s){t===W&&ct(Z,e,s),f(t);var r=g(e,!0);return f(s),p(Q,r)?(s.enumerable?(p(t,B)&&t[B][r]&&(t[B][r]=!1),s=b(s,{enumerable:x(0,!1)})):(p(t,B)||G(t,B,x(1,{})),t[B][r]=!0),nt(t,r,s)):G(t,r,s)},ht=function(t,e){f(t);var s=y(e),r=v(s).concat(ft(s));return F(r,(function(e){o&&!pt.call(s,e)||ct(t,e,s[e])})),t},lt=function(t,e){return void 0===e?b(t):ht(b(t),e)},pt=function(t){var e=g(t,!0),s=J.call(this,e);return!(this===W&&p(Q,e)&&!p(Z,e))&&(!(s||!p(this,e)||!p(Q,e)||p(this,B)&&this[B][e])||s)},ut=function(t,e){var s=y(t),r=g(e,!0);if(s!==W||!p(Q,r)||p(Z,r)){var i=X(s,r);return!i||!p(Q,r)||p(s,B)&&s[B][r]||(i.enumerable=!0),i}},dt=function(t){var e=Y(y(t)),s=[];return F(e,(function(t){p(Q,t)||p(O,t)||s.push(t)})),s},ft=function(t){var e=t===W,s=Y(e?Z:y(t)),r=[];return F(s,(function(t){!p(Q,t)||e&&!p(W,t)||r.push(Q[t])})),r};if(c||(K=function(){if(this instanceof K)throw TypeError("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,e=D(t),s=function(t){this===W&&s.call(Z,t),p(this,B)&&p(this[B],e)&&(this[B][e]=!1),nt(this,e,x(1,t))};return o&&it&&nt(W,e,{configurable:!0,set:s}),at(e,t)},k(K[q],"toString",(function(){return H(this).tag})),k(K,"withoutSetter",(function(t){return at(D(t),t)})),S.f=pt,A.f=ct,E.f=ut,w.f=P.f=dt,T.f=ft,L.f=function(t){return at(M(t),t)},o&&(G(K[q],"description",{configurable:!0,get:function(){return H(this).description}}),a||k(W,"propertyIsEnumerable",pt,{unsafe:!0}))),r({global:!0,wrap:!0,forced:!c,sham:!c},{Symbol:K}),F(v(st),(function(t){_(t)})),r({target:U,stat:!0,forced:!c},{for:function(t){var e=String(t);if(p(tt,e))return tt[e];var s=K(e);return tt[e]=s,et[s]=e,s},keyFor:function(t){if(!ot(t))throw TypeError(t+" is not a symbol");if(p(et,t))return et[t]},useSetter:function(){it=!0},useSimple:function(){it=!1}}),r({target:"Object",stat:!0,forced:!c,sham:!o},{create:lt,defineProperty:ct,defineProperties:ht,getOwnPropertyDescriptor:ut}),r({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:dt,getOwnPropertySymbols:ft}),r({target:"Object",stat:!0,forced:l((function(){T.f(1)}))},{getOwnPropertySymbols:function(t){return T.f(m(t))}}),$){var mt=!c||l((function(){var t=K();return"[null]"!=$([t])||"{}"!=$({a:t})||"{}"!=$(Object(t))}));r({target:"JSON",stat:!0,forced:mt},{stringify:function(t,e,s){var r,i=[t],n=1;while(arguments.length>n)i.push(arguments[n++]);if(r=e,(d(e)||void 0!==t)&&!ot(t))return u(e)||(e=function(t,e){if("function"==typeof r&&(e=r.call(this,t,e)),!ot(e))return e}),i[1]=e,$.apply(null,i)}})}K[q][V]||C(K[q],V,K[q].valueOf),R(K,U),O[B]=!0},"417f":function(t,e,s){var r=s("3d8a");t.exports=function(t,e,s){for(var i in e)r(t,i,e[i],s);return t}},"41f6":function(t,e){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},4301:function(t,e,s){var r=s("ac83"),i=s("d68d"),n=s("df22");t.exports=function(t,e){if(r(t),i(e)&&e.constructor===t)return e;var s=n.f(t),a=s.resolve;return a(e),s.promise}},4423:function(t,e,s){"use strict";var r=s("91fe"),i=s("407d").some,n=s("fb11"),a=s("6885"),o=n("some"),c=a("some");r({target:"Array",proto:!0,forced:!o||!c},{some:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},4445:function(t,e,s){var r=s("4ccd");t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},4758:function(t,e){"function"===typeof Object.create?t.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(t,e){t.super_=e;var s=function(){};s.prototype=e.prototype,t.prototype=new s,t.prototype.constructor=t}},4888:function(t,e){t.exports={}},"49a5":function(t,e,s){(function(t){var r=Object.getOwnPropertyDescriptors||function(t){for(var e=Object.keys(t),s={},r=0;r<e.length;r++)s[e[r]]=Object.getOwnPropertyDescriptor(t,e[r]);return s},i=/%[sdj%]/g;e.format=function(t){if(!P(t)){for(var e=[],s=0;s<arguments.length;s++)e.push(o(arguments[s]));return e.join(" ")}s=1;for(var r=arguments,n=r.length,a=String(t).replace(i,(function(t){if("%%"===t)return"%";if(s>=n)return t;switch(t){case"%s":return String(r[s++]);case"%d":return Number(r[s++]);case"%j":try{return JSON.stringify(r[s++])}catch(e){return"[Circular]"}default:return t}})),c=r[s];s<n;c=r[++s])b(c)||!S(c)?a+=" "+c:a+=" "+o(c);return a},e.deprecate=function(s,r){if("undefined"!==typeof t&&!0===t.noDeprecation)return s;if("undefined"===typeof t)return function(){return e.deprecate(s,r).apply(this,arguments)};var i=!1;function n(){if(!i){if(t.throwDeprecation)throw new Error(r);t.traceDeprecation?console.trace(r):console.error(r),i=!0}return s.apply(this,arguments)}return n};var n,a={};function o(t,s){var r={seen:[],stylize:h};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),x(s)?r.showHidden=s:s&&e._extend(r,s),E(r.showHidden)&&(r.showHidden=!1),E(r.depth)&&(r.depth=2),E(r.colors)&&(r.colors=!1),E(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=c),p(r,t,r.depth)}function c(t,e){var s=o.styles[e];return s?"["+o.colors[s][0]+"m"+t+"["+o.colors[s][1]+"m":t}function h(t,e){return t}function l(t){var e={};return t.forEach((function(t,s){e[t]=!0})),e}function p(t,s,r){if(t.customInspect&&s&&N(s.inspect)&&s.inspect!==e.inspect&&(!s.constructor||s.constructor.prototype!==s)){var i=s.inspect(r,t);return P(i)||(i=p(t,i,r)),i}var n=u(t,s);if(n)return n;var a=Object.keys(s),o=l(a);if(t.showHidden&&(a=Object.getOwnPropertyNames(s)),k(s)&&(a.indexOf("message")>=0||a.indexOf("description")>=0))return d(s);if(0===a.length){if(N(s)){var c=s.name?": "+s.name:"";return t.stylize("[Function"+c+"]","special")}if(A(s))return t.stylize(RegExp.prototype.toString.call(s),"regexp");if(C(s))return t.stylize(Date.prototype.toString.call(s),"date");if(k(s))return d(s)}var h,x="",b=!1,v=["{","}"];if(g(s)&&(b=!0,v=["[","]"]),N(s)){var w=s.name?": "+s.name:"";x=" [Function"+w+"]"}return A(s)&&(x=" "+RegExp.prototype.toString.call(s)),C(s)&&(x=" "+Date.prototype.toUTCString.call(s)),k(s)&&(x=" "+d(s)),0!==a.length||b&&0!=s.length?r<0?A(s)?t.stylize(RegExp.prototype.toString.call(s),"regexp"):t.stylize("[Object]","special"):(t.seen.push(s),h=b?f(t,s,r,o,a):a.map((function(e){return m(t,s,r,o,e,b)})),t.seen.pop(),y(h,x,v)):v[0]+x+v[1]}function u(t,e){if(E(e))return t.stylize("undefined","undefined");if(P(e)){var s="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(s,"string")}return w(e)?t.stylize(""+e,"number"):x(e)?t.stylize(""+e,"boolean"):b(e)?t.stylize("null","null"):void 0}function d(t){return"["+Error.prototype.toString.call(t)+"]"}function f(t,e,s,r,i){for(var n=[],a=0,o=e.length;a<o;++a)_(e,String(a))?n.push(m(t,e,s,r,String(a),!0)):n.push("");return i.forEach((function(i){i.match(/^\d+$/)||n.push(m(t,e,s,r,i,!0))})),n}function m(t,e,s,r,i,n){var a,o,c;if(c=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]},c.get?o=c.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):c.set&&(o=t.stylize("[Setter]","special")),_(r,i)||(a="["+i+"]"),o||(t.seen.indexOf(c.value)<0?(o=b(s)?p(t,c.value,null):p(t,c.value,s-1),o.indexOf("\n")>-1&&(o=n?o.split("\n").map((function(t){return"  "+t})).join("\n").substr(2):"\n"+o.split("\n").map((function(t){return"   "+t})).join("\n"))):o=t.stylize("[Circular]","special")),E(a)){if(n&&i.match(/^\d+$/))return o;a=JSON.stringify(""+i),a.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=t.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=t.stylize(a,"string"))}return a+": "+o}function y(t,e,s){var r=t.reduce((function(t,e){return e.indexOf("\n")>=0&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return r>60?s[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+s[1]:s[0]+e+" "+t.join(", ")+" "+s[1]}function g(t){return Array.isArray(t)}function x(t){return"boolean"===typeof t}function b(t){return null===t}function v(t){return null==t}function w(t){return"number"===typeof t}function P(t){return"string"===typeof t}function T(t){return"symbol"===typeof t}function E(t){return void 0===t}function A(t){return S(t)&&"[object RegExp]"===O(t)}function S(t){return"object"===typeof t&&null!==t}function C(t){return S(t)&&"[object Date]"===O(t)}function k(t){return S(t)&&("[object Error]"===O(t)||t instanceof Error)}function N(t){return"function"===typeof t}function I(t){return null===t||"boolean"===typeof t||"number"===typeof t||"string"===typeof t||"symbol"===typeof t||"undefined"===typeof t}function O(t){return Object.prototype.toString.call(t)}function D(t){return t<10?"0"+t.toString(10):t.toString(10)}e.debuglog=function(s){if(E(n)&&(n=Object({NODE_ENV:"production",BASE_URL:"/form-generator/"}).NODE_DEBUG||""),s=s.toUpperCase(),!a[s])if(new RegExp("\\b"+s+"\\b","i").test(n)){var r=t.pid;a[s]=function(){var t=e.format.apply(e,arguments);console.error("%s %d: %s",s,r,t)}}else a[s]=function(){};return a[s]},e.inspect=o,o.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},o.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.isArray=g,e.isBoolean=x,e.isNull=b,e.isNullOrUndefined=v,e.isNumber=w,e.isString=P,e.isSymbol=T,e.isUndefined=E,e.isRegExp=A,e.isObject=S,e.isDate=C,e.isError=k,e.isFunction=N,e.isPrimitive=I,e.isBuffer=s("dc62");var M=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function L(){var t=new Date,e=[D(t.getHours()),D(t.getMinutes()),D(t.getSeconds())].join(":");return[t.getDate(),M[t.getMonth()],e].join(" ")}function _(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.log=function(){console.log("%s - %s",L(),e.format.apply(e,arguments))},e.inherits=s("4758"),e._extend=function(t,e){if(!e||!S(e))return t;var s=Object.keys(e),r=s.length;while(r--)t[s[r]]=e[s[r]];return t};var R="undefined"!==typeof Symbol?Symbol("util.promisify.custom"):void 0;function j(t,e){if(!t){var s=new Error("Promise was rejected with a falsy value");s.reason=t,t=s}return e(t)}function F(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');function s(){for(var s=[],r=0;r<arguments.length;r++)s.push(arguments[r]);var i=s.pop();if("function"!==typeof i)throw new TypeError("The last argument must be of type Function");var n=this,a=function(){return i.apply(n,arguments)};e.apply(this,s).then((function(e){t.nextTick(a,null,e)}),(function(e){t.nextTick(j,e,a)}))}return Object.setPrototypeOf(s,Object.getPrototypeOf(e)),Object.defineProperties(s,r(e)),s}e.promisify=function(t){if("function"!==typeof t)throw new TypeError('The "original" argument must be of type Function');if(R&&t[R]){var e=t[R];if("function"!==typeof e)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(e,R,{value:e,enumerable:!1,writable:!1,configurable:!0}),e}function e(){for(var e,s,r=new Promise((function(t,r){e=t,s=r})),i=[],n=0;n<arguments.length;n++)i.push(arguments[n]);i.push((function(t,r){t?s(t):e(r)}));try{t.apply(this,i)}catch(a){s(a)}return r}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),R&&Object.defineProperty(e,R,{value:e,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(e,r(t))},e.promisify.custom=R,e.callbackify=F}).call(this,s("eef6"))},"4aef":function(t,e,s){var r=s("7a23"),i=s("354c"),n=s("aec8"),a=s("8c47"),o=s("7dc7"),c=s("f28d"),h=s("88b4"),l=Object.getOwnPropertyDescriptor;e.f=r?l:function(t,e){if(t=a(t),e=o(e,!0),h)try{return l(t,e)}catch(s){}if(c(t,e))return n(!i.f.call(t,e),t[e])}},"4ccd":function(t,e,s){var r=s("f30e");t.exports=!!Object.getOwnPropertySymbols&&!r((function(){return!String(Symbol())}))},"4ce0":function(t,e,s){var r=s("ac83"),i=s("da66");t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,s={};try{t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,t.call(s,[]),e=s instanceof Array}catch(n){}return function(s,n){return r(s),i(n),e?t.call(s,n):s.__proto__=n,s}}():void 0)},"4d52":function(t,e,s){var r=s("f880"),i=s("9db6"),n=r("keys");t.exports=function(t){return n[t]||(n[t]=i(t))}},"527d":function(t,e,s){var r=s("c85c"),i=Function.toString;"function"!=typeof r.inspectSource&&(r.inspectSource=function(t){return i.call(t)}),t.exports=r.inspectSource},"536c":function(t,e,s){"use strict";var r=s("3303").charAt;t.exports=function(t,e,s){return e+(s?r(t,e).length:1)}},"55b0":function(t,e,s){var r=s("7a23"),i=s("c223"),n=s("ac83"),a=s("16e5");t.exports=r?Object.defineProperties:function(t,e){n(t);var s,r=a(e),o=r.length,c=0;while(o>c)i.f(t,s=r[c++],e[s]);return t}},5646:function(t,e,s){"use strict";var r=s("91fe"),i=s("ed51"),n=s("90a7"),a=s("4ce0"),o=s("94d7"),c=s("2ba5"),h=s("3d8a"),l=s("57c4"),p=s("e17a"),u=s("ed35"),d=s("143b"),f=d.IteratorPrototype,m=d.BUGGY_SAFARI_ITERATORS,y=l("iterator"),g="keys",x="values",b="entries",v=function(){return this};t.exports=function(t,e,s,l,d,w,P){i(s,e,l);var T,E,A,S=function(t){if(t===d&&O)return O;if(!m&&t in N)return N[t];switch(t){case g:return function(){return new s(this,t)};case x:return function(){return new s(this,t)};case b:return function(){return new s(this,t)}}return function(){return new s(this)}},C=e+" Iterator",k=!1,N=t.prototype,I=N[y]||N["@@iterator"]||d&&N[d],O=!m&&I||S(d),D="Array"==e&&N.entries||I;if(D&&(T=n(D.call(new t)),f!==Object.prototype&&T.next&&(p||n(T)===f||(a?a(T,f):"function"!=typeof T[y]&&c(T,y,v)),o(T,C,!0,!0),p&&(u[C]=v))),d==x&&I&&I.name!==x&&(k=!0,O=function(){return I.call(this)}),p&&!P||N[y]===O||c(N,y,O),u[e]=O,d)if(E={values:S(x),keys:w?O:S(g),entries:S(b)},P)for(A in E)(m||k||!(A in N))&&h(N,A,E[A]);else r({target:e,proto:!0,forced:m||k},E);return E}},5751:function(t,e,s){var r=s("57c4"),i=s("641d"),n=s("c223"),a=r("unscopables"),o=Array.prototype;void 0==o[a]&&n.f(o,a,{configurable:!0,value:i(null)}),t.exports=function(t){o[a][t]=!0}},"57c4":function(t,e,s){var r=s("d5dc"),i=s("f880"),n=s("f28d"),a=s("9db6"),o=s("4ccd"),c=s("4445"),h=i("wks"),l=r.Symbol,p=c?l:l&&l.withoutSetter||a;t.exports=function(t){return n(h,t)||(o&&n(l,t)?h[t]=l[t]:h[t]=p("Symbol."+t)),h[t]}},"5c90":function(t,e){t.exports=function(t){try{return{error:!1,value:t()}}catch(e){return{error:!0,value:e}}}},"60f2":function(t,e,s){var r=s("d68d"),i=s("4ce0");t.exports=function(t,e,s){var n,a;return i&&"function"==typeof(n=e.constructor)&&n!==s&&r(a=n.prototype)&&a!==s.prototype&&i(t,a),t}},"618d":function(t,e,s){"use strict";var r=s("91fe"),i=s("e17a"),n=s("644f"),a=s("f30e"),o=s("df50"),c=s("fb8e"),h=s("4301"),l=s("3d8a"),p=!!n&&a((function(){n.prototype["finally"].call({then:function(){}},(function(){}))}));r({target:"Promise",proto:!0,real:!0,forced:p},{finally:function(t){var e=c(this,o("Promise")),s="function"==typeof t;return this.then(s?function(s){return h(e,t()).then((function(){return s}))}:t,s?function(s){return h(e,t()).then((function(){throw s}))}:t)}}),i||"function"!=typeof n||n.prototype["finally"]||l(n.prototype,"finally",o("Promise").prototype["finally"])},6266:function(t,e,s){(function(t){function s(t,e){for(var s=0,r=t.length-1;r>=0;r--){var i=t[r];"."===i?t.splice(r,1):".."===i?(t.splice(r,1),s++):s&&(t.splice(r,1),s--)}if(e)for(;s--;s)t.unshift("..");return t}function r(t){"string"!==typeof t&&(t+="");var e,s=0,r=-1,i=!0;for(e=t.length-1;e>=0;--e)if(47===t.charCodeAt(e)){if(!i){s=e+1;break}}else-1===r&&(i=!1,r=e+1);return-1===r?"":t.slice(s,r)}function i(t,e){if(t.filter)return t.filter(e);for(var s=[],r=0;r<t.length;r++)e(t[r],r,t)&&s.push(t[r]);return s}e.resolve=function(){for(var e="",r=!1,n=arguments.length-1;n>=-1&&!r;n--){var a=n>=0?arguments[n]:t.cwd();if("string"!==typeof a)throw new TypeError("Arguments to path.resolve must be strings");a&&(e=a+"/"+e,r="/"===a.charAt(0))}return e=s(i(e.split("/"),(function(t){return!!t})),!r).join("/"),(r?"/":"")+e||"."},e.normalize=function(t){var r=e.isAbsolute(t),a="/"===n(t,-1);return t=s(i(t.split("/"),(function(t){return!!t})),!r).join("/"),t||r||(t="."),t&&a&&(t+="/"),(r?"/":"")+t},e.isAbsolute=function(t){return"/"===t.charAt(0)},e.join=function(){var t=Array.prototype.slice.call(arguments,0);return e.normalize(i(t,(function(t,e){if("string"!==typeof t)throw new TypeError("Arguments to path.join must be strings");return t})).join("/"))},e.relative=function(t,s){function r(t){for(var e=0;e<t.length;e++)if(""!==t[e])break;for(var s=t.length-1;s>=0;s--)if(""!==t[s])break;return e>s?[]:t.slice(e,s-e+1)}t=e.resolve(t).substr(1),s=e.resolve(s).substr(1);for(var i=r(t.split("/")),n=r(s.split("/")),a=Math.min(i.length,n.length),o=a,c=0;c<a;c++)if(i[c]!==n[c]){o=c;break}var h=[];for(c=o;c<i.length;c++)h.push("..");return h=h.concat(n.slice(o)),h.join("/")},e.sep="/",e.delimiter=":",e.dirname=function(t){if("string"!==typeof t&&(t+=""),0===t.length)return".";for(var e=t.charCodeAt(0),s=47===e,r=-1,i=!0,n=t.length-1;n>=1;--n)if(e=t.charCodeAt(n),47===e){if(!i){r=n;break}}else i=!1;return-1===r?s?"/":".":s&&1===r?"/":t.slice(0,r)},e.basename=function(t,e){var s=r(t);return e&&s.substr(-1*e.length)===e&&(s=s.substr(0,s.length-e.length)),s},e.extname=function(t){"string"!==typeof t&&(t+="");for(var e=-1,s=0,r=-1,i=!0,n=0,a=t.length-1;a>=0;--a){var o=t.charCodeAt(a);if(47!==o)-1===r&&(i=!1,r=a+1),46===o?-1===e?e=a:1!==n&&(n=1):-1!==e&&(n=-1);else if(!i){s=a+1;break}}return-1===e||-1===r||0===n||1===n&&e===r-1&&e===s+1?"":t.slice(e,r)};var n="b"==="ab".substr(-1)?function(t,e,s){return t.substr(e,s)}:function(t,e,s){return e<0&&(e=t.length+e),t.substr(e,s)}}).call(this,s("eef6"))},"641d":function(t,e,s){var r,i=s("ac83"),n=s("55b0"),a=s("6807"),o=s("4888"),c=s("c49e"),h=s("032e"),l=s("4d52"),p=">",u="<",d="prototype",f="script",m=l("IE_PROTO"),y=function(){},g=function(t){return u+f+p+t+u+"/"+f+p},x=function(t){t.write(g("")),t.close();var e=t.parentWindow.Object;return t=null,e},b=function(){var t,e=h("iframe"),s="java"+f+":";return e.style.display="none",c.appendChild(e),e.src=String(s),t=e.contentWindow.document,t.open(),t.write(g("document.F=Object")),t.close(),t.F},v=function(){try{r=document.domain&&new ActiveXObject("htmlfile")}catch(e){}v=r?x(r):b();var t=a.length;while(t--)delete v[d][a[t]];return v()};o[m]=!0,t.exports=Object.create||function(t,e){var s;return null!==t?(y[d]=i(t),s=new y,y[d]=null,s[m]=t):s=v(),void 0===e?s:n(s,e)}},"644f":function(t,e,s){var r=s("d5dc");t.exports=r.Promise},"65af":function(t,e,s){var r=s("02d0"),i=s("6807"),n=i.concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,n)}},"66b2":function(t,e,s){var r=s("ac83"),i=s("0532"),n=s("684e"),a=s("0b29"),o=s("e28b"),c=s("2bba"),h=function(t,e){this.stopped=t,this.result=e},l=t.exports=function(t,e,s,l,p){var u,d,f,m,y,g,x,b=a(e,s,l?2:1);if(p)u=t;else{if(d=o(t),"function"!=typeof d)throw TypeError("Target is not iterable");if(i(d)){for(f=0,m=n(t.length);m>f;f++)if(y=l?b(r(x=t[f])[0],x[1]):b(t[f]),y&&y instanceof h)return y;return new h(!1)}u=d.call(t)}g=u.next;while(!(x=g.call(u)).done)if(y=c(u,b,x.value,l),"object"==typeof y&&y&&y instanceof h)return y;return new h(!1)};l.stop=function(t){return new h(!0,t)}},"67ea":function(t,e){var s={}.toString;t.exports=function(t){return s.call(t).slice(8,-1)}},6807:function(t,e){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"684e":function(t,e,s){var r=s("f240"),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},6885:function(t,e,s){var r=s("7a23"),i=s("f30e"),n=s("f28d"),a=Object.defineProperty,o={},c=function(t){throw t};t.exports=function(t,e){if(n(o,t))return o[t];e||(e={});var s=[][t],h=!!n(e,"ACCESSORS")&&e.ACCESSORS,l=n(e,0)?e[0]:c,p=n(e,1)?e[1]:void 0;return o[t]=!!s&&!i((function(){if(h&&!r)return!0;var t={length:-1};h?a(t,1,{enumerable:!0,get:c}):t[1]=1,s.call(t,l,p)}))}},"6be9":function(t,e,s){var r=s("8c47"),i=s("684e"),n=s("0192"),a=function(t){return function(e,s,a){var o,c=r(e),h=i(c.length),l=n(a,h);if(t&&s!=s){while(h>l)if(o=c[l++],o!=o)return!0}else for(;h>l;l++)if((t||l in c)&&c[l]===s)return t||l||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},"6dcf":function(t,e,s){var r,i,n,a=s("d5dc"),o=s("f30e"),c=s("67ea"),h=s("0b29"),l=s("c49e"),p=s("032e"),u=s("c044"),d=a.location,f=a.setImmediate,m=a.clearImmediate,y=a.process,g=a.MessageChannel,x=a.Dispatch,b=0,v={},w="onreadystatechange",P=function(t){if(v.hasOwnProperty(t)){var e=v[t];delete v[t],e()}},T=function(t){return function(){P(t)}},E=function(t){P(t.data)},A=function(t){a.postMessage(t+"",d.protocol+"//"+d.host)};f&&m||(f=function(t){var e=[],s=1;while(arguments.length>s)e.push(arguments[s++]);return v[++b]=function(){("function"==typeof t?t:Function(t)).apply(void 0,e)},r(b),b},m=function(t){delete v[t]},"process"==c(y)?r=function(t){y.nextTick(T(t))}:x&&x.now?r=function(t){x.now(T(t))}:g&&!u?(i=new g,n=i.port2,i.port1.onmessage=E,r=h(n.postMessage,n,1)):!a.addEventListener||"function"!=typeof postMessage||a.importScripts||o(A)?r=w in p("script")?function(t){l.appendChild(p("script"))[w]=function(){l.removeChild(this),P(t)}}:function(t){setTimeout(T(t),0)}:(r=A,a.addEventListener("message",E,!1))),t.exports={set:f,clear:m}},7267:function(t,e,s){"use strict";var r=s("3d8a"),i=s("ac83"),n=s("f30e"),a=s("0618"),o="toString",c=RegExp.prototype,h=c[o],l=n((function(){return"/a/b"!=h.call({source:"a",flags:"b"})})),p=h.name!=o;(l||p)&&r(RegExp.prototype,o,(function(){var t=i(this),e=String(t.source),s=t.flags,r=String(void 0===s&&t instanceof RegExp&&!("flags"in c)?a.call(t):s);return"/"+e+"/"+r}),{unsafe:!0})},7287:function(t,e,s){var r=s("57c4");e.f=r},"79dd":function(t,e,s){var r=s("91fe"),i=s("ee6f"),n=s("16e5"),a=s("f30e"),o=a((function(){n(1)}));r({target:"Object",stat:!0,forced:o},{keys:function(t){return n(i(t))}})},"7a23":function(t,e,s){var r=s("f30e");t.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},"7ae7":function(t,e,s){"use strict";var r=s("91fe"),i=s("f30e"),n=s("a8c9"),a=s("d68d"),o=s("ee6f"),c=s("684e"),h=s("01d7"),l=s("3132"),p=s("b1a1"),u=s("57c4"),d=s("bf98"),f=u("isConcatSpreadable"),m=9007199254740991,y="Maximum allowed index exceeded",g=d>=51||!i((function(){var t=[];return t[f]=!1,t.concat()[0]!==t})),x=p("concat"),b=function(t){if(!a(t))return!1;var e=t[f];return void 0!==e?!!e:n(t)},v=!g||!x;r({target:"Array",proto:!0,forced:v},{concat:function(t){var e,s,r,i,n,a=o(this),p=l(a,0),u=0;for(e=-1,r=arguments.length;e<r;e++)if(n=-1===e?a:arguments[e],b(n)){if(i=c(n.length),u+i>m)throw TypeError(y);for(s=0;s<i;s++,u++)s in n&&h(p,u,n[s])}else{if(u>=m)throw TypeError(y);h(p,u++,n)}return p.length=u,p}})},"7dc7":function(t,e,s){var r=s("d68d");t.exports=function(t,e){if(!r(t))return t;var s,i;if(e&&"function"==typeof(s=t.toString)&&!r(i=s.call(t)))return i;if("function"==typeof(s=t.valueOf)&&!r(i=s.call(t)))return i;if(!e&&"function"==typeof(s=t.toString)&&!r(i=s.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},"80d3":function(t,e,s){"use strict";var r=s("91fe"),i=s("4aef").f,n=s("684e"),a=s("e1c9"),o=s("3193"),c=s("30c9"),h=s("e17a"),l="".endsWith,p=Math.min,u=c("endsWith"),d=!h&&!u&&!!function(){var t=i(String.prototype,"endsWith");return t&&!t.writable}();r({target:"String",proto:!0,forced:!d&&!u},{endsWith:function(t){var e=String(o(this));a(t);var s=arguments.length>1?arguments[1]:void 0,r=n(e.length),i=void 0===s?r:p(n(s),r),c=String(t);return l?l.call(e,c,i):e.slice(i-c.length,i)===c}})},"81a0":function(t,e,s){var r=s("67ea"),i=s("21d4");t.exports=function(t,e){var s=t.exec;if("function"===typeof s){var n=s.call(t,e);if("object"!==typeof n)throw TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==r(t))throw TypeError("RegExp#exec called on incompatible receiver");return i.call(t,e)}},"861d":function(t,e,s){"use strict";function r(t,e,s,r){var i,n=!1,a=0;function o(){i&&clearTimeout(i)}function c(){o(),n=!0}function h(){var c=this,h=Date.now()-a,l=arguments;function p(){a=Date.now(),s.apply(c,l)}function u(){i=void 0}n||(r&&!i&&p(),o(),void 0===r&&h>t?p():!0!==e&&(i=setTimeout(r?u:p,void 0===r?t-h:t)))}return"boolean"!==typeof e&&(r=s,s=e,e=void 0),h.cancel=c,h}function i(t,e,s){return void 0===s?r(t,e,!1):r(t,s,!1!==e)}s.d(e,"a",(function(){return i}))},"86dd":function(t,e,s){"use strict";var r=s("91fe"),i=s("407d").filter,n=s("b1a1"),a=s("6885"),o=n("filter"),c=a("filter");r({target:"Array",proto:!0,forced:!o||!c},{filter:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"88b4":function(t,e,s){var r=s("7a23"),i=s("f30e"),n=s("032e");t.exports=!r&&!i((function(){return 7!=Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a}))},"8c13":function(t,e,s){t.exports=function(t){var e={};function s(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,s),i.l=!0,i.exports}return s.m=t,s.c=e,s.d=function(t,e,r){s.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(t,e){if(1&e&&(t=s(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(s.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)s.d(r,i,function(e){return t[e]}.bind(null,i));return r},s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,"a",e),e},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.p="",s(s.s="fb15")}({"02f4":function(t,e,s){var r=s("4588"),i=s("be13");t.exports=function(t){return function(e,s){var n,a,o=String(i(e)),c=r(s),h=o.length;return c<0||c>=h?t?"":void 0:(n=o.charCodeAt(c),n<55296||n>56319||c+1===h||(a=o.charCodeAt(c+1))<56320||a>57343?t?o.charAt(c):n:t?o.slice(c,c+2):a-56320+(n-55296<<10)+65536)}}},"0390":function(t,e,s){"use strict";var r=s("02f4")(!0);t.exports=function(t,e,s){return e+(s?r(t,e).length:1)}},"07e3":function(t,e){var s={}.hasOwnProperty;t.exports=function(t,e){return s.call(t,e)}},"0bfb":function(t,e,s){"use strict";var r=s("cb7c");t.exports=function(){var t=r(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"0fc9":function(t,e,s){var r=s("3a38"),i=Math.max,n=Math.min;t.exports=function(t,e){return t=r(t),t<0?i(t+e,0):n(t,e)}},1654:function(t,e,s){"use strict";var r=s("71c1")(!0);s("30f1")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,s=this._i;return s>=e.length?{value:void 0,done:!0}:(t=r(e,s),this._i+=t.length,{value:t,done:!1})}))},1691:function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},"1af6":function(t,e,s){var r=s("63b6");r(r.S,"Array",{isArray:s("9003")})},"1bc3":function(t,e,s){var r=s("f772");t.exports=function(t,e){if(!r(t))return t;var s,i;if(e&&"function"==typeof(s=t.toString)&&!r(i=s.call(t)))return i;if("function"==typeof(s=t.valueOf)&&!r(i=s.call(t)))return i;if(!e&&"function"==typeof(s=t.toString)&&!r(i=s.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},"1ec9":function(t,e,s){var r=s("f772"),i=s("e53d").document,n=r(i)&&r(i.createElement);t.exports=function(t){return n?i.createElement(t):{}}},"20fd":function(t,e,s){"use strict";var r=s("d9f6"),i=s("aebd");t.exports=function(t,e,s){e in t?r.f(t,e,i(0,s)):t[e]=s}},"214f":function(t,e,s){"use strict";s("b0c5");var r=s("2aba"),i=s("32e9"),n=s("79e5"),a=s("be13"),o=s("2b4c"),c=s("520a"),h=o("species"),l=!n((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),p=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var s="ab".split(t);return 2===s.length&&"a"===s[0]&&"b"===s[1]}();t.exports=function(t,e,s){var u=o(t),d=!n((function(){var e={};return e[u]=function(){return 7},7!=""[t](e)})),f=d?!n((function(){var e=!1,s=/a/;return s.exec=function(){return e=!0,null},"split"===t&&(s.constructor={},s.constructor[h]=function(){return s}),s[u](""),!e})):void 0;if(!d||!f||"replace"===t&&!l||"split"===t&&!p){var m=/./[u],y=s(a,u,""[t],(function(t,e,s,r,i){return e.exec===c?d&&!i?{done:!0,value:m.call(e,s,r)}:{done:!0,value:t.call(s,e,r)}:{done:!1}})),g=y[0],x=y[1];r(String.prototype,t,g),i(RegExp.prototype,u,2==e?function(t,e){return x.call(t,this,e)}:function(t){return x.call(t,this)})}}},"230e":function(t,e,s){var r=s("d3f4"),i=s("7726").document,n=r(i)&&r(i.createElement);t.exports=function(t){return n?i.createElement(t):{}}},"23c6":function(t,e,s){var r=s("2d95"),i=s("2b4c")("toStringTag"),n="Arguments"==r(function(){return arguments}()),a=function(t,e){try{return t[e]}catch(s){}};t.exports=function(t){var e,s,o;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(s=a(e=Object(t),i))?s:n?r(e):"Object"==(o=r(e))&&"function"==typeof e.callee?"Arguments":o}},"241e":function(t,e,s){var r=s("25eb");t.exports=function(t){return Object(r(t))}},"25eb":function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},"294c":function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},"2aba":function(t,e,s){var r=s("7726"),i=s("32e9"),n=s("69a8"),a=s("ca5a")("src"),o=s("fa5b"),c="toString",h=(""+o).split(c);s("8378").inspectSource=function(t){return o.call(t)},(t.exports=function(t,e,s,o){var c="function"==typeof s;c&&(n(s,"name")||i(s,"name",e)),t[e]!==s&&(c&&(n(s,a)||i(s,a,t[e]?""+t[e]:h.join(String(e)))),t===r?t[e]=s:o?t[e]?t[e]=s:i(t,e,s):(delete t[e],i(t,e,s)))})(Function.prototype,c,(function(){return"function"==typeof this&&this[a]||o.call(this)}))},"2b4c":function(t,e,s){var r=s("5537")("wks"),i=s("ca5a"),n=s("7726").Symbol,a="function"==typeof n,o=t.exports=function(t){return r[t]||(r[t]=a&&n[t]||(a?n:i)("Symbol."+t))};o.store=r},"2d00":function(t,e){t.exports=!1},"2d95":function(t,e){var s={}.toString;t.exports=function(t){return s.call(t).slice(8,-1)}},"2fdb":function(t,e,s){"use strict";var r=s("5ca1"),i=s("d2c8"),n="includes";r(r.P+r.F*s("5147")(n),"String",{includes:function(t){return!!~i(this,t,n).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"30f1":function(t,e,s){"use strict";var r=s("b8e3"),i=s("63b6"),n=s("9138"),a=s("35e8"),o=s("481b"),c=s("8f60"),h=s("45f2"),l=s("53e2"),p=s("5168")("iterator"),u=!([].keys&&"next"in[].keys()),d="@@iterator",f="keys",m="values",y=function(){return this};t.exports=function(t,e,s,g,x,b,v){c(s,e,g);var w,P,T,E=function(t){if(!u&&t in k)return k[t];switch(t){case f:return function(){return new s(this,t)};case m:return function(){return new s(this,t)}}return function(){return new s(this,t)}},A=e+" Iterator",S=x==m,C=!1,k=t.prototype,N=k[p]||k[d]||x&&k[x],I=N||E(x),O=x?S?E("entries"):I:void 0,D="Array"==e&&k.entries||N;if(D&&(T=l(D.call(new t)),T!==Object.prototype&&T.next&&(h(T,A,!0),r||"function"==typeof T[p]||a(T,p,y))),S&&N&&N.name!==m&&(C=!0,I=function(){return N.call(this)}),r&&!v||!u&&!C&&k[p]||a(k,p,I),o[e]=I,o[A]=y,x)if(w={values:S?I:E(m),keys:b?I:E(f),entries:O},v)for(P in w)P in k||n(k,P,w[P]);else i(i.P+i.F*(u||C),e,w);return w}},"32a6":function(t,e,s){var r=s("241e"),i=s("c3a1");s("ce7e")("keys",(function(){return function(t){return i(r(t))}}))},"32e9":function(t,e,s){var r=s("86cc"),i=s("4630");t.exports=s("9e1e")?function(t,e,s){return r.f(t,e,i(1,s))}:function(t,e,s){return t[e]=s,t}},"32fc":function(t,e,s){var r=s("e53d").document;t.exports=r&&r.documentElement},"335c":function(t,e,s){var r=s("6b4c");t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},"355d":function(t,e){e.f={}.propertyIsEnumerable},"35e8":function(t,e,s){var r=s("d9f6"),i=s("aebd");t.exports=s("8e60")?function(t,e,s){return r.f(t,e,i(1,s))}:function(t,e,s){return t[e]=s,t}},"36c3":function(t,e,s){var r=s("335c"),i=s("25eb");t.exports=function(t){return r(i(t))}},3702:function(t,e,s){var r=s("481b"),i=s("5168")("iterator"),n=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||n[i]===t)}},"3a38":function(t,e){var s=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:s)(t)}},"40c3":function(t,e,s){var r=s("6b4c"),i=s("5168")("toStringTag"),n="Arguments"==r(function(){return arguments}()),a=function(t,e){try{return t[e]}catch(s){}};t.exports=function(t){var e,s,o;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(s=a(e=Object(t),i))?s:n?r(e):"Object"==(o=r(e))&&"function"==typeof e.callee?"Arguments":o}},4588:function(t,e){var s=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:s)(t)}},"45f2":function(t,e,s){var r=s("d9f6").f,i=s("07e3"),n=s("5168")("toStringTag");t.exports=function(t,e,s){t&&!i(t=s?t:t.prototype,n)&&r(t,n,{configurable:!0,value:e})}},4630:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},"469f":function(t,e,s){s("6c1c"),s("1654"),t.exports=s("7d7b")},"481b":function(t,e){t.exports={}},"4aa6":function(t,e,s){t.exports=s("dc62")},"4bf8":function(t,e,s){var r=s("be13");t.exports=function(t){return Object(r(t))}},"4ee1":function(t,e,s){var r=s("5168")("iterator"),i=!1;try{var n=[7][r]();n["return"]=function(){i=!0},Array.from(n,(function(){throw 2}))}catch(a){}t.exports=function(t,e){if(!e&&!i)return!1;var s=!1;try{var n=[7],o=n[r]();o.next=function(){return{done:s=!0}},n[r]=function(){return o},t(n)}catch(a){}return s}},"50ed":function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},5147:function(t,e,s){var r=s("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(s){try{return e[r]=!1,!"/./"[t](e)}catch(i){}}return!0}},5168:function(t,e,s){var r=s("dbdb")("wks"),i=s("62a0"),n=s("e53d").Symbol,a="function"==typeof n,o=t.exports=function(t){return r[t]||(r[t]=a&&n[t]||(a?n:i)("Symbol."+t))};o.store=r},5176:function(t,e,s){t.exports=s("51b6")},"51b6":function(t,e,s){s("a3c3"),t.exports=s("584a").Object.assign},"520a":function(t,e,s){"use strict";var r=s("0bfb"),i=RegExp.prototype.exec,n=String.prototype.replace,a=i,o="lastIndex",c=function(){var t=/a/,e=/b*/g;return i.call(t,"a"),i.call(e,"a"),0!==t[o]||0!==e[o]}(),h=void 0!==/()??/.exec("")[1],l=c||h;l&&(a=function(t){var e,s,a,l,p=this;return h&&(s=new RegExp("^"+p.source+"$(?!\\s)",r.call(p))),c&&(e=p[o]),a=i.call(p,t),c&&a&&(p[o]=p.global?a.index+a[0].length:e),h&&a&&a.length>1&&n.call(a[0],s,(function(){for(l=1;l<arguments.length-2;l++)void 0===arguments[l]&&(a[l]=void 0)})),a}),t.exports=a},"53e2":function(t,e,s){var r=s("07e3"),i=s("241e"),n=s("5559")("IE_PROTO"),a=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=i(t),r(t,n)?t[n]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?a:null}},"549b":function(t,e,s){"use strict";var r=s("d864"),i=s("63b6"),n=s("241e"),a=s("b0dc"),o=s("3702"),c=s("b447"),h=s("20fd"),l=s("7cd6");i(i.S+i.F*!s("4ee1")((function(t){Array.from(t)})),"Array",{from:function(t){var e,s,i,p,u=n(t),d="function"==typeof this?this:Array,f=arguments.length,m=f>1?arguments[1]:void 0,y=void 0!==m,g=0,x=l(u);if(y&&(m=r(m,f>2?arguments[2]:void 0,2)),void 0==x||d==Array&&o(x))for(e=c(u.length),s=new d(e);e>g;g++)h(s,g,y?m(u[g],g):u[g]);else for(p=x.call(u),s=new d;!(i=p.next()).done;g++)h(s,g,y?a(p,m,[i.value,g],!0):i.value);return s.length=g,s}})},"54a1":function(t,e,s){s("6c1c"),s("1654"),t.exports=s("95d5")},5537:function(t,e,s){var r=s("8378"),i=s("7726"),n="__core-js_shared__",a=i[n]||(i[n]={});(t.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:r.version,mode:s("2d00")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},5559:function(t,e,s){var r=s("dbdb")("keys"),i=s("62a0");t.exports=function(t){return r[t]||(r[t]=i(t))}},"584a":function(t,e){var s=t.exports={version:"2.6.5"};"number"==typeof __e&&(__e=s)},"5b4e":function(t,e,s){var r=s("36c3"),i=s("b447"),n=s("0fc9");t.exports=function(t){return function(e,s,a){var o,c=r(e),h=i(c.length),l=n(a,h);if(t&&s!=s){while(h>l)if(o=c[l++],o!=o)return!0}else for(;h>l;l++)if((t||l in c)&&c[l]===s)return t||l||0;return!t&&-1}}},"5ca1":function(t,e,s){var r=s("7726"),i=s("8378"),n=s("32e9"),a=s("2aba"),o=s("9b43"),c="prototype",h=function(t,e,s){var l,p,u,d,f=t&h.F,m=t&h.G,y=t&h.S,g=t&h.P,x=t&h.B,b=m?r:y?r[e]||(r[e]={}):(r[e]||{})[c],v=m?i:i[e]||(i[e]={}),w=v[c]||(v[c]={});for(l in m&&(s=e),s)p=!f&&b&&void 0!==b[l],u=(p?b:s)[l],d=x&&p?o(u,r):g&&"function"==typeof u?o(Function.call,u):u,b&&a(b,l,u,t&h.U),v[l]!=u&&n(v,l,d),g&&w[l]!=u&&(w[l]=u)};r.core=i,h.F=1,h.G=2,h.S=4,h.P=8,h.B=16,h.W=32,h.U=64,h.R=128,t.exports=h},"5d73":function(t,e,s){t.exports=s("469f")},"5f1b":function(t,e,s){"use strict";var r=s("23c6"),i=RegExp.prototype.exec;t.exports=function(t,e){var s=t.exec;if("function"===typeof s){var n=s.call(t,e);if("object"!==typeof n)throw new TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==r(t))throw new TypeError("RegExp#exec called on incompatible receiver");return i.call(t,e)}},"626a":function(t,e,s){var r=s("2d95");t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},"62a0":function(t,e){var s=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++s+r).toString(36))}},"63b6":function(t,e,s){var r=s("e53d"),i=s("584a"),n=s("d864"),a=s("35e8"),o=s("07e3"),c="prototype",h=function(t,e,s){var l,p,u,d=t&h.F,f=t&h.G,m=t&h.S,y=t&h.P,g=t&h.B,x=t&h.W,b=f?i:i[e]||(i[e]={}),v=b[c],w=f?r:m?r[e]:(r[e]||{})[c];for(l in f&&(s=e),s)p=!d&&w&&void 0!==w[l],p&&o(b,l)||(u=p?w[l]:s[l],b[l]=f&&"function"!=typeof w[l]?s[l]:g&&p?n(u,r):x&&w[l]==u?function(t){var e=function(e,s,r){if(this instanceof t){switch(arguments.length){case 0:return new t;case 1:return new t(e);case 2:return new t(e,s)}return new t(e,s,r)}return t.apply(this,arguments)};return e[c]=t[c],e}(u):y&&"function"==typeof u?n(Function.call,u):u,y&&((b.virtual||(b.virtual={}))[l]=u,t&h.R&&v&&!v[l]&&a(v,l,u)))};h.F=1,h.G=2,h.S=4,h.P=8,h.B=16,h.W=32,h.U=64,h.R=128,t.exports=h},6762:function(t,e,s){"use strict";var r=s("5ca1"),i=s("c366")(!0);r(r.P,"Array",{includes:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),s("9c6c")("includes")},6821:function(t,e,s){var r=s("626a"),i=s("be13");t.exports=function(t){return r(i(t))}},"69a8":function(t,e){var s={}.hasOwnProperty;t.exports=function(t,e){return s.call(t,e)}},"6a99":function(t,e,s){var r=s("d3f4");t.exports=function(t,e){if(!r(t))return t;var s,i;if(e&&"function"==typeof(s=t.toString)&&!r(i=s.call(t)))return i;if("function"==typeof(s=t.valueOf)&&!r(i=s.call(t)))return i;if(!e&&"function"==typeof(s=t.toString)&&!r(i=s.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},"6b4c":function(t,e){var s={}.toString;t.exports=function(t){return s.call(t).slice(8,-1)}},"6c1c":function(t,e,s){s("c367");for(var r=s("e53d"),i=s("35e8"),n=s("481b"),a=s("5168")("toStringTag"),o="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),c=0;c<o.length;c++){var h=o[c],l=r[h],p=l&&l.prototype;p&&!p[a]&&i(p,a,h),n[h]=n.Array}},"71c1":function(t,e,s){var r=s("3a38"),i=s("25eb");t.exports=function(t){return function(e,s){var n,a,o=String(i(e)),c=r(s),h=o.length;return c<0||c>=h?t?"":void 0:(n=o.charCodeAt(c),n<55296||n>56319||c+1===h||(a=o.charCodeAt(c+1))<56320||a>57343?t?o.charAt(c):n:t?o.slice(c,c+2):a-56320+(n-55296<<10)+65536)}}},7726:function(t,e){var s=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=s)},"774e":function(t,e,s){t.exports=s("d2d5")},"77f1":function(t,e,s){var r=s("4588"),i=Math.max,n=Math.min;t.exports=function(t,e){return t=r(t),t<0?i(t+e,0):n(t,e)}},"794b":function(t,e,s){t.exports=!s("8e60")&&!s("294c")((function(){return 7!=Object.defineProperty(s("1ec9")("div"),"a",{get:function(){return 7}}).a}))},"79aa":function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},"79e5":function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},"7cd6":function(t,e,s){var r=s("40c3"),i=s("5168")("iterator"),n=s("481b");t.exports=s("584a").getIteratorMethod=function(t){if(void 0!=t)return t[i]||t["@@iterator"]||n[r(t)]}},"7d7b":function(t,e,s){var r=s("e4ae"),i=s("7cd6");t.exports=s("584a").getIterator=function(t){var e=i(t);if("function"!=typeof e)throw TypeError(t+" is not iterable!");return r(e.call(t))}},"7e90":function(t,e,s){var r=s("d9f6"),i=s("e4ae"),n=s("c3a1");t.exports=s("8e60")?Object.defineProperties:function(t,e){i(t);var s,a=n(e),o=a.length,c=0;while(o>c)r.f(t,s=a[c++],e[s]);return t}},8378:function(t,e){var s=t.exports={version:"2.6.5"};"number"==typeof __e&&(__e=s)},8436:function(t,e){t.exports=function(){}},"86cc":function(t,e,s){var r=s("cb7c"),i=s("c69a"),n=s("6a99"),a=Object.defineProperty;e.f=s("9e1e")?Object.defineProperty:function(t,e,s){if(r(t),e=n(e,!0),r(s),i)try{return a(t,e,s)}catch(o){}if("get"in s||"set"in s)throw TypeError("Accessors not supported!");return"value"in s&&(t[e]=s.value),t}},"8aae":function(t,e,s){s("32a6"),t.exports=s("584a").Object.keys},"8e60":function(t,e,s){t.exports=!s("294c")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},"8f60":function(t,e,s){"use strict";var r=s("a159"),i=s("aebd"),n=s("45f2"),a={};s("35e8")(a,s("5168")("iterator"),(function(){return this})),t.exports=function(t,e,s){t.prototype=r(a,{next:i(1,s)}),n(t,e+" Iterator")}},9003:function(t,e,s){var r=s("6b4c");t.exports=Array.isArray||function(t){return"Array"==r(t)}},9138:function(t,e,s){t.exports=s("35e8")},9306:function(t,e,s){"use strict";var r=s("c3a1"),i=s("9aa9"),n=s("355d"),a=s("241e"),o=s("335c"),c=Object.assign;t.exports=!c||s("294c")((function(){var t={},e={},s=Symbol(),r="abcdefghijklmnopqrst";return t[s]=7,r.split("").forEach((function(t){e[t]=t})),7!=c({},t)[s]||Object.keys(c({},e)).join("")!=r}))?function(t,e){var s=a(t),c=arguments.length,h=1,l=i.f,p=n.f;while(c>h){var u,d=o(arguments[h++]),f=l?r(d).concat(l(d)):r(d),m=f.length,y=0;while(m>y)p.call(d,u=f[y++])&&(s[u]=d[u])}return s}:c},9427:function(t,e,s){var r=s("63b6");r(r.S,"Object",{create:s("a159")})},"95d5":function(t,e,s){var r=s("40c3"),i=s("5168")("iterator"),n=s("481b");t.exports=s("584a").isIterable=function(t){var e=Object(t);return void 0!==e[i]||"@@iterator"in e||n.hasOwnProperty(r(e))}},"9aa9":function(t,e){e.f=Object.getOwnPropertySymbols},"9b43":function(t,e,s){var r=s("d8e8");t.exports=function(t,e,s){if(r(t),void 0===e)return t;switch(s){case 1:return function(s){return t.call(e,s)};case 2:return function(s,r){return t.call(e,s,r)};case 3:return function(s,r,i){return t.call(e,s,r,i)}}return function(){return t.apply(e,arguments)}}},"9c6c":function(t,e,s){var r=s("2b4c")("unscopables"),i=Array.prototype;void 0==i[r]&&s("32e9")(i,r,{}),t.exports=function(t){i[r][t]=!0}},"9def":function(t,e,s){var r=s("4588"),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},"9e1e":function(t,e,s){t.exports=!s("79e5")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},a159:function(t,e,s){var r=s("e4ae"),i=s("7e90"),n=s("1691"),a=s("5559")("IE_PROTO"),o=function(){},c="prototype",h=function(){var t,e=s("1ec9")("iframe"),r=n.length,i="<",a=">";e.style.display="none",s("32fc").appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write(i+"script"+a+"document.F=Object"+i+"/script"+a),t.close(),h=t.F;while(r--)delete h[c][n[r]];return h()};t.exports=Object.create||function(t,e){var s;return null!==t?(o[c]=r(t),s=new o,o[c]=null,s[a]=t):s=h(),void 0===e?s:i(s,e)}},a352:function(t,e){t.exports=s("2480")},a3c3:function(t,e,s){var r=s("63b6");r(r.S+r.F,"Object",{assign:s("9306")})},a481:function(t,e,s){"use strict";var r=s("cb7c"),i=s("4bf8"),n=s("9def"),a=s("4588"),o=s("0390"),c=s("5f1b"),h=Math.max,l=Math.min,p=Math.floor,u=/\$([$&`']|\d\d?|<[^>]*>)/g,d=/\$([$&`']|\d\d?)/g,f=function(t){return void 0===t?t:String(t)};s("214f")("replace",2,(function(t,e,s,m){return[function(r,i){var n=t(this),a=void 0==r?void 0:r[e];return void 0!==a?a.call(r,n,i):s.call(String(n),r,i)},function(t,e){var i=m(s,t,this,e);if(i.done)return i.value;var p=r(t),u=String(this),d="function"===typeof e;d||(e=String(e));var g=p.global;if(g){var x=p.unicode;p.lastIndex=0}var b=[];while(1){var v=c(p,u);if(null===v)break;if(b.push(v),!g)break;var w=String(v[0]);""===w&&(p.lastIndex=o(u,n(p.lastIndex),x))}for(var P="",T=0,E=0;E<b.length;E++){v=b[E];for(var A=String(v[0]),S=h(l(a(v.index),u.length),0),C=[],k=1;k<v.length;k++)C.push(f(v[k]));var N=v.groups;if(d){var I=[A].concat(C,S,u);void 0!==N&&I.push(N);var O=String(e.apply(void 0,I))}else O=y(A,u,S,C,N,e);S>=T&&(P+=u.slice(T,S)+O,T=S+A.length)}return P+u.slice(T)}];function y(t,e,r,n,a,o){var c=r+t.length,h=n.length,l=d;return void 0!==a&&(a=i(a),l=u),s.call(o,l,(function(s,i){var o;switch(i.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,r);case"'":return e.slice(c);case"<":o=a[i.slice(1,-1)];break;default:var l=+i;if(0===l)return s;if(l>h){var u=p(l/10);return 0===u?s:u<=h?void 0===n[u-1]?i.charAt(1):n[u-1]+i.charAt(1):s}o=n[l-1]}return void 0===o?"":o}))}}))},a4bb:function(t,e,s){t.exports=s("8aae")},a745:function(t,e,s){t.exports=s("f410")},aae3:function(t,e,s){var r=s("d3f4"),i=s("2d95"),n=s("2b4c")("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[n])?!!e:"RegExp"==i(t))}},aebd:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},b0c5:function(t,e,s){"use strict";var r=s("520a");s("5ca1")({target:"RegExp",proto:!0,forced:r!==/./.exec},{exec:r})},b0dc:function(t,e,s){var r=s("e4ae");t.exports=function(t,e,s,i){try{return i?e(r(s)[0],s[1]):e(s)}catch(a){var n=t["return"];throw void 0!==n&&r(n.call(t)),a}}},b447:function(t,e,s){var r=s("3a38"),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},b8e3:function(t,e){t.exports=!0},be13:function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},c366:function(t,e,s){var r=s("6821"),i=s("9def"),n=s("77f1");t.exports=function(t){return function(e,s,a){var o,c=r(e),h=i(c.length),l=n(a,h);if(t&&s!=s){while(h>l)if(o=c[l++],o!=o)return!0}else for(;h>l;l++)if((t||l in c)&&c[l]===s)return t||l||0;return!t&&-1}}},c367:function(t,e,s){"use strict";var r=s("8436"),i=s("50ed"),n=s("481b"),a=s("36c3");t.exports=s("30f1")(Array,"Array",(function(t,e){this._t=a(t),this._i=0,this._k=e}),(function(){var t=this._t,e=this._k,s=this._i++;return!t||s>=t.length?(this._t=void 0,i(1)):i(0,"keys"==e?s:"values"==e?t[s]:[s,t[s]])}),"values"),n.Arguments=n.Array,r("keys"),r("values"),r("entries")},c3a1:function(t,e,s){var r=s("e6f3"),i=s("1691");t.exports=Object.keys||function(t){return r(t,i)}},c649:function(t,e,s){"use strict";(function(t){s.d(e,"c",(function(){return p})),s.d(e,"a",(function(){return h})),s.d(e,"b",(function(){return a})),s.d(e,"d",(function(){return l}));s("a481");var r=s("4aa6"),i=s.n(r);function n(){return"undefined"!==typeof window?window.console:t.console}var a=n();function o(t){var e=i()(null);return function(s){var r=e[s];return r||(e[s]=t(s))}}var c=/-(\w)/g,h=o((function(t){return t.replace(c,(function(t,e){return e?e.toUpperCase():""}))}));function l(t){null!==t.parentElement&&t.parentElement.removeChild(t)}function p(t,e,s){var r=0===s?t.children[0]:t.children[s-1].nextSibling;t.insertBefore(e,r)}}).call(this,s("c8ba"))},c69a:function(t,e,s){t.exports=!s("9e1e")&&!s("79e5")((function(){return 7!=Object.defineProperty(s("230e")("div"),"a",{get:function(){return 7}}).a}))},c8ba:function(t,e){var s;s=function(){return this}();try{s=s||new Function("return this")()}catch(r){"object"===typeof window&&(s=window)}t.exports=s},c8bb:function(t,e,s){t.exports=s("54a1")},ca5a:function(t,e){var s=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++s+r).toString(36))}},cb7c:function(t,e,s){var r=s("d3f4");t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},ce7e:function(t,e,s){var r=s("63b6"),i=s("584a"),n=s("294c");t.exports=function(t,e){var s=(i.Object||{})[t]||Object[t],a={};a[t]=e(s),r(r.S+r.F*n((function(){s(1)})),"Object",a)}},d2c8:function(t,e,s){var r=s("aae3"),i=s("be13");t.exports=function(t,e,s){if(r(e))throw TypeError("String#"+s+" doesn't accept regex!");return String(i(t))}},d2d5:function(t,e,s){s("1654"),s("549b"),t.exports=s("584a").Array.from},d3f4:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},d864:function(t,e,s){var r=s("79aa");t.exports=function(t,e,s){if(r(t),void 0===e)return t;switch(s){case 1:return function(s){return t.call(e,s)};case 2:return function(s,r){return t.call(e,s,r)};case 3:return function(s,r,i){return t.call(e,s,r,i)}}return function(){return t.apply(e,arguments)}}},d8e8:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},d9f6:function(t,e,s){var r=s("e4ae"),i=s("794b"),n=s("1bc3"),a=Object.defineProperty;e.f=s("8e60")?Object.defineProperty:function(t,e,s){if(r(t),e=n(e,!0),r(s),i)try{return a(t,e,s)}catch(o){}if("get"in s||"set"in s)throw TypeError("Accessors not supported!");return"value"in s&&(t[e]=s.value),t}},dbdb:function(t,e,s){var r=s("584a"),i=s("e53d"),n="__core-js_shared__",a=i[n]||(i[n]={});(t.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:r.version,mode:s("b8e3")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},dc62:function(t,e,s){s("9427");var r=s("584a").Object;t.exports=function(t,e){return r.create(t,e)}},e4ae:function(t,e,s){var r=s("f772");t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},e53d:function(t,e){var s=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=s)},e6f3:function(t,e,s){var r=s("07e3"),i=s("36c3"),n=s("5b4e")(!1),a=s("5559")("IE_PROTO");t.exports=function(t,e){var s,o=i(t),c=0,h=[];for(s in o)s!=a&&r(o,s)&&h.push(s);while(e.length>c)r(o,s=e[c++])&&(~n(h,s)||h.push(s));return h}},f410:function(t,e,s){s("1af6"),t.exports=s("584a").Array.isArray},f559:function(t,e,s){"use strict";var r=s("5ca1"),i=s("9def"),n=s("d2c8"),a="startsWith",o=""[a];r(r.P+r.F*s("5147")(a),"String",{startsWith:function(t){var e=n(this,t,a),s=i(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),r=String(t);return o?o.call(e,r,s):e.slice(s,s+r.length)===r}})},f772:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},fa5b:function(t,e,s){t.exports=s("5537")("native-function-to-string",Function.toString)},fb15:function(t,e,s){"use strict";var r;(s.r(e),"undefined"!==typeof window)&&((r=window.document.currentScript)&&(r=r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(s.p=r[1]));var i=s("5176"),n=s.n(i),a=(s("f559"),s("a4bb")),o=s.n(a),c=s("a745"),h=s.n(c);function l(t){if(h()(t))return t}var p=s("5d73"),u=s.n(p);function d(t,e){var s=[],r=!0,i=!1,n=void 0;try{for(var a,o=u()(t);!(r=(a=o.next()).done);r=!0)if(s.push(a.value),e&&s.length===e)break}catch(c){i=!0,n=c}finally{try{r||null==o["return"]||o["return"]()}finally{if(i)throw n}}return s}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}function m(t,e){return l(t)||d(t,e)||f()}s("6762"),s("2fdb");function y(t){if(h()(t)){for(var e=0,s=new Array(t.length);e<t.length;e++)s[e]=t[e];return s}}var g=s("774e"),x=s.n(g),b=s("c8bb"),v=s.n(b);function w(t){if(v()(Object(t))||"[object Arguments]"===Object.prototype.toString.call(t))return x()(t)}function P(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function T(t){return y(t)||w(t)||P()}var E=s("a352"),A=s.n(E),S=s("c649");function C(t,e,s){return void 0===s||(t=t||{},t[e]=s),t}function k(t,e){return t.map((function(t){return t.elm})).indexOf(e)}function N(t,e,s,r){if(!t)return[];var i=t.map((function(t){return t.elm})),n=e.length-r,a=T(e).map((function(t,e){return e>=n?i.length:i.indexOf(t)}));return s?a.filter((function(t){return-1!==t})):a}function I(t,e){var s=this;this.$nextTick((function(){return s.$emit(t.toLowerCase(),e)}))}function O(t){var e=this;return function(s){null!==e.realList&&e["onDrag"+t](s),I.call(e,t,s)}}function D(t){return["transition-group","TransitionGroup"].includes(t)}function M(t){if(!t||1!==t.length)return!1;var e=m(t,1),s=e[0].componentOptions;return!!s&&D(s.tag)}function L(t,e,s){return t[s]||(e[s]?e[s]():void 0)}function _(t,e,s){var r=0,i=0,n=L(e,s,"header");n&&(r=n.length,t=t?[].concat(T(n),T(t)):T(n));var a=L(e,s,"footer");return a&&(i=a.length,t=t?[].concat(T(t),T(a)):T(a)),{children:t,headerOffset:r,footerOffset:i}}function R(t,e){var s=null,r=function(t,e){s=C(s,t,e)},i=o()(t).filter((function(t){return"id"===t||t.startsWith("data-")})).reduce((function(e,s){return e[s]=t[s],e}),{});if(r("attrs",i),!e)return s;var a=e.on,c=e.props,h=e.attrs;return r("on",a),r("props",c),n()(s.attrs,h),s}var j=["Start","Add","Remove","Update","End"],F=["Choose","Unchoose","Sort","Filter","Clone"],B=["Move"].concat(j,F).map((function(t){return"on"+t})),U=null,q={options:Object,list:{type:Array,required:!1,default:null},value:{type:Array,required:!1,default:null},noTransitionOnDrag:{type:Boolean,default:!1},clone:{type:Function,default:function(t){return t}},element:{type:String,default:"div"},tag:{type:String,default:null},move:{type:Function,default:null},componentData:{type:Object,required:!1,default:null}},V={name:"draggable",inheritAttrs:!1,props:q,data:function(){return{transitionMode:!1,noneFunctionalComponentMode:!1}},render:function(t){var e=this.$slots.default;this.transitionMode=M(e);var s=_(e,this.$slots,this.$scopedSlots),r=s.children,i=s.headerOffset,n=s.footerOffset;this.headerOffset=i,this.footerOffset=n;var a=R(this.$attrs,this.componentData);return t(this.getTag(),a,r)},created:function(){null!==this.list&&null!==this.value&&S["b"].error("Value and list props are mutually exclusive! Please set one or another."),"div"!==this.element&&S["b"].warn("Element props is deprecated please use tag props instead. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#element-props"),void 0!==this.options&&S["b"].warn("Options props is deprecated, add sortable options directly as vue.draggable item, or use v-bind. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#options-props")},mounted:function(){var t=this;if(this.noneFunctionalComponentMode=this.getTag().toLowerCase()!==this.$el.nodeName.toLowerCase()&&!this.getIsFunctional(),this.noneFunctionalComponentMode&&this.transitionMode)throw new Error("Transition-group inside component is not supported. Please alter tag value or remove transition-group. Current tag value: ".concat(this.getTag()));var e={};j.forEach((function(s){e["on"+s]=O.call(t,s)})),F.forEach((function(s){e["on"+s]=I.bind(t,s)}));var s=o()(this.$attrs).reduce((function(e,s){return e[Object(S["a"])(s)]=t.$attrs[s],e}),{}),r=n()({},this.options,s,e,{onMove:function(e,s){return t.onDragMove(e,s)}});!("draggable"in r)&&(r.draggable=">*"),this._sortable=new A.a(this.rootContainer,r),this.computeIndexes()},beforeDestroy:function(){void 0!==this._sortable&&this._sortable.destroy()},computed:{rootContainer:function(){return this.transitionMode?this.$el.children[0]:this.$el},realList:function(){return this.list?this.list:this.value}},watch:{options:{handler:function(t){this.updateOptions(t)},deep:!0},$attrs:{handler:function(t){this.updateOptions(t)},deep:!0},realList:function(){this.computeIndexes()}},methods:{getIsFunctional:function(){var t=this._vnode.fnOptions;return t&&t.functional},getTag:function(){return this.tag||this.element},updateOptions:function(t){for(var e in t){var s=Object(S["a"])(e);-1===B.indexOf(s)&&this._sortable.option(s,t[e])}},getChildrenNodes:function(){if(this.noneFunctionalComponentMode)return this.$children[0].$slots.default;var t=this.$slots.default;return this.transitionMode?t[0].child.$slots.default:t},computeIndexes:function(){var t=this;this.$nextTick((function(){t.visibleIndexes=N(t.getChildrenNodes(),t.rootContainer.children,t.transitionMode,t.footerOffset)}))},getUnderlyingVm:function(t){var e=k(this.getChildrenNodes()||[],t);if(-1===e)return null;var s=this.realList[e];return{index:e,element:s}},getUnderlyingPotencialDraggableComponent:function(t){var e=t.__vue__;return e&&e.$options&&D(e.$options._componentTag)?e.$parent:!("realList"in e)&&1===e.$children.length&&"realList"in e.$children[0]?e.$children[0]:e},emitChanges:function(t){var e=this;this.$nextTick((function(){e.$emit("change",t)}))},alterList:function(t){if(this.list)t(this.list);else{var e=T(this.value);t(e),this.$emit("input",e)}},spliceList:function(){var t=arguments,e=function(e){return e.splice.apply(e,T(t))};this.alterList(e)},updatePosition:function(t,e){var s=function(s){return s.splice(e,0,s.splice(t,1)[0])};this.alterList(s)},getRelatedContextFromMoveEvent:function(t){var e=t.to,s=t.related,r=this.getUnderlyingPotencialDraggableComponent(e);if(!r)return{component:r};var i=r.realList,a={list:i,component:r};if(e!==s&&i&&r.getUnderlyingVm){var o=r.getUnderlyingVm(s);if(o)return n()(o,a)}return a},getVmIndex:function(t){var e=this.visibleIndexes,s=e.length;return t>s-1?s:e[t]},getComponent:function(){return this.$slots.default[0].componentInstance},resetTransitionData:function(t){if(this.noTransitionOnDrag&&this.transitionMode){var e=this.getChildrenNodes();e[t].data=null;var s=this.getComponent();s.children=[],s.kept=void 0}},onDragStart:function(t){this.context=this.getUnderlyingVm(t.item),t.item._underlying_vm_=this.clone(this.context.element),U=t.item},onDragAdd:function(t){var e=t.item._underlying_vm_;if(void 0!==e){Object(S["d"])(t.item);var s=this.getVmIndex(t.newIndex);this.spliceList(s,0,e),this.computeIndexes();var r={element:e,newIndex:s};this.emitChanges({added:r})}},onDragRemove:function(t){if(Object(S["c"])(this.rootContainer,t.item,t.oldIndex),"clone"!==t.pullMode){var e=this.context.index;this.spliceList(e,1);var s={element:this.context.element,oldIndex:e};this.resetTransitionData(e),this.emitChanges({removed:s})}else Object(S["d"])(t.clone)},onDragUpdate:function(t){Object(S["d"])(t.item),Object(S["c"])(t.from,t.item,t.oldIndex);var e=this.context.index,s=this.getVmIndex(t.newIndex);this.updatePosition(e,s);var r={element:this.context.element,oldIndex:e,newIndex:s};this.emitChanges({moved:r})},updateProperty:function(t,e){t.hasOwnProperty(e)&&(t[e]+=this.headerOffset)},computeFutureIndex:function(t,e){if(!t.element)return 0;var s=T(e.to.children).filter((function(t){return"none"!==t.style["display"]})),r=s.indexOf(e.related),i=t.component.getVmIndex(r),n=-1!==s.indexOf(U);return n||!e.willInsertAfter?i:i+1},onDragMove:function(t,e){var s=this.move;if(!s||!this.realList)return!0;var r=this.getRelatedContextFromMoveEvent(t),i=this.context,a=this.computeFutureIndex(r,t);n()(i,{futureIndex:a});var o=n()({},t,{relatedContext:r,draggedContext:i});return s(o,e)},onDragEnd:function(){this.computeIndexes(),U=null}}};"undefined"!==typeof window&&"Vue"in window&&window.Vue.component("draggable",V);var z=V;e["default"]=z}})["default"]},"8c47":function(t,e,s){var r=s("fee7"),i=s("3193");t.exports=function(t){return r(i(t))}},"90a7":function(t,e,s){var r=s("f28d"),i=s("ee6f"),n=s("4d52"),a=s("1f53"),o=n("IE_PROTO"),c=Object.prototype;t.exports=a?Object.getPrototypeOf:function(t){return t=i(t),r(t,o)?t[o]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?c:null}},"91fe":function(t,e,s){var r=s("d5dc"),i=s("4aef").f,n=s("2ba5"),a=s("3d8a"),o=s("200e"),c=s("f69c"),h=s("12d9");t.exports=function(t,e){var s,l,p,u,d,f,m=t.target,y=t.global,g=t.stat;if(l=y?r:g?r[m]||o(m,{}):(r[m]||{}).prototype,l)for(p in e){if(d=e[p],t.noTargetGet?(f=i(l,p),u=f&&f.value):u=l[p],s=h(y?p:m+(g?".":"#")+p,t.forced),!s&&void 0!==u){if(typeof d===typeof u)continue;c(d,u)}(t.sham||u&&u.sham)&&n(d,"sham",!0),a(l,p,d,t)}}},9249:function(t,e,s){"use strict";var r=s("91fe"),i=s("c1c8").left,n=s("fb11"),a=s("6885"),o=n("reduce"),c=a("reduce",{1:0});r({target:"Array",proto:!0,forced:!o||!c},{reduce:function(t){return i(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"94d7":function(t,e,s){var r=s("c223").f,i=s("f28d"),n=s("57c4"),a=n("toStringTag");t.exports=function(t,e,s){t&&!i(t=s?t:t.prototype,a)&&r(t,a,{configurable:!0,value:e})}},9552:function(t,e,s){var r=s("efd1"),i=s("67ea"),n=s("57c4"),a=n("toStringTag"),o="Arguments"==i(function(){return arguments}()),c=function(t,e){try{return t[e]}catch(s){}};t.exports=r?i:function(t){var e,s,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(s=c(e=Object(t),a))?s:o?i(e):"Object"==(r=i(e))&&"function"==typeof e.callee?"Arguments":r}},"9a14":function(t,e,s){var r=s("d5dc"),i=s("41f6"),n=s("021b"),a=s("2ba5");for(var o in i){var c=r[o],h=c&&c.prototype;if(h&&h.forEach!==n)try{a(h,"forEach",n)}catch(l){h.forEach=n}}},"9db6":function(t,e){var s=0,r=Math.random();t.exports=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++s+r).toString(36)}},a406:function(t,e,s){var r=s("df50");t.exports=r("navigator","userAgent")||""},a74f:function(t,e,s){(function(e){(function(e,s){t.exports=s()})(0,(function(){"use strict";var t=function(t){var e=t.id,s=t.viewBox,r=t.content;this.id=e,this.viewBox=s,this.content=r};t.prototype.stringify=function(){return this.content},t.prototype.toString=function(){return this.stringify()},t.prototype.destroy=function(){var t=this;["id","viewBox","content"].forEach((function(e){return delete t[e]}))};var s=function(t){var e=!!document.importNode,s=(new DOMParser).parseFromString(t,"image/svg+xml").documentElement;return e?document.importNode(s,!0):s};"undefined"!==typeof window?window:"undefined"!==typeof e||"undefined"!==typeof self&&self;function r(t,e){return e={exports:{}},t(e,e.exports),e.exports}var i=r((function(t,e){(function(e,s){t.exports=s()})(0,(function(){function t(t){var e=t&&"object"===typeof t;return e&&"[object RegExp]"!==Object.prototype.toString.call(t)&&"[object Date]"!==Object.prototype.toString.call(t)}function e(t){return Array.isArray(t)?[]:{}}function s(s,r){var i=r&&!0===r.clone;return i&&t(s)?n(e(s),s,r):s}function r(e,r,i){var a=e.slice();return r.forEach((function(r,o){"undefined"===typeof a[o]?a[o]=s(r,i):t(r)?a[o]=n(e[o],r,i):-1===e.indexOf(r)&&a.push(s(r,i))})),a}function i(e,r,i){var a={};return t(e)&&Object.keys(e).forEach((function(t){a[t]=s(e[t],i)})),Object.keys(r).forEach((function(o){t(r[o])&&e[o]?a[o]=n(e[o],r[o],i):a[o]=s(r[o],i)})),a}function n(t,e,n){var a=Array.isArray(e),o=n||{arrayMerge:r},c=o.arrayMerge||r;return a?Array.isArray(t)?c(t,e,n):s(e,n):i(t,e,n)}return n.all=function(t,e){if(!Array.isArray(t)||t.length<2)throw new Error("first argument should be an array with at least two elements");return t.reduce((function(t,s){return n(t,s,e)}))},n}))})),n=r((function(t,e){var s={svg:{name:"xmlns",uri:"http://www.w3.org/2000/svg"},xlink:{name:"xmlns:xlink",uri:"http://www.w3.org/1999/xlink"}};e.default=s,t.exports=e.default})),a=function(t){return Object.keys(t).map((function(e){var s=t[e].toString().replace(/"/g,"&quot;");return e+'="'+s+'"'})).join(" ")},o=n.svg,c=n.xlink,h={};h[o.name]=o.uri,h[c.name]=c.uri;var l=function(t,e){void 0===t&&(t="");var s=i(h,e||{}),r=a(s);return"<svg "+r+">"+t+"</svg>"},p=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={isMounted:{}};return r.isMounted.get=function(){return!!this.node},e.createFromExistingNode=function(t){return new e({id:t.getAttribute("id"),viewBox:t.getAttribute("viewBox"),content:t.outerHTML})},e.prototype.destroy=function(){this.isMounted&&this.unmount(),t.prototype.destroy.call(this)},e.prototype.mount=function(t){if(this.isMounted)return this.node;var e="string"===typeof t?document.querySelector(t):t,s=this.render();return this.node=s,e.appendChild(s),s},e.prototype.render=function(){var t=this.stringify();return s(l(t)).childNodes[0]},e.prototype.unmount=function(){this.node.parentNode.removeChild(this.node)},Object.defineProperties(e.prototype,r),e}(t);return p}))}).call(this,s("d314"))},a7d9:function(t,e,s){var r=s("d5dc");t.exports=function(t,e){var s=r.console;s&&s.error&&(1===arguments.length?s.error(t):s.error(t,e))}},a867:function(t,e,s){"use strict";var r=s("df50"),i=s("c223"),n=s("57c4"),a=s("7a23"),o=n("species");t.exports=function(t){var e=r(t),s=i.f;a&&e&&!e[o]&&s(e,o,{configurable:!0,get:function(){return this}})}},a8c9:function(t,e,s){var r=s("67ea");t.exports=Array.isArray||function(t){return"Array"==r(t)}},a9f2:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t}},ac83:function(t,e,s){var r=s("d68d");t.exports=function(t){if(!r(t))throw TypeError(String(t)+" is not an object");return t}},aec8:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},af82:function(t,e,s){"use strict";var r=s("91fe"),i=s("021b");r({target:"Array",proto:!0,forced:[].forEach!=i},{forEach:i})},b128:function(t,e,s){var r=s("7a23"),i=s("d5dc"),n=s("12d9"),a=s("60f2"),o=s("c223").f,c=s("65af").f,h=s("e1dd"),l=s("0618"),p=s("dcb6"),u=s("3d8a"),d=s("f30e"),f=s("d0e2").set,m=s("a867"),y=s("57c4"),g=y("match"),x=i.RegExp,b=x.prototype,v=/a/g,w=/a/g,P=new x(v)!==v,T=p.UNSUPPORTED_Y,E=r&&n("RegExp",!P||T||d((function(){return w[g]=!1,x(v)!=v||x(w)==w||"/a/i"!=x(v,"i")})));if(E){var A=function(t,e){var s,r=this instanceof A,i=h(t),n=void 0===e;if(!r&&i&&t.constructor===A&&n)return t;P?i&&!n&&(t=t.source):t instanceof A&&(n&&(e=l.call(t)),t=t.source),T&&(s=!!e&&e.indexOf("y")>-1,s&&(e=e.replace(/y/g,"")));var o=a(P?new x(t,e):x(t,e),r?this:b,A);return T&&s&&f(o,{sticky:s}),o},S=function(t){t in A||o(A,t,{configurable:!0,get:function(){return x[t]},set:function(e){x[t]=e}})},C=c(x),k=0;while(C.length>k)S(C[k++]);b.constructor=A,A.prototype=b,u(i,"RegExp",A)}m("RegExp")},b1a1:function(t,e,s){var r=s("f30e"),i=s("57c4"),n=s("bf98"),a=i("species");t.exports=function(t){return n>=51||!r((function(){var e=[],s=e.constructor={};return s[a]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},b3f9:function(t,e,s){"use strict";var r=s("91fe"),i=s("21d4");r({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},b41f:function(t,e,s){var r,i,n,a,o,c,h,l,p=s("d5dc"),u=s("4aef").f,d=s("67ea"),f=s("6dcf").set,m=s("c044"),y=p.MutationObserver||p.WebKitMutationObserver,g=p.process,x=p.Promise,b="process"==d(g),v=u(p,"queueMicrotask"),w=v&&v.value;w||(r=function(){var t,e;b&&(t=g.domain)&&t.exit();while(i){e=i.fn,i=i.next;try{e()}catch(s){throw i?a():n=void 0,s}}n=void 0,t&&t.enter()},b?a=function(){g.nextTick(r)}:y&&!m?(o=!0,c=document.createTextNode(""),new y(r).observe(c,{characterData:!0}),a=function(){c.data=o=!o}):x&&x.resolve?(h=x.resolve(void 0),l=h.then,a=function(){l.call(h,r)}):a=function(){f.call(p,r)}),t.exports=w||function(t){var e={fn:t,next:void 0};n&&(n.next=e),i||(i=e,a()),n=e}},bf98:function(t,e,s){var r,i,n=s("d5dc"),a=s("a406"),o=n.process,c=o&&o.versions,h=c&&c.v8;h?(r=h.split("."),i=r[0]+r[1]):a&&(r=a.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/),r&&(i=r[1]))),t.exports=i&&+i},c044:function(t,e,s){var r=s("a406");t.exports=/(iphone|ipod|ipad).*applewebkit/i.test(r)},c0aa:function(t,e,s){var r=s("2a2f"),i=s("f28d"),n=s("7287"),a=s("c223").f;t.exports=function(t){var e=r.Symbol||(r.Symbol={});i(e,t)||a(e,t,{value:n.f(t)})}},c1b0:function(t,e,s){"use strict";var r=s("91fe"),i=s("0192"),n=s("f240"),a=s("684e"),o=s("ee6f"),c=s("3132"),h=s("01d7"),l=s("b1a1"),p=s("6885"),u=l("splice"),d=p("splice",{ACCESSORS:!0,0:0,1:2}),f=Math.max,m=Math.min,y=9007199254740991,g="Maximum allowed length exceeded";r({target:"Array",proto:!0,forced:!u||!d},{splice:function(t,e){var s,r,l,p,u,d,x=o(this),b=a(x.length),v=i(t,b),w=arguments.length;if(0===w?s=r=0:1===w?(s=0,r=b-v):(s=w-2,r=m(f(n(e),0),b-v)),b+s-r>y)throw TypeError(g);for(l=c(x,r),p=0;p<r;p++)u=v+p,u in x&&h(l,p,x[u]);if(l.length=r,s<r){for(p=v;p<b-r;p++)u=p+r,d=p+s,u in x?x[d]=x[u]:delete x[d];for(p=b;p>b-r+s;p--)delete x[p-1]}else if(s>r)for(p=b-r;p>v;p--)u=p+r-1,d=p+s-1,u in x?x[d]=x[u]:delete x[d];for(p=0;p<s;p++)x[p+v]=arguments[p+2];return x.length=b-r+s,l}})},c1c8:function(t,e,s){var r=s("a9f2"),i=s("ee6f"),n=s("fee7"),a=s("684e"),o=function(t){return function(e,s,o,c){r(s);var h=i(e),l=n(h),p=a(h.length),u=t?p-1:0,d=t?-1:1;if(o<2)while(1){if(u in l){c=l[u],u+=d;break}if(u+=d,t?u<0:p<=u)throw TypeError("Reduce of empty array with no initial value")}for(;t?u>=0:p>u;u+=d)u in l&&(c=s(c,l[u],u,h));return c}};t.exports={left:o(!1),right:o(!0)}},c223:function(t,e,s){var r=s("7a23"),i=s("88b4"),n=s("ac83"),a=s("7dc7"),o=Object.defineProperty;e.f=r?o:function(t,e,s){if(n(t),e=a(e,!0),n(s),i)try{return o(t,e,s)}catch(r){}if("get"in s||"set"in s)throw TypeError("Accessors not supported");return"value"in s&&(t[e]=s.value),t}},c354:function(t,e,s){var r=s("7a23"),i=s("c223").f,n=Function.prototype,a=n.toString,o=/^\s*function ([^ (]*)/,c="name";r&&!(c in n)&&i(n,c,{configurable:!0,get:function(){try{return a.call(this).match(o)[1]}catch(t){return""}}})},c451:function(t,e,s){"use strict";s.d(e,"a",(function(){return n}));s("4178"),s("86dd"),s("af82"),s("3f36"),s("f4dd"),s("79dd"),s("9a14");function r(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}function i(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,r)}return s}function n(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?i(Object(s),!0).forEach((function(e){r(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):i(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}},c49e:function(t,e,s){var r=s("df50");t.exports=r("document","documentElement")},c85c:function(t,e,s){var r=s("d5dc"),i=s("200e"),n="__core-js_shared__",a=r[n]||i(n,{});t.exports=a},c9db:function(t,e,s){"use strict";var r,i,n,a,o=s("91fe"),c=s("e17a"),h=s("d5dc"),l=s("df50"),p=s("644f"),u=s("3d8a"),d=s("417f"),f=s("94d7"),m=s("a867"),y=s("d68d"),g=s("a9f2"),x=s("40d4"),b=s("67ea"),v=s("527d"),w=s("66b2"),P=s("e52f"),T=s("fb8e"),E=s("6dcf").set,A=s("b41f"),S=s("4301"),C=s("a7d9"),k=s("df22"),N=s("5c90"),I=s("d0e2"),O=s("12d9"),D=s("57c4"),M=s("bf98"),L=D("species"),_="Promise",R=I.get,j=I.set,F=I.getterFor(_),B=p,U=h.TypeError,q=h.document,V=h.process,z=l("fetch"),H=k.f,W=H,K="process"==b(V),$=!!(q&&q.createEvent&&h.dispatchEvent),X="unhandledrejection",G="rejectionhandled",Y=0,J=1,Q=2,Z=1,tt=2,et=O(_,(function(){var t=v(B)!==String(B);if(!t){if(66===M)return!0;if(!K&&"function"!=typeof PromiseRejectionEvent)return!0}if(c&&!B.prototype["finally"])return!0;if(M>=51&&/native code/.test(B))return!1;var e=B.resolve(1),s=function(t){t((function(){}),(function(){}))},r=e.constructor={};return r[L]=s,!(e.then((function(){}))instanceof s)})),st=et||!P((function(t){B.all(t)["catch"]((function(){}))})),rt=function(t){var e;return!(!y(t)||"function"!=typeof(e=t.then))&&e},it=function(t,e,s){if(!e.notified){e.notified=!0;var r=e.reactions;A((function(){var i=e.value,n=e.state==J,a=0;while(r.length>a){var o,c,h,l=r[a++],p=n?l.ok:l.fail,u=l.resolve,d=l.reject,f=l.domain;try{p?(n||(e.rejection===tt&&ct(t,e),e.rejection=Z),!0===p?o=i:(f&&f.enter(),o=p(i),f&&(f.exit(),h=!0)),o===l.promise?d(U("Promise-chain cycle")):(c=rt(o))?c.call(o,u,d):u(o)):d(i)}catch(m){f&&!h&&f.exit(),d(m)}}e.reactions=[],e.notified=!1,s&&!e.rejection&&at(t,e)}))}},nt=function(t,e,s){var r,i;$?(r=q.createEvent("Event"),r.promise=e,r.reason=s,r.initEvent(t,!1,!0),h.dispatchEvent(r)):r={promise:e,reason:s},(i=h["on"+t])?i(r):t===X&&C("Unhandled promise rejection",s)},at=function(t,e){E.call(h,(function(){var s,r=e.value,i=ot(e);if(i&&(s=N((function(){K?V.emit("unhandledRejection",r,t):nt(X,t,r)})),e.rejection=K||ot(e)?tt:Z,s.error))throw s.value}))},ot=function(t){return t.rejection!==Z&&!t.parent},ct=function(t,e){E.call(h,(function(){K?V.emit("rejectionHandled",t):nt(G,t,e.value)}))},ht=function(t,e,s,r){return function(i){t(e,s,i,r)}},lt=function(t,e,s,r){e.done||(e.done=!0,r&&(e=r),e.value=s,e.state=Q,it(t,e,!0))},pt=function(t,e,s,r){if(!e.done){e.done=!0,r&&(e=r);try{if(t===s)throw U("Promise can't be resolved itself");var i=rt(s);i?A((function(){var r={done:!1};try{i.call(s,ht(pt,t,r,e),ht(lt,t,r,e))}catch(n){lt(t,r,n,e)}})):(e.value=s,e.state=J,it(t,e,!1))}catch(n){lt(t,{done:!1},n,e)}}};et&&(B=function(t){x(this,B,_),g(t),r.call(this);var e=R(this);try{t(ht(pt,this,e),ht(lt,this,e))}catch(s){lt(this,e,s)}},r=function(t){j(this,{type:_,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:Y,value:void 0})},r.prototype=d(B.prototype,{then:function(t,e){var s=F(this),r=H(T(this,B));return r.ok="function"!=typeof t||t,r.fail="function"==typeof e&&e,r.domain=K?V.domain:void 0,s.parent=!0,s.reactions.push(r),s.state!=Y&&it(this,s,!1),r.promise},catch:function(t){return this.then(void 0,t)}}),i=function(){var t=new r,e=R(t);this.promise=t,this.resolve=ht(pt,t,e),this.reject=ht(lt,t,e)},k.f=H=function(t){return t===B||t===n?new i(t):W(t)},c||"function"!=typeof p||(a=p.prototype.then,u(p.prototype,"then",(function(t,e){var s=this;return new B((function(t,e){a.call(s,t,e)})).then(t,e)}),{unsafe:!0}),"function"==typeof z&&o({global:!0,enumerable:!0,forced:!0},{fetch:function(t){return S(B,z.apply(h,arguments))}}))),o({global:!0,wrap:!0,forced:et},{Promise:B}),f(B,_,!1,!0),m(_),n=l(_),o({target:_,stat:!0,forced:et},{reject:function(t){var e=H(this);return e.reject.call(void 0,t),e.promise}}),o({target:_,stat:!0,forced:c||et},{resolve:function(t){return S(c&&this===n?B:this,t)}}),o({target:_,stat:!0,forced:st},{all:function(t){var e=this,s=H(e),r=s.resolve,i=s.reject,n=N((function(){var s=g(e.resolve),n=[],a=0,o=1;w(t,(function(t){var c=a++,h=!1;n.push(void 0),o++,s.call(e,t).then((function(t){h||(h=!0,n[c]=t,--o||r(n))}),i)})),--o||r(n)}));return n.error&&i(n.value),s.promise},race:function(t){var e=this,s=H(e),r=s.reject,i=N((function(){var i=g(e.resolve);w(t,(function(t){i.call(e,t).then(s.resolve,r)}))}));return i.error&&r(i.value),s.promise}})},d0e2:function(t,e,s){var r,i,n,a=s("3109"),o=s("d5dc"),c=s("d68d"),h=s("2ba5"),l=s("f28d"),p=s("4d52"),u=s("4888"),d=o.WeakMap,f=function(t){return n(t)?i(t):r(t,{})},m=function(t){return function(e){var s;if(!c(e)||(s=i(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return s}};if(a){var y=new d,g=y.get,x=y.has,b=y.set;r=function(t,e){return b.call(y,t,e),e},i=function(t){return g.call(y,t)||{}},n=function(t){return x.call(y,t)}}else{var v=p("state");u[v]=!0,r=function(t,e){return h(t,v,e),e},i=function(t){return l(t,v)?t[v]:{}},n=function(t){return l(t,v)}}t.exports={set:r,get:i,has:n,enforce:f,getterFor:m}},d314:function(t,e){var s;s=function(){return this}();try{s=s||new Function("return this")()}catch(r){"object"===typeof window&&(s=window)}t.exports=s},d5dc:function(t,e,s){(function(e){var s=function(t){return t&&t.Math==Math&&t};t.exports=s("object"==typeof globalThis&&globalThis)||s("object"==typeof window&&window)||s("object"==typeof self&&self)||s("object"==typeof e&&e)||Function("return this")()}).call(this,s("d314"))},d68d:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},d780:function(t,e,s){"use strict";var r=s("deaa"),i=s("ac83"),n=s("ee6f"),a=s("684e"),o=s("f240"),c=s("3193"),h=s("536c"),l=s("81a0"),p=Math.max,u=Math.min,d=Math.floor,f=/\$([$&'`]|\d\d?|<[^>]*>)/g,m=/\$([$&'`]|\d\d?)/g,y=function(t){return void 0===t?t:String(t)};r("replace",2,(function(t,e,s,r){var g=r.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,x=r.REPLACE_KEEPS_$0,b=g?"$":"$0";return[function(s,r){var i=c(this),n=void 0==s?void 0:s[t];return void 0!==n?n.call(s,i,r):e.call(String(i),s,r)},function(t,r){if(!g&&x||"string"===typeof r&&-1===r.indexOf(b)){var n=s(e,t,this,r);if(n.done)return n.value}var c=i(t),d=String(this),f="function"===typeof r;f||(r=String(r));var m=c.global;if(m){var w=c.unicode;c.lastIndex=0}var P=[];while(1){var T=l(c,d);if(null===T)break;if(P.push(T),!m)break;var E=String(T[0]);""===E&&(c.lastIndex=h(d,a(c.lastIndex),w))}for(var A="",S=0,C=0;C<P.length;C++){T=P[C];for(var k=String(T[0]),N=p(u(o(T.index),d.length),0),I=[],O=1;O<T.length;O++)I.push(y(T[O]));var D=T.groups;if(f){var M=[k].concat(I,N,d);void 0!==D&&M.push(D);var L=String(r.apply(void 0,M))}else L=v(k,d,N,I,D,r);N>=S&&(A+=d.slice(S,N)+L,S=N+k.length)}return A+d.slice(S)}];function v(t,s,r,i,a,o){var c=r+t.length,h=i.length,l=m;return void 0!==a&&(a=n(a),l=f),e.call(o,l,(function(e,n){var o;switch(n.charAt(0)){case"$":return"$";case"&":return t;case"`":return s.slice(0,r);case"'":return s.slice(c);case"<":o=a[n.slice(1,-1)];break;default:var l=+n;if(0===l)return e;if(l>h){var p=d(l/10);return 0===p?e:p<=h?void 0===i[p-1]?n.charAt(1):i[p-1]+n.charAt(1):e}o=i[l-1]}return void 0===o?"":o}))}}))},d886:function(t,e,s){"use strict";s.d(e,"a",(function(){return r}));s("4178"),s("fc88"),s("e350"),s("d9a3"),s("3a20"),s("ef8e"),s("252a");function r(t){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}},d9a3:function(t,e,s){"use strict";var r=s("8c47"),i=s("5751"),n=s("ed35"),a=s("d0e2"),o=s("5646"),c="Array Iterator",h=a.set,l=a.getterFor(c);t.exports=o(Array,"Array",(function(t,e){h(this,{type:c,target:r(t),index:0,kind:e})}),(function(){var t=l(this),e=t.target,s=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==s?{value:r,done:!1}:"values"==s?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),"values"),n.Arguments=n.Array,i("keys"),i("values"),i("entries")},da66:function(t,e,s){var r=s("d68d");t.exports=function(t){if(!r(t)&&null!==t)throw TypeError("Can't set "+String(t)+" as a prototype");return t}},dc62:function(t,e){t.exports=function(t){return t&&"object"===typeof t&&"function"===typeof t.copy&&"function"===typeof t.fill&&"function"===typeof t.readUInt8}},dcb6:function(t,e,s){"use strict";var r=s("f30e");function i(t,e){return RegExp(t,e)}e.UNSUPPORTED_Y=r((function(){var t=i("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),e.BROKEN_CARET=r((function(){var t=i("^r","gy");return t.lastIndex=2,null!=t.exec("str")}))},de3e:function(t,e,s){var r=s("91fe"),i=s("e045");r({target:"Object",stat:!0,forced:Object.assign!==i},{assign:i})},deaa:function(t,e,s){"use strict";s("b3f9");var r=s("3d8a"),i=s("f30e"),n=s("57c4"),a=s("21d4"),o=s("2ba5"),c=n("species"),h=!i((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),l=function(){return"$0"==="a".replace(/./,"$0")}(),p=n("replace"),u=function(){return!!/./[p]&&""===/./[p]("a","$0")}(),d=!i((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var s="ab".split(t);return 2!==s.length||"a"!==s[0]||"b"!==s[1]}));t.exports=function(t,e,s,p){var f=n(t),m=!i((function(){var e={};return e[f]=function(){return 7},7!=""[t](e)})),y=m&&!i((function(){var e=!1,s=/a/;return"split"===t&&(s={},s.constructor={},s.constructor[c]=function(){return s},s.flags="",s[f]=/./[f]),s.exec=function(){return e=!0,null},s[f](""),!e}));if(!m||!y||"replace"===t&&(!h||!l||u)||"split"===t&&!d){var g=/./[f],x=s(f,""[t],(function(t,e,s,r,i){return e.exec===a?m&&!i?{done:!0,value:g.call(e,s,r)}:{done:!0,value:t.call(s,e,r)}:{done:!1}}),{REPLACE_KEEPS_$0:l,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:u}),b=x[0],v=x[1];r(String.prototype,t,b),r(RegExp.prototype,f,2==e?function(t,e){return v.call(t,this,e)}:function(t){return v.call(t,this)})}p&&o(RegExp.prototype[f],"sham",!0)}},df22:function(t,e,s){"use strict";var r=s("a9f2"),i=function(t){var e,s;this.promise=new t((function(t,r){if(void 0!==e||void 0!==s)throw TypeError("Bad Promise constructor");e=t,s=r})),this.resolve=r(e),this.reject=r(s)};t.exports.f=function(t){return new i(t)}},df50:function(t,e,s){var r=s("2a2f"),i=s("d5dc"),n=function(t){return"function"==typeof t?t:void 0};t.exports=function(t,e){return arguments.length<2?n(r[t])||n(i[t]):r[t]&&r[t][e]||i[t]&&i[t][e]}},e045:function(t,e,s){"use strict";var r=s("7a23"),i=s("f30e"),n=s("16e5"),a=s("1072"),o=s("354c"),c=s("ee6f"),h=s("fee7"),l=Object.assign,p=Object.defineProperty;t.exports=!l||i((function(){if(r&&1!==l({b:1},l(p({},"a",{enumerable:!0,get:function(){p(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},s=Symbol(),i="abcdefghijklmnopqrst";return t[s]=7,i.split("").forEach((function(t){e[t]=t})),7!=l({},t)[s]||n(l({},e)).join("")!=i}))?function(t,e){var s=c(t),i=arguments.length,l=1,p=a.f,u=o.f;while(i>l){var d,f=h(arguments[l++]),m=p?n(f).concat(p(f)):n(f),y=m.length,g=0;while(y>g)d=m[g++],r&&!u.call(f,d)||(s[d]=f[d])}return s}:l},e16c:function(t,e,s){(function(e){(function(e,s){t.exports=s()})(0,(function(){"use strict";"undefined"!==typeof window?window:"undefined"!==typeof e||"undefined"!==typeof self&&self;function t(t,e){return e={exports:{}},t(e,e.exports),e.exports}var s=t((function(t,e){(function(e,s){t.exports=s()})(0,(function(){function t(t){var e=t&&"object"===typeof t;return e&&"[object RegExp]"!==Object.prototype.toString.call(t)&&"[object Date]"!==Object.prototype.toString.call(t)}function e(t){return Array.isArray(t)?[]:{}}function s(s,r){var i=r&&!0===r.clone;return i&&t(s)?n(e(s),s,r):s}function r(e,r,i){var a=e.slice();return r.forEach((function(r,o){"undefined"===typeof a[o]?a[o]=s(r,i):t(r)?a[o]=n(e[o],r,i):-1===e.indexOf(r)&&a.push(s(r,i))})),a}function i(e,r,i){var a={};return t(e)&&Object.keys(e).forEach((function(t){a[t]=s(e[t],i)})),Object.keys(r).forEach((function(o){t(r[o])&&e[o]?a[o]=n(e[o],r[o],i):a[o]=s(r[o],i)})),a}function n(t,e,n){var a=Array.isArray(e),o=n||{arrayMerge:r},c=o.arrayMerge||r;return a?Array.isArray(t)?c(t,e,n):s(e,n):i(t,e,n)}return n.all=function(t,e){if(!Array.isArray(t)||t.length<2)throw new Error("first argument should be an array with at least two elements");return t.reduce((function(t,s){return n(t,s,e)}))},n}))}));function r(t){return t=t||Object.create(null),{on:function(e,s){(t[e]||(t[e]=[])).push(s)},off:function(e,s){t[e]&&t[e].splice(t[e].indexOf(s)>>>0,1)},emit:function(e,s){(t[e]||[]).map((function(t){t(s)})),(t["*"]||[]).map((function(t){t(e,s)}))}}}var i=t((function(t,e){var s={svg:{name:"xmlns",uri:"http://www.w3.org/2000/svg"},xlink:{name:"xmlns:xlink",uri:"http://www.w3.org/1999/xlink"}};e.default=s,t.exports=e.default})),n=function(t){return Object.keys(t).map((function(e){var s=t[e].toString().replace(/"/g,"&quot;");return e+'="'+s+'"'})).join(" ")},a=i.svg,o=i.xlink,c={};c[a.name]=a.uri,c[o.name]=o.uri;var h,l=function(t,e){void 0===t&&(t="");var r=s(c,e||{}),i=n(r);return"<svg "+i+">"+t+"</svg>"},p=i.svg,u=i.xlink,d={attrs:(h={style:["position: absolute","width: 0","height: 0"].join("; ")},h[p.name]=p.uri,h[u.name]=u.uri,h)},f=function(t){this.config=s(d,t||{}),this.symbols=[]};f.prototype.add=function(t){var e=this,s=e.symbols,r=this.find(t.id);return r?(s[s.indexOf(r)]=t,!1):(s.push(t),!0)},f.prototype.remove=function(t){var e=this,s=e.symbols,r=this.find(t);return!!r&&(s.splice(s.indexOf(r),1),r.destroy(),!0)},f.prototype.find=function(t){return this.symbols.filter((function(e){return e.id===t}))[0]||null},f.prototype.has=function(t){return null!==this.find(t)},f.prototype.stringify=function(){var t=this.config,e=t.attrs,s=this.symbols.map((function(t){return t.stringify()})).join("");return l(s,e)},f.prototype.toString=function(){return this.stringify()},f.prototype.destroy=function(){this.symbols.forEach((function(t){return t.destroy()}))};var m=function(t){var e=t.id,s=t.viewBox,r=t.content;this.id=e,this.viewBox=s,this.content=r};m.prototype.stringify=function(){return this.content},m.prototype.toString=function(){return this.stringify()},m.prototype.destroy=function(){var t=this;["id","viewBox","content"].forEach((function(e){return delete t[e]}))};var y=function(t){var e=!!document.importNode,s=(new DOMParser).parseFromString(t,"image/svg+xml").documentElement;return e?document.importNode(s,!0):s},g=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var s={isMounted:{}};return s.isMounted.get=function(){return!!this.node},e.createFromExistingNode=function(t){return new e({id:t.getAttribute("id"),viewBox:t.getAttribute("viewBox"),content:t.outerHTML})},e.prototype.destroy=function(){this.isMounted&&this.unmount(),t.prototype.destroy.call(this)},e.prototype.mount=function(t){if(this.isMounted)return this.node;var e="string"===typeof t?document.querySelector(t):t,s=this.render();return this.node=s,e.appendChild(s),s},e.prototype.render=function(){var t=this.stringify();return y(l(t)).childNodes[0]},e.prototype.unmount=function(){this.node.parentNode.removeChild(this.node)},Object.defineProperties(e.prototype,s),e}(m),x={autoConfigure:!0,mountTo:"body",syncUrlsWithBaseTag:!1,listenLocationChangeEvent:!0,locationChangeEvent:"locationChange",locationChangeAngularEmitter:!1,usagesToUpdate:"use[*|href]",moveGradientsOutsideSymbol:!1},b=function(t){return Array.prototype.slice.call(t,0)},v={isChrome:function(){return/chrome/i.test(navigator.userAgent)},isFirefox:function(){return/firefox/i.test(navigator.userAgent)},isIE:function(){return/msie/i.test(navigator.userAgent)||/trident/i.test(navigator.userAgent)},isEdge:function(){return/edge/i.test(navigator.userAgent)}},w=function(t,e){var s=document.createEvent("CustomEvent");s.initCustomEvent(t,!1,!1,e),window.dispatchEvent(s)},P=function(t){var e=[];return b(t.querySelectorAll("style")).forEach((function(t){t.textContent+="",e.push(t)})),e},T=function(t){return(t||window.location.href).split("#")[0]},E=function(t){angular.module("ng").run(["$rootScope",function(e){e.$on("$locationChangeSuccess",(function(e,s,r){w(t,{oldUrl:r,newUrl:s})}))}])},A="linearGradient, radialGradient, pattern",S=function(t,e){return void 0===e&&(e=A),b(t.querySelectorAll("symbol")).forEach((function(t){b(t.querySelectorAll(e)).forEach((function(e){t.parentNode.insertBefore(e,t)}))})),t};function C(t,e){var s=b(t).reduce((function(t,s){if(!s.attributes)return t;var r=b(s.attributes),i=e?r.filter(e):r;return t.concat(i)}),[]);return s}var k=i.xlink.uri,N="xlink:href",I=/[{}|\\\^\[\]`"<>]/g;function O(t){return t.replace(I,(function(t){return"%"+t[0].charCodeAt(0).toString(16).toUpperCase()}))}function D(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function M(t,e,s){return b(t).forEach((function(t){var r=t.getAttribute(N);if(r&&0===r.indexOf(e)){var i=r.replace(e,s);t.setAttributeNS(k,N,i)}})),t}var L,_=["clipPath","colorProfile","src","cursor","fill","filter","marker","markerStart","markerMid","markerEnd","mask","stroke","style"],R=_.map((function(t){return"["+t+"]"})).join(","),j=function(t,e,s,r){var i=O(s),n=O(r),a=t.querySelectorAll(R),o=C(a,(function(t){var e=t.localName,s=t.value;return-1!==_.indexOf(e)&&-1!==s.indexOf("url("+i)}));o.forEach((function(t){return t.value=t.value.replace(new RegExp(D(i),"g"),n)})),M(e,i,n)},F={MOUNT:"mount",SYMBOL_MOUNT:"symbol_mount"},B=function(t){function e(e){var i=this;void 0===e&&(e={}),t.call(this,s(x,e));var n=r();this._emitter=n,this.node=null;var a=this,o=a.config;if(o.autoConfigure&&this._autoConfigure(e),o.syncUrlsWithBaseTag){var c=document.getElementsByTagName("base")[0].getAttribute("href");n.on(F.MOUNT,(function(){return i.updateUrls("#",c)}))}var h=this._handleLocationChange.bind(this);this._handleLocationChange=h,o.listenLocationChangeEvent&&window.addEventListener(o.locationChangeEvent,h),o.locationChangeAngularEmitter&&E(o.locationChangeEvent),n.on(F.MOUNT,(function(t){o.moveGradientsOutsideSymbol&&S(t)})),n.on(F.SYMBOL_MOUNT,(function(t){o.moveGradientsOutsideSymbol&&S(t.parentNode),(v.isIE()||v.isEdge())&&P(t)}))}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var i={isMounted:{}};return i.isMounted.get=function(){return!!this.node},e.prototype._autoConfigure=function(t){var e=this,s=e.config;"undefined"===typeof t.syncUrlsWithBaseTag&&(s.syncUrlsWithBaseTag="undefined"!==typeof document.getElementsByTagName("base")[0]),"undefined"===typeof t.locationChangeAngularEmitter&&(s.locationChangeAngularEmitter="angular"in window),"undefined"===typeof t.moveGradientsOutsideSymbol&&(s.moveGradientsOutsideSymbol=v.isFirefox())},e.prototype._handleLocationChange=function(t){var e=t.detail,s=e.oldUrl,r=e.newUrl;this.updateUrls(s,r)},e.prototype.add=function(e){var s=this,r=t.prototype.add.call(this,e);return this.isMounted&&r&&(e.mount(s.node),this._emitter.emit(F.SYMBOL_MOUNT,e.node)),r},e.prototype.attach=function(t){var e=this,s=this;if(s.isMounted)return s.node;var r="string"===typeof t?document.querySelector(t):t;return s.node=r,this.symbols.forEach((function(t){t.mount(s.node),e._emitter.emit(F.SYMBOL_MOUNT,t.node)})),b(r.querySelectorAll("symbol")).forEach((function(t){var e=g.createFromExistingNode(t);e.node=t,s.add(e)})),this._emitter.emit(F.MOUNT,r),r},e.prototype.destroy=function(){var t=this,e=t.config,s=t.symbols,r=t._emitter;s.forEach((function(t){return t.destroy()})),r.off("*"),window.removeEventListener(e.locationChangeEvent,this._handleLocationChange),this.isMounted&&this.unmount()},e.prototype.mount=function(t,e){void 0===t&&(t=this.config.mountTo),void 0===e&&(e=!1);var s=this;if(s.isMounted)return s.node;var r="string"===typeof t?document.querySelector(t):t,i=s.render();return this.node=i,e&&r.childNodes[0]?r.insertBefore(i,r.childNodes[0]):r.appendChild(i),this._emitter.emit(F.MOUNT,i),i},e.prototype.render=function(){return y(this.stringify())},e.prototype.unmount=function(){this.node.parentNode.removeChild(this.node)},e.prototype.updateUrls=function(t,e){if(!this.isMounted)return!1;var s=document.querySelectorAll(this.config.usagesToUpdate);return j(this.node,s,T(t)+"#",T(e)+"#"),!0},Object.defineProperties(e.prototype,i),e}(f),U=t((function(t){
/*!
  * domready (c) Dustin Diaz 2014 - License MIT
  */
!function(e,s){t.exports=s()}(0,(function(){var t,e=[],s=document,r=s.documentElement.doScroll,i="DOMContentLoaded",n=(r?/^loaded|^c/:/^loaded|^i|^c/).test(s.readyState);return n||s.addEventListener(i,t=function(){s.removeEventListener(i,t),n=1;while(t=e.shift())t()}),function(t){n?setTimeout(t,0):e.push(t)}}))})),q="__SVG_SPRITE_NODE__",V="__SVG_SPRITE__",z=!!window[V];z?L=window[V]:(L=new B({attrs:{id:q}}),window[V]=L);var H=function(){var t=document.getElementById(q);t?L.attach(t):L.mount(document.body,!0)};document.body?H():U(H);var W=L;return W}))}).call(this,s("d314"))},e17a:function(t,e){t.exports=!1},e1c9:function(t,e,s){var r=s("e1dd");t.exports=function(t){if(r(t))throw TypeError("The method doesn't accept regular expressions");return t}},e1dd:function(t,e,s){var r=s("d68d"),i=s("67ea"),n=s("57c4"),a=n("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[a])?!!e:"RegExp"==i(t))}},e28b:function(t,e,s){var r=s("9552"),i=s("ed35"),n=s("57c4"),a=n("iterator");t.exports=function(t){if(void 0!=t)return t[a]||t["@@iterator"]||i[r(t)]}},e350:function(t,e,s){var r=s("c0aa");r("iterator")},e52f:function(t,e,s){var r=s("57c4"),i=r("iterator"),n=!1;try{var a=0,o={next:function(){return{done:!!a++}},return:function(){n=!0}};o[i]=function(){return this},Array.from(o,(function(){throw 2}))}catch(c){}t.exports=function(t,e){if(!e&&!n)return!1;var s=!1;try{var r={};r[i]=function(){return{next:function(){return{done:s=!0}}}},t(r)}catch(c){}return s}},e628:function(t,e,s){var r=s("df50"),i=s("65af"),n=s("1072"),a=s("ac83");t.exports=r("Reflect","ownKeys")||function(t){var e=i.f(a(t)),s=n.f;return s?e.concat(s(t)):e}},e90a:function(t,e,s){"use strict";function r(t,e,s,r,i,n,a,o){var c,h="function"===typeof t?t.options:t;if(e&&(h.render=e,h.staticRenderFns=s,h._compiled=!0),r&&(h.functional=!0),n&&(h._scopeId="data-v-"+n),a?(c=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},h._ssrRegister=c):i&&(c=o?function(){i.call(this,this.$root.$options.shadowRoot)}:i),c)if(h.functional){h._injectStyles=c;var l=h.render;h.render=function(t,e){return c.call(e),l(t,e)}}else{var p=h.beforeCreate;h.beforeCreate=p?[].concat(p,c):[c]}return{exports:t,options:h}}s.d(e,"a",(function(){return r}))},e90c:function(t,e,s){"use strict";var r=s("91fe"),i=s("fee7"),n=s("8c47"),a=s("fb11"),o=[].join,c=i!=Object,h=a("join",",");r({target:"Array",proto:!0,forced:c||!h},{join:function(t){return o.call(n(this),void 0===t?",":t)}})},ecc0:function(t,e,s){(function(s){var r,i,n;(function(s,a){i=[],r=a,n="function"===typeof r?r.apply(e,i):r,void 0===n||(t.exports=n)})(0,(function(){"use strict";function e(t,e){return"undefined"==typeof e?e={autoBom:!1}:"object"!=typeof e&&(console.warn("Deprecated: Expected third argument to be a object"),e={autoBom:!e}),e.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob(["\ufeff",t],{type:t.type}):t}function r(t,e,s){var r=new XMLHttpRequest;r.open("GET",t),r.responseType="blob",r.onload=function(){o(r.response,e,s)},r.onerror=function(){console.error("could not download file")},r.send()}function i(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch(t){}return 200<=e.status&&299>=e.status}function n(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(r){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var a="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof s&&s.global===s?s:void 0,o=a.saveAs||("object"!=typeof window||window!==a?function(){}:"download"in HTMLAnchorElement.prototype?function(t,e,s){var o=a.URL||a.webkitURL,c=document.createElement("a");e=e||t.name||"download",c.download=e,c.rel="noopener","string"==typeof t?(c.href=t,c.origin===location.origin?n(c):i(c.href)?r(t,e,s):n(c,c.target="_blank")):(c.href=o.createObjectURL(t),setTimeout((function(){o.revokeObjectURL(c.href)}),4e4),setTimeout((function(){n(c)}),0))}:"msSaveOrOpenBlob"in navigator?function(t,s,a){if(s=s||t.name||"download","string"!=typeof t)navigator.msSaveOrOpenBlob(e(t,a),s);else if(i(t))r(t,s,a);else{var o=document.createElement("a");o.href=t,o.target="_blank",setTimeout((function(){n(o)}))}}:function(t,e,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),"string"==typeof t)return r(t,e,s);var n="application/octet-stream"===t.type,o=/constructor/i.test(a.HTMLElement)||a.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||n&&o)&&"object"==typeof FileReader){var h=new FileReader;h.onloadend=function(){var t=h.result;t=c?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=t:location=t,i=null},h.readAsDataURL(t)}else{var l=a.URL||a.webkitURL,p=l.createObjectURL(t);i?i.location=p:location.href=p,i=null,setTimeout((function(){l.revokeObjectURL(p)}),4e4)}});a.saveAs=o.saveAs=o,t.exports=o}))}).call(this,s("d314"))},ed35:function(t,e){t.exports={}},ed51:function(t,e,s){"use strict";var r=s("143b").IteratorPrototype,i=s("641d"),n=s("aec8"),a=s("94d7"),o=s("ed35"),c=function(){return this};t.exports=function(t,e,s){var h=e+" Iterator";return t.prototype=i(r,{next:n(1,s)}),a(t,h,!1,!0),o[h]=c,t}},ee6f:function(t,e,s){var r=s("3193");t.exports=function(t){return Object(r(t))}},eef6:function(t,e,s){e.nextTick=function(t){var e=Array.prototype.slice.call(arguments);e.shift(),setTimeout((function(){t.apply(null,e)}),0)},e.platform=e.arch=e.execPath=e.title="browser",e.pid=1,e.browser=!0,e.env={},e.argv=[],e.binding=function(t){throw new Error("No such module. (Possibly not yet loaded)")},function(){var t,r="/";e.cwd=function(){return r},e.chdir=function(e){t||(t=s("6266")),r=t.resolve(e,r)}}(),e.exit=e.kill=e.umask=e.dlopen=e.uptime=e.memoryUsage=e.uvCounters=function(){},e.features={}},ef8e:function(t,e,s){"use strict";var r=s("3303").charAt,i=s("d0e2"),n=s("5646"),a="String Iterator",o=i.set,c=i.getterFor(a);n(String,"String",(function(t){o(this,{type:a,string:String(t),index:0})}),(function(){var t,e=c(this),s=e.string,i=e.index;return i>=s.length?{value:void 0,done:!0}:(t=r(s,i),e.index+=t.length,{value:t,done:!1})}))},efd1:function(t,e,s){var r=s("57c4"),i=r("toStringTag"),n={};n[i]="z",t.exports="[object z]"===String(n)},f240:function(t,e){var s=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:s)(t)}},f28d:function(t,e){var s={}.hasOwnProperty;t.exports=function(t,e){return s.call(t,e)}},f30e:function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},f348:function(t,e,s){
/*!
 * clipboard.js v2.0.6
 * https://clipboardjs.com/
 * 
 * Licensed MIT © Zeno Rocha
 */
(function(e,s){t.exports=s()})(0,(function(){return function(t){var e={};function s(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,s),i.l=!0,i.exports}return s.m=t,s.c=e,s.d=function(t,e,r){s.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(t,e){if(1&e&&(t=s(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(s.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)s.d(r,i,function(e){return t[e]}.bind(null,i));return r},s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,"a",e),e},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.p="",s(s.s=6)}([function(t,e){function s(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var s=t.hasAttribute("readonly");s||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),s||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var r=window.getSelection(),i=document.createRange();i.selectNodeContents(t),r.removeAllRanges(),r.addRange(i),e=r.toString()}return e}t.exports=s},function(t,e){function s(){}s.prototype={on:function(t,e,s){var r=this.e||(this.e={});return(r[t]||(r[t]=[])).push({fn:e,ctx:s}),this},once:function(t,e,s){var r=this;function i(){r.off(t,i),e.apply(s,arguments)}return i._=e,this.on(t,i,s)},emit:function(t){var e=[].slice.call(arguments,1),s=((this.e||(this.e={}))[t]||[]).slice(),r=0,i=s.length;for(r;r<i;r++)s[r].fn.apply(s[r].ctx,e);return this},off:function(t,e){var s=this.e||(this.e={}),r=s[t],i=[];if(r&&e)for(var n=0,a=r.length;n<a;n++)r[n].fn!==e&&r[n].fn._!==e&&i.push(r[n]);return i.length?s[t]=i:delete s[t],this}},t.exports=s,t.exports.TinyEmitter=s},function(t,e,s){var r=s(3),i=s(4);function n(t,e,s){if(!t&&!e&&!s)throw new Error("Missing required arguments");if(!r.string(e))throw new TypeError("Second argument must be a String");if(!r.fn(s))throw new TypeError("Third argument must be a Function");if(r.node(t))return a(t,e,s);if(r.nodeList(t))return o(t,e,s);if(r.string(t))return c(t,e,s);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function a(t,e,s){return t.addEventListener(e,s),{destroy:function(){t.removeEventListener(e,s)}}}function o(t,e,s){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,s)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,s)}))}}}function c(t,e,s){return i(document.body,t,e,s)}t.exports=n},function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var s=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===s||"[object HTMLCollection]"===s)&&"length"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return"string"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e}},function(t,e,s){var r=s(5);function i(t,e,s,r,i){var n=a.apply(this,arguments);return t.addEventListener(s,n,i),{destroy:function(){t.removeEventListener(s,n,i)}}}function n(t,e,s,r,n){return"function"===typeof t.addEventListener?i.apply(null,arguments):"function"===typeof s?i.bind(null,document).apply(null,arguments):("string"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return i(t,e,s,r,n)})))}function a(t,e,s,i){return function(s){s.delegateTarget=r(s.target,e),s.delegateTarget&&i.call(t,s)}}t.exports=n},function(t,e){var s=9;if("undefined"!==typeof Element&&!Element.prototype.matches){var r=Element.prototype;r.matches=r.matchesSelector||r.mozMatchesSelector||r.msMatchesSelector||r.oMatchesSelector||r.webkitMatchesSelector}function i(t,e){while(t&&t.nodeType!==s){if("function"===typeof t.matches&&t.matches(e))return t;t=t.parentNode}}t.exports=i},function(t,e,s){"use strict";s.r(e);var r=s(0),i=s.n(r),n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a=function(){function t(t,e){for(var s=0;s<e.length;s++){var r=e[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,s,r){return s&&t(e.prototype,s),r&&t(e,r),e}}();function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var c=function(){function t(e){o(this,t),this.resolveOptions(e),this.initSelection()}return a(t,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action=t.action,this.container=t.container,this.emitter=t.emitter,this.target=t.target,this.text=t.text,this.trigger=t.trigger,this.selectedText=""}},{key:"initSelection",value:function(){this.text?this.selectFake():this.target&&this.selectTarget()}},{key:"selectFake",value:function(){var t=this,e="rtl"==document.documentElement.getAttribute("dir");this.removeFake(),this.fakeHandlerCallback=function(){return t.removeFake()},this.fakeHandler=this.container.addEventListener("click",this.fakeHandlerCallback)||!0,this.fakeElem=document.createElement("textarea"),this.fakeElem.style.fontSize="12pt",this.fakeElem.style.border="0",this.fakeElem.style.padding="0",this.fakeElem.style.margin="0",this.fakeElem.style.position="absolute",this.fakeElem.style[e?"right":"left"]="-9999px";var s=window.pageYOffset||document.documentElement.scrollTop;this.fakeElem.style.top=s+"px",this.fakeElem.setAttribute("readonly",""),this.fakeElem.value=this.text,this.container.appendChild(this.fakeElem),this.selectedText=i()(this.fakeElem),this.copyText()}},{key:"removeFake",value:function(){this.fakeHandler&&(this.container.removeEventListener("click",this.fakeHandlerCallback),this.fakeHandler=null,this.fakeHandlerCallback=null),this.fakeElem&&(this.container.removeChild(this.fakeElem),this.fakeElem=null)}},{key:"selectTarget",value:function(){this.selectedText=i()(this.target),this.copyText()}},{key:"copyText",value:function(){var t=void 0;try{t=document.execCommand(this.action)}catch(e){t=!1}this.handleResult(t)}},{key:"handleResult",value:function(t){this.emitter.emit(t?"success":"error",{action:this.action,text:this.selectedText,trigger:this.trigger,clearSelection:this.clearSelection.bind(this)})}},{key:"clearSelection",value:function(){this.trigger&&this.trigger.focus(),document.activeElement.blur(),window.getSelection().removeAllRanges()}},{key:"destroy",value:function(){this.removeFake()}},{key:"action",set:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"copy";if(this._action=t,"copy"!==this._action&&"cut"!==this._action)throw new Error('Invalid "action" value, use either "copy" or "cut"')},get:function(){return this._action}},{key:"target",set:function(t){if(void 0!==t){if(!t||"object"!==("undefined"===typeof t?"undefined":n(t))||1!==t.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===this.action&&t.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===this.action&&(t.hasAttribute("readonly")||t.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes');this._target=t}},get:function(){return this._target}}]),t}(),h=c,l=s(1),p=s.n(l),u=s(2),d=s.n(u),f="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m=function(){function t(t,e){for(var s=0;s<e.length;s++){var r=e[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,s,r){return s&&t(e.prototype,s),r&&t(e,r),e}}();function y(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function g(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function x(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var b=function(t){function e(t,s){y(this,e);var r=g(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return r.resolveOptions(s),r.listenClick(t),r}return x(e,t),m(e,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof t.action?t.action:this.defaultAction,this.target="function"===typeof t.target?t.target:this.defaultTarget,this.text="function"===typeof t.text?t.text:this.defaultText,this.container="object"===f(t.container)?t.container:document.body}},{key:"listenClick",value:function(t){var e=this;this.listener=d()(t,"click",(function(t){return e.onClick(t)}))}},{key:"onClick",value:function(t){var e=t.delegateTarget||t.currentTarget;this.clipboardAction&&(this.clipboardAction=null),this.clipboardAction=new h({action:this.action(e),target:this.target(e),text:this.text(e),container:this.container,trigger:e,emitter:this})}},{key:"defaultAction",value:function(t){return v("action",t)}},{key:"defaultTarget",value:function(t){var e=v("target",t);if(e)return document.querySelector(e)}},{key:"defaultText",value:function(t){return v("text",t)}},{key:"destroy",value:function(){this.listener.destroy(),this.clipboardAction&&(this.clipboardAction.destroy(),this.clipboardAction=null)}}],[{key:"isSupported",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],e="string"===typeof t?[t]:t,s=!!document.queryCommandSupported;return e.forEach((function(t){s=s&&!!document.queryCommandSupported(t)})),s}}]),e}(p.a);function v(t,e){var s="data-clipboard-"+t;if(e.hasAttribute(s))return e.getAttribute(s)}e["default"]=b}])["default"]}))},f4dd:function(t,e,s){var r=s("91fe"),i=s("7a23"),n=s("e628"),a=s("8c47"),o=s("4aef"),c=s("01d7");r({target:"Object",stat:!0,sham:!i},{getOwnPropertyDescriptors:function(t){var e,s,r=a(t),i=o.f,h=n(r),l={},p=0;while(h.length>p)s=i(r,e=h[p++]),void 0!==s&&c(l,e,s);return l}})},f69c:function(t,e,s){var r=s("f28d"),i=s("e628"),n=s("4aef"),a=s("c223");t.exports=function(t,e){for(var s=i(e),o=a.f,c=n.f,h=0;h<s.length;h++){var l=s[h];r(t,l)||o(t,l,c(e,l))}}},f880:function(t,e,s){var r=s("e17a"),i=s("c85c");(t.exports=function(t,e){return i[t]||(i[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.6.4",mode:r?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},fb11:function(t,e,s){"use strict";var r=s("f30e");t.exports=function(t,e){var s=[][t];return!!s&&r((function(){s.call(null,e||function(){throw 1},1)}))}},fb8e:function(t,e,s){var r=s("ac83"),i=s("a9f2"),n=s("57c4"),a=n("species");t.exports=function(t,e){var s,n=r(t).constructor;return void 0===n||void 0==(s=r(n)[a])?e:i(s)}},fc88:function(t,e,s){"use strict";var r=s("91fe"),i=s("7a23"),n=s("d5dc"),a=s("f28d"),o=s("d68d"),c=s("c223").f,h=s("f69c"),l=n.Symbol;if(i&&"function"==typeof l&&(!("description"in l.prototype)||void 0!==l().description)){var p={},u=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),e=this instanceof u?new l(t):void 0===t?l():l(t);return""===t&&(p[e]=!0),e};h(u,l);var d=u.prototype=l.prototype;d.constructor=u;var f=d.toString,m="Symbol(test)"==String(l("test")),y=/^Symbol\((.*)\)[^)]+$/;c(d,"description",{configurable:!0,get:function(){var t=o(this)?this.valueOf():this,e=f.call(t);if(a(p,t))return"";var s=m?e.slice(7,-1):e.replace(y,"$1");return""===s?void 0:s}}),r({global:!0,forced:!0},{Symbol:u})}},fee7:function(t,e,s){var r=s("f30e"),i=s("67ea"),n="".split;t.exports=r((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==i(t)?n.call(t,""):Object(t)}:Object}}]);