{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\Profile.vue?vue&type=style&index=0&id=44d9130e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\Profile.vue", "mtime": 1757558749942}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757485139209}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757485152120}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757485142383}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757485135872}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Profile.vue"], "names": [], "mappings": ";AAqVA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Profile.vue", "sourceRoot": "src/views/profile", "sourcesContent": ["<template>\n  <div class=\"profile-page\">\n    <AppHeader />\n    \n    <div class=\"container\">\n      <div class=\"profile-layout\">\n        <!-- 侧边栏 -->\n        <div class=\"sidebar\">\n          <div class=\"user-card\">\n            <div class=\"avatar\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"user-info\">\n              <h3>{{ userInfo?.name || '用户' }}</h3>\n              <p>{{ userInfo?.mobile }}</p>\n              <StatusTag :status=\"userInfo?.auditStatus\" type=\"audit\" />\n            </div>\n          </div>\n          \n          <nav class=\"nav-menu\">\n            <p @click=\"handleNavItemClick('/profile')\" class=\"nav-item router-link-active\">\n              <i class=\"el-icon-user\"></i>\n              个人资料\n            </p>\n            <p @click=\"handleNavItemClick('/profile/bids')\" class=\"nav-item\">\n              <i class=\"el-icon-price-tag\"></i>\n              我的出价\n            </p>\n            <p @click=\"handleNavItemClick('/profile/projects')\" class=\"nav-item\">\n              <i class=\"el-icon-folder\"></i>\n              我的项目\n            </p>\n          </nav>\n        </div>\n\n        <!-- 主内容 -->\n        <div class=\"main-content\">\n          <div class=\"content-header\">\n            <h2>个人资料</h2>\n          </div>\n\n          <div class=\"profile-content\">\n            <!-- 统计卡片 -->\n            <div class=\"stats-cards\">\n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.totalBids || 0 }}</div>\n                  <div class=\"stat-label\">总出价次数</div>\n                </div>\n              </div>\n              \n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.wonBids || 0 }}</div>\n                  <div class=\"stat-label\">中标次数</div>\n                </div>\n              </div>\n              \n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.participatedProjects || 0 }}</div>\n                  <div class=\"stat-label\">参与项目</div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 个人信息表单 -->\n            <div class=\"profile-form-section\">\n              <h3>基本信息</h3>\n              <el-form\n                ref=\"profileForm\"\n                :model=\"profileForm\"\n                :rules=\"profileRules\"\n                label-width=\"120px\"\n                class=\"profile-form\"\n              >\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"手机号\">\n                      <el-input v-model=\"profileForm.mobile\" disabled />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"姓名\" prop=\"name\">\n                      <el-input v-model=\"profileForm.name\" />\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"企业名称\" prop=\"companyName\">\n                      <el-input v-model=\"profileForm.companyName\" />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"联系人\" prop=\"contactPerson\">\n                      <el-input v-model=\"profileForm.contactPerson\" />\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n\n                <el-form-item label=\"企业地址\" prop=\"companyAddress\">\n                  <el-input\n                    v-model=\"profileForm.companyAddress\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"联系电话\" prop=\"contactPhone\">\n                  <el-input v-model=\"profileForm.contactPhone\" />\n                </el-form-item>\n\n                <el-form-item>\n                  <el-button type=\"primary\" @click=\"updateProfile\" :loading=\"updating\">\n                    保存修改\n                  </el-button>\n                  <el-button @click=\"resetForm\">重置</el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n\n            <!-- 密码修改 -->\n            <div class=\"password-section\">\n              <h3>修改密码</h3>\n              <el-form\n                ref=\"passwordForm\"\n                :model=\"passwordForm\"\n                :rules=\"passwordRules\"\n                label-width=\"120px\"\n                class=\"password-form\"\n              >\n                <el-form-item label=\"当前密码\" prop=\"oldPassword\">\n                  <el-input\n                    v-model=\"passwordForm.oldPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"新密码\" prop=\"newPassword\">\n                  <el-input\n                    v-model=\"passwordForm.newPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n                  <el-input\n                    v-model=\"passwordForm.confirmPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item>\n                  <el-button type=\"primary\" @click=\"changePassword\" :loading=\"changingPassword\">\n                    修改密码\n                  </el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { getMyStats } from '@/api/user'\nimport { changePassword } from '@/api/auth'\n\nexport default {\n  name: 'Profile',\n  data() {\n    // 确认密码验证\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.passwordForm.newPassword) {\n        callback(new Error('两次输入的密码不一致'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      stats: {},\n      profileForm: {\n        mobile: '',\n        name: '',\n        companyName: '',\n        companyAddress: '',\n        contactPerson: '',\n        contactPhone: ''\n      },\n      profileRules: {\n        name: [\n          { required: true, message: '请输入姓名', trigger: 'blur' }\n        ],\n        companyName: [\n          { required: true, message: '请输入企业名称', trigger: 'blur' }\n        ],\n        companyAddress: [\n          { required: true, message: '请输入企业地址', trigger: 'blur' }\n        ],\n        contactPerson: [\n          { required: true, message: '请输入联系人', trigger: 'blur' }\n        ],\n        contactPhone: [\n          { required: true, message: '请输入联系电话', trigger: 'blur' }\n        ]\n      },\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      passwordRules: {\n        oldPassword: [\n          { required: true, message: '请输入当前密码', trigger: 'blur' }\n        ],\n        newPassword: [\n          { required: true, message: '请输入新密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认新密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      updating: false,\n      changingPassword: false\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['userInfo'])\n  },\n  async mounted() {\n    await this.fetchStats()\n    this.initProfileForm()\n  },\n  methods: {\n    // 获取统计数据\n    async fetchStats() {\n      try {\n        const response = await getMyStats()\n        if (response.data.code === 200) {\n          this.stats = response.data.data\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error)\n      }\n    },\n\n    // 初始化个人信息表单\n    initProfileForm() {\n      if (this.userInfo) {\n        this.profileForm = {\n          mobile: this.userInfo.mobile || '',\n          name: this.userInfo.name || '',\n          companyName: this.userInfo.companyName || '',\n          companyAddress: this.userInfo.companyAddress || '',\n          contactPerson: this.userInfo.contactPerson || '',\n          contactPhone: this.userInfo.contactPhone || ''\n        }\n      }\n    },\n\n    // 更新个人资料\n    async updateProfile() {\n      try {\n        await this.$refs.profileForm.validate()\n      } catch (error) {\n        return\n      }\n\n      this.updating = true\n      try {\n        const result = await this.$store.dispatch('user/updateProfile', this.profileForm)\n        if (result.success) {\n          this.$message.success('个人资料更新成功')\n          // 更新用户信息\n          await this.$store.dispatch('auth/getUserInfo')\n        } else {\n          this.$message.error(result.message)\n        }\n      } catch (error) {\n        this.$message.error('更新失败，请稍后重试')\n      } finally {\n        this.updating = false\n      }\n    },\n\n    // 重置表单\n    resetForm() {\n      this.initProfileForm()\n    },\n\n    // 修改密码\n    async changePassword() {\n      try {\n        await this.$refs.passwordForm.validate()\n      } catch (error) {\n        return\n      }\n\n      this.changingPassword = true\n      try {\n        const response = await changePassword({\n          oldPassword: this.passwordForm.oldPassword,\n          newPassword: this.passwordForm.newPassword\n        })\n        \n        if (response.data.code === 200) {\n          this.$message.success('密码修改成功')\n          this.passwordForm = {\n            oldPassword: '',\n            newPassword: '',\n            confirmPassword: ''\n          }\n          this.$refs.passwordForm.resetFields()\n        } else {\n          this.$message.error(response.data.msg || '密码修改失败')\n        }\n      } catch (error) {\n        this.$message.error('密码修改失败，请稍后重试')\n      } finally {\n        this.changingPassword = false\n      }\n    },\n    handleNavItemClick(path) {\n      this.$router.push(path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-page {\n  min-height: 110vh;\n  background: $bg-color;\n}\n\n.container {\n  padding: 20px;\n}\n\n.profile-layout {\n  display: flex;\n  gap: 30px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.sidebar {\n  width: 280px;\n  flex-shrink: 0;\n\n  .user-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    padding: 30px 20px;\n    text-align: center;\n    margin-bottom: 20px;\n\n    .avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background: $primary-color;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      margin: 0 auto 20px;\n    }\n\n    .user-info {\n      h3 {\n        color: $text-primary;\n        margin-bottom: 10px;\n      }\n\n      p {\n        color: $text-secondary;\n        margin-bottom: 15px;\n      }\n    }\n  }\n\n  .nav-menu {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 15px 20px;\n      color: $text-regular;\n      text-decoration: none;\n      border-bottom: 1px solid $border-color;\n      transition: all 0.3s;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover,\n      &.router-link-active {\n        background: $primary-color;\n        color: white;\n      }\n\n      i {\n        margin-right: 10px;\n        font-size: 16px;\n      }\n    }\n  }\n}\n\n.main-content {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: $box-shadow;\n  overflow: hidden;\n\n  .content-header {\n    padding: 30px 30px 0;\n    border-bottom: 1px solid $border-color;\n\n    h2 {\n      color: $text-primary;\n      margin-bottom: 30px;\n    }\n  }\n\n  .profile-content {\n    padding: 30px;\n\n    .stats-cards {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 40px;\n\n      .stat-card {\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        padding: 25px;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        gap: 15px;\n\n        .stat-icon {\n          font-size: 32px;\n          opacity: 0.8;\n        }\n\n        .stat-info {\n          .stat-number {\n            font-size: 24px;\n            font-weight: 700;\n            margin-bottom: 5px;\n          }\n\n          .stat-label {\n            font-size: 14px;\n            opacity: 0.9;\n          }\n        }\n      }\n    }\n\n    .profile-form-section,\n    .password-section {\n      margin-bottom: 40px;\n\n      h3 {\n        color: $text-primary;\n        margin-bottom: 20px;\n        padding-bottom: 10px;\n        border-bottom: 2px solid $primary-color;\n        display: inline-block;\n      }\n    }\n\n    .profile-form,\n    .password-form {\n      max-width: 600px;\n    }\n  }\n}\n\n@media (max-width: $tablet) {\n  .profile-layout {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n\n    .user-card {\n      display: flex;\n      align-items: center;\n      text-align: left;\n      gap: 20px;\n\n      .avatar {\n        margin: 0;\n      }\n    }\n\n    .nav-menu {\n      display: flex;\n      overflow-x: auto;\n\n      .nav-item {\n        white-space: nowrap;\n        border-bottom: none;\n        border-right: 1px solid $border-color;\n\n        &:last-child {\n          border-right: none;\n        }\n      }\n    }\n  }\n}\n\n@media (max-width: $mobile) {\n  .container {\n    padding: 15px;\n  }\n\n  .profile-content {\n    padding: 20px !important;\n\n    .stats-cards {\n      grid-template-columns: 1fr;\n    }\n  }\n}\n</style>\n"]}]}