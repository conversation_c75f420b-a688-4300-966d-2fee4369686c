/*
 * @Descripttion:工具包
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 18:11:16
 * @LastEditors: dlg
 * @LastEditTime: 2020-08-24 15:28:53
 */

// 适应不同屏幕下大小
export const nowSize = (val, initWidth = 1920) => {
    let nowWidth = window.screen.width;
    return val * (nowWidth / initWidth);
}

//深拷贝
export const deepClone = (obj) => {
    //参数安全检查
    if (!obj && typeof obj !== 'object') {
        throw new Error('error arguments', 'deepClone')
    }
    //具体类型区分克隆
    let objClone = Array.isArray(obj) ? [] : {};
    if (obj && typeof obj === "object") {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) { // 确定某属性是否是对象本身的属性,排除原型中继承的属性
                //判断ojb子元素是否为对象，如果是，递归复制
                if (obj[key] && typeof obj[key] === "object") {
                    objClone[key] = deepClone(obj[key]);
                } else {
                    //如果不是，简单复制
                    objClone[key] = obj[key];
                }
            }
        }
    }
    return objClone;
}

//表格导出处理函数
export const exportExcel = (data, fileName = "导出表格.xlsx") => {
    let blobObj = new Blob([data], { type: "application/vnd.ms-excel" });
    // for IE
    if (window.navigator && window.navigator.msSaveBlob) {
        window.navigator.msSaveBlob(blobObj, fileName);
    } else {
        let blobUrl = window.URL.createObjectURL(blobObj);
        const link = document.createElement("a"); // 创建a标签
        link.href = blobUrl;
        link.download = fileName;
        link.click(); // 模拟点击a标签
        window.URL.revokeObjectURL(link.href);
    }
}
