<template>
  <span class="countdown-timer" :class="{ 'urgent': isUrgent }">
    {{ formattedTime }}
  </span>
</template>

<script>
export default {
  name: 'CountdownTimer',
  props: {
    endTime: {
      type: [String, Date],
      required: true
    },
    urgentThreshold: {
      type: Number,
      default: 300 // 5分钟
    }
  },
  data() {
    return {
      remainingSeconds: 0,
      timer: null
    }
  },
  computed: {
    formattedTime() {
      if (this.remainingSeconds <= 0) {
        return '已结束'
      }
      
      const hours = Math.floor(this.remainingSeconds / 3600)
      const minutes = Math.floor((this.remainingSeconds % 3600) / 60)
      const seconds = Math.floor(this.remainingSeconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      } else {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
      }
    },
    
    isUrgent() {
      return this.remainingSeconds > 0 && this.remainingSeconds <= this.urgentThreshold
    }
  },
  mounted() {
    this.startTimer()
  },
  beforeD<PERSON>roy() {
    this.clearTimer()
  },
  watch: {
    endTime() {
      this.startTimer()
    }
  },
  methods: {
    startTimer() {
      this.clearTimer()
      this.updateRemainingTime()
      
      this.timer = setInterval(() => {
        this.updateRemainingTime()
        
        if (this.remainingSeconds <= 0) {
          this.clearTimer()
          this.$emit('finished')
        }
      }, 1000)
    },
    
    updateRemainingTime() {
      const now = new Date().getTime()
      const endTime = new Date(this.endTime).getTime()
      this.remainingSeconds = Math.max(0, Math.floor((endTime - now) / 1000))
    },
    
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.countdown-timer {
  font-weight: 600;
  color: $text-primary;
  
  &.urgent {
    color: $danger-color;
    animation: blink 1s infinite;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.5;
  }
}
</style>
