package v1

import (
	"auction-sys/constants"
	"auction-sys/global/response"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/service"
	"auction-sys/utils"
	"encoding/json"
	"fmt"

	"github.com/gin-gonic/gin"
)

// @Tags authorityAndMenu ：权限菜单
// @Summary 获取用户动态路由
// @Security ApiKeyAuth
// @Produce  application/json
// @Success 200 {string} string "{"code":200,"data":{RouterMenuResponse},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取失败"}"
// @Router /menu/getMenu [post]
func GetMenu(c *gin.Context) {
	claims, _ := c.Get("claims")
	waitUse := claims.(*req.CustomClaims)
	err, menus := service.GetMenuTree(int(waitUse.ID))
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取失败，%v", err), c)
	} else {
		response.OkWithData(resp.RouterMenuResponse{Menus: menus}, c)
	}
}

// @Tags menu ：菜单管理
// @Summary 分页获取基础menu列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.PageInfo true "分页获取基础menu列表"
// @Success 200 {string} string "{"code":200,"data":{PageResult},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取失败"}"
// @Router /menu/getMenuList [post]
func GetMenuList(c *gin.Context) {

	err, menuList := service.GetInfoList()
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取数据失败，%v", err), c)
	} else {
		response.OkWithData(resp.PageResult{
			List: menuList,
		}, c)
	}
}

// @Tags menu ：菜单管理
// @Summary 新增菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysBaseMenu true "新增菜单"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"添加成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"添加失败"}"
// @Router /menu/addBaseMenu [post]
func AddBaseMenu(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var menu model.SysBaseMenu
	_ = c.ShouldBindJSON(&menu)
	if menu.Category == constants.CATEGORY_MENU {
		MenuVerify := utils.Rules{
			"Path": {utils.NotEmpty()},
			"Name": {utils.NotEmpty()},
			"Sort": {utils.Ge("0"), "ge=0"},
		}
		MenuVerifyErr := utils.Verify(menu, MenuVerify)
		if MenuVerifyErr != nil {
			response.FailWithMessage(MenuVerifyErr.Error(), c)
			return
		}
	} else {
		MenuVerify := utils.Rules{
			"Keyval":   {utils.NotEmpty()},
			"ParentId": {utils.NotEmpty()},
		}
		MenuVerifyErr := utils.Verify(menu, MenuVerify)
		if MenuVerifyErr != nil {
			response.FailWithMessage(MenuVerifyErr.Error(), c)
			return
		}
	}
	MetaVerify := utils.Rules{
		"Title": {utils.NotEmpty()},
	}
	MetaVerifyErr := utils.Verify(menu.Meta, MetaVerify)
	if MetaVerifyErr != nil {
		response.FailWithMessage(MetaVerifyErr.Error(), c)
		return
	}
	err := service.AddBaseMenu(menu)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(menu)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "菜单管理-添加菜单", c.ClientIP(), string(requestParam), "添加失败", response.ERROR, agent)
		response.FailWithMessage(fmt.Sprintf("添加失败，%v", err), c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "菜单管理-添加菜单", c.ClientIP(), string(requestParam), "添加成功", response.SUCCESS, agent)
		response.OkWithMessage("添加成功", c)
	}
}

// @Tags menu ：菜单管理
// @Summary 删除菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "删除菜单"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"删除成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"删除失败"}"
// @Router /menu/deleteBaseMenu [post]
func DeleteBaseMenu(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var idInfo req.GetById
	_ = c.ShouldBindJSON(&idInfo)
	IdVerifyErr := utils.Verify(idInfo, utils.CustomizeMap["IdVerify"])
	if IdVerifyErr != nil {
		response.FailWithMessage(IdVerifyErr.Error(), c)
		return
	}
	err := service.DeleteBaseMenu(idInfo.Id)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(idInfo)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "菜单管理-删除菜单", c.ClientIP(), string(requestParam), "删除失败", response.ERROR, agent)
		response.FailWithMessage(err.Error(), c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "菜单管理-删除菜单", c.ClientIP(), string(requestParam), "删除成功", response.SUCCESS, agent)
		response.OkWithMessage("删除成功", c)

	}
}

// @Tags menu ：菜单管理
// @Summary 更新菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysBaseMenu true "更新菜单"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /menu/updateBaseMenu [post]
func UpdateBaseMenu(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var menu model.SysBaseMenu
	_ = c.ShouldBindJSON(&menu)
	if menu.Category == constants.CATEGORY_MENU {
		MenuVerify := utils.Rules{
			"Path": {"notEmpty"},
			"Name": {utils.NotEmpty()},
			"Sort": {utils.Ge("0"), "ge=0"},
		}
		MenuVerifyErr := utils.Verify(menu, MenuVerify)
		if MenuVerifyErr != nil {
			response.FailWithMessage(MenuVerifyErr.Error(), c)
			return
		}
	} else {
		MenuVerify := utils.Rules{
			"Keyval":   {utils.NotEmpty()},
			"ParentId": {utils.NotEmpty()},
		}
		MenuVerifyErr := utils.Verify(menu, MenuVerify)
		if MenuVerifyErr != nil {
			response.FailWithMessage(MenuVerifyErr.Error(), c)
			return
		}
	}
	MetaVerify := utils.Rules{
		"Title": {utils.NotEmpty()},
	}
	MetaVerifyErr := utils.Verify(menu.Meta, MetaVerify)
	if MetaVerifyErr != nil {
		response.FailWithMessage(MetaVerifyErr.Error(), c)
		return
	}
	err := service.UpdateBaseMenu(menu)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(menu)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "菜单管理-编辑菜单", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage("更新失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "菜单管理-编辑菜单", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags menu ：菜单管理
// @Summary 根据id获取菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "根据id获取菜单"
// @Success 200 {string} string "{"code":200,"data":{SysBaseMenuResponse},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"查询失败"}"
// @Router /menu/getBaseMenuById [post]
func GetBaseMenuById(c *gin.Context) {
	var idInfo req.GetById
	_ = c.ShouldBindJSON(&idInfo)
	MenuVerify := utils.Rules{
		"Id": {"notEmpty"},
	}
	MenuVerifyErr := utils.Verify(idInfo, MenuVerify)
	if MenuVerifyErr != nil {
		response.FailWithMessage(MenuVerifyErr.Error(), c)
		return
	}
	err, menu := service.GetBaseMenuById(idInfo.Id)
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("查询失败：%v", err), c)
	} else {
		response.OkWithData(resp.SysBaseMenuResponse{Menu: menu}, c)
	}
}

// @Tags menu ：菜单管理
// @Summary 根据角色id获取菜单,并赋值状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "根据角色id获取菜单"
// @Success 200 {string} string "{"code":200,"data":{SysAuthorityMenuResponse},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"查询失败"}"
// @Router /menu/getMenuAuthorityInit [post]
func GetMenuAuthorityInit(c *gin.Context) {
	var idInfo req.GetById
	_ = c.ShouldBindJSON(&idInfo)

	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	err, menu := service.GetMenuAuthorityInit(idInfo.Id, claims.UUID.String())
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("查询失败:%v", err), c)
	} else {
		response.OkWithData(resp.SysAuthorityMenuResponse{Role: menu}, c)
	}
}

// @Tags menu ：菜单管理
// @Summary 根据角色id获取菜单，可多个角色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []request.GetById true "根据角色id获取菜单，可多个角色"
// @Success 200 {string} string "{"code":200,"data":{[]response.Menu},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"加载失败"}"
// @Router /menu/getAuthMenus [post]
func GetAuthMenus(c *gin.Context) {
	var reqIds []req.GetById
	_ = c.ShouldBindJSON(&reqIds)
	for _, reqId := range reqIds {
		IdVerifyErr := utils.Verify(reqId, utils.CustomizeMap["IdVerify"])
		if IdVerifyErr != nil {
			response.FailWithMessage(IdVerifyErr.Error(), c)
			return
		}
	}
	err, menus := service.GetAuthMenus(reqIds)
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("加载失败，%v", err), c)
	} else {
		response.OkWithData(menus, c)
	}
}
