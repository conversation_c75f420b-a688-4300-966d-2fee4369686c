package router

import (
	v1 "auction-sys/api/v1"
	"auction-sys/middleware"

	"github.com/gin-gonic/gin"
)

func InitProductCategoryRouter(Router *gin.RouterGroup) {
	ProductCategoryRouter := Router.Group("productCategory").Use(middleware.JWTAuth())
	{
		ProductCategoryRouter.POST("addProductCategory", v1.AddProductCategory)                   // 添加商品类别
		ProductCategoryRouter.POST("deleteProductCategory", v1.DeleteProductCategory)             // 删除商品类别
		ProductCategoryRouter.POST("getProductCategoryList", v1.GetProductCategoryList)           // 分页获取商品类别
		ProductCategoryRouter.POST("updateProductCategoryStatus", v1.UpdateProductCategoryStatus) // 更新商品类别状态
		ProductCategoryRouter.POST("getProductCategory", v1.GetProductCategory)                   // 根据id查询商品类别
		ProductCategoryRouter.POST("getProductCategoryCondition", v1.GetProductCategoryCondition) // 商品类别下拉选项
	}

	ProductMemberRouter := Router.Group("productCategoryMember")
	{
		ProductMemberRouter.POST("getProductCategoryCondition", v1.GetProductCategoryCondition) // 商品类别下拉选项
	}

}
