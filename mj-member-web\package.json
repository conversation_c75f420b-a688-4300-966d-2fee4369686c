{"name": "mj-member-web", "version": "1.0.0", "description": "棉副产品竞价平台 - 客户端", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2", "axios": "^0.27.2", "element-ui": "^2.15.9", "moment": "^2.29.4", "socket.io-client": "^4.7.2", "js-cookie": "^3.0.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}