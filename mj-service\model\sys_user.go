package model

import (
	"github.com/Gre-Z/common/jtime"
	"github.com/satori/go.uuid"
)

// 用户对象
type SysUser struct {
	Model
	UUID           uuid.UUID      `json:"uuid" gorm:"comment:'用户UUID'"`
	Username       string         `json:"userName" gorm:"comment:'用户登录名'"`
	Password       string         `json:"-"  gorm:"comment:'用户登录密码'"`
	NickName       string         `json:"nickName" gorm:"default:'系统用户';comment:'用户昵称'" `
	HeaderImg      string         `json:"headerImg" gorm:"comment:'用户头像'"`
	SysAuthoritys  []SysAuthority `json:"authoritys" gorm:"many2many:sys_authority_users"`
	CompanyCode    string         `json:"companyCode" gorm:"comment:'公司标识'"`
	PasswordStatus int8           `json:"passwordStatus" gorm:"comment:'密码状态'"`
	Status         int8           `json:"status" gorm:"comment:'用户状态'"`
	Email          string         `json:"email" gorm:"comment:'邮箱'"`
	Mobile         string         `json:"mobile"`
	DeptName       string         `json:"deptName"`
	Desc           string         `json:"desc"`
	Gender         int8           `json:"gender" gorm:"comment:'性别'"`
	Age            int            `json:"age" gorm:"comment:'年龄'"`
	LoginStatus    int8           `json:"loginStatus" gorm:"comment:'登录状态'"`
	LoginTime      jtime.JsonTime `json:"loginTime" gorm:"comment:'登录时间'"`
	OpenId         string         `json:"openId"`
	QQ             string         `json:"qq"`
	Address        string         `json:"address"`
	IsFarmer       int            `json:"isFarmer"`
	IdCard         string         `json:"idCard"`
	ShiNum         int            `json:"shiNum"` // 师
	TuanNum        int            `json:"tuanNum"` // 团
	LianNum        int            `json:"lianNum"` // 连
	CreateBy       int            `json:"createBy"`
	IsRoot         bool           `json:"-" gorm:"-"`
	Params         string         `json:"params" gorm:"comment:'权限参数';default:'0000000000' "`
}

func (SysUser) TableName() string {
	return "sys_users"
}

//我的烤房微信报警
func (u *SysUser) OwnerWxAlarm() bool {
	if u.Params == "" {
		return false
	}
	return u.Params[0] == '1'
}

//我的烤房设备修改
func (u *SysUser) OwnerEdit() bool {
	if len(u.Params) < 2 {
		return false
	}
	return u.Params[1] == '1'
}

//我关注的烤房微信报警
func (u *SysUser) ShareWxAlarm() bool {
	if len(u.Params) < 3 {
		return false
	}
	return u.Params[2] == '1'
}

//我关注的烤房设备修改
func (u *SysUser) ShareEdit() bool {
	if len(u.Params) < 4 {
		return false
	}
	return u.Params[3] == '1'
}

//我的烤房下发设置数据
func (u *SysUser) OwnerSet() bool {
	if len(u.Params) < 5 {
		return false
	}
	return u.Params[4] == '1'
}

//我的烤房设备分配
func (u *SysUser) OwnerShare() bool {
	if len(u.Params) < 6 {
		return false
	}
	return u.Params[5] == '1'
}

//我关注的烤房下发设置数据
func (u *SysUser) ShareSet() bool {
	if len(u.Params) < 7 {
		return false
	}
	return u.Params[6] == '1'
}

//我关注的烤房设备分配
func (u *SysUser) ShareShare() bool {
	if len(u.Params) < 8 {
		return false
	}
	return u.Params[7] == '1'
}

//我的烤房生成报表
func (u *SysUser) OwnerReport() bool {
	if len(u.Params) < 9 {
		return false
	}
	return u.Params[8] == '1'
}

//我关注的烤房生成报表
func (u *SysUser) ShareReport() bool {
	if len(u.Params) < 10 {
		return false
	}
	return u.Params[9] == '1'
}
