import request from './request'

// 用户登录
export function login(data) {
  return request({
    url: '/auction/member/login',
    method: 'post',
    data
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/auction/member/register',
    method: 'post',
    data
  })
}

// 发送短信验证码
export function sendSmsCode(mobile) {
  return request({
    url: '/auction/member/sms',
    method: 'post',
    data: { mobile }
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/member/info',
    method: 'get'
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/member/logout',
    method: 'post'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/member/change-password',
    method: 'post',
    data
  })
}
