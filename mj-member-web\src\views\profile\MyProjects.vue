<template>
  <div class="my-projects-page">
    <AppHeader />
    
    <div class="container">
      <div class="profile-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
          <div class="user-card">
            <div class="avatar">
              <i class="el-icon-user"></i>
            </div>
            <div class="user-info">
              <h3>{{ userInfo?.name || '用户' }}</h3>
              <p>{{ userInfo?.mobile }}</p>
              <StatusTag :status="userInfo?.auditStatus" type="audit" />
            </div>
          </div>
          
          <nav class="nav-menu">
            <p @click="handleNavItemClick('/profile')" class="nav-item" >
              <i class="el-icon-user"></i>
              个人资料
            </p>
            <p @click="handleNavItemClick('/profile/bids')" class="nav-item">
              <i class="el-icon-price-tag"></i>
              我的出价
            </p>
            <p @click="handleNavItemClick('/profile/projects')" class="nav-item router-link-active">
              <i class="el-icon-folder"></i>
              我的项目
            </p>
          </nav>
        </div>

        <!-- 主内容 -->
        <div class="main-content">
          <div class="content-header">
            <h2>我的项目</h2>
            <div class="filters">
              <el-select v-model="filters.status" placeholder="项目状态" clearable @change="fetchProjects">
                <el-option label="即将开始" :value="0" />
                <el-option label="竞价中" :value="1" />
                <el-option label="已结束" :value="2" />
                <el-option label="已终止" :value="3" />
              </el-select>
              <el-select v-model="filters.participated" placeholder="参与状态" clearable @change="fetchProjects">
                <el-option label="已参与" :value="true" />
                <el-option label="未参与" :value="false" />
              </el-select>
            </div>
          </div>

          <div class="projects-content" v-loading="loading">
            <!-- 统计卡片 -->
            <div class="stats-summary">
              <div class="summary-item">
                <div class="summary-number">{{ summary.participatedProjects || 0 }}</div>
                <div class="summary-label">已参与项目</div>
              </div>
              <div class="summary-item">
                <div class="summary-number">{{ summary.activeProjects || 0 }}</div>
                <div class="summary-label">进行中项目</div>
              </div>
              <div class="summary-item">
                <div class="summary-number">{{ summary.wonProjects || 0 }}</div>
                <div class="summary-label">中标项目</div>
              </div>
            </div>

            <!-- 项目列表 -->
            <div class="projects-list">
              <div
                v-for="project in projects"
                :key="project.id"
                class="project-item"
                :class="{ 'participated': project.participated, 'won': project.isWon }"
              >
                <div class="project-header">
                  <h4 class="project-title" @click="goToProject(project.id)">
                    {{ project.title }}
                  </h4>
                  <div class="project-status">
                    <StatusTag :status="project.status" />
                    <div class="participation-badge" v-if="project.participated">
                      <i class="el-icon-check"></i>
                      已参与
                    </div>
                    <div class="won-badge" v-if="project.isWon">
                      <i class="el-icon-trophy"></i>
                      中标
                    </div>
                  </div>
                </div>

                <div class="project-meta">
                  <span class="category">{{ project.categoryName }}</span>
                  <span class="quantity">{{ project.quantity }}{{ project.unit }}</span>
                </div>

                <div class="project-info">
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="label">起拍价：</span>
                      <span class="value">¥{{ formatMoney(project.startPrice) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">当前价：</span>
                      <span class="value current-price">¥{{ formatMoney(project.currentPrice) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">出价次数：</span>
                      <span class="value">{{ project.bidCount }}次</span>
                    </div>
                    <div class="info-item" v-if="project.participated">
                      <span class="label">我的出价：</span>
                      <span class="value my-bid">¥{{ formatMoney(project.myBidAmount) }}</span>
                    </div>
                  </div>
                </div>

                <div class="project-time">
                  <template v-if="project.status === 0">
                    <span class="time-label">开始时间：</span>
                    <span class="time-value">{{ project.startTime | formatTime }}</span>
                  </template>
                  <template v-else-if="project.status === 1">
                    <span class="time-label">剩余时间：</span>
                    <CountdownTimer :end-time="project.endTime" />
                  </template>
                  <template v-else>
                    <span class="time-label">结束时间：</span>
                    <span class="time-value">{{ project.endTime | formatTime }}</span>
                  </template>
                </div>

                <div class="project-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="goToProject(project.id)"
                    v-if="project.status === 1"
                  >
                    {{ project.participated ? '继续出价' : '立即参与' }}
                  </el-button>
                  <el-button
                    size="small"
                    @click="goToProject(project.id)"
                  >
                    查看详情
                  </el-button>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="projects.length === 0 && !loading" class="empty-state">
                <el-empty description="暂无相关项目">
                  <el-button type="primary" @click="$router.push('/home')">
                    浏览项目
                  </el-button>
                </el-empty>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination" v-if="total > 0">
              <el-pagination
                @current-change="handlePageChange"
                :current-page="page"
                :page-size="pageSize"
                :total="total"
                layout="total, prev, pager, next, jumper"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'MyProjects',
  data() {
    return {
      projects: [],
      summary: {},
      filters: {
        status: '',
        participated: ''
      },
      loading: false,
      page: 1,
      pageSize: 10,
      total: 0
    }
  },
  computed: {
    ...mapGetters('auth', ['userInfo'])
  },
  mounted() {
    this.fetchProjects()
  },
  methods: {
    // 获取项目列表
    async fetchProjects() {
      this.loading = true
      try {
        const result = await this.$store.dispatch('user/fetchMyProjects', {
          page: this.page,
          pageSize: this.pageSize,
          ...this.filters
        })
        
        if (result.success) {
          this.projects = result.data.list || []
          this.total = result.data.total || 0
          this.summary = result.data.summary || {}
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 页码变化
    handlePageChange(newPage) {
      this.page = newPage
      this.fetchProjects()
    },

    // 跳转到项目详情
    goToProject(projectId) {
      this.$router.push(`/auction/${projectId}`)
    },

    // 格式化金额
    formatMoney(value) {
      if (!value && value !== 0) return '0'
      return Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    handleNavItemClick(path) {
      this.$router.push(path)
    }
  },
  watch: {
    filters: {
      handler() {
        this.page = 1
        this.fetchProjects()
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.my-projects-page {
  min-height: 110vh;
  background: $bg-color;
}

.container {
  padding: 20px;
}

.profile-layout {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.sidebar {
  width: 280px;
  flex-shrink: 0;

  .user-card {
    background: white;
    border-radius: 8px;
    box-shadow: $box-shadow;
    padding: 30px 20px;
    text-align: center;
    margin-bottom: 20px;

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: $primary-color;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      margin: 0 auto 20px;
    }

    .user-info {
      h3 {
        color: $text-primary;
        margin-bottom: 10px;
      }

      p {
        color: $text-secondary;
        margin-bottom: 15px;
      }
    }
  }

  .nav-menu {
    background: white;
    border-radius: 8px;
    box-shadow: $box-shadow;
    overflow: hidden;

    .nav-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      color: $text-regular;
      text-decoration: none;
      border-bottom: 1px solid $border-color;
      transition: all 0.3s;

      &:last-child {
        border-bottom: none;
      }

      &:hover,
      &.router-link-active {
        background: $primary-color;
        color: white;
      }

      i {
        margin-right: 10px;
        font-size: 16px;
      }
    }
  }
}

.main-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: $box-shadow;
  overflow: hidden;

  .content-header {
    padding: 30px 30px 0;
    border-bottom: 1px solid $border-color;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      color: $text-primary;
      margin-bottom: 30px;
    }

    .filters {
      display: flex;
      gap: 15px;
      margin-bottom: 30px;
    }
  }

  .projects-content {
    padding: 30px;

    .stats-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;

      .summary-item {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;

        .summary-number {
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 8px;
        }

        .summary-label {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }

    .projects-list {
      .project-item {
        background: $light-gray;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid transparent;
        transition: all 0.3s;

        &.participated {
          border-left-color: $primary-color;
          background: linear-gradient(90deg, #f0f9ff 0%, $light-gray 100%);
        }

        &.won {
          border-left-color: $success-color;
          background: linear-gradient(90deg, #f0f9f0 0%, $light-gray 100%);
        }

        .project-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 15px;

          .project-title {
            color: $text-primary;
            cursor: pointer;
            transition: color 0.3s;
            flex: 1;
            margin-right: 20px;

            &:hover {
              color: $primary-color;
            }
          }

          .project-status {
            display: flex;
            align-items: center;
            gap: 10px;

            .participation-badge,
            .won-badge {
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;
              display: flex;
              align-items: center;
              gap: 4px;
            }

            .participation-badge {
              background: $primary-color;
              color: white;
            }

            .won-badge {
              background: $success-color;
              color: white;
            }
          }
        }

        .project-meta {
          display: flex;
          align-items: center;
          gap: 15px;
          margin-bottom: 15px;

          .category,
          .quantity {
            padding: 4px 8px;
            background: white;
            border-radius: 4px;
            font-size: 12px;
            color: $text-secondary;
          }
        }

        .project-info {
          margin-bottom: 15px;

          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;

            .info-item {
              display: flex;
              justify-content: space-between;

              .label {
                color: $text-secondary;
              }

              .value {
                font-weight: 500;
                color: $text-primary;

                &.current-price {
                  color: $danger-color;
                }

                &.my-bid {
                  color: $primary-color;
                  font-weight: 600;
                }
              }
            }
          }
        }

        .project-time {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          padding-top: 15px;
          border-top: 1px solid #e0e0e0;

          .time-label {
            color: $text-secondary;
            font-size: 14px;
          }

          .time-value {
            font-weight: 600;
            color: $text-primary;
          }
        }

        .project-actions {
          text-align: right;
        }
      }

      .empty-state {
        padding: 60px 0;
        text-align: center;
      }
    }

    .pagination {
      margin-top: 30px;
      text-align: center;
    }
  }
}

@media (max-width: $tablet) {
  .profile-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;

    .user-card {
      display: flex;
      align-items: center;
      text-align: left;
      gap: 20px;

      .avatar {
        margin: 0;
      }
    }

    .nav-menu {
      display: flex;
      overflow-x: auto;

      .nav-item {
        white-space: nowrap;
        border-bottom: none;
        border-right: 1px solid $border-color;

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  .main-content .content-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: $mobile) {
  .container {
    padding: 15px;
  }

  .projects-content {
    padding: 20px !important;

    .stats-summary {
      grid-template-columns: repeat(2, 1fr);
    }

    .projects-list .project-item {
      .project-header {
        flex-direction: column;
        gap: 10px;
      }

      .project-info .info-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
