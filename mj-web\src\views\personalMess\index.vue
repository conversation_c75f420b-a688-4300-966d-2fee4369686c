<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-08-31 18:04:06
 * @LastEditors: dlg
 * @LastEditTime: 2020-09-01 18:41:55
-->
<template>
  <div class="app-container">
    <div class="personal-main">
      <div class="personal-center">
        <div class="avtarPhoto">
          <el-upload
            class="avatar-uploader"
            action="/admin/user/uploadHeaderImg"
            :headers="headerMsg"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="logoPreviewPath" :src="logoPreviewPath" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <p class="avatar-uploader-p">图片大小限制120px*120px</p>
          <p class="avatar-uploader-p">图片大小小于1MB</p>
          <p class="avatar-uploader-p">支持jpg,jpeg,png</p>
        </div>
        <el-form label-width="120px"
                 :model="personForm"
                 :rules="rules"
                 ref="personForm">
          <el-form-item label="企业名称">
            <div style="color:#333333;">{{ companyName }}</div>
          </el-form-item>
          <el-form-item label="组织代码">
            <div style="color:#333333;">{{ companyCode }}</div>
          </el-form-item>
          <el-form-item label="账户">
            <div style="color:#333333;">{{ username }}</div>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input v-model="personForm.name" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="personForm.email" placeholder="请输入邮箱地址" />
          </el-form-item>
          <el-form-item label="性别">
            <el-radio v-model="personForm.gender" :label="1">男</el-radio>
            <el-radio v-model="personForm.gender" :label="2">女</el-radio>
          </el-form-item>
          <el-form-item label="年龄" prop="age">
            <el-input v-model.number="personForm.age" placeholder="请输入年龄" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="confirm">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { getAllCategory, updatePersonInfo } from "@/api/personalMess";

export default {
  name: "PersonalMess",
  data() {
    var validateAge = (rule, value, callback) => {
      if (!Number.isInteger(value)) {
        callback(new Error("请输入数字值"));
      } else {
        if (value < 1 || value > 100) {
          callback(new Error("请输入正确的年龄"));
        } else {
          callback();
        }
      }
    };
    return {
      headerMsg: { Authorization: getToken() },
      logoPreviewPath: "",
      companyName: "",
      companyCode: "",
      username: "",
      personForm: {
        email: "",
        name: "",
        gender: 0,
        age: "",
      },
      rules: {
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: "blur",
          },
        ],
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        age: [{ validator: validateAge, trigger: "blur" }],
      },
    };
  },
  created() {
    this.userInfo = this.$store.getters.userInfo;
    this.logoPreviewPath = this.userInfo.headerImg;
    this.companyName = this.userInfo.companyName;
    this.companyCode = this.userInfo.companyCode;
    this.username = this.userInfo.userName;

    this.getAllCategory();
  },
  methods: {
    handleAvatarSuccess(res) {
      if (res.code == 200) {
        this.logoPreviewPath = res.data.imageUrl;
        this.userInfo.headerImg = this.logoPreviewPath;
        this.$store.dispatch("updateUserInfo", this.userInfo);
      } else {
        this.$message({
          type: "error",
          message: res.msg,
        });
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isPNG = file.type === "image/png";
      const isLt1M = file.size / 1024 / 1024 < 1;

      if (!isJPG && !isPNG) {
        this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
        return Promise.reject();
      }
      if (!isLt1M) {
        this.$message.error("上传头像图片大小不能超过 1MB!");
        return Promise.reject();
      }
      const isSize = new Promise(function (resolve, reject) {
        let width = 120;
        let height = 120;
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = function () {
          let valid = img.width > width || img.height > height;
          valid ? reject() : resolve();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          return file;
        },
        () => {
          this.$message.error("上传的图片宽高必须是小于或等于120*120!");
          return Promise.reject();
        }
      );
      return (isJPG || isPNG) && isLt1M && isSize;
    },
    async getAllCategory() {
      const res = await getAllCategory();
      if (res.data.code == 200) {
        this.personForm.email = res.data.data.email;
        this.personForm.name = res.data.data.nickName;
        this.personForm.gender = res.data.data.gender;
        this.personForm.age = res.data.data.age;
      }
    },
    updatePersonInfo() {
      this.$refs["personForm"].validate(async (valid) => {
        if (valid) {
          const data = {
            username: this.username,
            email: this.personForm.email,
            nickName: this.personForm.name,
            gender: this.personForm.gender,
            age: this.personForm.age,
          };
          const res = await updatePersonInfo(data);
          if (res.data.code == 200) {
            this.$message({
              message: res.data.msg,
              type: "success",
            });
            this.userInfo.nickName = this.personForm.name;
            this.$store.dispatch("updateUserInfo", this.userInfo);
          } else {
            this.$message({
              message: res.data.msg,
              type: "error",
            });
          }
        } else {
          return false;
        }
      });
    },
    confirm() {
      this.updatePersonInfo();
    },
  },
};
</script>

<style scoped>
.personal-main {
  flex: 1;
  display: flex;
  justify-content: center;
  overflow-y: auto;
}
.personal-center {
  width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avtarPhoto {
  text-align: center;
}
.avtarPhoto .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  margin-top: 12px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avtarPhoto .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avtarPhoto .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  border-radius: 60px;
  border: 1px solid #dddddd;
}
.avtarPhoto .avatar {
  width: 120px;
  height: 120px;
  border-radius: 60px;
  display: block;
}
.avatar-uploader-p {
  line-height: 1;
  color: #dddddd;
  font-size: 14px;
}
</style>

<style>
.personal-center .el-form-item__label {
  color: #aaaaaa;
  padding-right: 32px;
}
.personal-center .el-input__inner {
  color: #333333;
}
</style>