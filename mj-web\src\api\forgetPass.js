/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-10-09 17:15:09
 * @LastEditors: dlg
 * @LastEditTime: 2020-10-09 17:17:39
 */
import request from '@/utils/request'

/**
 * @description: 发送短信验证码
 * @param {type}
 * @return {type}
 */
export const sendMess = (data) => {
    return request({
        url: '/admin/base/send',
        method: 'post',
        data
    })
}

/**
 * @description: 检验验证码
 * @param {type}
 * @return {type}
 */
export const checkCaptcha = (data) => {
    return request({
        url: '/admin/base/checkCaptcha',
        method: 'post',
        data
    })
}

/**
 * @description: 忘记密码修改
 * @param {type}
 * @return {type}
 */
export const forgetPassword = (data) => {
    return request({
        url: '/admin/base/forgetPassword',
        method: 'post',
        data
    })
}