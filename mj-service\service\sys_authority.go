package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"errors"
	"fmt"

	"github.com/jinzhu/gorm"
)

// @Title 创建角色
// @Description
// <AUTHOR>
// @Param auths 		[]request.SaveOrUpdateAuthority 	角色权限信息
// @Param uuid 			string 								用户唯一标识
// @Return err 			error 								错误信息

func CreateAuthority(auths []req.SaveOrUpdateAuthority, uuid string) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var currentUser model.SysUser
		global.GVA_DB.Where("uuid = ?", uuid).First(&currentUser)

		// 如果不是系统管理员，增加标识
		for _, auth := range auths {
			if err = tx.Where("authority_name = ? and company_code = ?", auth.AuthorityName, auth.CompanyCode).First(&model.SysAuthority{}).Error; err == nil {
				return errors.New("存在重复角色名称")
			}
			var authority model.SysAuthority

			authority.Status = constants.STATUS_ENABLED
			authority.AuthorityType = constants.AUTHORITY_TYPE_NORMAL
			authority.CompanyCode = currentUser.CompanyCode
			authority.AuthorityName = auth.AuthorityName
			err = tx.Create(&authority).Error
			if err != nil {
				return err
			}
			// 保存级联
			err = saveAuthorityMenu(int(authority.ID), auth.MenuIds, tx)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// @Title 保存角色菜单级联
// @Description
// <AUTHOR>
// @Param authId 	int	 	角色 id
// @Param menuIds 	[]int 	菜单 id 数组
// @Return err 		error 	错误信息

func saveAuthorityMenu(authId int, menuIds []int, tx *gorm.DB) (err error) {
	for _, menuId := range menuIds {
		var authMenu model.SysAuthorityMenu
		authMenu.SysBaseMenuId = menuId
		authMenu.SysAuthorityId = authId
		err = tx.Create(&authMenu).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	return err
}

// @Title 更新角色
// @Description
// <AUTHOR>
// @Param auth 		request.SaveOrUpdateAuthority	角色信息
// @Return err 		error 							错误信息

func UpdateAuthority(auth req.SaveOrUpdateAuthority) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 删除原有关联
		err = tx.Where("sys_authority_id = ?", auth.ID).Delete(&model.SysAuthorityMenu{}).Error
		if err != nil {
			tx.Rollback()
			return err
		}

		var authority model.SysAuthority
		authority.ID = auth.ID
		authority.AuthorityName = auth.AuthorityName
		err = tx.Where("id = ?", auth.ID).First(&model.SysAuthority{}).Updates(&authority).Error
		if err != nil {
			tx.Rollback()
			return err
		}

		err = saveAuthorityMenu(auth.ID, auth.MenuIds, tx)
		if err != nil {
			return err
		}
		return nil
	})
}

// @Title 删除角色
// @Description
// <AUTHOR>
// @Param reqIds 	[]request.GetById	角色 id 数组
// @Return err 		error 				错误信息

func DeleteAuthority(reqIds []req.GetById) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, reqId := range reqIds {
			var auth model.SysAuthority
			err = tx.Preload("SysUser").Where("id = ?", reqId.Id).First(&auth).Error
			if err == nil && len(auth.SysUser) > 0 {
				return errors.New("此角色有用户正在使用禁止删除:" + auth.AuthorityName)
			}
			db := tx.Preload("SysBaseMenus").Where("id = ?", reqId.Id).First(&auth).Unscoped().Delete(&auth)
			if len(auth.SysBaseMenus) > 0 {
				err = db.Association("SysBaseMenus").Delete(auth.SysBaseMenus).Error
				if err != nil {
					return err
				}
			} else {
				err = db.Error
				if err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// @Title 分页获取数据
// @Description
// <AUTHOR>
// @Param info 	request.GetAuthorityList	查询条件
// @Param uuid 	string						用户唯一标识
// @Return err 	error 						错误信息

func GetAuthorityInfoList(info req.GetAuthorityList, uuid string) (err error, list interface{}, total int, enableNum, disableNum int) {
	var currentUser model.SysUser
	global.GVA_DB.Where("uuid = ?", uuid).First(&currentUser)
	db := global.GVA_DB
	var authority []model.SysAuthority
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		db = db.Where("company_code = ?", currentUser.CompanyCode)
		db = db.Where("authority_type = ?", constants.AUTHORITY_TYPE_NORMAL)
	}
	if info.Name != "" {
		db = db.Where("instr(authority_name, ?)", info.Name)
	}
	err = db.Find(&authority).Count(&total).Error
	err = db.Preload("SysBaseMenus").Find(&authority).Error
	err = db.Where("status = ?", constants.STATUS_ENABLED).Find(&[]model.SysAuthority{}).Count(&enableNum).Error
	err = db.Where("status = ?", constants.STATUS_DISABLED).Find(&[]model.SysAuthority{}).Count(&disableNum).Error
	return err, authority, total, enableNum, disableNum
}

// @Title 更改角色状态
// @Description
// <AUTHOR>
// @Param auths 	[]model.SysAuthority	角色数组
// @Return err 		error 					错误信息

func UpdateAuthorityStatus(auths []model.SysAuthority) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, auth := range auths {
			err = tx.Model(&auth).Update("status", auth.Status).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// @Title 获取角色和绑定用户
// @Description
// <AUTHOR>
// @Param info 			request.GetAuthAndUsers	查询条件
// @Return err 			error 					错误信息
// @Return list 		error 					列表
// @Return total 		error 					总条数
// @Return authId 		error 					角色id
// @Return authName 	error 					角色名称

func GetAuthorityAndUsers(info req.GetAuthAndUsers) (err error, list interface{}, total int, authId int, authName string) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var authority model.SysAuthority
	err = global.GVA_DB.Where("id = ?", info.ID).First(&authority).Error
	if err != nil {
		return errors.New("获取角色错误"), list, total, info.ID, authName
	}
	authId = info.ID
	authName = authority.AuthorityName
	db := global.GVA_DB
	sql := fmt.Sprintf("select id, username, nick_name, status from sys_users user left join sys_authority_users au on au.sys_user_id = user.id where au.sys_authority_id = %v", info.ID)
	sql = sql + fmt.Sprintf(" and company_code = '%v'", authority.CompanyCode)
	if info.Username != "" {
		sql = sql + fmt.Sprintf(" and instr(username, '%v')", info.Username)
	}
	var user []resp.BindingUsers
	err = db.Table("sys_users").Raw(sql).Scan(&user).Count(&total).Error
	total = len(user)
	err = db.Table("sys_users").Limit(limit).Offset(offset).Raw(sql).Scan(&user).Error
	return err, user, total, authId, authName
}

// @Title 获取变更角色用户列表
// @Description
// <AUTHOR>
// @Param id 					float64							查询条件
// @Return err 					error 							错误信息
// @Return authBindingUsers 	response.GetAuthBindingUsers 	角色用户绑定信息

func GetAuthBindingUsers(id int) (err error, authBindingUsers resp.GetAuthBindingUsers) {
	var authorizations []resp.BindingUsers
	sql := "select user.id,user.nick_name, user.username from sys_users as user" +
		" left join sys_authority_users as au on au.sys_user_id = user.id" +
		" where au.sys_authority_id = ?"
	err = global.GVA_DB.Raw(sql, id).Scan(&authorizations).Error
	var electeds []resp.BindingUsers
	sql = "SELECT user.id, user.nick_name, user.username FROM sys_users user" +
		" LEFT JOIN sys_authorities auth ON user.company_code = auth.company_code" +
		" WHERE user.id NOT IN (SELECT sys_user_id FROM sys_authority_users WHERE sys_authority_id = ?) " +
		"AND auth.id = ? and user.deleted_at is null"
	err = global.GVA_DB.Raw(sql, id, id).Scan(&electeds).Error
	authBindingUsers.Authorizations = authorizations
	authBindingUsers.Electeds = electeds
	return err, authBindingUsers
}

// @Title 更新变更角色用户列表
// @Description
// <AUTHOR>
// @Param auth 		request.UpdateAuthBindingUsers	角色用户绑定信息
// @Return err 		error 							错误信息

func UpdateAuthBindingUsers(auth req.UpdateAuthBindingUsers) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		err = tx.Where("sys_authority_id = ?", auth.AuthId).Delete(&model.SysAuthorityUsers{}).Error
		if err != nil {
			return err
		}
		for _, userId := range auth.UserIds {
			var au model.SysAuthorityUsers
			au.SysAuthorityId = auth.AuthId
			au.SysUserId = userId
			err = tx.Create(&au).Error
			if err != nil {
				return err
			}
		}

		// 根据角色id查询当前角色绑定了多少用户，更新角色表中BindingUsers
		var bindUserNum int
		tx.Table("sys_authority_users").Where("sys_authority_id = ?", auth.AuthId).Find(&model.SysAuthorityUsers{}).Count(&bindUserNum)
		err = tx.Table("sys_authorities").Where("id = ?", auth.AuthId).Update("binding_users", bindUserNum).Error
		if err != nil {
			return err
		}
		return nil
	})
}

// @Title 获取自己公司的角色
// @Description
// <AUTHOR>
// @Param uuid 			string						角色用户绑定信息
// @Return err 			error 						错误信息
// @Return authList 	[]response.AuthCondition 	角色列表

func GetAuthBySelfCompany(uuid string) (err error, authList []resp.AuthCondition) {
	var CurrentUser model.SysUser
	err = global.GVA_DB.Where("uuid = ?", uuid).First(&CurrentUser).Error
	if err != nil {
		return err, authList
	}
	db := global.GVA_DB
	if CurrentUser.Username != global.GVA_CONFIG.Admin.Username {
		db = db.Where("company_code = ? and authority_type = ? ", CurrentUser.CompanyCode, constants.AUTHORITY_TYPE_NORMAL)
	}
	err = db.Find(&[]model.SysAuthority{}).Scan(&authList).Error
	if err != nil {
		return err, authList
	}
	return err, authList
}
