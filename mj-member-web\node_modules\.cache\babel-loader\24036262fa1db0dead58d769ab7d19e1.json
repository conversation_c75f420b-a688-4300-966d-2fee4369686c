{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\Profile.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\Profile.vue", "mtime": 1757558749942}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "getMyStats", "changePassword", "name", "data", "validateConfirmPassword", "rule", "value", "callback", "passwordForm", "newPassword", "Error", "stats", "profileForm", "mobile", "companyName", "companyAddress", "<PERSON><PERSON><PERSON>", "contactPhone", "profileRules", "required", "message", "trigger", "oldPassword", "confirmPassword", "passwordRules", "min", "max", "validator", "updating", "changingPassword", "computed", "mounted", "fetchStats", "initProfileForm", "methods", "response", "code", "error", "console", "userInfo", "updateProfile", "$refs", "validate", "result", "$store", "dispatch", "success", "$message", "resetForm", "resetFields", "msg", "handleNavItemClick", "path", "$router", "push"], "sources": ["src/views/profile/Profile.vue"], "sourcesContent": ["<template>\n  <div class=\"profile-page\">\n    <AppHeader />\n    \n    <div class=\"container\">\n      <div class=\"profile-layout\">\n        <!-- 侧边栏 -->\n        <div class=\"sidebar\">\n          <div class=\"user-card\">\n            <div class=\"avatar\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"user-info\">\n              <h3>{{ userInfo?.name || '用户' }}</h3>\n              <p>{{ userInfo?.mobile }}</p>\n              <StatusTag :status=\"userInfo?.auditStatus\" type=\"audit\" />\n            </div>\n          </div>\n          \n          <nav class=\"nav-menu\">\n            <p @click=\"handleNavItemClick('/profile')\" class=\"nav-item router-link-active\">\n              <i class=\"el-icon-user\"></i>\n              个人资料\n            </p>\n            <p @click=\"handleNavItemClick('/profile/bids')\" class=\"nav-item\">\n              <i class=\"el-icon-price-tag\"></i>\n              我的出价\n            </p>\n            <p @click=\"handleNavItemClick('/profile/projects')\" class=\"nav-item\">\n              <i class=\"el-icon-folder\"></i>\n              我的项目\n            </p>\n          </nav>\n        </div>\n\n        <!-- 主内容 -->\n        <div class=\"main-content\">\n          <div class=\"content-header\">\n            <h2>个人资料</h2>\n          </div>\n\n          <div class=\"profile-content\">\n            <!-- 统计卡片 -->\n            <div class=\"stats-cards\">\n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.totalBids || 0 }}</div>\n                  <div class=\"stat-label\">总出价次数</div>\n                </div>\n              </div>\n              \n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.wonBids || 0 }}</div>\n                  <div class=\"stat-label\">中标次数</div>\n                </div>\n              </div>\n              \n              <div class=\"stat-card\">\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">{{ stats.participatedProjects || 0 }}</div>\n                  <div class=\"stat-label\">参与项目</div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 个人信息表单 -->\n            <div class=\"profile-form-section\">\n              <h3>基本信息</h3>\n              <el-form\n                ref=\"profileForm\"\n                :model=\"profileForm\"\n                :rules=\"profileRules\"\n                label-width=\"120px\"\n                class=\"profile-form\"\n              >\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"手机号\">\n                      <el-input v-model=\"profileForm.mobile\" disabled />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"姓名\" prop=\"name\">\n                      <el-input v-model=\"profileForm.name\" />\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"企业名称\" prop=\"companyName\">\n                      <el-input v-model=\"profileForm.companyName\" />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"联系人\" prop=\"contactPerson\">\n                      <el-input v-model=\"profileForm.contactPerson\" />\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n\n                <el-form-item label=\"企业地址\" prop=\"companyAddress\">\n                  <el-input\n                    v-model=\"profileForm.companyAddress\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"联系电话\" prop=\"contactPhone\">\n                  <el-input v-model=\"profileForm.contactPhone\" />\n                </el-form-item>\n\n                <el-form-item>\n                  <el-button type=\"primary\" @click=\"updateProfile\" :loading=\"updating\">\n                    保存修改\n                  </el-button>\n                  <el-button @click=\"resetForm\">重置</el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n\n            <!-- 密码修改 -->\n            <div class=\"password-section\">\n              <h3>修改密码</h3>\n              <el-form\n                ref=\"passwordForm\"\n                :model=\"passwordForm\"\n                :rules=\"passwordRules\"\n                label-width=\"120px\"\n                class=\"password-form\"\n              >\n                <el-form-item label=\"当前密码\" prop=\"oldPassword\">\n                  <el-input\n                    v-model=\"passwordForm.oldPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"新密码\" prop=\"newPassword\">\n                  <el-input\n                    v-model=\"passwordForm.newPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n                  <el-input\n                    v-model=\"passwordForm.confirmPassword\"\n                    type=\"password\"\n                    show-password\n                  />\n                </el-form-item>\n\n                <el-form-item>\n                  <el-button type=\"primary\" @click=\"changePassword\" :loading=\"changingPassword\">\n                    修改密码\n                  </el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { getMyStats } from '@/api/user'\nimport { changePassword } from '@/api/auth'\n\nexport default {\n  name: 'Profile',\n  data() {\n    // 确认密码验证\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.passwordForm.newPassword) {\n        callback(new Error('两次输入的密码不一致'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      stats: {},\n      profileForm: {\n        mobile: '',\n        name: '',\n        companyName: '',\n        companyAddress: '',\n        contactPerson: '',\n        contactPhone: ''\n      },\n      profileRules: {\n        name: [\n          { required: true, message: '请输入姓名', trigger: 'blur' }\n        ],\n        companyName: [\n          { required: true, message: '请输入企业名称', trigger: 'blur' }\n        ],\n        companyAddress: [\n          { required: true, message: '请输入企业地址', trigger: 'blur' }\n        ],\n        contactPerson: [\n          { required: true, message: '请输入联系人', trigger: 'blur' }\n        ],\n        contactPhone: [\n          { required: true, message: '请输入联系电话', trigger: 'blur' }\n        ]\n      },\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      passwordRules: {\n        oldPassword: [\n          { required: true, message: '请输入当前密码', trigger: 'blur' }\n        ],\n        newPassword: [\n          { required: true, message: '请输入新密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认新密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      updating: false,\n      changingPassword: false\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['userInfo'])\n  },\n  async mounted() {\n    await this.fetchStats()\n    this.initProfileForm()\n  },\n  methods: {\n    // 获取统计数据\n    async fetchStats() {\n      try {\n        const response = await getMyStats()\n        if (response.data.code === 200) {\n          this.stats = response.data.data\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error)\n      }\n    },\n\n    // 初始化个人信息表单\n    initProfileForm() {\n      if (this.userInfo) {\n        this.profileForm = {\n          mobile: this.userInfo.mobile || '',\n          name: this.userInfo.name || '',\n          companyName: this.userInfo.companyName || '',\n          companyAddress: this.userInfo.companyAddress || '',\n          contactPerson: this.userInfo.contactPerson || '',\n          contactPhone: this.userInfo.contactPhone || ''\n        }\n      }\n    },\n\n    // 更新个人资料\n    async updateProfile() {\n      try {\n        await this.$refs.profileForm.validate()\n      } catch (error) {\n        return\n      }\n\n      this.updating = true\n      try {\n        const result = await this.$store.dispatch('user/updateProfile', this.profileForm)\n        if (result.success) {\n          this.$message.success('个人资料更新成功')\n          // 更新用户信息\n          await this.$store.dispatch('auth/getUserInfo')\n        } else {\n          this.$message.error(result.message)\n        }\n      } catch (error) {\n        this.$message.error('更新失败，请稍后重试')\n      } finally {\n        this.updating = false\n      }\n    },\n\n    // 重置表单\n    resetForm() {\n      this.initProfileForm()\n    },\n\n    // 修改密码\n    async changePassword() {\n      try {\n        await this.$refs.passwordForm.validate()\n      } catch (error) {\n        return\n      }\n\n      this.changingPassword = true\n      try {\n        const response = await changePassword({\n          oldPassword: this.passwordForm.oldPassword,\n          newPassword: this.passwordForm.newPassword\n        })\n        \n        if (response.data.code === 200) {\n          this.$message.success('密码修改成功')\n          this.passwordForm = {\n            oldPassword: '',\n            newPassword: '',\n            confirmPassword: ''\n          }\n          this.$refs.passwordForm.resetFields()\n        } else {\n          this.$message.error(response.data.msg || '密码修改失败')\n        }\n      } catch (error) {\n        this.$message.error('密码修改失败，请稍后重试')\n      } finally {\n        this.changingPassword = false\n      }\n    },\n    handleNavItemClick(path) {\n      this.$router.push(path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-page {\n  min-height: 110vh;\n  background: $bg-color;\n}\n\n.container {\n  padding: 20px;\n}\n\n.profile-layout {\n  display: flex;\n  gap: 30px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.sidebar {\n  width: 280px;\n  flex-shrink: 0;\n\n  .user-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    padding: 30px 20px;\n    text-align: center;\n    margin-bottom: 20px;\n\n    .avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background: $primary-color;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      margin: 0 auto 20px;\n    }\n\n    .user-info {\n      h3 {\n        color: $text-primary;\n        margin-bottom: 10px;\n      }\n\n      p {\n        color: $text-secondary;\n        margin-bottom: 15px;\n      }\n    }\n  }\n\n  .nav-menu {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 15px 20px;\n      color: $text-regular;\n      text-decoration: none;\n      border-bottom: 1px solid $border-color;\n      transition: all 0.3s;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover,\n      &.router-link-active {\n        background: $primary-color;\n        color: white;\n      }\n\n      i {\n        margin-right: 10px;\n        font-size: 16px;\n      }\n    }\n  }\n}\n\n.main-content {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: $box-shadow;\n  overflow: hidden;\n\n  .content-header {\n    padding: 30px 30px 0;\n    border-bottom: 1px solid $border-color;\n\n    h2 {\n      color: $text-primary;\n      margin-bottom: 30px;\n    }\n  }\n\n  .profile-content {\n    padding: 30px;\n\n    .stats-cards {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 40px;\n\n      .stat-card {\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        padding: 25px;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        gap: 15px;\n\n        .stat-icon {\n          font-size: 32px;\n          opacity: 0.8;\n        }\n\n        .stat-info {\n          .stat-number {\n            font-size: 24px;\n            font-weight: 700;\n            margin-bottom: 5px;\n          }\n\n          .stat-label {\n            font-size: 14px;\n            opacity: 0.9;\n          }\n        }\n      }\n    }\n\n    .profile-form-section,\n    .password-section {\n      margin-bottom: 40px;\n\n      h3 {\n        color: $text-primary;\n        margin-bottom: 20px;\n        padding-bottom: 10px;\n        border-bottom: 2px solid $primary-color;\n        display: inline-block;\n      }\n    }\n\n    .profile-form,\n    .password-form {\n      max-width: 600px;\n    }\n  }\n}\n\n@media (max-width: $tablet) {\n  .profile-layout {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n\n    .user-card {\n      display: flex;\n      align-items: center;\n      text-align: left;\n      gap: 20px;\n\n      .avatar {\n        margin: 0;\n      }\n    }\n\n    .nav-menu {\n      display: flex;\n      overflow-x: auto;\n\n      .nav-item {\n        white-space: nowrap;\n        border-bottom: none;\n        border-right: 1px solid $border-color;\n\n        &:last-child {\n          border-right: none;\n        }\n      }\n    }\n  }\n}\n\n@media (max-width: $mobile) {\n  .container {\n    padding: 15px;\n  }\n\n  .profile-content {\n    padding: 20px !important;\n\n    .stats-cards {\n      grid-template-columns: 1fr;\n    }\n  }\n}\n</style>\n"], "mappings": ";;AA4KA,SAAAA,UAAA;AACA,SAAAC,UAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;IACA,MAAAC,uBAAA,GAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA,UAAAE,YAAA,CAAAC,WAAA;QACAF,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IAEA;MACAI,KAAA;MACAC,WAAA;QACAC,MAAA;QACAX,IAAA;QACAY,WAAA;QACAC,cAAA;QACAC,aAAA;QACAC,YAAA;MACA;MACAC,YAAA;QACAhB,IAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,WAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,cAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,aAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,YAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAb,YAAA;QACAc,WAAA;QACAb,WAAA;QACAc,eAAA;MACA;MACAC,aAAA;QACAF,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,WAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAI,GAAA;UAAAC,GAAA;UAAAN,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,eAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,SAAA,EAAAvB,uBAAA;UAAAiB,OAAA;QAAA;MAEA;MACAO,QAAA;MACAC,gBAAA;IACA;EACA;EACAC,QAAA;IACA,GAAA/B,UAAA;EACA;EACA,MAAAgC,QAAA;IACA,WAAAC,UAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAF,WAAA;MACA;QACA,MAAAG,QAAA,SAAAnC,UAAA;QACA,IAAAmC,QAAA,CAAAhC,IAAA,CAAAiC,IAAA;UACA,KAAAzB,KAAA,GAAAwB,QAAA,CAAAhC,IAAA,CAAAA,IAAA;QACA;MACA,SAAAkC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAJ,gBAAA;MACA,SAAAM,QAAA;QACA,KAAA3B,WAAA;UACAC,MAAA,OAAA0B,QAAA,CAAA1B,MAAA;UACAX,IAAA,OAAAqC,QAAA,CAAArC,IAAA;UACAY,WAAA,OAAAyB,QAAA,CAAAzB,WAAA;UACAC,cAAA,OAAAwB,QAAA,CAAAxB,cAAA;UACAC,aAAA,OAAAuB,QAAA,CAAAvB,aAAA;UACAC,YAAA,OAAAsB,QAAA,CAAAtB,YAAA;QACA;MACA;IACA;IAEA;IACA,MAAAuB,cAAA;MACA;QACA,WAAAC,KAAA,CAAA7B,WAAA,CAAA8B,QAAA;MACA,SAAAL,KAAA;QACA;MACA;MAEA,KAAAT,QAAA;MACA;QACA,MAAAe,MAAA,cAAAC,MAAA,CAAAC,QAAA,4BAAAjC,WAAA;QACA,IAAA+B,MAAA,CAAAG,OAAA;UACA,KAAAC,QAAA,CAAAD,OAAA;UACA;UACA,WAAAF,MAAA,CAAAC,QAAA;QACA;UACA,KAAAE,QAAA,CAAAV,KAAA,CAAAM,MAAA,CAAAvB,OAAA;QACA;MACA,SAAAiB,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA;MACA;QACA,KAAAT,QAAA;MACA;IACA;IAEA;IACAoB,UAAA;MACA,KAAAf,eAAA;IACA;IAEA;IACA,MAAAhC,eAAA;MACA;QACA,WAAAwC,KAAA,CAAAjC,YAAA,CAAAkC,QAAA;MACA,SAAAL,KAAA;QACA;MACA;MAEA,KAAAR,gBAAA;MACA;QACA,MAAAM,QAAA,SAAAlC,cAAA;UACAqB,WAAA,OAAAd,YAAA,CAAAc,WAAA;UACAb,WAAA,OAAAD,YAAA,CAAAC;QACA;QAEA,IAAA0B,QAAA,CAAAhC,IAAA,CAAAiC,IAAA;UACA,KAAAW,QAAA,CAAAD,OAAA;UACA,KAAAtC,YAAA;YACAc,WAAA;YACAb,WAAA;YACAc,eAAA;UACA;UACA,KAAAkB,KAAA,CAAAjC,YAAA,CAAAyC,WAAA;QACA;UACA,KAAAF,QAAA,CAAAV,KAAA,CAAAF,QAAA,CAAAhC,IAAA,CAAA+C,GAAA;QACA;MACA,SAAAb,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA;MACA;QACA,KAAAR,gBAAA;MACA;IACA;IACAsB,mBAAAC,IAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,IAAA;IACA;EACA;AACA", "ignoreList": []}]}