package v1

import (
	"auction-sys/model/req"
	"auction-sys/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// @Tags WebSocket
// @Summary 管理员WebSocket连接
// @Security ApiKeyAuth
// @Router /auction/admin/ws [get]
func AdminWebSocket(c *gin.Context) {
	// 获取当前用户信息
	cl, exists := c.Get("claims")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	
	claims, ok := cl.(*req.CustomClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证信息"})
		return
	}

	// 升级为WebSocket连接
	service.HandleWebSocket(c.Writer, c.Request, claims.ID, "admin")
}

// @Tags WebSocket
// @Summary 会员WebSocket连接
// @Security ApiKeyAuth
// @Router /auction/member/ws [get]
func MemberWebSocket(c *gin.Context) {
	// 获取当前用户信息
	cl, exists := c.Get("claims")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	
	claims, ok := cl.(*req.CustomClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证信息"})
		return
	}

	// 升级为WebSocket连接
	service.HandleWebSocket(c.Writer, c.Request, claims.ID, "member")
}

// @Tags WebSocket
// @Summary 公开WebSocket连接（无需认证，用于访客查看）
// @Router /auction/public/ws [get]
func PublicWebSocket(c *gin.Context) {
	// 从查询参数获取访客ID（可选）
	guestIDStr := c.Query("guestId")
	guestID := 0
	if guestIDStr != "" {
		if id, err := strconv.Atoi(guestIDStr); err == nil {
			guestID = id
		}
	}

	// 升级为WebSocket连接
	service.HandleWebSocket(c.Writer, c.Request, guestID, "guest")
}
