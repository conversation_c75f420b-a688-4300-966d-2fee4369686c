package resp

import "github.com/Gre-Z/common/jtime"

// 日志查询返回
type TLogListResponse struct {
	List        interface{} `json:"list"`        // 列表
	Tab         int         `json:"tab"`         // 页签
	StartTime   string      `json:"startTime"`   // 开始日期
	EndTime     string      `json:"endTime"`     // 结束日期
	CompanyCode []string    `json:"companyCode"` // 企业编码
	Username    string      `json:"username"`    // 用户名
	Page        int         `json:"page"`        // 当前页
	PageSize    int         `json:"pageSize"`    // 每页条数
	Total       int         `json:"total"`       // 总条数
}

// 登录日志列表
type LoginLogList struct {
	Username     string         `json:"username"`     // 用户名
	Nickname     string         `json:"nickname"`     // 真实姓名
	LoginTime    jtime.JsonTime `json:"loginTime"`    // 登录时间
	LoginCount   int            `json:"loginCount"`   // 登录次数
	WebLogin     int            `json:"webLogin"`     // 网页登录次数
	AppletsLogin int            `json:"appletsLogin"` // 小程序登录次数
}

// 操作日志列表
type OperationLogList struct {
	Username      string         `json:"username"`      // 用户名
	Nickname      string         `json:"nickname"`      // 真实姓名
	Operation     string         `json:"operation"`     // 操作模块
	OperationTime jtime.JsonTime `json:"operationTime"` // 操作时间
	LoginMethod   int            `json:"loginMethod"`   // 登录方式
	Ip            string         `json:"ip"`            // ip 地址
	Address       string         `json:"address"`       // ip 对应真实地址
}
