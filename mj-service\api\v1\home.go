package v1

import (
	"auction-sys/global/response"
	"auction-sys/service"

	"github.com/gin-gonic/gin"
)

// @Tags Farmland
// @Summary 大屏 middle-top
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Farmland true "创建农田表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /home/<USER>
func HomeTopInfo(c *gin.Context) {
	info := service.GetMiddleTop()
	response.OkWithData(info, c)
}

// @Tags Farmland
// @Summary 大屏 middle-top
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Farmland true "创建农田表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /home/<USER>
func HomeRightTopInfo(c *gin.Context) {
	info := service.GetRightTop()
	response.OkWithData(info, c)
}
