好的，这是整合所有需求后的完整产品需求说明书。

---

# 产品需求说明书（PRD）：棉副产品竞价平台

**文件状态：** 正式发布
**当前版本：** 1.1
**编制人：** [您的姓名/产品团队]
**编制日期：** 2023-10-27
**修订历史：**

| 版本 | 日期 | 修订人 | 修订说明 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2023-10-27 | [姓名] | 初稿创建 |
| 1.1 | 2023-10-27 | [姓名] | 新增需求点14：出价频率控制（间隔30秒） |

## 1. 引言

### 1.1 项目背景
为适应棉副产品贸易数字化发展，提升交易效率与透明度，特开发此棉副产品在线竞价平台。平台旨在为货权人提供一个公开、公平、公正的销售渠道，并为采购方（竞价参与者）创造一个高效、便捷、安全的竞购环境。通过标准化的流程和实时的竞价机制，最大化商品价值，简化传统交易流程。

### 1.2 项目目标
- **短期目标：** 完成平台核心竞价功能上线，支持至少100个并发用户稳定交易。
- **长期目标：** 成为国内领先的、专业化的棉副产品线上现货交易平台，建立行业标准。

### 1.3 读者对象
本项目PRD的读者包括但不限于：项目经理、UI/UX设计师、技术开发团队（前端、后端、测试）、运营团队及管理层。

### 1.4 名词解释
- **商品中类:** 对棉副产品进行的标准化分类，如“棉纱”、“精梳棉”、“落棉”等。
- **最小加价幅度:** 单次出价必须高于当前价格的最小金额值。
- **“出价时间+5分钟”规则:** 任何一次新的有效出价，都会将当前竞价商品的结束时间自动延长至“该次出价时间点 + 5分钟”，但最终结束时间不得晚于初始设置的“竞价结束时间”。
- **出价间隔:** 自上一次成功出价后，下一次成功出价必须等待的时间（30秒）。

## 2. 整体描述

### 2.1 用户角色

| 角色 | 描述 | 主要功能 |
| :--- | :--- | :--- |
| **后台管理员 (sys_user)** | 平台运营方 | 管理用户、商品类别、发布竞价项目、监控竞价、审核会员、终止竞价、查看结果 |
| **竞价参与者 (sys_member)** | 经过认证的采购企业 | 注册/登录、浏览竞价项目、参与出价、查看个人记录 |

### 2.2 系统架构概述
系统采用典型的前后端分离架构。前端分为后台管理端（Web）和用户竞价端（Web）。后端提供统一的RESTful API，处理业务逻辑、数据存储及用户认证。

### 2.3 业务流程图

```mermaid
graph TD
    A[管理员发布竞价] --> B[设置可参与竞价的成员];
    C[会员注册/提交资料] --> D[管理员审核];
    D -- 审核通过 --> E[会员登录平台];
    B --> F[竞价项目上线];
    E --> G[浏览/筛选竞价商品];
    G --> H{是否可出价?};
    H -- 是 --> I[参与出价];
    H -- 否 --> G;
    I --> J{校验: 出价>当前价+加价幅?};
    J -- 否 --> I;
    J -- 是 --> K{校验: 距上次出价≥30秒?};
    K -- 否 --> L[提示: 出价过于频繁, 请等待];
    L --> I;
    K -- 是 --> M[出价成功];
    M --> N{触发延时规则?};
    N -- 是 --> O[更新结束时间: Max(出价时间+5m, 原结束时间)];
    N -- 否 --> P[等待结束];
    O --> P;
    P --> Q[竞价结束, 生成结果];
    Q --> R[管理员查看竞价过程与结果];
```

## 3. 功能需求详述

### 3.1 后台管理系统

#### 3.1.1 用户与权限管理
- **功能描述:** 管理系统用户（sys_user）和竞价成员（sys_member）账户。
- **需求明细:**
    - 可对后台管理人员进行增删改查及权限分配。
    - 可查看、搜索、禁用竞价参与者账户。
    - **竞价参与者审核:** 对注册会员提交的企业信息（手机号、姓名、企业名称、营业执照等）进行审核，审核通过后方可登录竞价。

#### 3.1.2 商品中类管理
- **功能描述:** 维护平台交易的商品分类体系。
- **需求明细:**
    - 可对商品中类进行增删改查操作。
    - 每个商品中类必须包含字段：`中类名称`（如：精梳棉）、`计量单位`（如：吨、公斤）。

#### 3.1.3 竞价项目管理 (核心)
- **功能描述:** 创建和管理所有竞价项目。
- **需求明细:**
    - **创建竞价:** 表单需包含以下必填字段：
        - `竞价项目标题`
        - `竞价商品`（关联3.1.2中维护的商品中类）
        - `商品数量` + `计量单位`（自动带出）
        - `起拍价`
        - `最小加价幅度`
        - `竞价开始时间`（日期时间选择器，精确到秒）
        - `竞价结束时间`（日期时间选择器，精确到秒）
        - `货权人`
        - `仓库地址`
        - `费用说明`（文本，如：含出库费、运费自理等）
        - `备注`（文本）
    - **指派参与权限:** 在创建或编辑竞价时，管理员必须从已审核通过的会员列表中，选择允许参与此项目竞价的成员。
    - **状态管理与终止功能:**
        - 系统自动根据时间规则更新状态：“即将开始” -> “竞价中” -> “已结束”。
        - 管理员可手动将“竞价中”的项目状态更改为“已终止”（提前终止），终止后所有用户无法再出价。
    - **列表视图:** 以列表形式展示所有竞价项目，支持按状态、标题等筛选和搜索。

#### 3.1.4 竞价监控与结果查询
- **功能描述:** 实时监控竞价过程并查看历史结果。
- **需求明细:**
    - 选择任一竞价项目，可查看其**完整的出价记录**（出价人、出价时间、出价金额、IP地址）。
    - 竞价结束后，页面清晰显示**最终竞得者**、**最终成交价**。
    - 支持将竞价过程与结果导出为Excel或PDF。

### 3.2 用户竞价系统

#### 3.2.1 会员注册与登录
- **功能描述:** 供竞价参与者注册和登录。
- **需求明细:**
    - **注册界面:** 需提供：`手机号`（必填，需短信验证）、`姓名`（必填）、`密码`（必填）、`竞价企业名称`（必填）、`企业营业执照`（图片上传）等。
    - **登录:** 使用手机号和密码登录。

#### 3.2.2 竞价大厅
- **功能描述:** 用户主界面，展示所有竞价项目。
- **需求明细:**
    - **状态筛选:** 提供“即将开始”、“竞价中”、“已结束”三个状态的Tab页或筛选器。
    - **列表信息:** 每个竞价项目卡片需显示：标题、当前最高价/起拍价、商品数量、仓库地址、当前状态、剩余时间（对于“竞价中”和“即将开始”的项目）。
    - **排序:** 支持按时间、价格等排序。

#### 3.2.3 商品详情与竞价界面 (核心)
- **功能描述:** 用户在此页面出价。
- **需求明细:**
    - 展示3.1.3中创建的所有商品详情。
    - **出价逻辑:**
        1.  **权限校验:** 用户进入页面时，系统首先校验该用户是否被管理员授权可参与此项目竞价。如未授权，则**出价按钮置灰**，并提示“您暂无出价权限”。
        2.  **信息展示:** 如授权，则显示当前价格、最小加价幅度。
        3.  **出价校验:** 用户输入出价金额或点击快捷按钮，点击“出价”按钮后，系统进行**两级后端校验**：
            -   **校验一（金额）:**
                -   `出价金额 > 当前最高价`
                -   `(出价金额 - 当前最高价) >= 最小加价幅度`
            -   **校验二（频率）:**
                -   针对**该特定竞价商品**，系统检查最后一次**成功出价**的时间（无论是哪个用户出的价）。
                -   计算当前时间与最后一次成功出价时间的间隔。
                -   如果间隔 `< 30秒`，则出价失败，返回明确提示：`“出价过于频繁，请等待片刻后再试。”`
                -   如果间隔 `>= 30秒`，则校验通过。
        4.  **出价成功:** 两级校验均通过，出价成功。更新当前价，并**触发结束时间规则**：`新结束时间 = Max(当前服务器时间 + 5分钟, 原结束时间)`。同时记录本次成功出价的时间。
    - **快捷加价按钮:** 提供三个按钮，点击后自动计算出价金额：
        - `+（1 * 最小加价幅度）`
        - `+（2 * 最小加价幅度）`
        - `+（5 * 最小加价幅度）`
    - **UI/UX交互:**
        - 页面需明确提示“同一商品两次出价需间隔30秒”。
        - 当用户因频率限制出价失败时，前端可动态显示一个等待倒计时，提升体验。
    - **实时更新:** 页面需通过WebSocket或定时轮询，实时更新当前最高价、出价记录列表和倒计时。

#### 3.2.4 “我的参与”页面
- **功能描述:** 用户查看自己参与过的竞价。
- **需求明细:**
    - 展示我出过价的所有项目，及其状态（竞价中/已结束/已终止）和结果（是否竞得）。
