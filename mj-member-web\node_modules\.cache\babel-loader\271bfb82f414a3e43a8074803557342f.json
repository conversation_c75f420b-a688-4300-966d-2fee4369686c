{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\MyBids.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\MyBids.vue", "mtime": 1757558372898}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "name", "data", "bids", "summary", "filters", "status", "isWinning", "loading", "page", "pageSize", "total", "computed", "mounted", "fetchBids", "methods", "result", "$store", "dispatch", "success", "list", "error", "console", "handlePageChange", "newPage", "goToProject", "projectId", "$router", "push", "formatMoney", "value", "Number", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "handleNavItemClick", "path", "watch", "handler", "deep"], "sources": ["src/views/profile/MyBids.vue"], "sourcesContent": ["<template>\n  <div class=\"my-bids-page\">\n    <AppHeader />\n    \n    <div class=\"container\">\n      <div class=\"profile-layout\">\n        <!-- 侧边栏 -->\n        <div class=\"sidebar\">\n          <div class=\"user-card\">\n            <div class=\"avatar\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"user-info\">\n              <h3>{{ userInfo?.name || '用户' }}</h3>\n              <p>{{ userInfo?.mobile }}</p>\n              <StatusTag :status=\"userInfo?.auditStatus\" type=\"audit\" />\n            </div>\n          </div>\n          \n          <nav class=\"nav-menu\">\n            <p @click=\"handleNavItemClick('/profile')\" class=\"nav-item\" >\n              <i class=\"el-icon-user\"></i>\n              个人资料\n            </p>\n            <p @click=\"handleNavItemClick('/profile/bids')\" class=\"nav-item router-link-active\" >\n              <i class=\"el-icon-price-tag\"></i>\n              我的出价\n            </p>\n            <p @click=\"handleNavItemClick('/profile/projects')\" class=\"nav-item\" >\n              <i class=\"el-icon-folder\"></i>\n              我的项目\n            </p>\n          </nav>\n        </div>\n\n        <!-- 主内容 -->\n        <div class=\"main-content\">\n          <div class=\"content-header\">\n            <h2>我的出价</h2>\n            <div class=\"filters\">\n              <el-select v-model=\"filters.status\" placeholder=\"项目状态\" clearable @change=\"fetchBids\">\n                <el-option label=\"即将开始\" :value=\"0\" />\n                <el-option label=\"竞价中\" :value=\"1\" />\n                <el-option label=\"已结束\" :value=\"2\" />\n                <el-option label=\"已终止\" :value=\"3\" />\n              </el-select>\n              <el-select v-model=\"filters.isWinning\" placeholder=\"出价状态\" clearable @change=\"fetchBids\">\n                <el-option label=\"领先\" :value=\"true\" />\n                <el-option label=\"被超越\" :value=\"false\" />\n              </el-select>\n            </div>\n          </div>\n\n          <div class=\"bids-content\" v-loading=\"loading\">\n            <!-- 统计卡片 -->\n            <div class=\"stats-summary\">\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.totalBids || 0 }}</div>\n                <div class=\"summary-label\">总出价次数</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.leadingBids || 0 }}</div>\n                <div class=\"summary-label\">当前领先</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.wonBids || 0 }}</div>\n                <div class=\"summary-label\">中标次数</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">¥{{ formatMoney(summary.totalAmount) }}</div>\n                <div class=\"summary-label\">累计出价金额</div>\n              </div>\n            </div>\n\n            <!-- 出价列表 -->\n            <div class=\"bids-list\">\n              <div\n                v-for=\"bid in bids\"\n                :key=\"bid.id\"\n                class=\"bid-item\"\n                :class=\"{ 'is-winning': bid.isWinning, 'is-won': bid.isWon }\"\n              >\n                <div class=\"bid-project\">\n                  <h4 class=\"project-title\" @click=\"goToProject(bid.projectId)\">\n                    {{ bid.projectTitle }}\n                  </h4>\n                  <div class=\"project-meta\">\n                    <StatusTag :status=\"bid.projectStatus\" />\n                    <span class=\"category\">{{ bid.categoryName }}</span>\n                    <span class=\"quantity\">{{ bid.quantity }}{{ bid.unit }}</span>\n                  </div>\n                </div>\n\n                <div class=\"bid-info\">\n                  <div class=\"bid-details\">\n                    <div class=\"detail-item\">\n                      <span class=\"label\">我的出价：</span>\n                      <span class=\"value my-bid\">¥{{ formatMoney(bid.bidAmount) }}</span>\n                    </div>\n                    <div class=\"detail-item\">\n                      <span class=\"label\">当前最高价：</span>\n                      <span class=\"value current-price\">¥{{ formatMoney(bid.currentPrice) }}</span>\n                    </div>\n                    <div class=\"detail-item\">\n                      <span class=\"label\">出价时间：</span>\n                      <span class=\"value\">{{ bid.bidTime | formatTime }}</span>\n                    </div>\n                  </div>\n\n                  <div class=\"bid-status\">\n                    <div class=\"status-badge\" v-if=\"bid.isWon\">\n                      <i class=\"el-icon-trophy\"></i>\n                      中标\n                    </div>\n                    <div class=\"status-badge winning\" v-else-if=\"bid.isWinning\">\n                      <i class=\"el-icon-star-on\"></i>\n                      领先\n                    </div>\n                    <div class=\"status-badge\" v-else>\n                      <i class=\"el-icon-star-off\"></i>\n                      被超越\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"bid-actions\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"goToProject(bid.projectId)\"\n                    v-if=\"bid.projectStatus === 1\"\n                  >\n                    继续出价\n                  </el-button>\n                  <el-button\n                    size=\"small\"\n                    @click=\"goToProject(bid.projectId)\"\n                  >\n                    查看详情\n                  </el-button>\n                </div>\n              </div>\n\n              <!-- 空状态 -->\n              <div v-if=\"bids.length === 0 && !loading\" class=\"empty-state\">\n                <el-empty description=\"暂无出价记录\">\n                  <el-button type=\"primary\" @click=\"$router.push('/home')\">\n                    去竞价\n                  </el-button>\n                </el-empty>\n              </div>\n            </div>\n\n            <!-- 分页 -->\n            <div class=\"pagination\" v-if=\"total > 0\">\n              <el-pagination\n                @current-change=\"handlePageChange\"\n                :current-page=\"page\"\n                :page-size=\"pageSize\"\n                :total=\"total\"\n                layout=\"total, prev, pager, next, jumper\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'MyBids',\n  data() {\n    return {\n      bids: [],\n      summary: {},\n      filters: {\n        status: '',\n        isWinning: ''\n      },\n      loading: false,\n      page: 1,\n      pageSize: 10,\n      total: 0\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['userInfo'])\n  },\n  mounted() {\n    this.fetchBids()\n  },\n  methods: {\n    // 获取出价记录\n    async fetchBids() {\n      this.loading = true\n      try {\n        const result = await this.$store.dispatch('user/fetchMyBids', {\n          page: this.page,\n          pageSize: this.pageSize,\n          ...this.filters\n        })\n        \n        if (result.success) {\n          this.bids = result.data.list || []\n          this.total = result.data.total || 0\n          this.summary = result.data.summary || {}\n        }\n      } catch (error) {\n        console.error('获取出价记录失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 页码变化\n    handlePageChange(newPage) {\n      this.page = newPage\n      this.fetchBids()\n    },\n\n    // 跳转到项目详情\n    goToProject(projectId) {\n      this.$router.push(`/auction/${projectId}`)\n    },\n\n    // 格式化金额\n    formatMoney(value) {\n      if (!value && value !== 0) return '0'\n      return Number(value).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })\n    },\n    handleNavItemClick(path) {\n      this.$router.push(path)\n    }\n  },\n  watch: {\n    filters: {\n      handler() {\n        this.page = 1\n        this.fetchBids()\n      },\n      deep: true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.my-bids-page {\n  min-height: 110vh;\n  background: $bg-color;\n}\n\n.container {\n  padding: 20px;\n}\n\n.profile-layout {\n  display: flex;\n  gap: 30px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.sidebar {\n  width: 280px;\n  flex-shrink: 0;\n\n  .user-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    padding: 30px 20px;\n    text-align: center;\n    margin-bottom: 20px;\n\n    .avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background: $primary-color;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      margin: 0 auto 20px;\n    }\n\n    .user-info {\n      h3 {\n        color: $text-primary;\n        margin-bottom: 10px;\n      }\n\n      p {\n        color: $text-secondary;\n        margin-bottom: 15px;\n      }\n    }\n  }\n\n  .nav-menu {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 15px 20px;\n      color: $text-regular;\n      text-decoration: none;\n      border-bottom: 1px solid $border-color;\n      transition: all 0.3s;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover,\n      &.router-link-active {\n        background: $primary-color;\n        color: white;\n      }\n\n      i {\n        margin-right: 10px;\n        font-size: 16px;\n      }\n    }\n  }\n}\n\n.main-content {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: $box-shadow;\n  overflow: hidden;\n\n  .content-header {\n    padding: 30px 30px 0;\n    border-bottom: 1px solid $border-color;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    h2 {\n      color: $text-primary;\n      margin-bottom: 30px;\n    }\n\n    .filters {\n      display: flex;\n      gap: 15px;\n      margin-bottom: 30px;\n    }\n  }\n\n  .bids-content {\n    padding: 30px;\n\n    .stats-summary {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 30px;\n\n      .summary-item {\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        padding: 20px;\n        border-radius: 8px;\n        text-align: center;\n\n        .summary-number {\n          font-size: 24px;\n          font-weight: 700;\n          margin-bottom: 8px;\n        }\n\n        .summary-label {\n          font-size: 14px;\n          opacity: 0.9;\n        }\n      }\n    }\n\n    .bids-list {\n      .bid-item {\n        background: $light-gray;\n        border-radius: 8px;\n        padding: 20px;\n        margin-bottom: 20px;\n        border-left: 4px solid transparent;\n        transition: all 0.3s;\n\n        &.is-winning {\n          border-left-color: $warning-color;\n          background: linear-gradient(90deg, #fff7e6 0%, $light-gray 100%);\n        }\n\n        &.is-won {\n          border-left-color: $success-color;\n          background: linear-gradient(90deg, #f0f9f0 0%, $light-gray 100%);\n        }\n\n        .bid-project {\n          margin-bottom: 15px;\n\n          .project-title {\n            color: $text-primary;\n            margin-bottom: 10px;\n            cursor: pointer;\n            transition: color 0.3s;\n\n            &:hover {\n              color: $primary-color;\n            }\n          }\n\n          .project-meta {\n            display: flex;\n            align-items: center;\n            gap: 15px;\n\n            .category,\n            .quantity {\n              padding: 2px 8px;\n              background: white;\n              border-radius: 4px;\n              font-size: 12px;\n              color: $text-secondary;\n            }\n          }\n        }\n\n        .bid-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n\n          .bid-details {\n            .detail-item {\n              display: flex;\n              gap: 10px;\n              margin-bottom: 5px;\n\n              .label {\n                color: $text-secondary;\n                min-width: 100px;\n              }\n\n              .value {\n                font-weight: 500;\n                color: $text-primary;\n\n                &.my-bid {\n                  color: $primary-color;\n                  font-weight: 600;\n                }\n\n                &.current-price {\n                  color: $danger-color;\n                  font-weight: 600;\n                }\n              }\n            }\n          }\n\n          .bid-status {\n            .status-badge {\n              padding: 8px 12px;\n              border-radius: 20px;\n              font-size: 14px;\n              font-weight: 500;\n              background: $text-secondary;\n              color: white;\n              display: flex;\n              align-items: center;\n              gap: 5px;\n\n              &.winning {\n                background: $warning-color;\n              }\n\n              i {\n                font-size: 16px;\n              }\n            }\n          }\n        }\n\n        .bid-actions {\n          text-align: right;\n        }\n      }\n\n      .empty-state {\n        padding: 60px 0;\n        text-align: center;\n      }\n    }\n\n    .pagination {\n      margin-top: 30px;\n      text-align: center;\n    }\n  }\n}\n\n@media (max-width: $tablet) {\n  .profile-layout {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n\n    .user-card {\n      display: flex;\n      align-items: center;\n      text-align: left;\n      gap: 20px;\n\n      .avatar {\n        margin: 0;\n      }\n    }\n\n    .nav-menu {\n      display: flex;\n      overflow-x: auto;\n\n      .nav-item {\n        white-space: nowrap;\n        border-bottom: none;\n        border-right: 1px solid $border-color;\n\n        &:last-child {\n          border-right: none;\n        }\n      }\n    }\n  }\n\n  .main-content .content-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n}\n\n@media (max-width: $mobile) {\n  .container {\n    padding: 15px;\n  }\n\n  .bids-content {\n    padding: 20px !important;\n\n    .stats-summary {\n      grid-template-columns: repeat(2, 1fr);\n    }\n\n    .bids-list .bid-item .bid-info {\n      flex-direction: column;\n      align-items: flex-start;\n      gap: 15px;\n    }\n  }\n}\n</style>\n"], "mappings": ";AA2KA,SAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;MACAC,OAAA;QACAC,MAAA;QACAC,SAAA;MACA;MACAC,OAAA;MACAC,IAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAZ,UAAA;EACA;EACAa,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAD,UAAA;MACA,KAAAN,OAAA;MACA;QACA,MAAAQ,MAAA,cAAAC,MAAA,CAAAC,QAAA;UACAT,IAAA,OAAAA,IAAA;UACAC,QAAA,OAAAA,QAAA;UACA,QAAAL;QACA;QAEA,IAAAW,MAAA,CAAAG,OAAA;UACA,KAAAhB,IAAA,GAAAa,MAAA,CAAAd,IAAA,CAAAkB,IAAA;UACA,KAAAT,KAAA,GAAAK,MAAA,CAAAd,IAAA,CAAAS,KAAA;UACA,KAAAP,OAAA,GAAAY,MAAA,CAAAd,IAAA,CAAAE,OAAA;QACA;MACA,SAAAiB,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;QACA,KAAAb,OAAA;MACA;IACA;IAEA;IACAe,iBAAAC,OAAA;MACA,KAAAf,IAAA,GAAAe,OAAA;MACA,KAAAV,SAAA;IACA;IAEA;IACAW,YAAAC,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,aAAAF,SAAA;IACA;IAEA;IACAG,YAAAC,KAAA;MACA,KAAAA,KAAA,IAAAA,KAAA;MACA,OAAAC,MAAA,CAAAD,KAAA,EAAAE,cAAA;QACAC,qBAAA;QACAC,qBAAA;MACA;IACA;IACAC,mBAAAC,IAAA;MACA,KAAAT,OAAA,CAAAC,IAAA,CAAAQ,IAAA;IACA;EACA;EACAC,KAAA;IACAhC,OAAA;MACAiC,QAAA;QACA,KAAA7B,IAAA;QACA,KAAAK,SAAA;MACA;MACAyB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}