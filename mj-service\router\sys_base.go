package router

import (
	v1 "auction-sys/api/v1"

	"github.com/gin-gonic/gin"
)

func InitBaseRouter(Router *gin.RouterGroup) (R gin.IRoutes) {
	BaseRouter := Router.Group("base")
	{
		BaseRouter.POST("login", v1.Login)
		BaseRouter.POST("wxLogin", v1.AppletLogin)
		BaseRouter.POST("touristLogin", v1.AppletTouristLogin)
		BaseRouter.POST("refresh", v1.RefreshToken)            // 刷新token
		BaseRouter.POST("checkUserExists", v1.CheckUserExists) // 校验用户是否存在
		BaseRouter.POST("send", v1.SendSms)                    // 发送短信
		BaseRouter.POST("checkCaptcha", v1.CheckCaptcha)       // 校验验证码
		BaseRouter.POST("forgetPassword", v1.ForgetPassword)   // 忘记密码
		BaseRouter.POST("captcha", v1.Captcha)                 // 生成验证码
		BaseRouter.POST("reg", v1.RegUser)                     // 生成验证码
		BaseRouter.POST("uploadFile", v1.UploadFile)           // 文件上传
	}
	return BaseRouter
}
