{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\MyBids.vue?vue&type=style&index=0&id=62be72c2&lang=scss&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\profile\\MyBids.vue", "mtime": 1757558372898}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757485139209}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757485152120}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757485142383}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757485135872}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MyBids.vue"], "names": [], "mappings": ";AA8PA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MyBids.vue", "sourceRoot": "src/views/profile", "sourcesContent": ["<template>\n  <div class=\"my-bids-page\">\n    <AppHeader />\n    \n    <div class=\"container\">\n      <div class=\"profile-layout\">\n        <!-- 侧边栏 -->\n        <div class=\"sidebar\">\n          <div class=\"user-card\">\n            <div class=\"avatar\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"user-info\">\n              <h3>{{ userInfo?.name || '用户' }}</h3>\n              <p>{{ userInfo?.mobile }}</p>\n              <StatusTag :status=\"userInfo?.auditStatus\" type=\"audit\" />\n            </div>\n          </div>\n          \n          <nav class=\"nav-menu\">\n            <p @click=\"handleNavItemClick('/profile')\" class=\"nav-item\" >\n              <i class=\"el-icon-user\"></i>\n              个人资料\n            </p>\n            <p @click=\"handleNavItemClick('/profile/bids')\" class=\"nav-item router-link-active\" >\n              <i class=\"el-icon-price-tag\"></i>\n              我的出价\n            </p>\n            <p @click=\"handleNavItemClick('/profile/projects')\" class=\"nav-item\" >\n              <i class=\"el-icon-folder\"></i>\n              我的项目\n            </p>\n          </nav>\n        </div>\n\n        <!-- 主内容 -->\n        <div class=\"main-content\">\n          <div class=\"content-header\">\n            <h2>我的出价</h2>\n            <div class=\"filters\">\n              <el-select v-model=\"filters.status\" placeholder=\"项目状态\" clearable @change=\"fetchBids\">\n                <el-option label=\"即将开始\" :value=\"0\" />\n                <el-option label=\"竞价中\" :value=\"1\" />\n                <el-option label=\"已结束\" :value=\"2\" />\n                <el-option label=\"已终止\" :value=\"3\" />\n              </el-select>\n              <el-select v-model=\"filters.isWinning\" placeholder=\"出价状态\" clearable @change=\"fetchBids\">\n                <el-option label=\"领先\" :value=\"true\" />\n                <el-option label=\"被超越\" :value=\"false\" />\n              </el-select>\n            </div>\n          </div>\n\n          <div class=\"bids-content\" v-loading=\"loading\">\n            <!-- 统计卡片 -->\n            <div class=\"stats-summary\">\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.totalBids || 0 }}</div>\n                <div class=\"summary-label\">总出价次数</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.leadingBids || 0 }}</div>\n                <div class=\"summary-label\">当前领先</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">{{ summary.wonBids || 0 }}</div>\n                <div class=\"summary-label\">中标次数</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-number\">¥{{ formatMoney(summary.totalAmount) }}</div>\n                <div class=\"summary-label\">累计出价金额</div>\n              </div>\n            </div>\n\n            <!-- 出价列表 -->\n            <div class=\"bids-list\">\n              <div\n                v-for=\"bid in bids\"\n                :key=\"bid.id\"\n                class=\"bid-item\"\n                :class=\"{ 'is-winning': bid.isWinning, 'is-won': bid.isWon }\"\n              >\n                <div class=\"bid-project\">\n                  <h4 class=\"project-title\" @click=\"goToProject(bid.projectId)\">\n                    {{ bid.projectTitle }}\n                  </h4>\n                  <div class=\"project-meta\">\n                    <StatusTag :status=\"bid.projectStatus\" />\n                    <span class=\"category\">{{ bid.categoryName }}</span>\n                    <span class=\"quantity\">{{ bid.quantity }}{{ bid.unit }}</span>\n                  </div>\n                </div>\n\n                <div class=\"bid-info\">\n                  <div class=\"bid-details\">\n                    <div class=\"detail-item\">\n                      <span class=\"label\">我的出价：</span>\n                      <span class=\"value my-bid\">¥{{ formatMoney(bid.bidAmount) }}</span>\n                    </div>\n                    <div class=\"detail-item\">\n                      <span class=\"label\">当前最高价：</span>\n                      <span class=\"value current-price\">¥{{ formatMoney(bid.currentPrice) }}</span>\n                    </div>\n                    <div class=\"detail-item\">\n                      <span class=\"label\">出价时间：</span>\n                      <span class=\"value\">{{ bid.bidTime | formatTime }}</span>\n                    </div>\n                  </div>\n\n                  <div class=\"bid-status\">\n                    <div class=\"status-badge\" v-if=\"bid.isWon\">\n                      <i class=\"el-icon-trophy\"></i>\n                      中标\n                    </div>\n                    <div class=\"status-badge winning\" v-else-if=\"bid.isWinning\">\n                      <i class=\"el-icon-star-on\"></i>\n                      领先\n                    </div>\n                    <div class=\"status-badge\" v-else>\n                      <i class=\"el-icon-star-off\"></i>\n                      被超越\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"bid-actions\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"goToProject(bid.projectId)\"\n                    v-if=\"bid.projectStatus === 1\"\n                  >\n                    继续出价\n                  </el-button>\n                  <el-button\n                    size=\"small\"\n                    @click=\"goToProject(bid.projectId)\"\n                  >\n                    查看详情\n                  </el-button>\n                </div>\n              </div>\n\n              <!-- 空状态 -->\n              <div v-if=\"bids.length === 0 && !loading\" class=\"empty-state\">\n                <el-empty description=\"暂无出价记录\">\n                  <el-button type=\"primary\" @click=\"$router.push('/home')\">\n                    去竞价\n                  </el-button>\n                </el-empty>\n              </div>\n            </div>\n\n            <!-- 分页 -->\n            <div class=\"pagination\" v-if=\"total > 0\">\n              <el-pagination\n                @current-change=\"handlePageChange\"\n                :current-page=\"page\"\n                :page-size=\"pageSize\"\n                :total=\"total\"\n                layout=\"total, prev, pager, next, jumper\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'MyBids',\n  data() {\n    return {\n      bids: [],\n      summary: {},\n      filters: {\n        status: '',\n        isWinning: ''\n      },\n      loading: false,\n      page: 1,\n      pageSize: 10,\n      total: 0\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['userInfo'])\n  },\n  mounted() {\n    this.fetchBids()\n  },\n  methods: {\n    // 获取出价记录\n    async fetchBids() {\n      this.loading = true\n      try {\n        const result = await this.$store.dispatch('user/fetchMyBids', {\n          page: this.page,\n          pageSize: this.pageSize,\n          ...this.filters\n        })\n        \n        if (result.success) {\n          this.bids = result.data.list || []\n          this.total = result.data.total || 0\n          this.summary = result.data.summary || {}\n        }\n      } catch (error) {\n        console.error('获取出价记录失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 页码变化\n    handlePageChange(newPage) {\n      this.page = newPage\n      this.fetchBids()\n    },\n\n    // 跳转到项目详情\n    goToProject(projectId) {\n      this.$router.push(`/auction/${projectId}`)\n    },\n\n    // 格式化金额\n    formatMoney(value) {\n      if (!value && value !== 0) return '0'\n      return Number(value).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })\n    },\n    handleNavItemClick(path) {\n      this.$router.push(path)\n    }\n  },\n  watch: {\n    filters: {\n      handler() {\n        this.page = 1\n        this.fetchBids()\n      },\n      deep: true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.my-bids-page {\n  min-height: 110vh;\n  background: $bg-color;\n}\n\n.container {\n  padding: 20px;\n}\n\n.profile-layout {\n  display: flex;\n  gap: 30px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.sidebar {\n  width: 280px;\n  flex-shrink: 0;\n\n  .user-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    padding: 30px 20px;\n    text-align: center;\n    margin-bottom: 20px;\n\n    .avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background: $primary-color;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32px;\n      margin: 0 auto 20px;\n    }\n\n    .user-info {\n      h3 {\n        color: $text-primary;\n        margin-bottom: 10px;\n      }\n\n      p {\n        color: $text-secondary;\n        margin-bottom: 15px;\n      }\n    }\n  }\n\n  .nav-menu {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 15px 20px;\n      color: $text-regular;\n      text-decoration: none;\n      border-bottom: 1px solid $border-color;\n      transition: all 0.3s;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover,\n      &.router-link-active {\n        background: $primary-color;\n        color: white;\n      }\n\n      i {\n        margin-right: 10px;\n        font-size: 16px;\n      }\n    }\n  }\n}\n\n.main-content {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: $box-shadow;\n  overflow: hidden;\n\n  .content-header {\n    padding: 30px 30px 0;\n    border-bottom: 1px solid $border-color;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    h2 {\n      color: $text-primary;\n      margin-bottom: 30px;\n    }\n\n    .filters {\n      display: flex;\n      gap: 15px;\n      margin-bottom: 30px;\n    }\n  }\n\n  .bids-content {\n    padding: 30px;\n\n    .stats-summary {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 30px;\n\n      .summary-item {\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        padding: 20px;\n        border-radius: 8px;\n        text-align: center;\n\n        .summary-number {\n          font-size: 24px;\n          font-weight: 700;\n          margin-bottom: 8px;\n        }\n\n        .summary-label {\n          font-size: 14px;\n          opacity: 0.9;\n        }\n      }\n    }\n\n    .bids-list {\n      .bid-item {\n        background: $light-gray;\n        border-radius: 8px;\n        padding: 20px;\n        margin-bottom: 20px;\n        border-left: 4px solid transparent;\n        transition: all 0.3s;\n\n        &.is-winning {\n          border-left-color: $warning-color;\n          background: linear-gradient(90deg, #fff7e6 0%, $light-gray 100%);\n        }\n\n        &.is-won {\n          border-left-color: $success-color;\n          background: linear-gradient(90deg, #f0f9f0 0%, $light-gray 100%);\n        }\n\n        .bid-project {\n          margin-bottom: 15px;\n\n          .project-title {\n            color: $text-primary;\n            margin-bottom: 10px;\n            cursor: pointer;\n            transition: color 0.3s;\n\n            &:hover {\n              color: $primary-color;\n            }\n          }\n\n          .project-meta {\n            display: flex;\n            align-items: center;\n            gap: 15px;\n\n            .category,\n            .quantity {\n              padding: 2px 8px;\n              background: white;\n              border-radius: 4px;\n              font-size: 12px;\n              color: $text-secondary;\n            }\n          }\n        }\n\n        .bid-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n\n          .bid-details {\n            .detail-item {\n              display: flex;\n              gap: 10px;\n              margin-bottom: 5px;\n\n              .label {\n                color: $text-secondary;\n                min-width: 100px;\n              }\n\n              .value {\n                font-weight: 500;\n                color: $text-primary;\n\n                &.my-bid {\n                  color: $primary-color;\n                  font-weight: 600;\n                }\n\n                &.current-price {\n                  color: $danger-color;\n                  font-weight: 600;\n                }\n              }\n            }\n          }\n\n          .bid-status {\n            .status-badge {\n              padding: 8px 12px;\n              border-radius: 20px;\n              font-size: 14px;\n              font-weight: 500;\n              background: $text-secondary;\n              color: white;\n              display: flex;\n              align-items: center;\n              gap: 5px;\n\n              &.winning {\n                background: $warning-color;\n              }\n\n              i {\n                font-size: 16px;\n              }\n            }\n          }\n        }\n\n        .bid-actions {\n          text-align: right;\n        }\n      }\n\n      .empty-state {\n        padding: 60px 0;\n        text-align: center;\n      }\n    }\n\n    .pagination {\n      margin-top: 30px;\n      text-align: center;\n    }\n  }\n}\n\n@media (max-width: $tablet) {\n  .profile-layout {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n\n    .user-card {\n      display: flex;\n      align-items: center;\n      text-align: left;\n      gap: 20px;\n\n      .avatar {\n        margin: 0;\n      }\n    }\n\n    .nav-menu {\n      display: flex;\n      overflow-x: auto;\n\n      .nav-item {\n        white-space: nowrap;\n        border-bottom: none;\n        border-right: 1px solid $border-color;\n\n        &:last-child {\n          border-right: none;\n        }\n      }\n    }\n  }\n\n  .main-content .content-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n}\n\n@media (max-width: $mobile) {\n  .container {\n    padding: 15px;\n  }\n\n  .bids-content {\n    padding: 20px !important;\n\n    .stats-summary {\n      grid-template-columns: repeat(2, 1fr);\n    }\n\n    .bids-list .bid-item .bid-info {\n      flex-direction: column;\n      align-items: flex-start;\n      gap: 15px;\n    }\n  }\n}\n</style>\n"]}]}