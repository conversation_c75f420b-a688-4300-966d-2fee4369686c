/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-09-01 14:29:26
 * @LastEditors: dlg
 * @LastEditTime: 2020-09-01 14:32:21
 */
import request from '@/utils/request'

/**
 * @description: 获取个人信息
 * @param {type}
 * @return {type}
 */
export function getAllCategory() {
    return request({
        url: '/admin/user/getPersonInfo',
        method: 'post',
    })
}

/**
 * @description: 更新个人信息
 * @param {type}
 * @return {type}
 */
export function updatePersonInfo(data) {
    return request({
        url: '/admin/user/updatePersonInfo',
        method: 'post',
        data
    })
}

/**
 * @description: 用户修改密码
 * @param {type}
 * @return {type}
 */
export function changePassword(data) {
    return request({
        url: '/admin/user/changePassword',
        method: 'post',
        data
    })
}