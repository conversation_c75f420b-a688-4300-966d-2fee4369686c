<template>
  <div class="login-container">
    <div class="login-title">
    </div>
    <div class="login-forms">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        auto-complete="on"
      >
        <!-- 系统名称 -->
        <div class="title-container">
          <h3 class="title">欢迎登录</h3>
        </div>

        <!-- 账户 -->
        <el-form-item prop="username">
          <span class="svg-container">
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-username" />
            </svg>
          </span>
          <el-input
            type="text"
            v-model="loginForm.username"
            placeholder="账号"
            name="username"
            autocomplete="off"
            maxlength="20"
            @input="inputAccount"
          />
        </el-form-item>
        <!-- 密码 -->
        <el-form-item prop="password">
          <span class="svg-container">
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-password" />
            </svg>
          </span>
          <el-input
            :type="passwordType"
            v-model="loginForm.password"
            placeholder="密码"
            name="password"
            autocomplete="on"
            maxlength="20"
          />
          <span class="show-pwd" @click="showPwd">
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-eye" />
            </svg>
          </span>
        </el-form-item>

        <!-- 验证码 -->
        <el-form-item
          style="width: 60%; display: inline-block; padding-left: 6px"
        >
          <el-input
            v-model="loginForm.captcha"
            placeholder="验证码"
            maxlength="10"
          />
        </el-form-item>
        <div class="vPic">
          <img
            v-if="picPath"
            :src="picPath"
            width="100%"
            height="100%"
            alt="请输入验证码"
            @click="loginVefify()"
          >
        </div>

        <!-- 登陆按钮 -->
        <el-button
          :loading="loading"
          type="primary"
          style="width: 100%; margin-top: 30px"
          @click.native.prevent="handleLogin"
        >登录</el-button
        >
        <div class="forgetPass">
          <router-link
            tag="a"
            target="_blank"
            :to="{ path: '/forgetPass' }"
            replace
          >忘记密码? </router-link>
        </div>
      </el-form>
    </div>
    <div class="login-footer">
      <p>
        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_block">新ICP备2021000729号-1</a>
        <a href="">&nbsp;&nbsp;新公网安备:66010002000128</a>
      </p>
    </div>
  </div>
</template>

<script>
import { captcha } from "@/api/login";

export default {
  name: "Login",
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("账号不能为空"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("密码不能少于6位"));
      } else {
        callback();
      }
    };
    return {
      loginForm: {
        username: "",
        password: "",
        captcha: "",
        captchaId: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", validator: validateUsername },
        ],
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
      },
      passwordType: "password",
      loading: false,
      picPath: "",
      // redirect: undefined
      websock:null,
      loginKey:"",
    };
  },
  created() {
    this.loginVefify();
    let _self = this;
    document.onkeydown = function () {
      let key = window.event.keyCode
        ? window.event.keyCode
        : window.event.which;
      if (key === 13) {
        _self.handleLogin(); // 调用登录方法
      }
    };

  },
  destroyed() {
    document.onkeydown = null;
  },
  // watch: {
  //   $route: {
  //     handler: function(route) {
  //       // 在退出系统的时候，会监听到 $route 的变化，会通过query参数获取拿到退出系统时当时那个页面的路由，并缓存在 this.redirect 中，
  //       // 重新再次登录的时候，登陆成功的时候，路由会默认跳转到 this.redirect 所指向的路由页面
  //       this.redirect = route.query && route.query.redirect;
  //     },
  //     immediate: true // 该回调将会在侦听开始之后被立即调用
  //   }
  // },
  methods: {
    inputAccount(val) {
      this.loginForm.username = val
        .toString()
        .replace(/[^\u4E00-\u9FA5A-Za-z0-9]/, "");
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (!this.loginForm.captcha) {
            this.$message({
              message: "验证码不能为空",
              type: "error",
            });
            return;
          }
          this.loading = true;

          //初始化 websocket 读取信息 ,需要UKEY
          //this.initWebSocket();

          //直接登录不需要UEKY
           this.doLogin()
        }
      });
    },

    doLogin(){
      this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.loading = false;
              // 系统正常退出登录时，会跳转到 this.redirect ，否则跳转到 '/'
              // this.$router.push({ path: this.redirect || "/" });

              // 为了保证登出系统后，重新登录的用户权限没有登出之前所在页面的权限时，出现登录跳转到401或404页面情况。
              // (正常来说每次重新进入系统应该是初始化状态，不应该默认跳到上次退出系统时的页面)
              this.loginForm.password=""
              this.$router.push({ path: "/" });
            })
            .catch((err) => {
              this.loading = false;
              this.$message({
                showClose: true,
                message: err,
                type: "error",
              });
              this.loginForm.password="";
              this.loginVefify();
            });
    },
    // 获取验证码
    loginVefify() {
      captcha().then((res) => {
        if (res.data.code == 200) {
          this.picPath = res.data.data.picPath;
          this.loginForm.captchaId = res.data.data.captchaId;
        } else {
          this.$message({
            message: res.data.msg,
            type: "error",
          });
        }
      });
    },
    initWebSocket(){ //初始化weosocket
      const wsuri = "ws://127.0.0.1:18765/";
      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen(){ //连接建立之后执行send方法发送数据
      this.websock.send(this.loginForm.password);
    },
    websocketonerror(){//连接建立失败
      this.$refs.startServer.click();
      this.loading = false;
      console.log("插件未启动！")
    },
    websocketonmessage(e){ //数据接收

      if (e.data=="-1"){
        this.$message({
            message: "请插入U盾",
            type: "error",
          });
        this.loading = false;
         return ;
      }
      if(e.data== "-2"){
        this.$message({
            message: "U盾不匹配",
            type: "error",
          });
        this.loading = false;
         return ;
      }
      this.loginForm.password=e.data;
      this.doLogin()
      console.log(this.loginKey);
      this.websock.close();
    },
    websocketclose(){  //关闭
      //console.log('连接断开');
    },
  },
};
</script>

<style lang="scss">
// 重置element-ui样式
.login-container {
  .el-input {
    display: inline-block;
    height: 48px;
    width: 85%;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 2px 12px 2px;
      color: #fff;
      height: 48px;
      caret-color: #fff; // 修改光标颜色
      &:-webkit-autofill {
        -webkit-box-shadow: 0 0 0px 1000px #282e42 inset !important;
        -webkit-text-fill-color: #fff !important;
      }
    }
  }
  .el-form-item {
    border-bottom: 1px solid #404657;
  }
  .el-form-item__content {
    line-height: 32px;
  }
  // 重置IE10+ 浏览器输入框默认的图标
  input::-ms-reveal {
    display: none;
  }
  input::-ms-clear {
    display: none;
  }
}
</style>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  width: 100%;
  background-color: #1a1f30;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(../../assets/images/loginBg/loginBg.png);
  overflow: hidden;
  .login-form {
    position: relative;
    width: 380px;
    max-width: 100%;
    transform: translateX(-60%);
    padding: 20px 30px;
    overflow: hidden;
    background-color: rgba($color: #000000, $alpha: .7);
    border-radius: 4px;
  }
  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      color: #fff;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }
  .svg-container {
    padding: 6px 5px 10px 12px;
    color: #495065;
    vertical-align: middle;
    width: 32px;
    display: inline-block;
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: #889aa4;
    cursor: pointer;
    user-select: none;
  }
}
.login-title {
  padding-top: 130px;
  text-align: center;
  color: #000;
  font-size: 40px;
  img{
    height: 100px;
    width: 100px;
    vertical-align: middle;
  }
}
.login-forms {
  width: 100%;
  height: calc(100% - 126px);
  display: flex;
  align-items: center;
  justify-content: center;
}
.vPic {
  width: 34%;
  height: 48px;
  float: right !important;
  background: #ccc;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.forgetPass {
  float: right;
  padding: 10px 0;
  color: #c0c4cc;
  font-size: 14px;
}
.login-footer{
  position: absolute;
    bottom: 0px;
    width: 100%;
    text-align: center;
    color: #fff;
    font-size: 12px;
}
</style>
