package v1

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/global/response"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/service"
	"auction-sys/utils"

	"github.com/gin-gonic/gin"
)

// @Tags SysUser ：日志管理
// @Summary 分页获取日志列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.TLogListRequest true "分页获取日志列表"
// @Success 200 {string} string "{"code":200,"data":{TLogListResponse},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取数据失败"}"
// @Router /log/getLogList [post]
func GetLogList(c *gin.Context) {
	var logRequest req.TLogListRequest
	_ = c.ShouldBindJSON(&logRequest)
	PageVerifyErr := utils.Verify(logRequest, utils.CustomizeMap["PageVerify"])
	if PageVerifyErr != nil {
		response.FailWithMessage(PageVerifyErr.Error(), c)
		return
	}

	claims, _ := c.Get("claims")
	// 这里我们通过断言获取 claims内的所有内容
	waitUse := claims.(*req.CustomClaims)
	uuid := waitUse.UUID
	// 登录日志
	if logRequest.Tab == constants.LOG_LOGIN_LIST {
		err, list, total := service.GetLoginLogList(uuid.String(), logRequest)
		if err != nil {
			global.GVA_LOG.Error(err.Error())
			response.FailWithMessage("加载失败", c)
		} else {
			response.OkWithData(resp.TLogListResponse{
				List:        list,
				Tab:         logRequest.Tab,
				StartTime:   logRequest.StartTime,
				EndTime:     logRequest.EndTime,
				CompanyCode: logRequest.CompanyCode,
				Username:    logRequest.Username,
				Page:        logRequest.Page,
				PageSize:    logRequest.PageSize,
				Total:       total,
			}, c)
		}
	} else {
		err, list, total := service.GetOperationLogList(uuid.String(), logRequest)
		if err != nil {
			response.FailWithMessage("加载失败", c)
		} else {
			response.OkWithData(resp.TLogListResponse{
				List:        list,
				Tab:         logRequest.Tab,
				StartTime:   logRequest.StartTime,
				EndTime:     logRequest.EndTime,
				CompanyCode: logRequest.CompanyCode,
				Username:    logRequest.Username,
				Page:        logRequest.Page,
				PageSize:    logRequest.PageSize,
				Total:       total,
			}, c)
		}
	}

}
