import request from './request'

// 获取竞价项目列表
export function getAuctionList(params) {
  return request({
    url: '/auction/member/projects',
    method: 'post',
    data: params
  })
}

// 获取竞价项目详情
export function getAuctionDetail(data) {
  return request({
    url: '/auction/member/projects/detail',
    method: 'post',
    data
  })
}

// 出价
export function placeBid(data) {
  return request({
    url: '/auction/member/bid',
    method: 'post',
    data
  })
}

// 获取出价历史
export function getBidHistory(data) {
  return request({
    url: '/auction/member/participation',
    method: 'post',
    data
  })
}

// 获取商品分类（使用管理端接口）
export function getCategories() {
  return request({
    url: '/productCategoryMember/getProductCategoryCondition',
    method: 'post'
  })
}

// 获取公开竞价项目列表（无需登录，不包含价格信息）
export function getPublicAuctionList(params) {
  return request({
    url: '/auction/public/projects',
    method: 'post',
    data: params
  })
}
