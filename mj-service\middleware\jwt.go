package middleware

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/global/response"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/service"
	"errors"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 我们这里jwt鉴权取头部信息 x-token 登录时回返回token信息 这里前端需要把token存储到cookie或者本地localSstorage中 不过需要跟后端协商过期时间 可以约定刷新令牌或者重新登录
		token := c.Request.Header.Get("Authorization")
		modelToken := model.JwtBlacklist{
			Jwt: token,
		}
		if token == "" {
			response.Result(response.ERROR, gin.H{
				"reload": true,
			}, "未登录或非法访问", c)
			c.Abort()
			return
		}
		if service.IsBlacklist(token, modelToken) {
			response.Result(response.REMOTE_LOGIN, gin.H{
				"reload": true,
			}, "您的帐户异地登陆或令牌失效", c)
			c.Abort()
			return
		}
		j := NewJWT()
		// parseToken 解析token包含的信息
		claims, err := j.ParseToken(token)
		if err != nil {
			if err == TokenExpired {
				response.Result(response.INVALIDATION, gin.H{
					"reload": true,
				}, "授权已过期", c)
				c.Abort()
				return
			}
			response.Result(response.INVALIDATION, gin.H{
				"reload": true,
			}, err.Error(), c)
			c.Abort()
			return
		}
		c.Set("claims", claims)
		global.GVA_REDIS.HSet("online_"+claims.UUID.String(), "currentTime", time.Now().Add(time.Minute*30).Unix())
		global.GVA_LOG.Debug("设置30分钟后时间为：", time.Now().Add(time.Minute*30).Format(constants.TIME_FORMAT))
		c.Next()
	}
}

type JWT struct {
	SigningKey []byte
}

var (
	TokenExpired     = errors.New("Token is expired")
	TokenNotValidYet = errors.New("Token not active yet")
	TokenMalformed   = errors.New("That's not even a token")
	TokenInvalid     = errors.New("Couldn't handle this token:")
)

func NewJWT() *JWT {
	return &JWT{
		[]byte(global.GVA_CONFIG.JWT.SigningKey),
	}
}

// 创建一个token
func (j *JWT) CreateToken(claims req.CustomClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.SigningKey)
}

// 解析 token
func (j *JWT) ParseToken(tokenString string) (*req.CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &req.CustomClaims{}, func(token *jwt.Token) (i interface{}, e error) {
		return j.SigningKey, nil
	})
	if err != nil {
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, TokenMalformed
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				// Token is expired
				return nil, TokenExpired
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, TokenNotValidYet
			} else {
				return nil, TokenInvalid
			}
		}
	}
	if token != nil {
		if claims, ok := token.Claims.(*req.CustomClaims); ok && token.Valid {
			return claims, nil
		}
		return nil, TokenInvalid

	} else {
		return nil, TokenInvalid

	}

}

// 更新token
func (j *JWT) RefreshToken(tokenString string, hour time.Duration) (string, error) {
	jwt.TimeFunc = func() time.Time {
		return time.Unix(0, 0)
	}
	token, err := jwt.ParseWithClaims(tokenString, &req.CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.SigningKey, nil
	})
	if err != nil {
		return "", err
	}
	if claims, ok := token.Claims.(*req.CustomClaims); ok && token.Valid {
		jwt.TimeFunc = time.Now
		claims.StandardClaims.ExpiresAt = time.Now().Add(hour * time.Hour).Unix()
		return j.CreateToken(*claims)
	}
	return "", TokenInvalid
}

func Refresh(user model.SysUser, expires int64) (token string, expiresAt int64, err error) {
	// 超时时间 + 20 分钟为最大过期时间
	ms := int64(60 * 20 * 1000)
	expires = expires + ms
	// 获取当前时间戳
	currentUnix := time.Now().Unix()
	if currentUnix > expires {
		return token, expiresAt, errors.New("请重新登录")
	}
	j := NewJWT()
	clams := req.CustomClaims{
		UUID:        user.UUID,
		ID:          user.ID,
		NickName:    user.NickName,
		CompanyCode: user.CompanyCode,
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 1000,  // 签名生效时间
			ExpiresAt: time.Now().Unix() + 60*60, // 过期时间 1个小时
			//ExpiresAt: time.Now().Unix() + 60*11,
			Issuer: "qmPlus", // 签名的发行者
		},
	}
	token, err = j.CreateToken(clams)
	if err != nil {
		return token, expiresAt, errors.New("获取token失败")
	}
	return token, clams.ExpiresAt * 1000, err
}
