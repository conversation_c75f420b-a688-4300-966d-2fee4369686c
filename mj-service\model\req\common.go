package req

// Paging common input parameter structure
type PageInfo struct {
	Page     int `json:"page" form:"page"`
	PageSize int `json:"pageSize" form:"pageSize"`
}

func (p *PageInfo) Limit() int {
	return p.PageSize
}
func (p *PageInfo) Offset() int {
	return (p.Page - 1) * p.PageSize
}

// Find by id structure
type GetById struct {
	Id        int `json:"id" form:"id"`
	ProjectId int `json:"projectId" form:"projectId"`
}

// Find by id structure
type IdsReq struct {
	Ids []int `json:"ids" form:"ids"`
}

// 微信发送的 itemValue
type ItemValue struct {
	Value string `json:"value"`
}
