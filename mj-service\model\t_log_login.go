package model

import "github.com/Gre-Z/common/jtime"

// 登录日志
type TLogLogin struct {
	ID          int            `json:"ID" gorm:"primary_key"`
	Username    string         `json:"username" gorm:"comment:'用户名'"`
	Nickname    string         `json:"nickname" gorm:"comment:'真实姓名'"`
	LoginTime   jtime.JsonTime `json:"loginTime" gorm:"comment:'登录时间'"`
	LoginMethod int8           `json:"loginMethod" gorm:"comment:'登录方式'"`
	CompanyCode string         `json:"companyCode" gorm:"comment:'企业编码'"`
}
