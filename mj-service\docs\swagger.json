{"swagger": "2.0", "info": {"description": "This is a sample Server pets", "title": "Swagger Example API", "contact": {}, "license": {}, "version": "0.0.1"}, "basePath": "/", "paths": {"/authority/createAuthority": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "创建角色", "parameters": [{"description": "创建角色", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SysAuthority"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"创建成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"创建失败\"}", "schema": {"type": "string"}}}}}, "/authority/deleteAuthority": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "删除角色", "parameters": [{"description": "删除角色", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/request.GetById"}}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"删除成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"删除失败\"}", "schema": {"type": "string"}}}}}, "/authority/getAuthBindingUsers": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "获取变更角色用户列表", "parameters": [{"description": "获取变更角色用户列表", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetById"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{authBindingUsers},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"获取失败\"}", "schema": {"type": "string"}}}}}, "/authority/getAuthBySelfCompany": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "获取自己公司的角色", "responses": {"200": {"description": "{\"code\":200,\"data\":{authList},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"加载失败\"}", "schema": {"type": "string"}}}}}, "/authority/getAuthorityAndUsers": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "获取角色和绑定的用户", "parameters": [{"description": "获取角色和绑定的用户", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetAuthAndUsers"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{BindingUsersResult},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"获取失败\"}", "schema": {"type": "string"}}}}}, "/authority/getAuthorityList": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "分页获取角色列表", "parameters": [{"description": "分页获取角色列表", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetAuthorityList"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{PageResult},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"获取数据失败\"}", "schema": {"type": "string"}}}}}, "/authority/updateAuthBindingUsers": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "更新变更角色用户列表", "parameters": [{"description": "更新变更角色用户列表", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SysAuthority"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/authority/updateAuthority": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "修改角色", "parameters": [{"description": "修改角色", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SysAuthority"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/authority/updateAuthorityStatus": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["authority ：角色管理"], "summary": "更改角色状态", "parameters": [{"description": "更改角色状态", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/model.SysAuthority"}}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/base/captcha": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["base"], "summary": "生成验证码", "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"获取成功\"}", "schema": {"type": "string"}}}}}, "/base/captcha/:captchaId": {"get": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["base"], "summary": "生成验证码图片路径", "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"获取成功\"}", "schema": {"type": "string"}}}}}, "/base/login": {"post": {"produces": ["application/json"], "tags": ["Base"], "summary": "用户登录", "parameters": [{"description": "用户登录接口", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.RegisterAndLoginStruct"}}], "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"登陆成功\"}", "schema": {"type": "string"}}}}}, "/company/addCompany": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "新增公司", "parameters": [{"description": "新增公司", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SysCompany"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"创建成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"创建失败\"}", "schema": {"type": "string"}}}}}, "/company/deleteCompany": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "删除公司", "parameters": [{"description": "删除公司", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/request.GetById"}}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"删除成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"删除失败\"}", "schema": {"type": "string"}}}}}, "/company/getCompany": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "根据id查询公司", "parameters": [{"description": "根据id查询公司", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetById"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{SysCompanyResponse},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"查询失败\"}", "schema": {"type": "string"}}}}}, "/company/getCompanyCondition": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "筛选条件栏企业数据", "responses": {"200": {"description": "{\"code\":200,\"data\":{[]response.CompanyCondition},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"加载失败\"}", "schema": {"type": "string"}}}}}, "/company/getCompanyList": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "获取公司", "parameters": [{"description": "获取公司", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetCompanyList"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{GetCompanyList},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"后去数据失败\"}", "schema": {"type": "string"}}}}}, "/company/updateCompany": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "更新公司", "parameters": [{"description": "更新公司", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SysCompany"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/company/updateCompanyStatus": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "更新公司状态", "parameters": [{"description": "更新公司状态", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/model.SysCompany"}}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/company/uploadCompanyLogo": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["company ：企业管理"], "summary": "上传公司Logo", "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"上传成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"上传失败\"}", "schema": {"type": "string"}}}}}, "/jwt/jsonInBlacklist": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jwt ：权限管理"], "summary": "jwt加入黑名单", "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"拉黑成功\"}", "schema": {"type": "string"}}}}}, "/menu/addBaseMenu": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["menu ：菜单管理"], "summary": "新增菜单", "parameters": [{"description": "新增菜单", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SysBaseMenu"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"添加成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"添加失败\"}", "schema": {"type": "string"}}}}}, "/menu/addMenuBtn": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["menu ：菜单管理"], "summary": "新增按钮", "parameters": [{"description": "新增按钮", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.AddMenuBtn"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"添加成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"添加失败\"}", "schema": {"type": "string"}}}}}, "/menu/deleteBaseMenu": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["menu ：菜单管理"], "summary": "删除菜单", "parameters": [{"description": "删除菜单", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetById"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"删除成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"删除失败\"}", "schema": {"type": "string"}}}}}, "/menu/getAuthMenus": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["menu ：菜单管理"], "summary": "根据角色id获取菜单，可多个角色", "parameters": [{"description": "根据角色id获取菜单，可多个角色", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/request.GetById"}}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{[]response.Menu},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"加载失败\"}", "schema": {"type": "string"}}}}}, "/menu/getBaseMenuById": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["menu ：菜单管理"], "summary": "根据角色id获取菜单,并赋值状态", "parameters": [{"description": "根据角色id获取菜单", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetById"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{SysAuthorityMenuResponse},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"查询失败\"}", "schema": {"type": "string"}}}}}, "/menu/getMenu": {"post": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["authorityAndMenu ：权限菜单"], "summary": "获取用户动态路由", "responses": {"200": {"description": "{\"code\":200,\"data\":{RouterMenuResponse},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"获取失败\"}", "schema": {"type": "string"}}}}}, "/menu/getMenuList": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["menu ：菜单管理"], "summary": "分页获取基础menu列表", "parameters": [{"description": "分页获取基础menu列表", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.PageInfo"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{PageResult},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"获取失败\"}", "schema": {"type": "string"}}}}}, "/menu/updateBaseMenu": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["menu ：菜单管理"], "summary": "更新菜单", "parameters": [{"description": "更新菜单", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SysBaseMenu"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/system/ReloadSystem": {"post": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["system"], "summary": "设置配置文件内容", "parameters": [{"description": "设置配置文件内容", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.System"}}], "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"返回成功\"}", "schema": {"type": "string"}}}}}, "/system/getSystemConfig": {"post": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["system"], "summary": "获取配置文件内容", "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"返回成功\"}", "schema": {"type": "string"}}}}}, "/system/setSystemConfig": {"post": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["system"], "summary": "设置配置文件内容", "parameters": [{"description": "设置配置文件内容", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.System"}}], "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"返回成功\"}", "schema": {"type": "string"}}}}}, "/user/addUser": {"post": {"produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "添加用户", "parameters": [{"description": "添加用户", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.RegisterStruct"}}], "responses": {"200": {"description": "{\"success\":true,\"data\":{},\"msg\":\"注册成功\"}", "schema": {"type": "string"}}}}}, "/user/changePassword": {"put": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "用户修改密码", "parameters": [{"description": "用户修改密码", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.ChangePasswordStruct"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"修改成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"修改失败\"}", "schema": {"type": "string"}}}}}, "/user/deleteUser": {"delete": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "删除用户", "parameters": [{"description": "删除用户", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/request.GetById"}}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"删除成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"删除失败\"}", "schema": {"type": "string"}}}}}, "/user/getUserList": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "分页获取用户列表", "parameters": [{"description": "分页获取用户列表", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.GetUserList"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{GetUserList},\"msg\":\"操作成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"获取数据失败\"}", "schema": {"type": "string"}}}}}, "/user/resetPassword": {"put": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "重置用户密码", "parameters": [{"description": "重置用户密码", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.ChangePasswordStruct"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"重置成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"重置失败\"}", "schema": {"type": "string"}}}}}, "/user/updateUser": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "更改用户", "parameters": [{"description": "更改用户", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/request.RegisterStruct"}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/user/updateUserStatus": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "更改用户状态", "parameters": [{"description": "更改用户状态", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/model.SysUser"}}}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"更新成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"更新失败\"}", "schema": {"type": "string"}}}}}, "/user/uploadHeaderImg": {"post": {"security": [{"ApiKeyAuth": []}], "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["SysUser ：用户管理"], "summary": "用户上传头像", "parameters": [{"type": "file", "description": "用户上传头像", "name": "headerImg", "in": "formData", "required": true}], "responses": {"200": {"description": "{\"code\":200,\"data\":{},\"msg\":\"上传成功\"}", "schema": {"type": "string"}}, "400": {"description": "{\"code\":400,\"data\":{},\"msg\":\"上传失败\"}", "schema": {"type": "string"}}}}}}, "definitions": {"config.Admin": {"type": "object", "properties": {"username": {"type": "string"}}}, "config.Captcha": {"type": "object", "properties": {"imgHeight": {"type": "integer"}, "imgWidth": {"type": "integer"}, "keyLong": {"type": "integer"}}}, "config.Casbin": {"type": "object", "properties": {"modelPath": {"type": "string"}}}, "config.Image": {"type": "object", "properties": {"allowExts": {"type": "array", "items": {"type": "string"}}, "height": {"type": "integer"}, "maxSize": {"type": "integer"}, "prefixUrl": {"type": "string"}, "runtimeRootPath": {"type": "string"}, "savePath": {"type": "string"}, "width": {"type": "integer"}}}, "config.JWT": {"type": "object", "properties": {"signingKey": {"type": "string"}}}, "config.Log": {"type": "object", "properties": {"file": {"type": "string"}, "logFile": {"type": "boolean"}, "prefix": {"type": "string"}, "stdout": {"type": "string"}}}, "config.Logo": {"type": "object", "properties": {"company": {"type": "string"}, "user": {"type": "string"}}}, "config.Mysql": {"type": "object", "properties": {"config": {"type": "string"}, "dbname": {"type": "string"}, "logMode": {"type": "boolean"}, "maxIdleConns": {"type": "integer"}, "maxOpenConns": {"type": "integer"}, "password": {"type": "string"}, "path": {"type": "string"}, "username": {"type": "string"}}}, "config.Qiniu": {"type": "object", "properties": {"accessKey": {"type": "string"}, "bucket": {"type": "string"}, "imgPath": {"type": "string"}, "secretKey": {"type": "string"}}}, "config.Redis": {"type": "object", "properties": {"addr": {"type": "string"}, "db": {"type": "integer"}, "password": {"type": "string"}}}, "config.Server": {"type": "object", "properties": {"admin": {"type": "object", "$ref": "#/definitions/config.Admin"}, "captcha": {"type": "object", "$ref": "#/definitions/config.Captcha"}, "casbin": {"type": "object", "$ref": "#/definitions/config.Casbin"}, "image": {"type": "object", "$ref": "#/definitions/config.Image"}, "jwt": {"type": "object", "$ref": "#/definitions/config.JWT"}, "log": {"type": "object", "$ref": "#/definitions/config.Log"}, "logo": {"type": "object", "$ref": "#/definitions/config.Logo"}, "mysql": {"type": "object", "$ref": "#/definitions/config.Mysql"}, "qiniu": {"type": "object", "$ref": "#/definitions/config.Qiniu"}, "redis": {"type": "object", "$ref": "#/definitions/config.Redis"}, "sqlite": {"type": "object", "$ref": "#/definitions/config.Sqlite"}, "system": {"type": "object", "$ref": "#/definitions/config.System"}}}, "config.Sqlite": {"type": "object", "properties": {"config": {"type": "string"}, "logMode": {"type": "boolean"}, "password": {"type": "string"}, "path": {"type": "string"}, "username": {"type": "string"}}}, "config.System": {"type": "object", "properties": {"addr": {"type": "integer"}, "dbType": {"type": "string"}, "env": {"type": "string"}, "useMultipoint": {"type": "boolean"}}}, "model.SysAuthority": {"type": "object", "properties": {"authorityName": {"description": "角色名", "type": "string"}, "authorityType": {"description": "角色类别 0：系统开发管理员 1：企业管理员 2：普通用户", "type": "integer"}, "bindingUsers": {"description": "角色绑定用户数", "type": "integer"}, "companyCode": {"description": "企业标识，企业信用代码", "type": "string"}, "description": {"description": "角色描述", "type": "string"}, "menus": {"description": "菜单对象", "type": "array", "items": {"$ref": "#/definitions/model.SysBaseMenu"}}, "status": {"description": "角色状态 1：启用 0：禁用", "type": "integer"}, "users": {"description": "用户对象", "type": "array", "items": {"$ref": "#/definitions/model.SysUser"}}}}, "model.SysBaseMenu": {"type": "object", "properties": {"authoritys": {"description": "角色数组", "type": "array", "items": {"$ref": "#/definitions/model.SysAuthority"}}, "category": {"description": "菜单类别 菜单：0  按钮：1", "type": "integer"}, "children": {"description": "子级菜单数组", "type": "array", "items": {"$ref": "#/definitions/model.SysBaseMenu"}}, "component": {"description": "前端文件路径", "type": "string"}, "defaultMenu": {"description": "是否是基础路由", "type": "boolean"}, "hidden": {"description": "是否隐藏", "type": "boolean"}, "icon": {"description": "菜单图标", "type": "string"}, "keepAlive": {"description": "是否缓存", "type": "boolean"}, "keyval": {"description": "按钮标识", "type": "string"}, "name": {"description": "路由name", "type": "string"}, "parentId": {"description": "父菜单ID", "type": "integer"}, "path": {"description": "路由path", "type": "string"}, "sort": {"description": "排序标记", "type": "integer"}, "title": {"description": "菜单名", "type": "string"}}}, "model.SysCompany": {"type": "object", "properties": {"address": {"description": "企业地址", "type": "string"}, "adminUser": {"description": "企业管理员账号", "type": "string"}, "code": {"description": "企业编码，信用代码", "type": "string"}, "email": {"description": "企业邮箱", "type": "string"}, "logoPreviewPath": {"description": "企业logo浏览地址", "type": "string"}, "logoSavePath": {"description": "企业logo存储地址", "type": "string"}, "name": {"description": "企业名称", "type": "string"}, "personLiable": {"description": "责任人", "type": "string"}, "postcode": {"type": "integer"}, "status": {"description": "企业状态 1:启用 0:禁用", "type": "integer"}, "telephone": {"description": "企业手机号", "type": "string"}}}, "model.SysUser": {"type": "object", "properties": {"authoritys": {"description": "角色对象", "type": "array", "items": {"$ref": "#/definitions/model.SysAuthority"}}, "companyCode": {"description": "企业编码，唯一标识", "type": "string"}, "email": {"description": "邮箱", "type": "string"}, "headerImg": {"description": "用户浏览头像路径", "type": "string"}, "headerImgSave": {"description": "头像存储路径", "type": "string"}, "loginStatus": {"description": "登录状态 1：在线 0：离线", "type": "integer"}, "loginTime": {"description": "最后登录时间", "type": "string"}, "nickName": {"description": "真实姓名", "type": "string"}, "passwordStatus": {"description": "密码状态 1：不用修改  0：必须修改", "type": "integer"}, "status": {"description": "用户状态 1:启用 0:禁用", "type": "integer"}, "userName": {"description": "用户登录名", "type": "string"}, "uuid": {"description": "UUID", "type": "string"}}}, "model.System": {"type": "object", "properties": {"config": {"type": "object", "$ref": "#/definitions/config.Server"}}}, "request.AddMenuBtn": {"type": "object", "properties": {"category": {"description": "类别", "type": "integer"}, "createdAt": {"type": "string"}, "hidden": {"description": "是否隐藏", "type": "boolean"}, "id": {"type": "integer"}, "keyval": {"description": "按钮唯一标识", "type": "string"}, "parentId": {"description": "父级id", "type": "integer"}, "title": {"description": "按钮名称", "type": "string"}}}, "request.ChangePasswordStruct": {"type": "object", "properties": {"id": {"description": "用户id（重置密码时使用）", "type": "integer"}, "newPassword": {"description": "新密码", "type": "string"}, "password": {"description": "旧密码", "type": "string"}}}, "request.GetAuthAndUsers": {"type": "object", "properties": {"id": {"description": "角色id", "type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "username": {"description": "用户名", "type": "string"}}}, "request.GetAuthorityList": {"type": "object", "properties": {"name": {"type": "string"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}}}, "request.GetById": {"type": "object", "properties": {"id": {"type": "number"}}}, "request.GetCompanyList": {"type": "object", "properties": {"code": {"description": "企业编码，信用代码", "type": "string"}, "name": {"description": "企业名称", "type": "string"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}}}, "request.GetUserList": {"type": "object", "properties": {"companyCode": {"description": "企业编码", "type": "string"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "username": {"description": "用户名", "type": "string"}}}, "request.PageInfo": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer"}}}, "request.RegisterAndLoginStruct": {"type": "object", "properties": {"captcha": {"description": "暂时不用", "type": "string"}, "captchaId": {"description": "暂时不用", "type": "string"}, "password": {"description": "面膜", "type": "string"}, "username": {"description": "用户名", "type": "string"}}}, "request.RegisterStruct": {"type": "object", "properties": {"authorityIds": {"description": "角色id数组", "type": "array", "items": {"type": "integer"}}, "email": {"description": "邮箱", "type": "string"}, "headerImg": {"description": "头像", "type": "string"}, "id": {"type": "integer"}, "nickName": {"description": "真实姓名", "type": "string"}, "passWord": {"description": "密码", "type": "string"}, "status": {"description": "状态 1：启用 0：禁用", "type": "integer"}, "userName": {"description": "用户登录名", "type": "string"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-token", "in": "header"}}}