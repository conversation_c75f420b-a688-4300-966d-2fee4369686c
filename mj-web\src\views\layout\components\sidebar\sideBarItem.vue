<template>
  <div v-if="!item.hidden">
    <!-- 路由目录层级只有一级时 -->
    <!-- 此处判断为两种情况，1.判断出要显示的子路由个数为1个时 2.判断出要显示的子路由个数为0个时，均判断为一个层级来渲染 -->
    <template
      v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren)"
    >
      <!-- isNest 用来控制item 无子节点时的 item 项 title 前面的 icon 图标在切换展示-隐藏之间状态时的显示位置 -->
      <el-menu-item
        v-if="onlyOneChild.meta"
        :index="resolvePath(onlyOneChild.path)"
        :class="{'submenu-title-noDropdown':!isNest}"
        @click="toRouterView(resolvePath(onlyOneChild.path))"
      >
        <svg class="svg-icon" aria-hidden="true">
          <use
            :xlink:href="`#icon-${ onlyOneChild.meta.icon ? onlyOneChild.meta.icon : item.meta.icon }`"
          />
        </svg>
        <span class="item-title" slot="title">{{ onlyOneChild.meta.title }}</span>
      </el-menu-item>
    </template>

    <!-- 路由目录层级嵌套大于或等于2级时 -->
    <el-submenu v-else ref="submenu" :index="resolvePath(item.path)">
      <!-- 第一级路由 -->
      <template slot="title">
        <svg class="svg-icon" aria-hidden="true">
          <use :xlink:href="`#icon-${ item.meta.icon }`" />
        </svg>
        <span class="item-title">{{ item.meta.title }}</span>
      </template>
      <!-- 第二级路由 -->
      <div v-for="child in item.children" :key="child.path">
        <template v-if="!child.hidden">
          <!-- 路由目录层级嵌套大于2层级时，采用自身递归调用 -->
          <!-- 递归组件必须要有 name -->
          <!-- :is-nest="true" 表示超过 2 级菜单时，2级菜单自身前面的icon图标样式是sidebar展开时的样式 -->
          <side-bar-item
            v-if="child.children && child.children.length>0"
            :is-nest="true"
            :item="child"
            :key="child.path"
            :base-path="resolvePath(child.path)"
            class="menu-ul"
          />
          <!-- 路由目录层级嵌套为2级时 -->
          <el-menu-item
            v-else
            :index="resolvePath(child.path)"
            :key="child.path"
            @click="toRouterView(resolvePath(child.path))"
          >
            <svg class="svg-icon" aria-hidden="true">
              <use :xlink:href="`#icon-${ child.meta.icon }`" />
            </svg>
            <span class="item-title" slot="title">{{ child.meta.title }}</span>
          </el-menu-item>
        </template>
      </div>
    </el-submenu>
  </div>
</template>

<script>
import path from "path";

export default {
  name: "SideBarItem",
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      onlyOneChild: null //临时存储单个子路由对象
    };
  },
  methods: {
    // 判断路由层级大小
    hasOneShowingChild(children, parent) {
      if (children==null){
          children=[];
      }
      //定义一个子路由判断结果的缓存数组showingChildren
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          //默认不显示子路由，则返回一个空数组
          return false;
        } else {
          // 如果显示子路由，则将当前此子路由保存至onlyOneChild，并返回包含所有符合条件的子路由组成的一个数组
          this.onlyOneChild = item;
          return true;
        }
      });
      // 以下是对上面的缓存数组 showingChildren 的长度进行判断，根据 showingChildren.length 的值就可以知道要显示的路由
      // 当只有一个子路由时，这个子路由被作为默认显示
      if (showingChildren.length === 1) {
        return true;
      }

      // 如果没有子路由，则显示当前父路由
      if (showingChildren.length === 0) {
        // 保存当前父路由到onlyOneChild，noShowingChildren参数设置表示不显示子路由
        this.onlyOneChild = { ...parent, path: "", noShowingChildren: true };
        return true;
      }
      // 如果父路由下要显示的子路由超过1个，则返回false，即判断为路由层级嵌套大于一级层级嵌套
      return false;
    },
    // 利用node核心模块path模块处理拼接当前path
    resolvePath(routePath) {
      return path.resolve(this.basePath, routePath);
    },
    // 路由跳转(编程式路由跳转)
    toRouterView(pathRouter) {
      this.$router.push({ path: pathRouter });
    }
  }
};
</script>
