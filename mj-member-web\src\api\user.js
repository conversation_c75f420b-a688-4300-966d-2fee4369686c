import request from './request'

// 获取我的出价记录
export function getMyBids(params) {
  return request({
    url: '/auction/member/participation',
    method: 'post',
    data: params
  })
}

// 获取我参与的项目
export function getMyProjects(params) {
  return request({
    url: '/auction/member/participation',
    method: 'post',
    data: params
  })
}

// 更新个人资料
export function updateProfile(data) {
  return request({
    url: '/auction/member/profile',
    method: 'put',
    data
  })
}

// 获取个人统计信息
export function getMyStats() {
  return request({
    url: '/auction/member/stats',
    method: 'get'
  })
}
