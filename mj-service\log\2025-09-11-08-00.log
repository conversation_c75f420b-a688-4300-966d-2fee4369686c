[BUG_MONITOR]2025/09/11 - 10:54:12.891 E:/pywsp/auction-sys/mj-service/initialize/db_table.go:24 ▶ [DEBUG] register table success
[BUG_MONITOR]2025/09/11 - 10:54:12.910 E:/pywsp/auction-sys/mj-service/initialize/redis.go:20 ▶ [INFO] redis connect ping response: PONG
[BUG_MONITOR]2025/09/11 - 10:54:12.911 E:/pywsp/auction-sys/mj-service/initialize/router.go:21 ▶ [DEBUG] use middleware logger
[BUG_MONITOR]2025/09/11 - 10:54:12.911 E:/pywsp/auction-sys/mj-service/initialize/router.go:24 ▶ [DEBUG] use middleware cors
[BUG_MONITOR]2025/09/11 - 10:54:12.912 E:/pywsp/auction-sys/mj-service/initialize/router.go:26 ▶ [DEBUG] register swagger handler
[BUG_MONITOR]2025/09/11 - 10:54:12.921 E:/pywsp/auction-sys/mj-service/initialize/router.go:41 ▶ [INFO] router register success
[BUG_MONITOR]2025/09/11 - 10:54:12.922 E:/pywsp/auction-sys/mj-service/core/server.go:35 ▶ [DEBUG] server run success on  :8888
