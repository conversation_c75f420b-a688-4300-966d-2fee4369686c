{"go.buildOnSave": "workspace", "go.lintOnSave": "package", "go.vetOnSave": "package", "go.buildTags": "", "go.buildFlags": [], "go.lintFlags": [], "go.vetFlags": [], "go.coverOnSave": true, "go.useCodeSnippetsOnFunctionSuggest": true, "go.formatTool": "goreturns", "go.gocodeAutoBuild": true, "go.gocodePackageLookupMode": "go", "go.autocompleteUnimportedPackages": true, "go.gotoSymbol.includeImports": true, "go.inferGopath": true, "files.autoSave": "onFocusChange", "window.zoomLevel": 0, "files.autoGuessEncoding": true, "editor.mouseWheelZoom": true, "terminal.integrated.rendererType": "dom", "editor.wordWrap": "on", "editor.fontSize": 18}