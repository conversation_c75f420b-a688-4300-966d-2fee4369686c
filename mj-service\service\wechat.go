// @Title 微信相关
// @Description 请填写文件描述
// <AUTHOR> 2020/11/27 18:14

package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/utils"
	"auction-sys/wechat"
	"errors"

	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
)

// @Title 解密并保存（更新）用户信息
// @Description 函数的详细描述
// <AUTHOR> 2020/11/27 18:05
// @Param
// @Return
func DecryptUserInfo(encryptedData req.EncryptedData, pc *wechat.WXUserDataCrypt, openid string) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 调用微信解密接口对数据进行解密
		userInfo, err := pc.Decrypt(encryptedData.EncryptedData, encryptedData.Iv)
		if err != nil {
			global.GVA_LOG.Error(err.Error())
			return errors.New("保存失败")
		}

		// 存在，更新
		upDateMap := make(map[string]interface{})
		upDateMap["nick_name"] = userInfo.NickName
		upDateMap["avatar_url"] = userInfo.AvatarURL
		upDateMap["gender"] = userInfo.Gender
		upDateMap["country"] = userInfo.Country
		upDateMap["province"] = userInfo.Province
		upDateMap["city"] = userInfo.City
		upDateMap["language"] = userInfo.Language
		err = tx.Table("wx_user").Where("openid = ?", openid).Updates(upDateMap).Error
		if err != nil {
			global.GVA_LOG.Error(err.Error())
			return errors.New("保存失败")
		}

		return nil
	})
}

// @Title 游客登录
// @Description 函数的详细描述
// <AUTHOR> 2021/8/3 17:50
// @Param
// @Return

func AppletTouristLogin(param req.AppletTouristLogin) (err error, memberInter req.AppletLoginMember) {

	var member model.SysUser

	// 游客登录
	// 使用 code 调用 api 获取 openid
	err, result := wechat.GetUserOpenID(param.Code)
	if err != nil {
		return errors.New("网络错误"), memberInter
	}
	errCode := result["errcode"]
	if errCode != 0 && errCode != nil {
		return errors.New(result["errmsg"].(string)), memberInter
	}
	openid := result["openid"].(string)
	sessionKey := result["session_key"].(string)
	if !global.GVA_DB.Model(&model.SysUser{}).Where("open_id = ?", openid).Find(&member).RecordNotFound() {
		memberInter.UUID = member.UUID
		memberInter.NickName = member.NickName
		memberInter.LoginStatus = 1
		memberInter.Username = member.Username
		memberInter.CompanyCode = member.CompanyCode
		memberInter.ID = member.ID
		memberInter.IsFarmer = member.IsFarmer
	}
	memberInter.OpenID = openid
	memberInter.SessionKey = sessionKey

	return nil, memberInter

}

// @Title 用户登录
// @Description 函数的详细描述
// <AUTHOR> 2021/8/11 15:20
// @Param
// @Return

func AppletLogin(param req.AppletLogin) (err error, memberInter req.AppletLoginMember) {

	// 获取openid
	openid := global.GVA_REDIS.Get("_applet_" + param.Token + "_openid").Val()
	if openid == "" {

		global.GVA_LOG.Error("无法获取openid")
		return errors.New("登录失败，无法获取微信信息"), memberInter
	}

	// 获取sessionKey
	sessionKey := global.GVA_REDIS.Get("_applet_" + param.Token + "_session_key").Val()
	if sessionKey == "" {
		global.GVA_LOG.Error("无法获取sessionkey")
		return errors.New("登录失败，无法获取微信信息"), memberInter
	}

	memberInter.OpenID = openid
	memberInter.SessionKey = sessionKey

	// 授权登录
	if param.Token == "" {
		global.GVA_LOG.Error("token为空")
		return errors.New("无法获取授权"), memberInter
	}
	err, member := AuthLogin(param, openid, sessionKey)
	if err != nil {
		return err, memberInter
	}
	memberInter.ID = member.ID
	memberInter.LoginStatus = 1
	memberInter.Username = member.Username
	memberInter.UUID = member.UUID
	memberInter.CompanyCode = member.CompanyCode
	memberInter.NickName = member.NickName
	memberInter.IsFarmer = member.IsFarmer

	return nil, memberInter
}

// @Title 授权登录
// @Description 函数的详细描述
// <AUTHOR> 2021/8/3 17:07
// @Param
// @Return

func AuthLogin(param req.AppletLogin, openid, sessionKey string) (err error, member model.SysUser) {

	// 解密手机号
	phoneStruct, err := wechat.NewWXUserDataCrypt(global.GVA_CONFIG.Wechat.Appid, sessionKey).DecryptPhone(param.EncryptedData, param.Iv)
	userInfo, err := wechat.NewWXUserDataCrypt(global.GVA_CONFIG.Wechat.Appid, sessionKey).Decrypt(param.EncryptedUserInfo, param.IvUser)
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		global.GVA_LOG.Error("无法解密手机号")
		return errors.New("授权失败"), member
	}

	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 使用openid查询会员是否存在
		var flag bool

		// 使用手机号查询是否存在
		flag = global.GVA_DB.Where("username = ?", phoneStruct.PurePhoneNumber).First(&member).RecordNotFound()
		if flag {
			// 不存在，保存用户，并且
			err, member = saveMember(userInfo.NickName, phoneStruct.PurePhoneNumber, openid, tx)
			if err != nil {
				return err
			}
		} else {
			// 存在，判断手机号是否有openid，或者openid是否一致
			if member.OpenId == "" {

				// 没有绑定，更新绑定
				err = tx.Model(&model.SysUser{}).Where("username = ?", phoneStruct.PurePhoneNumber).
					Updates(map[string]interface{}{"open_id": openid}).Error
				if err != nil {
					global.GVA_LOG.Error(err.Error())
					return errors.New("登录失败")
				}
			} else if member.OpenId != openid {
				// 当前账号openid与本次不一致，绑定本次
				err = tx.Model(&model.SysUser{}).Where(
					"username = ?", phoneStruct.PurePhoneNumber).Updates(map[string]interface{}{"open_id": openid}).Error
				if err != nil {
					global.GVA_LOG.Error(err.Error())
					return errors.New("登录失败")
				}
			}
		}

		return nil
	})
	return err, member
}

func GetMemberByID(id int64) (err error, member model.SysUser) {
	err = global.GVA_DB.Where("id = ?", id).First(&member).Error
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return err, member
	}
	return nil, member
}

// @Title 保存会员
// @Description 函数的详细描述
// <AUTHOR> 2021/8/3 17:08
// @Param
// @Return

func saveMember(nickName, mobile, openid string, tx *gorm.DB) (err error, member model.SysUser) {
	var user model.SysUser
	user.Mobile = mobile
	user.Username = mobile
	user.OpenId = openid
	// 判断用户名是否注册
	var notRegister bool
	notRegister = tx.Where("username = ?", mobile).First(&user).RecordNotFound()

	// notRegister为false表明读取到了 不能注册
	if !notRegister {
		return errors.New("用户名或邮箱已被使用"), member
	} else {

		// 否则 附加uuid 密码md5简单加密 注册
		if user.Password == "" {
			user.Password = utils.MD5V([]byte(constants.PASSWORD_DEFAULT))
			user.PasswordStatus = constants.STATUS_ENABLED // 默认需要修改密码
		} else {
			user.Password = utils.MD5V([]byte(user.Password))
		}
		user.LoginStatus = constants.STATUS_DISABLED
		user.UUID = uuid.NewV4()
		// 头像赋值为默认头像
		user.HeaderImg = global.GVA_CONFIG.Logo.User
		user.CompanyCode = "91610131MA712U2W8N"
		user.Status = constants.STATUS_ENABLED
		user.IsFarmer = 1
		user.NickName = nickName
		err = tx.Create(&user).Error
		if err != nil {
			return err, user
		}
	}
	return nil, user
}
