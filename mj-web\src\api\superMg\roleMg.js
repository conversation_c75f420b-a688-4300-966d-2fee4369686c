/*
 * @Descripttion: 角色权限
 * @version:
 * @Author: dlg
 * @Date: 2020-06-19 11:10:15
 * @LastEditors: dlg
 * @LastEditTime: 2020-07-13 15:08:45
 */
import request from '@/utils/request'

/**
 * @description: 获取路由列表
 * @param {type}
 * @return: 路由列表
 */
export function getRoutes(data) {
    return request({
        url: '/admin/menu/getMenuAuthorityInit',
        method: 'post',
        data
    })
}

/**
 * @description: 获取角色列表
 * @param {type}
 * @return: 角色列表
 */
export function getRoles(data) {
    return request({
        url: '/admin/authority/getAuthorityList',
        method: 'post',
        data
    })
}

/**
 * @description:添加角色
 * @param {data:"object"}
 * @return: success or fail
 */
export function addRole(data) {
    return request({
        url: '/admin/authority/createAuthority',
        method: 'post',
        data
    })
}

/**
 * @description: 更新角色
 * @param {id:number,data:"object"}
 * @return: success or fail
 */
export function updateRole(data) {
    return request({
        url: '/admin/authority/updateAuthority',
        method: 'post',
        data
    })
}

/**
 * @description: 删除角色
 * @param {id:"number"}
 * @return: success or fail
 */
export function deleteRole(data) {
    return request({
        url: '/admin/authority/deleteAuthority',
        method: 'post',
        data
    })
}

/**
 * @description: 绑定用户列表查询
 * @param {type}
 * @return:
 */
export function getUserSearch(data) {
    return request({
        url: '/admin/authority/getAuthorityAndUsers',
        method: 'post',
        data
    })
}

/**
 * @description: 绑定用户角色权限查询
 * @param {type}
 * @return:
 */
export function getAuthSearch(data) {
    return request({
        url: '/admin/menu/getAuthMenus',
        method: 'post',
        data
    })
}

/**
 * @description: 改变状态
 * @param {type}
 * @return:
 */
export function changeStatus(data) {
    return request({
        url: '/admin/authority/updateAuthorityStatus',
        method: 'post',
        data
    })
}

/**
 * @description: 获取变更角色用户列表
 * @param {type}
 * @return:
 */
export function getChangeUserList(data) {
    return request({
        url: '/admin/authority/getAuthBindingUsers',
        method: 'post',
        data
    })
}

/**
 * @description:变更用户确认
 * @param {type}
 * @return:
 */
export function changeUpdateUser(data) {
    return request({
        url: '/admin/authority/updateAuthBindingUsers',
        method: 'post',
        data
    })
}