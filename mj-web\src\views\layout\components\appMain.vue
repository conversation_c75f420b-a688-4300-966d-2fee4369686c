<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-07-24 15:50:02
 * @LastEditors: dlg
 * @LastEditTime: 2020-08-21 17:24:39
-->
<template>
  <div class="app-main">
    <transition name="fade" mode="out-in">
      <keep-alive :include="cachedViews">
        <!-- 在不刷新页面的情况下，更新页面 -->
        <!-- 当遇到你需要刷新页面的情况，你就手动重定向页面到redirect页面，它会将页面重新redirect重定向回来，由于页面的 key 发生了变化，从而间接实现了刷新页面组件的效果。 -->
        <!-- 可以实现点击侧边标签，实现页面刷新，此处key用来标识唯一值，从而来触发前端路由请求后台并刷新页面(可根据自身需求来确定是否需要) -->
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </div>
</template>

<script>
export default {
  name: "AppMain",
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews; //计算需要缓存页面组件的路由数组
    },
    // key用来实现刷新前端页面
    key() {
      return this.$route.path;
    },
  },
};
</script>

<style scoped>
.app-main {
  /*94 = navbar + tags-view = 60 +34 */
  height: calc(100vh - 94px);
  width: 100%;
  padding: 0px 10px;
  position: relative;
  overflow: hidden;
}
</style>
