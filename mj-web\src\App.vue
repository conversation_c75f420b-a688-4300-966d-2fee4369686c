<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-08-28 15:29:36
 * @LastEditors: dlg
 * @LastEditTime: 2020-09-28 15:48:10
-->
<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import store from "@/store";
import { getToken } from "@/utils/auth";

export default {
  name: "App",
  created() {
    document.addEventListener("visibilitychange", function () {
      if (store.getters.token != getToken()) {
        location.reload();
      }
    });
  },
};
</script>

<style>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
