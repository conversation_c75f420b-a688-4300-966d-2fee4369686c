<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-08-21 10:33:29
 * @LastEditors: dlg
 * @LastEditTime: 2020-08-21 18:17:05
-->
<template>
  <div class="screenFullCls">
    <svg class="svg-icon" aria-hidden="true" @click="click">
      <use :xlink:href="`#icon-${ isFullscreen?'fullscreen-exit':'fullscreen' }`" />
    </svg>
  </div>
</template>

<script>
import screenfull from "screenfull";

export default {
  name: "Screenfull",
  data() {
    return {
      isFullscreen: false,
    };
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    this.destroy();
  },
  methods: {
    click() {
      if (!screenfull.isEnabled) {
        this.$message({
          message: "你的浏览器不支持正常全屏",
          type: "warning",
        });
        return false;
      }
      screenfull.toggle();
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
    },
    init() {
      if (screenfull.isEnabled) {
        screenfull.on("change", this.change);
      }
    },
    destroy() {
      if (screenfull.isEnabled) {
        screenfull.off("change", this.change);
      }
    },
  },
};
</script>

<style scoped>
.screenFullCls {
  display: inline-block;
  font-size: 40px;
  vertical-align: middle;
  color: #9fabce;
  margin-right: 10px;
  cursor: pointer;
}
</style>