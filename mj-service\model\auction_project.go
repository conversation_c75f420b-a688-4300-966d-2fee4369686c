package model

import (
	"github.com/Gre-Z/common/jtime"
)

// 竞价项目对象
type AuctionProject struct {
	Model
	Title              string         `json:"title" gorm:"comment:'竞价项目标题'"`
	ProductCategoryID  int            `json:"productCategoryId" gorm:"comment:'商品中类ID'"`
	ProductCategory    SysProductCategory `json:"productCategory" gorm:"foreignkey:ProductCategoryID"`
	Quantity           float64        `json:"quantity" gorm:"comment:'商品数量'"`
	Unit               string         `json:"unit" gorm:"comment:'计量单位'"`
	StartPrice         float64        `json:"startPrice" gorm:"comment:'起拍价'"`
	CurrentPrice       float64        `json:"currentPrice" gorm:"comment:'当前最高价'"`
	MinIncrement       float64        `json:"minIncrement" gorm:"comment:'最小加价幅度'"`
	StartTime          jtime.JsonTime `json:"startTime" gorm:"comment:'竞价开始时间'"`
	EndTime            jtime.JsonTime `json:"endTime" gorm:"comment:'竞价结束时间'"`
	OriginalEndTime    jtime.JsonTime `json:"originalEndTime" gorm:"comment:'原定结束时间'"`
	Owner              string         `json:"owner" gorm:"comment:'货权人'"`
	WarehouseAddress   string         `json:"warehouseAddress" gorm:"comment:'仓库地址'"`
	FeeDescription     string         `json:"feeDescription" gorm:"comment:'费用说明'"`
	Remark             string         `json:"remark" gorm:"comment:'备注'"`
	Status             int8           `json:"status" gorm:"default:0;comment:'状态(0:即将开始,1:竞价中,2:已结束,3:已终止)'"`
	WinnerMemberID     int            `json:"winnerMemberId" gorm:"comment:'竞得者会员ID'"`
	WinnerMember       SysMember      `json:"winnerMember" gorm:"foreignkey:WinnerMemberID"`
	FinalPrice         float64        `json:"finalPrice" gorm:"comment:'最终成交价'"`
	BidCount           int            `json:"bidCount" gorm:"default:0;comment:'出价次数'"`
	ViewCount          int            `json:"viewCount" gorm:"default:0;comment:'浏览次数'"`
	LastBidTime        jtime.JsonTime `json:"lastBidTime" gorm:"comment:'最后出价时间'"`
	CreateBy           int            `json:"createBy" gorm:"comment:'创建人ID'"`
	Creator            SysUser        `json:"creator" gorm:"foreignkey:CreateBy"`
}

func (AuctionProject) TableName() string {
	return "auction_projects"
}

// 检查竞价是否已开始
func (ap *AuctionProject) IsStarted() bool {
	return ap.Status >= 1
}

// 检查竞价是否已结束
func (ap *AuctionProject) IsEnded() bool {
	return ap.Status >= 2
}

// 检查竞价是否进行中
func (ap *AuctionProject) IsActive() bool {
	return ap.Status == 1
}

// 检查竞价是否被终止
func (ap *AuctionProject) IsTerminated() bool {
	return ap.Status == 3
}
