<template>
  <div class="register-page">
    <AppHeader />
    
    <div class="register-container">
      <div class="register-card">
        <div class="register-header">
          <h2>用户注册</h2>
        </div>

        <el-form
          ref="registerForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="register-form"
        >
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="form.mobile"
              placeholder="请输入手机号"
              maxlength="11"
            />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入真实姓名"
            />
          </el-form-item>

          <el-form-item label="企业名称" prop="companyName">
            <el-input
              v-model="form.companyName"
              placeholder="请输入企业名称"
            />
          </el-form-item>

          <el-form-item label="信用代码" prop="creditCode">
            <el-input
              v-model="form.creditCode"
              placeholder="信用代码"
            />
          </el-form-item>

          <el-form-item>

          </el-form-item>
          
          <div>
            <el-button
              type="primary"
              @click="handleRegister"
              :loading="loading"
              class="register-btn"
            >
              注册
            </el-button>
          </div>
          <div style="padding-top: 15px;text-align: center;">
            <el-checkbox v-model="form.agreeTerms">
              我已阅读并同意
              <a href="#" class="link">《用户协议》</a>
              和
              <a href="#" class="link">《隐私政策》</a>
            </el-checkbox>
          </div>
          <div class="login-link">
            已有账号？
            <router-link to="/login" class="link">立即登录</router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { sendSmsCode } from '@/api/auth'

export default {
  name: 'Register',
  data() {
    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.form.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    // 手机号验证
    const validateMobile = (rule, value, callback) => {
      const mobileReg = /^1[3-9]\d{9}$/
      if (!mobileReg.test(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }

    return {
      form: {
        mobile: '',
        password: '',
        confirmPassword: '',
        name: '',
        companyName: '',
        creditCode: '',
        agreeTerms: false
      },
      rules: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' }
        ],
        smsCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { min: 6, max: 6, message: '验证码长度为6位', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' }
        ],
        creditCode: [
          { required: true, message: '信用代码', trigger: 'blur' }
        ],
      },
      loading: false,
      smsLoading: false,
      smsCountdown: 0,
      smsTimer: null
    }
  },
  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  },
  methods: {
    // 发送短信验证码
    async sendSmsCode() {
      // 先验证手机号
      try {
        await this.$refs.registerForm.validateField('mobile')
      } catch (error) {
        return
      }

      this.smsLoading = true
      try {
        const response = await sendSmsCode(this.form.mobile)
        if (response.data.code === 200) {
          this.$message.success('验证码发送成功')
          this.startSmsCountdown()
        } else {
          this.$message.error(response.data.msg || '验证码发送失败')
        }
      } catch (error) {
        this.$message.error('验证码发送失败')
      } finally {
        this.smsLoading = false
      }
    },

    // 开始短信倒计时
    startSmsCountdown() {
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)
    },

    // 处理注册
    async handleRegister() {
      if (!this.form.agreeTerms) {
        this.$message.warning('请先同意用户协议和隐私政策')
        return
      }

      try {
        await this.$refs.registerForm.validate()
      } catch (error) {
        return
      }

      this.loading = true
      try {
        const result = await this.$store.dispatch('auth/register', this.form)
        if (result.success) {
          this.$message.success(result.message)
          this.$router.push('/login')
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        this.$message.error('注册失败，请稍后重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.register-page {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - #{$header-height});
  padding: 40px 20px;
}

.register-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;  
  max-width: 500px;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    color: $text-primary;
    margin-bottom: 10px;
  }

  p {
    color: $text-secondary;
    font-size: 14px;
  }
}

.register-form {
  .sms-input {
    display: flex;
    gap: 10px;

    .el-input {
      flex: 1;
    }

    .el-button {
      white-space: nowrap;
    }
  }

  .register-btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
  }

  .login-link {
    text-align: center;
    margin-top: 20px;
    color: $text-secondary;
  }

  .link {
    color: $primary-color;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: $mobile) {
  .register-card {
    padding: 30px 20px;
  }
}
</style>
