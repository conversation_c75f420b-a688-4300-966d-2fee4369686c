package v1

import (
	"auction-sys/global"
	"auction-sys/global/response"
	"auction-sys/middleware"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/service"
	"auction-sys/utils"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

// @Tags 竞价系统-会员注册
// @Summary 会员注册
// @accept application/json
// @Produce application/json
// @Param data body req.MemberRegisterReq true "会员注册"
// @Success 200 {string} string "注册成功"
// @Router /auction/member/register [post]
func MemberRegister(c *gin.Context) {
	var registerReq req.MemberRegisterReq
	_ = c.ShouldBindJSON(&registerReq)

	err := service.MemberRegister(registerReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("注册成功，请等待管理员审核", c)
	}
}

// @Tags 竞价系统-会员登录
// @Summary 会员登录
// @accept application/json
// @Produce application/json
// @Param data body req.MemberLoginReq true "会员登录"
// @Success 200 {object} resp.MemberLoginResp "登录成功"
// @Router /auction/member/login [post]
func MemberLogin(c *gin.Context) {
	var loginReq req.MemberLoginReq
	_ = c.ShouldBindJSON(&loginReq)

	// 参数验证
	LoginVerify := utils.Rules{
		"Mobile":   {utils.NotEmpty(), utils.Mobile(loginReq.Mobile)},
		"Password": {utils.NotEmpty()},
	}
	LoginVerifyErr := utils.Verify(loginReq, LoginVerify)
	if LoginVerifyErr != nil {
		response.FailWithMessage(LoginVerifyErr.Error(), c)
		return
	}

	err, loginResp := service.MemberLogin(loginReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		// 生成JWT Token
		memberTokenNext(c, loginResp)
	}
}

// 会员登录后生成JWT Token
func memberTokenNext(c *gin.Context, loginResp resp.MemberLoginResp) {
	j := &middleware.JWT{
		SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey),
	}

	claims := req.CustomClaims{
		UUID:        loginResp.Member.UUID,
		ID:          loginResp.Member.ID,
		NickName:    loginResp.Member.Name,
		CompanyCode: "", // 会员没有公司代码
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 1000,
			ExpiresAt: time.Now().Unix() + 60*60*24*7, // 7天过期
			Issuer:    "auction-sys",
		},
	}

	token, err := j.CreateToken(claims)
	if err != nil {
		response.FailWithMessage("获取token失败", c)
		return
	}

	loginResp.Token = token
	loginResp.ExpiresAt = claims.StandardClaims.ExpiresAt * 1000

	response.OkWithData(loginResp, c)
}

// @Tags 竞价系统-竞价大厅
// @Summary 获取竞价项目列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetAuctionProjectListReq true "获取竞价项目列表"
// @Success 200 {object} resp.GetAuctionProjectListResp "获取成功"
// @Router /auction/member/projects [post]
func GetMemberAuctionProjectList(c *gin.Context) {
	var listReq req.GetAuctionProjectListReq
	_ = c.ShouldBindJSON(&listReq)

	// 参数验证
	PageVerifyErr := utils.Verify(listReq, utils.CustomizeMap["PageVerify"])
	if PageVerifyErr != nil {
		response.FailWithMessage(PageVerifyErr.Error(), c)
		return
	}

	// 获取当前会员信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err, listResp := service.GetAuctionProjectList(listReq, claims.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(listResp, c)
	}
}

// @Tags 竞价系统-竞价详情
// @Summary 获取竞价项目详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetById true "获取竞价项目详情"
// @Success 200 {object} resp.AuctionProjectDetailResp "获取成功"
// @Router /auction/member/projects/detail [post]
func GetMemberAuctionProjectDetail(c *gin.Context) {
	var idReq req.GetById
	_ = c.ShouldBindJSON(&idReq)

	IdVerifyErr := utils.Verify(idReq, utils.CustomizeMap["IdVerify"])
	if IdVerifyErr != nil {
		response.FailWithMessage(IdVerifyErr.Error(), c)
		return
	}

	// 获取当前会员信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err, detail := service.GetAuctionProjectDetail(idReq.Id, claims.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(detail, c)
	}
}

// @Tags 竞价系统-出价
// @Summary 参与出价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.PlaceBidReq true "参与出价"
// @Success 200 {object} resp.PlaceBidResp "出价结果"
// @Router /auction/member/bid [post]
func PlaceBid(c *gin.Context) {
	var bidReq req.PlaceBidReq
	_ = c.ShouldBindJSON(&bidReq)

	// 参数验证
	BidVerify := utils.Rules{
		"ProjectID": {utils.NotEmpty()},
		"BidAmount": {utils.NotEmpty()},
	}
	BidVerifyErr := utils.Verify(bidReq, BidVerify)
	if BidVerifyErr != nil {
		response.FailWithMessage(BidVerifyErr.Error(), c)
		return
	}

	// 获取当前会员信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	// 获取客户端信息
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	err, bidResp := service.PlaceBid(bidReq, claims.ID, ipAddress, userAgent)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(bidResp, c)
	}
}

// @Tags 竞价系统-我的参与
// @Summary 获取我的参与记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.PageInfo true "分页信息"
// @Success 200 {object} resp.MyParticipationResp "获取成功"
// @Router /auction/member/participation [post]
func GetMyParticipation(c *gin.Context) {
	var pageInfo req.PageInfo
	_ = c.ShouldBindJSON(&pageInfo)

	// 参数验证
	PageVerifyErr := utils.Verify(pageInfo, utils.CustomizeMap["PageVerify"])
	if PageVerifyErr != nil {
		response.FailWithMessage(PageVerifyErr.Error(), c)
		return
	}

	// 获取当前会员信息
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err, participationResp := service.GetMyParticipation(claims.ID, pageInfo.Page, pageInfo.PageSize)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(participationResp, c)
	}
}

// @Tags 竞价系统-出价频率检查
// @Summary 检查出价频率限制
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body req.GetById true "项目ID"
// @Success 200 {object} map[string]interface{} "检查结果"
// @Router /auction/member/bid/check [post]
func CheckBidFrequency(c *gin.Context) {
	var idReq req.GetById
	_ = c.ShouldBindJSON(&idReq)

	IdVerifyErr := utils.Verify(idReq, utils.CustomizeMap["IdVerify"])
	if IdVerifyErr != nil {
		response.FailWithMessage(IdVerifyErr.Error(), c)
		return
	}

	canBid, remainingSeconds := service.CheckBidFrequency(idReq.Id)

	result := map[string]interface{}{
		"canBid":           canBid,
		"remainingSeconds": remainingSeconds,
	}

	response.OkWithData(result, c)
}

// @Tags 竞价系统-发送短信验证码
// @Summary 发送短信验证码
// @accept application/json
// @Produce application/json
// @Param data body map[string]string true "手机号"
// @Success 200 {string} string "发送成功"
// @Router /auction/member/sms [post]
func SendMemberSms(c *gin.Context) {
	var params map[string]string
	_ = c.ShouldBindJSON(&params)

	mobile := params["mobile"]
	if mobile == "" {
		response.FailWithMessage("手机号不能为空", c)
		return
	}

	// 验证手机号格式
	MobileVerify := utils.Rules{
		"Mobile": {utils.Mobile(mobile)},
	}
	if err := utils.Verify(map[string]string{"Mobile": mobile}, MobileVerify); err != nil {
		response.FailWithMessage("手机号格式不正确", c)
		return
	}

	if err := service.SendSms(mobile); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("发送成功", c)
	}
}

// @Tags 竞价系统-公开接口
// @Summary 获取公开竞价项目列表（无需登录，不包含价格信息）
// @accept application/json
// @Produce application/json
// @Param data body req.GetAuctionProjectListReq true "获取竞价项目列表"
// @Success 200 {object} resp.GetPublicAuctionProjectListResp "获取成功"
// @Router /auction/public/projects [post]
func GetPublicAuctionProjectList(c *gin.Context) {
	var listReq req.GetAuctionProjectListReq
	_ = c.ShouldBindJSON(&listReq)

	// 参数验证
	PageVerifyErr := utils.Verify(listReq, utils.CustomizeMap["PageVerify"])
	if PageVerifyErr != nil {
		response.FailWithMessage(PageVerifyErr.Error(), c)
		return
	}

	err, listResp := service.GetPublicAuctionProjectList(listReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(listResp, c)
	}
}
