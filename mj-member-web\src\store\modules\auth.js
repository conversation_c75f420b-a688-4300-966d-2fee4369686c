import { login, register, logout, getUserInfo } from '@/api/auth'
import { getToken, setToken, removeToken ,setUserInfo,getUserCookie,removeUserInfo} from '@/utils/auth'

const state = {
  token: getToken(),
  userInfo: getUserCookie(), 
  isLoggedIn: !!getToken()
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    state.isLoggedIn = !!token
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },
  CLEAR_AUTH(state) {
    state.token = null
    state.userInfo = null
    state.isLoggedIn = false
  }
}

const actions = {
  // 用户登录
  async login({ commit }, loginForm) {
    try {
      const response = await login(loginForm)
      if (response.data.code === 200) {
        const { token, member } = response.data.data
        commit('SET_TOKEN', token)
        commit('SET_USER_INFO', member)
        setToken(token) // 保存token到cookie
        setUserInfo(member) // 保存用户信息到cookie
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: '登录失败，请稍后重试' }
    }
  },

  // 用户注册
  async register({ commit }, registerForm) {
    try {
      const response = await register(registerForm)
      if (response.data.code === 200) {
        return { success: true, message: '注册成功，请等待审核' }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('注册失败:', error)
      return { success: false, message: '注册失败，请稍后重试' }
    }
  },

  // 获取用户信息
  async getUserInfo({ commit, state }) {
    try {
      if (!state.token) return { success: false }
      
      const response = await getUserInfo()
      if (response.data.code === 200) {
        setUserInfo(response.data.data)
        commit('SET_USER_INFO', response.data.data)
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return { success: false, message: '获取用户信息失败' }
    }
  },

  // 用户登出
  async logout({ commit }) {
    try {
      commit('CLEAR_AUTH')
      removeToken()
      removeUserInfo()
    } catch (error) {
      console.log(error)
    } finally {

    }
  }
}

const getters = {
  isLoggedIn: state => state.isLoggedIn,
  userInfo: state => state.userInfo,
  token: state => state.token
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
