package req

// 刷新token
type RefreshToken struct {
	UUID      string `json:"uuid"`      // 用户唯一标识
	ExpiresAt int64  `json:"expiresAt"` // 过期时间戳
}

// 添加用户对象
type RegisterStruct struct {
	ID           int    `json:"id"`
	Username     string `json:"userName"`     // 用户名
	Password     string `json:"passWord"`     // 密码
	NickName     string `json:"nickName"`     // 真实姓名
	HeaderImg    string `json:"headerImg"`    //头像
	AuthorityIds []int  `json:"authorityIds"` // 角色 id 数组
	Status       int8   `json:"status"`       // 状态
	Email        string `json:"email"`        // 邮箱
	Code         string `json:"code"`         //验证码
}

// 用户登录对象
type RegisterAndLoginStruct struct {
	Username  string `json:"username"`  // 用户名
	Password  string `json:"password"`  // 密码
	Captcha   string `json:"captcha"`   // 验证码
	CaptchaId string `json:"captchaId"` // 验证码 id
}

// 修改密码对象
type ChangePassword struct {
	ID          int    `json:"id"`
	Password    string `json:"password"`    // 旧密码
	NewPassword string `json:"newPassword"` //新密码
}

// 用户列表页查询条件
type GetUserList struct {
	PageInfo
	Username    string `json:"username"`    // 用户名
	CompanyCode string `json:"companyCode"` // 企业编码
	IsFarmer    int    `json:"isFarmer"`
	NickName    string `json:"nickName"`
}

// 验证码
type Captcha struct {
	Username string `json:"username"`
	Code     string `json:"code"`
}

// 忘记密码
type ForgetPassword struct {
	Username string `json:"username"` // 用户名
	Password string `json:"password"` // 密码
	Token    string `json:"token"`    // 加密串
}

//用户烤房权限数据设置
type UserSettingReq struct {
	Id     int    `json:"id"` //设备Id
	Params string `json:"params"`
	Auth   []int  `json:"auth"`
}
