@import './variables.scss';

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: $bg-color;
}

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 布局类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 间距类
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }
.ml-10 { margin-left: 10px; }
.ml-20 { margin-left: 20px; }
.mr-10 { margin-right: 10px; }
.mr-20 { margin-right: 20px; }

// 卡片样式
.card {
  background: $white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 20px;
  margin-bottom: 20px;
}

// 状态标签
.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  
  &.status-pending {
    background: #fdf6ec;
    color: $warning-color;
    border: 1px solid #faecd8;
  }
  
  &.status-active {
    background: #f0f9ff;
    color: $primary-color;
    border: 1px solid #d1ecf1;
  }
  
  &.status-success {
    background: #f0f9f0;
    color: $success-color;
    border: 1px solid #d4edda;
  }
  
  &.status-danger {
    background: #fef0f0;
    color: $danger-color;
    border: 1px solid #fecaca;
  }
}

// 响应式
@media (max-width: $mobile) {
  .container {
    padding: 0 15px;
  }
  
  .card {
    padding: 15px;
    margin-bottom: 15px;
  }
}
