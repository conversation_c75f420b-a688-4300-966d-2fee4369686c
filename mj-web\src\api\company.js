/*
 * @Descripttion: 企业管理
 * @version:
 * @Author: dlg
 * @Date: 2020-07-13 15:07:55
 * @LastEditors: dlg
 * @LastEditTime: 2020-07-13 17:45:09
 */
import request from '@/utils/request'

/**
 * @description: 获取列表信息
 * @param {type}
 * @return:
 */
export const getCompanyList = (data) => {
    return request({
        url: '/admin/company/getCompanyList',
        method: 'post',
        data
    })
}

/**
 * @description: 添加企业
 * @param {type}
 * @return:
 */
export const addCompany = (data) => {
    return request({
        url: '/admin/company/addCompany',
        method: 'post',
        data
    })
}

/**
 * @description: 删除企业
 * @param {type}
 * @return:
 */
export const delCompany = (data) => {
    return request({
        url: '/admin/company/deleteCompany',
        method: 'post',
        data
    })
}

/**
 * @description: 改变状态
 * @param {type}
 * @return:
 */
export function changeStatus(data) {
    return request({
        url: '/admin/company/updateCompanyStatus',
        method: 'post',
        data
    })
}