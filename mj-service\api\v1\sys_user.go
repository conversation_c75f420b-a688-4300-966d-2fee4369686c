package v1

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/global/response"
	"auction-sys/middleware"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/service"
	"auction-sys/utils"
	"auction-sys/utils/rsa"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// @Tags Base
// @Summary 用户登录
// @Produce  application/json
// @Param data body request.RegisterAndLoginStruct true "用户登录接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"登陆成功"}"
// @Router /base/login [post]
func Login(c *gin.Context) {
	var L req.RegisterAndLoginStruct
	_ = c.Should<PERSON>ind<PERSON>(&L)
	UserVerify := utils.Rules{
		"CaptchaId": {utils.NotEmpty()},
		"Captcha":   {utils.NotEmpty()},
		"Username":  {utils.NotEmpty()},
		"Password":  {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(L, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}

	if global.GVA_CONFIG.System.RsaLogin {
		dePass, err := rsa.EncryptWithPrivateKey(L.Password)
		if err != nil {
			log.Println("rsa decrypt error:", err.Error())
			response.FailWithMessage("登录失败", c)
			return
		} else {
			L.Password = dePass
		}
	}
	// 校验验证码
	if store.Verify(L.CaptchaId, L.Captcha, true) {
		U := &model.SysUser{Username: L.Username, Password: L.Password}
		// 校验用户名密码
		if err, user := service.Login(U); err != nil {
			response.FailWithMessage(err.Error(), c)
		} else {
			tokenNext(c, *user)
		}
	} else {
		response.FailWithMessage("验证码错误", c)
	}
}

// @Tags 小程序——基本接口
// @Summary 游客登录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AppletLogin true "游客登录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /base/touristLogin [post]
func AppletTouristLogin(c *gin.Context) {
	var param req.AppletTouristLogin
	_ = c.ShouldBindJSON(&param)

	if param.Code == "" {
		response.FailWithMessage("缺少Code", c)
	}

	err, member := service.AppletTouristLogin(param)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		appletTokenNext(c, member)
	}
}

// @Tags 小程序——基本接口
// @Summary 用户登录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AppletLogin true "用户登录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /base/wxLogin [post]
func AppletLogin(c *gin.Context) {
	var param req.AppletLogin
	_ = c.ShouldBindJSON(&param)

	err, member := service.AppletLogin(param)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		appletTokenNext(c, member)
	}
}

func appletTokenNext(c *gin.Context, member req.AppletLoginMember) {
	j := &middleware.JWT{
		SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey), // 唯一签名
	}
	//expiresAt:= int64(global.GVA_CONFIG.System.ExpiresTime*60) //秒
	clams := req.CustomClaims{
		UUID:        member.UUID,
		ID:          member.ID,
		NickName:    member.NickName,
		CompanyCode: member.CompanyCode,
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 1000, // 签名生效时间
			//ExpiresAt: time.Now().Unix() + expiresAt, // 过期时间 7 天
			ExpiresAt: time.Now().Unix() + 60*60*24*15, // 过期时间 30天
			Issuer:    "qmPlus",                        // 签名的发行者
		},
	}
	token, err := j.CreateToken(clams)
	if err != nil {
		response.FailWithMessage("获取token失败", c)
		return
	}

	setAppletRedisOnline(token, member)

	response.OkWithData(req.AppletLoginMemberResp{
		User:      member,
		Token:     token,
		ExpiresAt: clams.StandardClaims.ExpiresAt * 1000,
	}, c)
}

func setAppletRedisOnline(token string, member req.AppletLoginMember) {
	// 设置openid和sessionKey
	global.GVA_REDIS.Set("_applet_"+token+"_openid", member.OpenID, time.Hour*48)
	global.GVA_REDIS.Set("_applet_"+token+"_session_key", member.SessionKey, time.Hour*48)
}

// 登录以后签发jwt
func tokenNext(c *gin.Context, user resp.LoginStruct) {
	j := &middleware.JWT{
		SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey), // 唯一签名
	}
	clams := req.CustomClaims{
		UUID:        user.User.UUID,
		ID:          user.User.ID,
		NickName:    user.User.NickName,
		IsFarmer:    user.User.IsFarmer,
		CompanyCode: user.User.CompanyCode,
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 1000,  // 签名生效时间
			ExpiresAt: time.Now().Unix() + 60*60, // 过期时间 1个小时
			//ExpiresAt: time.Now().Unix() + 60*5, // 过期时间 1个小时
			Issuer: "qmPlus", // 签名的发行者
		},
	}
	token, err := j.CreateToken(clams)
	if err != nil {
		response.FailWithMessage("获取token失败", c)
		return
	}

	// TODO 限制是否允许多人登录
	if global.GVA_CONFIG.System.UseMultipoint {
		setRedisOnline(user.User)
		// 写登录日志
		go service.AddLoginLog(user.User)
		response.OkWithData(resp.LoginResponse{
			User:      user.User,
			Token:     token,
			ExpiresAt: clams.StandardClaims.ExpiresAt * 1000,
		}, c)
		return
	}
	var loginJwt model.JwtBlacklist
	loginJwt.Jwt = token
	// 每次先从 redis 中获取数据，如果能取到，证明用户是在线状态，则把旧的 token 扔黑名单
	err, jwtStr := service.GetRedisJWT(user.User.Username)
	if err == redis.Nil {
		if err := service.SetRedisJWT(loginJwt, user.User.Username); err != nil {
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
		setRedisOnline(user.User)
		// 写登录日志
		go service.AddLoginLog(user.User)
		response.OkWithData(resp.LoginResponse{
			User:      user.User,
			Token:     token,
			ExpiresAt: clams.StandardClaims.ExpiresAt * 1000,
		}, c)
	} else if err != nil {
		response.FailWithMessage(fmt.Sprintf("%v", err), c)
	} else {
		var blackJWT model.JwtBlacklist
		blackJWT.Jwt = jwtStr
		if err := service.JsonInBlacklist(blackJWT); err != nil {
			response.FailWithMessage("jwt作废失败", c)
			return
		}
		if err := service.SetRedisJWT(loginJwt, user.User.Username); err != nil {
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
		setRedisOnline(user.User)
		// 写登录日志
		go service.AddLoginLog(user.User)
		response.OkWithData(resp.LoginResponse{
			User:      user.User,
			Token:     token,
			ExpiresAt: clams.StandardClaims.ExpiresAt * 1000,
		}, c)
	}
}

// 设置用户在线相关数据
func setRedisOnline(user resp.LoginUserStruct) {
	// 用户uuid作为唯一标识，使用hset设置属性 唯一标识 uuit 和当前时间 currentTime +60s
	global.GVA_REDIS.HSet("online_"+user.UUID.String(), "uuid", user.UUID.String())
	global.GVA_REDIS.HSet("online_"+user.UUID.String(), "currentTime", time.Now().Add(time.Minute*30).Unix())
}

// @Tags SysUser : 用户管理
// @Summary 刷新token
// @Produce  application/json
// @Param data body request.RefreshToken true "刷新token"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"操作成功"}"
// @Router /base/refresh [post]
func RefreshToken(c *gin.Context) {

	var params req.RefreshToken
	_ = c.ShouldBindJSON(&params)
	TokenVerify := utils.Rules{
		"Uuid":      {utils.NotEmpty()},
		"ExpiresAt": {utils.NotEmpty()},
	}
	TokenVerifyErr := utils.Verify(params, TokenVerify)
	if TokenVerifyErr != nil {
		response.FailWithMessage(TokenVerifyErr.Error(), c)
		return
	}

	err, user := service.GetCurrentUser(params.UUID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	}

	token, expiresAt, err := middleware.Refresh(user, params.ExpiresAt)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp.LoginResponse{
			Token:     token,
			ExpiresAt: expiresAt,
		}, c)
	}
}

// @Tags SysUser ：用户管理
// @Summary 重置用户密码
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.ChangePasswordStruct true "重置用户密码"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"重置成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"重置失败"}"
// @Router /user/resetPassword [post]
func ResetPassword(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var params req.ChangePassword
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"ID": {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(params)
	if err := service.ResetPassword(params.ID); err != nil {
		go service.AddOperationLog(claims.UUID.String(), "用户管理-重置密码", c.ClientIP(), string(requestParam), "重置失败", response.ERROR, agent)
		response.FailWithMessage("重置失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "用户管理-重置密码", c.ClientIP(), string(requestParam), "重置成功", response.SUCCESS, agent)
		response.OkWithMessage("重置成功，初始密码为:"+constants.PASSWORD_DEFAULT, c)
	}
}

// @Tags SysUser ：用户管理
// @Summary 分页获取用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetUserList true "分页获取用户列表"
// @Success 200 {string} string "{"code":200,"data":{GetUserList},"msg":"操作成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"获取数据失败"}"
// @Router /user/getUserList [post]
func GetUserList(c *gin.Context) {
	claims, _ := c.Get("claims")
	// 这里我们通过断言获取 claims内的所有内容
	waitUse := claims.(*req.CustomClaims)
	uuid := waitUse.UUID
	var pageInfo req.GetUserList
	_ = c.ShouldBindJSON(&pageInfo)
	PageVerifyErr := utils.Verify(pageInfo, utils.CustomizeMap["PageVerify"])
	if PageVerifyErr != nil {
		response.FailWithMessage(PageVerifyErr.Error(), c)
		return
	}
	err, list, total, enableNum, disableNum := service.GetUserInfoList(pageInfo, uuid.String())
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("获取数据失败，%v", err), c)
	} else {
		response.OkWithData(resp.GetUserList{
			List:        list,
			Total:       total,
			Page:        pageInfo.Page,
			PageSize:    pageInfo.PageSize,
			Username:    pageInfo.Username,
			CompanyCode: pageInfo.CompanyCode,
			EnableNum:   enableNum,
			DisableNum:  disableNum,
		}, c)
	}
}

// @Tags SysUser ：用户管理
// @Summary 删除用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []request.GetById true "删除用户"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"删除成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"删除失败"}"
// @Router /user/deleteUser [delete]
func DeleteUser(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var reqIds []req.GetById
	_ = c.ShouldBindJSON(&reqIds)
	for _, reqId := range reqIds {
		IdVerifyErr := utils.Verify(reqId, utils.CustomizeMap["IdVerify"])
		if IdVerifyErr != nil {
			response.FailWithMessage(IdVerifyErr.Error(), c)
			return
		}
	}
	err := service.DeleteUser(reqIds)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(reqIds)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "用户管理-删除用户", c.ClientIP(), string(requestParam), "删除失败", response.ERROR, agent)
		response.FailWithMessage("删除失败", c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "用户管理-删除用户", c.ClientIP(), string(requestParam), "删除成功", response.SUCCESS, agent)
		response.OkWithMessage("删除成功", c)
	}
}

// @Tags SysUser ：用户管理
// @Summary 更改用户状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []model.SysUser true "更改用户状态"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /user/updateUserStatus [post]
func UpdateUserStatus(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var users []model.SysUser
	_ = c.ShouldBindJSON(&users)
	UserVerify := utils.Rules{
		"ID": {utils.NotEmpty()},
	}
	for _, user := range users {
		UserVerifyErr := utils.Verify(user, UserVerify)
		if UserVerifyErr != nil {
			response.FailWithMessage(UserVerifyErr.Error(), c)
			return
		}
	}
	if len(users) <= 0 {
		response.FailWithMessage("参数错误", c)
	}
	err := service.UpdateUserStatus(users)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(users)
	if err != nil {
		go service.AddOperationLog(claims.UUID.String(), "用户管理-更新状态", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage("更新失败", c)
	} else {
		if users[0].Status == constants.STATUS_ENABLED {
			go service.AddOperationLog(claims.UUID.String(), "用户管理-更新状态", c.ClientIP(), string(requestParam), "已启用", response.SUCCESS, agent)
			response.OkWithMessage("已启用", c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "用户管理-更新状态", c.ClientIP(), string(requestParam), "已禁用", response.SUCCESS, agent)
			response.OkWithMessage("已禁用", c)
		}

	}
}

// @Tags SysUser ：用户管理
// @Summary 更改用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.RegisterStruct true "更改用户"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /user/updateUser [post]
func UpdateUser(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var R req.RegisterStruct
	_ = c.ShouldBindJSON(&R)
	UserVerify := utils.Rules{
		"Username": {utils.NotEmpty(), utils.Mobile(R.Username)},
	}
	UserVerifyErr := utils.Verify(R, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	if R.Email != "" {
		EmailVerifyErr := utils.Verify(R, utils.Rules{"Email": {utils.Email(R.Email)}})
		if EmailVerifyErr != nil {
			response.FailWithMessage(EmailVerifyErr.Error(), c)
			return
		}
	}
	user := &model.SysUser{Username: R.Username, NickName: R.NickName, Status: R.Status, Email: R.Email}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(R)
	if R.ID != 0 {
		user.ID = R.ID
		err := service.UpdateUser(*user, R.AuthorityIds)
		if err != nil {
			go service.AddOperationLog(claims.UUID.String(), "用户管理-编辑用户", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
			response.FailWithMessage(err.Error(), c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "用户管理-编辑用户", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
			response.OkWithMessage("更新成功", c)
		}
	} else {
		cl, _ := c.Get("claims")
		claims, _ := cl.(*req.CustomClaims)
		user.CompanyCode = claims.CompanyCode
		user.CreateBy = claims.ID
		err := service.Register(*user, R.AuthorityIds)
		if err != nil {
			go service.AddOperationLog(claims.UUID.String(), "用户管理-添加用户", c.ClientIP(), string(requestParam), "添加失败", response.ERROR, agent)
			global.GVA_LOG.Debug(err.Error())
			response.FailWithMessage(err.Error(), c)
		} else {
			go service.AddOperationLog(claims.UUID.String(), "用户管理-添加用户", c.ClientIP(), string(requestParam), "添加成功", response.SUCCESS, agent)
			response.OkWithMessage("添加成功", c)
		}
	}
}

// @Tags SysUser ：用户管理
// @Summary 用户注册
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.RegisterStruct true "更改用户"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /user/reg [post]
func RegUser(c *gin.Context) {

	var R req.RegisterStruct
	_ = c.ShouldBindJSON(&R)
	UserVerify := utils.Rules{
		"Username": {utils.NotEmpty(), utils.Mobile(R.Username)},
		"Password": {utils.NotEmpty()},
		//"Code":     {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(R, UserVerify)

	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}

	//params := request.Captcha{Username: R.Username, Code: R.Code}
	//if err, _ := service.CheckCaptcha(params); err != nil {
	//	response.FailWithMessage(err.Error(), c)
	//	return
	//}
	user := &model.SysUser{Username: R.Username, Status: R.Status, Password: R.Password}
	user.IsFarmer = 1
	err := service.Register(*user, R.AuthorityIds)
	if err != nil {
		global.GVA_LOG.Debug(err.Error())
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("注册成功", c)
	}
}

// @Tags SysUser ：用户管理
// @Summary 更改用户角色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.RegisterStruct true "更改用户角色"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"更新成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"更新失败"}"
// @Router /user/updateUserAuth [post]
func UpdateUserAuth(c *gin.Context) {
	var R req.RegisterStruct
	_ = c.ShouldBindJSON(&R)
	UserVerify := utils.Rules{
		"ID":           {utils.NotEmpty()},
		"AuthorityIds": {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(R, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	err := service.UpdateUserAuth(R)
	if err != nil {
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags SysUser ：个人中心
// @Summary 用户上传头像
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param headerImg formData file true "用户上传头像"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"上传成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"上传失败"}"
// @Router /user/uploadHeaderImg [post]
func UploadHeaderImg(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	claims, _ := c.Get("claims")
	// 这里我们通过断言获取 claims内的所有内容
	waitUse := claims.(*req.CustomClaims)
	uuid := waitUse.UUID

	_, header, err := c.Request.FormFile("file")
	// 便于找到用户 以后从jwt中取
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("上传文件失败，%v", err), c)
	} else {
		// 文件上传后拿到文件路径
		err, filePath, _ := utils.Upload(header)
		if err != nil {
			response.FailWithMessage(fmt.Sprintf("接收返回值失败，%v", err), c)
		} else {
			// 修改数据库后得到修改后的user并且返回供前端使用
			// 更改数据库中头像链接
			err, _ = service.UploadHeaderImg(uuid, filePath)
			if err != nil {
				go service.AddOperationLog(uuid.String(), "个人中心-上传头像", c.ClientIP(), "", "上传失败", response.ERROR, agent)
				response.FailWithMessage(fmt.Sprintf("修改数据库链接失败，%v", err), c)
			} else {
				go service.AddOperationLog(uuid.String(), "个人中心-上传头像", c.ClientIP(), "", filePath, response.SUCCESS, agent)
				data := make(map[string]string)
				data["imageUrl"] = filePath
				response.OkWithData(data, c)
			}
		}
	}
}

// @Tags SysUser ：个人中心
// @Summary 用户修改密码
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.ChangePasswordStruct true "用户修改密码"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /user/changePassword [post]
func ChangePassword(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var params req.ChangePassword
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"Password":    {utils.NotEmpty()},
		"NewPassword": {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	U := &model.SysUser{UUID: claims.UUID, Password: params.Password}
	requestParam, _ := json.Marshal(params)
	if err, _ := service.ChangePassword(U, params.NewPassword); err != nil {
		go service.AddOperationLog(claims.UUID.String(), "个人中心-修改密码", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage(err.Error(), c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "个人中心-修改密码", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags SysUser ：个人中心
// @Summary 个人信息
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body true "个人信息"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /user/getPersonInfo [post]
func GetPersonInfo(c *gin.Context) {
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	err, personInfo := service.GetPersonInfo(claims.UUID.String())
	if err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(personInfo, c)
	}
}

// @Tags SysUser ：个人中心
// @Summary 修改个人信息
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body response.PersonInfo true "修改个人信息"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /user/updatePersonInfo [post]
func UpdatePersonInfo(c *gin.Context) {
	// 获取请求头
	agent := c.GetHeader("User-Agent")

	var params resp.PersonInfo
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"NickName": {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	if params.Email != "" {
		EmailVerifyErr := utils.Verify(params, utils.Rules{"Email": {utils.Email(params.Email)}})
		if EmailVerifyErr != nil {
			response.FailWithMessage(EmailVerifyErr.Error(), c)
			return
		}
	}
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)
	requestParam, _ := json.Marshal(params)
	if err := service.UpdatePersonInfo(claims.UUID.String(), params); err != nil {
		go service.AddOperationLog(claims.UUID.String(), "个人中心-更新信息", c.ClientIP(), string(requestParam), "更新失败", response.ERROR, agent)
		response.FailWithMessage(err.Error(), c)
	} else {
		go service.AddOperationLog(claims.UUID.String(), "个人中心-更新信息", c.ClientIP(), string(requestParam), "更新成功", response.SUCCESS, agent)
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags Base
// @Summary 检查用户是否存在
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body response.PersonInfo true "检查用户是否存在"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /base/checkUserExists [post]
func CheckUserExists(c *gin.Context) {
	var params resp.PersonInfo
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"Username": {utils.NotEmpty(), utils.Mobile(params.Username)},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	if err := service.CheckUserExists(params.Username); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

// @Tags Base
// @Summary 检查用户是否存在
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body response.PersonInfo true "检查用户是否存在"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /user/wxInfo [post]
func PostWxInfo(c *gin.Context) {
	var params req.EncryptedData
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"EncryptedData": {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage("参数有误", c)
		return
	}

	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	if err := service.SetOpenId(params, int64(claims.ID)); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

// @Tags User
// @Summary 微信登录。获取微信openId
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body response.PersonInfo true "检查用户是否存在"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /user/wxlogin [post]
func WxLogin(c *gin.Context) {
	var params req.WxLogin
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"Code": {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage("参数有误", c)
		return
	}

	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	if err := service.WxLogin(params, int64(claims.ID)); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

// @Tags Base
// @Summary 发送短信
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body response.PersonInfo true "发送短信"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /base/send [post]
func SendSms(c *gin.Context) {

	var params resp.PersonInfo
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"Username": {utils.NotEmpty(), utils.Mobile(params.Username)},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage("手机号不能为空", c)
		return
	}
	if params.Type != "reg" {
		if err := service.CheckUserExists(params.Username); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	}
	if err := service.SendSms(params.Username); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("发送成功", c)
	}
}

// @Tags Base
// @Summary 校验验证码
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.Captcha true "校验验证码"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /base/checkCaptcha [post]
func CheckCaptcha(c *gin.Context) {
	var params req.Captcha
	_ = c.ShouldBindJSON(&params)
	CaptchaVerify := utils.Rules{
		"Code":     {utils.NotEmpty()},
		"Username": {utils.NotEmpty()},
	}
	CaptchaVerifyErr := utils.Verify(params, CaptchaVerify)
	if CaptchaVerifyErr != nil {
		response.FailWithMessage("验证码不能为空", c)
		return
	}
	if err, token := service.CheckCaptcha(params); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(token, c)
	}
}

// @Tags Base
// @Summary 忘记密码
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.Captcha true "忘记密码"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /base/forgetPassword [post]
func ForgetPassword(c *gin.Context) {
	var params req.ForgetPassword
	_ = c.ShouldBindJSON(&params)
	UserVerify := utils.Rules{
		"Password": {utils.NotEmpty()},
		"Token":    {utils.NotEmpty()},
		"Username": {utils.NotEmpty()},
	}
	UserVerifyErr := utils.Verify(params, UserVerify)
	if UserVerifyErr != nil {
		response.FailWithMessage("密码不能为空", c)
		return
	}
	if err := service.ForgetPassword(params); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("密码修改成功", c)
	}
}

// @Tags user
// @Summary 获取用户列表
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.Captcha true "忘记密码"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /user/listSample [post]
func GetListSample(c *gin.Context) {
	var (
		users []resp.SampleUserInfo
	)
	cl, _ := c.Get("claims")
	claims, _ := cl.(*req.CustomClaims)

	_, users = service.GetUserListSample(claims.UUID.String())
	response.OkWithData(users, c)
}

// @Tags user
// @Summary 内部设置，烤房权限
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.Captcha true "忘记密码"
// @Success 200 {string} string "{"code":200,"data":{},"msg":"修改成功"}"
// @Failure 400 {string} string "{"code":400,"data":{},"msg":"修改失败"}"
// @Router /user/setParams [post]
func SetSettingParams(c *gin.Context) {
	var (
		params req.UserSettingReq
		err    error
	)
	_ = c.ShouldBindJSON(&params)
	err = service.SetUserParams(params)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("设置成功", c)
}
