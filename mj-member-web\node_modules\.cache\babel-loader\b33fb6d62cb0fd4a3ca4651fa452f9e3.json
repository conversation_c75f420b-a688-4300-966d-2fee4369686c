{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue?vue&type=template&id=fae5bece&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue", "mtime": 1757556310840}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1757485152609}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "type", "size", "on", "click", "scrollToAuctions", "isLoggedIn", "$event", "$router", "push", "_e", "ref", "placeholder", "clearable", "change", "fetchAuctions", "model", "value", "filters", "status", "callback", "$$v", "$set", "expression", "label", "categoryId", "_l", "categories", "category", "key", "id", "name", "directives", "rawName", "loading", "auctions", "auction", "goToAuction", "_s", "title", "formatMoney", "startPrice", "currentPrice", "quantity", "unit", "productCategory", "bidCount", "_f", "startTime", "endTime", "hasMore", "loadingMore", "loadMore", "staticRenderFns", "_withStripped"], "sources": ["E:/pywsp/auction-sys/mj-member-web/src/views/Home.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"home-page\" },\n    [\n      _c(\"AppHeader\"),\n      _c(\"section\", { staticClass: \"hero-section\" }, [\n        _c(\"div\", { staticClass: \"hero-content\" }, [\n          _c(\"h1\", [_vm._v(\"新疆生产建设兵团第一师棉麻有限责任公司竞价平台\")]),\n          _c(\"p\", [\n            _vm._v(\"专业的棉副产品在线竞价交易平台，公开透明，安全可靠\"),\n          ]),\n          _c(\n            \"div\",\n            { staticClass: \"hero-actions\" },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\", size: \"large\" },\n                  on: { click: _vm.scrollToAuctions },\n                },\n                [_vm._v(\" 立即竞价 \")]\n              ),\n              !_vm.isLoggedIn\n                ? _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"large\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.$router.push(\"/register\")\n                        },\n                      },\n                    },\n                    [_vm._v(\" 免费注册 \")]\n                  )\n                : _vm._e(),\n            ],\n            1\n          ),\n        ]),\n      ]),\n      _c(\n        \"section\",\n        { ref: \"auctionsSection\", staticClass: \"auctions-section\" },\n        [\n          _c(\"div\", { staticClass: \"container\" }, [\n            _c(\"div\", { staticClass: \"section-header\" }, [\n              _c(\"h2\", [_vm._v(\"热门竞价\")]),\n              _c(\n                \"div\",\n                { staticClass: \"filters\" },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"项目状态\", clearable: \"\" },\n                      on: { change: _vm.fetchAuctions },\n                      model: {\n                        value: _vm.filters.status,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.filters, \"status\", $$v)\n                        },\n                        expression: \"filters.status\",\n                      },\n                    },\n                    [\n                      _c(\"el-option\", {\n                        attrs: { label: \"即将开始\", value: 0 },\n                      }),\n                      _c(\"el-option\", { attrs: { label: \"竞价中\", value: 1 } }),\n                      _c(\"el-option\", { attrs: { label: \"已结束\", value: 2 } }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"商品分类\", clearable: \"\" },\n                      on: { change: _vm.fetchAuctions },\n                      model: {\n                        value: _vm.filters.categoryId,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.filters, \"categoryId\", $$v)\n                        },\n                        expression: \"filters.categoryId\",\n                      },\n                    },\n                    _vm._l(_vm.categories, function (category) {\n                      return _c(\"el-option\", {\n                        key: category.id,\n                        attrs: { label: category.name, value: category.id },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ]),\n            _c(\n              \"div\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.loading,\n                    expression: \"loading\",\n                  },\n                ],\n                staticClass: \"auctions-grid\",\n              },\n              _vm._l(_vm.auctions, function (auction) {\n                return _c(\n                  \"div\",\n                  {\n                    key: auction.id,\n                    staticClass: \"auction-card\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.goToAuction(auction)\n                      },\n                    },\n                  },\n                  [\n                    _c(\n                      \"div\",\n                      { staticClass: \"auction-content\" },\n                      [\n                        _c(\"StatusTag\", {\n                          staticClass: \"status-overlay\",\n                          attrs: { status: auction.status },\n                        }),\n                        _c(\"h3\", { staticClass: \"auction-title\" }, [\n                          _vm._v(_vm._s(auction.title)),\n                        ]),\n                        _c(\n                          \"div\",\n                          { staticClass: \"auction-info\" },\n                          [\n                            _vm.isLoggedIn\n                              ? [\n                                  _c(\"div\", { staticClass: \"info-item\" }, [\n                                    _c(\"span\", { staticClass: \"label\" }, [\n                                      _vm._v(\"起拍价\"),\n                                    ]),\n                                    _c(\"span\", { staticClass: \"value\" }, [\n                                      _vm._v(\n                                        \"¥\" +\n                                          _vm._s(\n                                            _vm.formatMoney(auction.startPrice)\n                                          )\n                                      ),\n                                    ]),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"info-item\" }, [\n                                    _c(\"span\", { staticClass: \"label\" }, [\n                                      _vm._v(\"当前价\"),\n                                    ]),\n                                    _c(\n                                      \"span\",\n                                      { staticClass: \"value current-price\" },\n                                      [\n                                        _vm._v(\n                                          \"¥\" +\n                                            _vm._s(\n                                              _vm.formatMoney(\n                                                auction.currentPrice\n                                              )\n                                            )\n                                        ),\n                                      ]\n                                    ),\n                                  ]),\n                                ]\n                              : [\n                                  _c(\"div\", { staticClass: \"info-item\" }, [\n                                    _c(\"span\", { staticClass: \"label\" }, [\n                                      _vm._v(\"数量\"),\n                                    ]),\n                                    _c(\"span\", { staticClass: \"value\" }, [\n                                      _vm._v(\n                                        _vm._s(auction.quantity) +\n                                          _vm._s(auction.unit)\n                                      ),\n                                    ]),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"info-item\" }, [\n                                    _c(\"span\", { staticClass: \"label\" }, [\n                                      _vm._v(\"商品类别\"),\n                                    ]),\n                                    _c(\"span\", { staticClass: \"value\" }, [\n                                      _vm._v(_vm._s(auction.productCategory)),\n                                    ]),\n                                  ]),\n                                ],\n                            _c(\"div\", { staticClass: \"info-item\" }, [\n                              _c(\"span\", { staticClass: \"label\" }, [\n                                _vm._v(\"出价次数\"),\n                              ]),\n                              _c(\"span\", { staticClass: \"value\" }, [\n                                _vm._v(_vm._s(auction.bidCount) + \"次\"),\n                              ]),\n                            ]),\n                          ],\n                          2\n                        ),\n                        _c(\n                          \"div\",\n                          { staticClass: \"auction-time\" },\n                          [\n                            auction.status === 0\n                              ? [\n                                  _c(\"span\", { staticClass: \"time-label\" }, [\n                                    _vm._v(\"开始时间：\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"time-value\" }, [\n                                    _vm._v(\n                                      _vm._s(\n                                        _vm._f(\"formatTime\")(\n                                          auction.startTime,\n                                          \"MM-DD HH:mm\"\n                                        )\n                                      )\n                                    ),\n                                  ]),\n                                ]\n                              : auction.status === 1\n                              ? [\n                                  _c(\"span\", { staticClass: \"time-label\" }, [\n                                    _vm._v(\"剩余时间：\"),\n                                  ]),\n                                  _c(\"CountdownTimer\", {\n                                    attrs: { \"end-time\": auction.endTime },\n                                  }),\n                                ]\n                              : [\n                                  _c(\"span\", { staticClass: \"time-label\" }, [\n                                    _vm._v(\"结束时间：\"),\n                                  ]),\n                                  _c(\"span\", { staticClass: \"time-value\" }, [\n                                    _vm._v(\n                                      _vm._s(\n                                        _vm._f(\"formatTime\")(\n                                          auction.endTime,\n                                          \"MM-DD HH:mm\"\n                                        )\n                                      )\n                                    ),\n                                  ]),\n                                ],\n                          ],\n                          2\n                        ),\n                      ],\n                      1\n                    ),\n                  ]\n                )\n              }),\n              0\n            ),\n            _vm.hasMore\n              ? _c(\n                  \"div\",\n                  { staticClass: \"load-more\" },\n                  [\n                    _c(\n                      \"el-button\",\n                      {\n                        attrs: { loading: _vm.loadingMore },\n                        on: { click: _vm.loadMore },\n                      },\n                      [_vm._v(\"加载更多\")]\n                    ),\n                  ],\n                  1\n                )\n              : _vm._e(),\n          ]),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,WAAW,CAAC,EACfA,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAC7CH,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACI,EAAE,CAAC,2BAA2B,CAAC,CACpC,CAAC,EACFH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IACzCC,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACU;IAAiB;EACpC,CAAC,EACD,CAACV,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACD,CAACJ,GAAG,CAACW,UAAU,GACXV,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAQ,CAAC;IACxBC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUG,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACa,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;MACtC;IACF;EACF,CAAC,EACD,CAACd,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,GACDJ,GAAG,CAACe,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,EACFd,EAAE,CACA,SAAS,EACT;IAAEe,GAAG,EAAE,iBAAiB;IAAEb,WAAW,EAAE;EAAmB,CAAC,EAC3D,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAU,CAAC,EAC1B,CACEF,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEY,WAAW,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAG,CAAC;IAC7CV,EAAE,EAAE;MAAEW,MAAM,EAAEnB,GAAG,CAACoB;IAAc,CAAC;IACjCC,KAAK,EAAE;MACLC,KAAK,EAAEtB,GAAG,CAACuB,OAAO,CAACC,MAAM;MACzBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB1B,GAAG,CAAC2B,IAAI,CAAC3B,GAAG,CAACuB,OAAO,EAAE,QAAQ,EAAEG,GAAG,CAAC;MACtC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACE3B,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAEwB,KAAK,EAAE,MAAM;MAAEP,KAAK,EAAE;IAAE;EACnC,CAAC,CAAC,EACFrB,EAAE,CAAC,WAAW,EAAE;IAAEI,KAAK,EAAE;MAAEwB,KAAK,EAAE,KAAK;MAAEP,KAAK,EAAE;IAAE;EAAE,CAAC,CAAC,EACtDrB,EAAE,CAAC,WAAW,EAAE;IAAEI,KAAK,EAAE;MAAEwB,KAAK,EAAE,KAAK;MAAEP,KAAK,EAAE;IAAE;EAAE,CAAC,CAAC,CACvD,EACD,CACF,CAAC,EACDrB,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEY,WAAW,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAG,CAAC;IAC7CV,EAAE,EAAE;MAAEW,MAAM,EAAEnB,GAAG,CAACoB;IAAc,CAAC;IACjCC,KAAK,EAAE;MACLC,KAAK,EAAEtB,GAAG,CAACuB,OAAO,CAACO,UAAU;MAC7BL,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB1B,GAAG,CAAC2B,IAAI,CAAC3B,GAAG,CAACuB,OAAO,EAAE,YAAY,EAAEG,GAAG,CAAC;MAC1C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,EACD5B,GAAG,CAAC+B,EAAE,CAAC/B,GAAG,CAACgC,UAAU,EAAE,UAAUC,QAAQ,EAAE;IACzC,OAAOhC,EAAE,CAAC,WAAW,EAAE;MACrBiC,GAAG,EAAED,QAAQ,CAACE,EAAE;MAChB9B,KAAK,EAAE;QAAEwB,KAAK,EAAEI,QAAQ,CAACG,IAAI;QAAEd,KAAK,EAAEW,QAAQ,CAACE;MAAG;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFlC,EAAE,CACA,KAAK,EACL;IACEoC,UAAU,EAAE,CACV;MACED,IAAI,EAAE,SAAS;MACfE,OAAO,EAAE,WAAW;MACpBhB,KAAK,EAAEtB,GAAG,CAACuC,OAAO;MAClBX,UAAU,EAAE;IACd,CAAC,CACF;IACDzB,WAAW,EAAE;EACf,CAAC,EACDH,GAAG,CAAC+B,EAAE,CAAC/B,GAAG,CAACwC,QAAQ,EAAE,UAAUC,OAAO,EAAE;IACtC,OAAOxC,EAAE,CACP,KAAK,EACL;MACEiC,GAAG,EAAEO,OAAO,CAACN,EAAE;MACfhC,WAAW,EAAE,cAAc;MAC3BK,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUG,MAAM,EAAE;UACvB,OAAOZ,GAAG,CAAC0C,WAAW,CAACD,OAAO,CAAC;QACjC;MACF;IACF,CAAC,EACD,CACExC,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,WAAW,EAAE;MACdE,WAAW,EAAE,gBAAgB;MAC7BE,KAAK,EAAE;QAAEmB,MAAM,EAAEiB,OAAO,CAACjB;MAAO;IAClC,CAAC,CAAC,EACFvB,EAAE,CAAC,IAAI,EAAE;MAAEE,WAAW,EAAE;IAAgB,CAAC,EAAE,CACzCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAAC2C,EAAE,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAC9B,CAAC,EACF3C,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEH,GAAG,CAACW,UAAU,GACV,CACEV,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFH,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CACJ,GAAG,GACDJ,GAAG,CAAC2C,EAAE,CACJ3C,GAAG,CAAC6C,WAAW,CAACJ,OAAO,CAACK,UAAU,CACpC,CACJ,CAAC,CACF,CAAC,CACH,CAAC,EACF7C,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFH,EAAE,CACA,MAAM,EACN;MAAEE,WAAW,EAAE;IAAsB,CAAC,EACtC,CACEH,GAAG,CAACI,EAAE,CACJ,GAAG,GACDJ,GAAG,CAAC2C,EAAE,CACJ3C,GAAG,CAAC6C,WAAW,CACbJ,OAAO,CAACM,YACV,CACF,CACJ,CAAC,CAEL,CAAC,CACF,CAAC,CACH,GACD,CACE9C,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFH,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CACJJ,GAAG,CAAC2C,EAAE,CAACF,OAAO,CAACO,QAAQ,CAAC,GACtBhD,GAAG,CAAC2C,EAAE,CAACF,OAAO,CAACQ,IAAI,CACvB,CAAC,CACF,CAAC,CACH,CAAC,EACFhD,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFH,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAAC2C,EAAE,CAACF,OAAO,CAACS,eAAe,CAAC,CAAC,CACxC,CAAC,CACH,CAAC,CACH,EACLjD,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFH,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CACnCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAAC2C,EAAE,CAACF,OAAO,CAACU,QAAQ,CAAC,GAAG,GAAG,CAAC,CACvC,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,EACDlD,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEsC,OAAO,CAACjB,MAAM,KAAK,CAAC,GAChB,CACEvB,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCH,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFH,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCH,GAAG,CAACI,EAAE,CACJJ,GAAG,CAAC2C,EAAE,CACJ3C,GAAG,CAACoD,EAAE,CAAC,YAAY,CAAC,CAClBX,OAAO,CAACY,SAAS,EACjB,aACF,CACF,CACF,CAAC,CACF,CAAC,CACH,GACDZ,OAAO,CAACjB,MAAM,KAAK,CAAC,GACpB,CACEvB,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCH,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFH,EAAE,CAAC,gBAAgB,EAAE;MACnBI,KAAK,EAAE;QAAE,UAAU,EAAEoC,OAAO,CAACa;MAAQ;IACvC,CAAC,CAAC,CACH,GACD,CACErD,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCH,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFH,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACxCH,GAAG,CAACI,EAAE,CACJJ,GAAG,CAAC2C,EAAE,CACJ3C,GAAG,CAACoD,EAAE,CAAC,YAAY,CAAC,CAClBX,OAAO,CAACa,OAAO,EACf,aACF,CACF,CACF,CAAC,CACF,CAAC,CACH,CACN,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDtD,GAAG,CAACuD,OAAO,GACPtD,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEkC,OAAO,EAAEvC,GAAG,CAACwD;IAAY,CAAC;IACnChD,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACyD;IAAS;EAC5B,CAAC,EACD,CAACzD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,GACDJ,GAAG,CAACe,EAAE,CAAC,CAAC,CACb,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAI2C,eAAe,GAAG,EAAE;AACxB3D,MAAM,CAAC4D,aAAa,GAAG,IAAI;AAE3B,SAAS5D,MAAM,EAAE2D,eAAe", "ignoreList": []}]}