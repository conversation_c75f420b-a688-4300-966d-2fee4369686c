package resp

import (
	"auction-sys/model"

	uuid "github.com/satori/go.uuid"
)

// 用户信息响应
type SysUserResponse struct {
	User model.SysUser `json:"user"` // 用户对象
}

// 用户登录
type LoginResponse struct {
	User      LoginUserStruct `json:"user"`      // 用户信息
	Token     string          `json:"token"`     // token
	ExpiresAt int64           `json:"expiresAt"` // 过期时间戳
}

// 用户登录返回
type LoginStruct struct {
	User LoginUserStruct `json:"user"` // 用户信息
}

// 用户登录返回数据
type LoginUserStruct struct {
	ID             int       `json:"id"`
	UUID           uuid.UUID `json:"uuid"`
	Username       string    `json:"userName"`       // 用户名
	NickName       string    `json:"nickName"`       // 真实姓名
	HeaderImg      string    `json:"headerImg"`      // 头像
	CompanyCode    string    `json:"companyCode"`    // 企业编码
	CompanyLogo    string    `json:"companyLogo"`    // 企业 logo
	CompanyName    string    `json:"companyName"`    // 企业名称
	PasswordStatus int8      `json:"passwordStatus"` // 密码状态
	IsRoot         int8      `json:"isRoot"`         // 是否超管
	WxStatus       bool      `json:"wxStatus"`
	IsFarmer       int       `json:"isFarmer"`
}

// 分页获取用户
type GetUserList struct {
	List        interface{} `json:"list"`        // 列表
	Total       int         `json:"total"`       // 总条数
	Page        int         `json:"page"`        // 当前页
	PageSize    int         `json:"pageSize"`    // 每页条数
	Username    string      `json:"username"`    // 用户名
	CompanyCode string      `json:"companyCode"` // 企业编码
	EnableNum   int         `json:"enableNum"`   // 启用数量
	DisableNum  int         `json:"disableNum"`  // 禁用数量
}

// 管理员获取用户信息
type GetUserInfoAdmin struct {
	ID       int    `json:"id"`
	Username string `json:"username"` // 用户名
	NickName string `json:"nickname"` // 真实姓名
	Email    string `json:"email"`    // 邮箱
	Status   int    `json:"status"`   // 状态
}

// 管理员获取用户信息
type SampleUserInfo struct {
	ID       int    `json:"id"`
	Username string `json:"username"` // 用户名
	NickName string `json:"nickname"` // 真实姓名
}

// 个人中心，用户信息
type PersonInfo struct {
	CompanyName string `json:"companyName" gorm:"-"` // 企业名称
	CompanyCode string `json:"companyCode"`          // 企业编码
	Username    string `json:"username"`             // 用户名
	Email       string `json:"email"`                // 邮箱
	NickName    string `json:"nickName"`             // 真实姓名
	Gender      int8   `json:"gender"`               // 性别
	Age         int    `json:"age"`                  // 年龄
	Type        string `json:"type"`                 // 类型
	QQ          string `json:"qq"`                   // qq
	Address     string `json:"address"`              // 地址
	Userinfo    string `json:"userinfo" gorm:"-"`    // 微信信息
	IdCard      string `json:"idCard"`
	ShiNum      int    `json:"shiNum"`
	TuanNum     int    `json:"tuanNum"`
	LianNum     int    `json:"lianNum"`
	DeptName    string `json:"deptName"`
}

type FarmlandUserInfo struct {
	ID       int    `json:"id"`
	NickName string `json:"nickname"` // 所属农户
	Username string `json:"userName" gorm:"comment:'用户登录名'"`
	IdCard   string `json:"idCard"`   // 身份证号
	Address  string `json:"address"`  // 地址
	DeptName string `json:"deptName"` // 单位
	Mobile   string `json:"mobile"`   // 电话
	Desc     string `json:"desc"`     // 描述
}
