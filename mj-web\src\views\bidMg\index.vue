<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div style="margin-bottom: 20px;min-width:720px;">
      <span style="color:#606266;font-size:14px;">项目名称：</span>
      <el-input
        v-model="searchForm.projectTitle"
        placeholder="请输入项目名称"
        style="width: 200px;margin-right:32px;"
        clearable
      />
      <span style="color:#606266;font-size:14px;">会员姓名：</span>
      <el-input
        v-model="searchForm.memberName"
        placeholder="请输入会员姓名"
        style="width: 200px;margin-right:32px;"
        clearable
      />
      <span style="color:#606266;font-size:14px;">出价状态：</span>
      <el-select v-model="searchForm.status"
                 placeholder="请选择出价状态"
                 style="margin-right:32px;"
                 clearable>
        <el-option label="有效" :value="1" />
        <el-option label="无效" :value="0" />
      </el-select>
      <span style="color:#606266;font-size:14px;">出价时间：</span>
      <el-date-picker
        v-model="searchForm.dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        style="margin-right:12px;"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
      />
      <el-button @click="getTableData" type="primary">查询</el-button>
      <el-button @click="resetSearch" type="default">重置</el-button>
    </div>

    <!-- 统计信息 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalBids || 0 }}</div>
            <div class="stat-label">总出价次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.validBids || 0 }}</div>
            <div class="stat-label">有效出价</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalAmount || 0 }}</div>
            <div class="stat-label">总出价金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.activeProjects || 0 }}</div>
            <div class="stat-label">活跃项目</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 出价记录表格 -->
    <el-table :data="tableData" height="100%" border>
      <el-table-column align="center" label="序号" min-width="80">
        <template slot-scope="scope">{{ scope.$index + (page - 1) * limit + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="项目名称" min-width="200" prop="projectTitle" />
      <el-table-column align="center" label="出价会员" min-width="120">
        <template slot-scope="scope">{{ scope.row.memberName || '***' }}</template>
      </el-table-column>
      <el-table-column align="center" label="出价金额" min-width="120">
        <template slot-scope="scope">¥{{ scope.row.bidAmount }}</template>
      </el-table-column>
      <el-table-column align="center" label="出价时间" min-width="160" prop="bidTime" />
      <el-table-column align="center" label="IP地址" min-width="120">
        <template slot-scope="scope">{{ scope.row.ipAddress || '***' }}</template>
      </el-table-column>
      <el-table-column align="center" label="状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '有效' : '无效' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否中标" min-width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isWinner === 1" type="warning">中标</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" min-width="150" prop="remark" show-overflow-tooltip />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="limit"
        :total="total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { getBidList, getAuctionStats } from '@/api/auction'

export default {
  name: 'BidManagement',
  data() {
    return {
      // 搜索表单
      searchForm: {
        projectTitle: '',
        memberName: '',
        status: '',
        dateRange: []
      },
      // 表格数据
      tableData: [],
      // 分页
      page: 1,
      limit: 10,
      total: 0,
      // 统计数据
      statistics: {
        totalBids: 0,
        validBids: 0,
        totalAmount: 0,
        activeProjects: 0
      }
    }
  },
  created() {
    this.getTableData()
    this.getStatistics()
  },
  methods: {
    // 获取表格数据
    async getTableData() {
      try {
        const params = {
          page: this.page,
          pageSize: this.limit,
          projectTitle: this.searchForm.projectTitle,
          memberName: this.searchForm.memberName,
          status: this.searchForm.status
        }

        // 处理时间范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.startTime = this.searchForm.dateRange[0]
          params.endTime = this.searchForm.dateRange[1]
        }

        const response = await getBidList(params)
        if (response.data.code === 200) {
          this.tableData = response.data.data.list || []
          this.total = response.data.data.total || 0
        } else {
          this.$message.error(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取出价记录失败:', error)
        this.$message.error('获取数据失败')
      }
    },

    // 获取统计数据
    async getStatistics() {
      try {
        const response = await getAuctionStats({})
        if (response.data.code === 200) {
          this.statistics = response.data.data || {}
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        projectTitle: '',
        memberName: '',
        status: '',
        dateRange: []
      }
      this.page = 1
      this.getTableData()
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.limit = val
      this.page = 1
      this.getTableData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.page = val
      this.getTableData()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}
</style>
