package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"errors"
	"fmt"
)

// @Title 增加商品类别
// @Description
// <AUTHOR> @Param category 		model.SysProductCategory	商品类别信息
// @Return err 			error 				错误信息

func AddProductCategory(category model.SysProductCategory) (err error) {
	// 查询条件sql
	sql := fmt.Sprintf("name = '%v' and deleted_at is null", category.Name)
	// 查询
	findOne := global.GVA_DB.Where(sql).First(&model.SysProductCategory{}).RecordNotFound()
	if !findOne {
		return errors.New("存在重复的类别名称，请修改")
	}

	err = global.GVA_DB.Create(&category).Error
	if err != nil {
		return errors.New("商品类别创建失败")
	}
	return nil
}

// @Title 更新商品类别
// @Description
// <AUTHOR> @Param category 		model.SysProductCategory	商品类别信息
// @Return err 			error 				错误信息

func UpdateProductCategory(category model.SysProductCategory) (err error) {
	var oldCategory model.SysProductCategory
	upDataMap := make(map[string]interface{})
	upDataMap["name"] = category.Name
	upDataMap["description"] = category.Description
	upDataMap["unit"] = category.Unit
	upDataMap["status"] = category.Status
	
	global.GVA_DB.Where("id = ? ", category.ID).First(&oldCategory)

	//查重
	if oldCategory.Name != category.Name {
		sql := fmt.Sprintf("id <> %v and name = '%v' and deleted_at is null", category.ID, category.Name)
		notSame := global.GVA_DB.Where(sql).First(&model.SysProductCategory{}).RecordNotFound()
		if !notSame {
			global.GVA_LOG.Debug("存在相同类别名称，修改失败")
			return errors.New("存在相同类别名称，修改失败")
		}
	}

	err = global.GVA_DB.Table("sys_product_category").Where("id = ?", category.ID).Updates(upDataMap).Error
	if err != nil {
		return err
	}
	return nil
}

// @Title 根据id获取商品类别
// @Description
// <AUTHOR> @Param id 		float64				商品类别 id
// @Return err 		error 				错误信息
// @Param category 	model.SysProductCategory	商品类别信息

func GetProductCategory(id int) (err error, category model.SysProductCategory) {
	err = global.GVA_DB.Where("id = ?", id).First(&category).Error
	return
}

// @Title 删除商品类别
// @Description
// <AUTHOR> @Param ids 		[]request.GetById	商品类别 ids
// @Return err 		error 				错误信息

func DeleteProductCategory(ids []req.GetById) (err error) {
	var category model.SysProductCategory
	for _, id := range ids {
		err = global.GVA_DB.Where("id = ?", id.Id).Delete(&category).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// @Title 分页获取商品类别
// @Description
// <AUTHOR> @Param category 		request.GetProductCategoryList	查询条件
// @Return err 			error 					错误信息
// @Return list 		interface{} 			列表
// @Return total 		int 					总数
// @Return enableNum 	int 					已启用数量
// @Return disableNum 	int 					已禁用数量

func GetProductCategoryList(category req.GetProductCategoryList) (err error, list interface{}, total int, enableNum, disableNum int) {
	limit := category.PageSize
	if limit <= 0 {
		limit = constants.LIMIT
	}
	offset := category.PageSize * (category.Page - 1)
	db := global.GVA_DB
	var categoryList []model.SysProductCategory
	// 模糊查询时拼接以下条件
	if len(category.Name) > 0 {
		db = db.Where("instr(name, ?)", category.Name)
	}
	err = db.Find(&categoryList).Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&categoryList).Error
	// 查询启用状态数量，禁用状态数量
	err = db.Where("status = ?", constants.STATUS_ENABLED).Find(&[]model.SysProductCategory{}).Count(&enableNum).Error
	err = db.Where("status = ?", constants.STATUS_DISABLED).Find(&[]model.SysProductCategory{}).Count(&disableNum).Error

	return err, categoryList, total, enableNum, disableNum
}

// @Title 更新商品类别状态
// @Description
// <AUTHOR> @Param categorys 	[]model.SysProductCategory	商品类别信息数组
// @Return err 		error 				错误信息

func UpdateProductCategoryStatus(categorys []model.SysProductCategory) (err error) {
	for _, category := range categorys {
		err = global.GVA_DB.Model(&category).Update("status", category.Status).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// @Title 下拉框中填充商品类别数据
// @Description
// <AUTHOR> @Return err 			error 							错误信息
// @Return categorys 	[]model.SysProductCategory 		商品类别数组

func GetProductCategoryCondition() (err error, categorys []model.SysProductCategory) {
	sql := fmt.Sprintf("status = %v", constants.STATUS_ENABLED)
	err = global.GVA_DB.Table("sys_product_category").Select("id, name, unit").Where(sql).Scan(&categorys).Error
	if err != nil {
		return err, categorys
	}
	return err, categorys
}