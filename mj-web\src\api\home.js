
import request from '@/utils/request'

/**
 * @description: 获取我的烤房
 * @param {type}
 * @return {type}
 */
export const listDevices = (data) => {
    return request({
        url: '/admin/device/list',
        method: 'post',
        data
    })
}

/**
 * @description: 获取关注的设备
 * @param {type}
 * @return {type}
 */
export const shareDeviceList = (data) => {
    return request({
        url: '/admin/device/shareList',
        method: 'post',
        data
    })
}

/**
 * @description: 统计设备数量
 * @param {type}
 * @return {type}
 */
export const countDevice = (data) => {
    return request({
        url: '/admin/device/counts',
        method: 'post',
        data
    })
}

