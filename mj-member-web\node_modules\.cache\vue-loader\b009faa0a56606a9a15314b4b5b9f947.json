{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\auth\\Register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\auth\\Register.vue", "mtime": 1757558980871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Register.vue"], "names": [], "mappings": ";AAiGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Register.vue", "sourceRoot": "src/views/auth", "sourcesContent": ["<template>\r\n  <div class=\"register-page\">\r\n    <AppHeader />\r\n    \r\n    <div class=\"register-container\">\r\n      <div class=\"register-card\">\r\n        <div class=\"register-header\">\r\n          <h2>用户注册</h2>\r\n        </div>\r\n\r\n        <el-form\r\n          ref=\"registerForm\"\r\n          :model=\"form\"\r\n          :rules=\"rules\"\r\n          label-width=\"100px\"\r\n          class=\"register-form\"\r\n        >\r\n          <el-form-item label=\"手机号\" prop=\"mobile\">\r\n            <el-input\r\n              v-model=\"form.mobile\"\r\n              placeholder=\"请输入手机号\"\r\n              maxlength=\"11\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"密码\" prop=\"password\">\r\n            <el-input\r\n              v-model=\"form.password\"\r\n              type=\"password\"\r\n              placeholder=\"请输入密码\"\r\n              show-password\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n            <el-input\r\n              v-model=\"form.confirmPassword\"\r\n              type=\"password\"\r\n              placeholder=\"请再次输入密码\"\r\n              show-password\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"form.name\"\r\n              placeholder=\"请输入真实姓名\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n            <el-input\r\n              v-model=\"form.companyName\"\r\n              placeholder=\"请输入企业名称\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"信用代码\" prop=\"creditCode\">\r\n            <el-input\r\n              v-model=\"form.creditCode\"\r\n              placeholder=\"信用代码\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n\r\n          </el-form-item>\r\n          \r\n          <div>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"handleRegister\"\r\n              :loading=\"loading\"\r\n              class=\"register-btn\"\r\n            >\r\n              注册\r\n            </el-button>\r\n          </div>\r\n          <div >\r\n            <el-checkbox v-model=\"form.agreeTerms\">\r\n              我已阅读并同意\r\n              <a href=\"#\" class=\"link\">《用户协议》</a>\r\n              和\r\n              <a href=\"#\" class=\"link\">《隐私政策》</a>\r\n            </el-checkbox>\r\n          </div>\r\n          <div class=\"login-link\">\r\n            已有账号？\r\n            <router-link to=\"/login\" class=\"link\">立即登录</router-link>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { sendSmsCode } from '@/api/auth'\r\n\r\nexport default {\r\n  name: 'Register',\r\n  data() {\r\n    // 确认密码验证\r\n    const validateConfirmPassword = (rule, value, callback) => {\r\n      if (value !== this.form.password) {\r\n        callback(new Error('两次输入的密码不一致'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n\r\n    // 手机号验证\r\n    const validateMobile = (rule, value, callback) => {\r\n      const mobileReg = /^1[3-9]\\d{9}$/\r\n      if (!mobileReg.test(value)) {\r\n        callback(new Error('请输入正确的手机号'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n\r\n    return {\r\n      form: {\r\n        mobile: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        name: '',\r\n        companyName: '',\r\n        creditCode: '',\r\n        agreeTerms: false\r\n      },\r\n      rules: {\r\n        mobile: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { validator: validateMobile, trigger: 'blur' }\r\n        ],\r\n        smsCode: [\r\n          { required: true, message: '请输入验证码', trigger: 'blur' },\r\n          { min: 6, max: 6, message: '验证码长度为6位', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' },\r\n          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, message: '请确认密码', trigger: 'blur' },\r\n          { validator: validateConfirmPassword, trigger: 'blur' }\r\n        ],\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' }\r\n        ],\r\n        companyName: [\r\n          { required: true, message: '请输入企业名称', trigger: 'blur' }\r\n        ],\r\n        creditCode: [\r\n          { required: true, message: '信用代码', trigger: 'blur' }\r\n        ],\r\n      },\r\n      loading: false,\r\n      smsLoading: false,\r\n      smsCountdown: 0,\r\n      smsTimer: null\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.smsTimer) {\r\n      clearInterval(this.smsTimer)\r\n    }\r\n  },\r\n  methods: {\r\n    // 发送短信验证码\r\n    async sendSmsCode() {\r\n      // 先验证手机号\r\n      try {\r\n        await this.$refs.registerForm.validateField('mobile')\r\n      } catch (error) {\r\n        return\r\n      }\r\n\r\n      this.smsLoading = true\r\n      try {\r\n        const response = await sendSmsCode(this.form.mobile)\r\n        if (response.data.code === 200) {\r\n          this.$message.success('验证码发送成功')\r\n          this.startSmsCountdown()\r\n        } else {\r\n          this.$message.error(response.data.msg || '验证码发送失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('验证码发送失败')\r\n      } finally {\r\n        this.smsLoading = false\r\n      }\r\n    },\r\n\r\n    // 开始短信倒计时\r\n    startSmsCountdown() {\r\n      this.smsCountdown = 60\r\n      this.smsTimer = setInterval(() => {\r\n        this.smsCountdown--\r\n        if (this.smsCountdown <= 0) {\r\n          clearInterval(this.smsTimer)\r\n          this.smsTimer = null\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    // 处理注册\r\n    async handleRegister() {\r\n      if (!this.form.agreeTerms) {\r\n        this.$message.warning('请先同意用户协议和隐私政策')\r\n        return\r\n      }\r\n\r\n      try {\r\n        await this.$refs.registerForm.validate()\r\n      } catch (error) {\r\n        return\r\n      }\r\n\r\n      this.loading = true\r\n      try {\r\n        const result = await this.$store.dispatch('auth/register', this.form)\r\n        if (result.success) {\r\n          this.$message.success(result.message)\r\n          this.$router.push('/login')\r\n        } else {\r\n          this.$message.error(result.message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('注册失败，请稍后重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.register-page {\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.register-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: calc(100vh - #{$header-height});\r\n  padding: 40px 20px;\r\n}\r\n\r\n.register-card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  padding: 40px;\r\n  width: 100%;  \r\n  max-width: 500px;\r\n}\r\n\r\n.register-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n\r\n  h2 {\r\n    color: $text-primary;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  p {\r\n    color: $text-secondary;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.register-form {\r\n  .sms-input {\r\n    display: flex;\r\n    gap: 10px;\r\n\r\n    .el-input {\r\n      flex: 1;\r\n    }\r\n\r\n    .el-button {\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .register-btn {\r\n    width: 100%;\r\n    height: 44px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .login-link {\r\n    text-align: center;\r\n    margin-top: 20px;\r\n    color: $text-secondary;\r\n  }\r\n\r\n  .link {\r\n    color: $primary-color;\r\n    text-decoration: none;\r\n\r\n    &:hover {\r\n      text-decoration: underline;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: $mobile) {\r\n  .register-card {\r\n    padding: 30px 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}