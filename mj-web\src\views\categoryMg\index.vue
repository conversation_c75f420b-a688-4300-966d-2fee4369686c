<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div style="margin-bottom: 20px;min-width:720px;">
      <span style="color:#606266;font-size:14px;">类别名称：</span>
      <el-input
        v-model="searchForm.name"
        placeholder="请输入类别名称"
        style="width: 200px;margin-right:32px;"
        clearable
      />
      <el-button @click="getTableData" type="primary">查询</el-button>
      <el-button @click="resetSearch" type="default">重置</el-button>
    </div>

    <!-- 操作按钮 -->
    <div class="button-box">
      <el-button type="primary" @click="addCategory">新增分类</el-button>
      <el-button type="danger" @click="batchDelete" :disabled="multipleSelection.length === 0">批量删除</el-button>
    </div>

    <!-- 商品分类表格 -->
    <el-table :data="tableData" height="100%" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="序号" min-width="80">
        <template slot-scope="scope">{{ scope.$index + (page - 1) * limit + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="类别名称" min-width="150" prop="name" />
      <el-table-column align="center" label="类别描述" min-width="200" prop="description" show-overflow-tooltip />
      <el-table-column align="center" label="计量单位" min-width="100" prop="unit" />
      <el-table-column align="center" label="创建时间" min-width="160" prop="createdAt" />
      <el-table-column align="center" label="状态" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="2"
            active-text="启用"
            inactive-text="禁用"
            @change="switchStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="editCategory(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="deleteCategory(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="limit"
        :total="total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="editForm.ID ? '编辑商品分类' : '新增商品分类'"
      :visible.sync="editDialog"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="100px">
        <el-form-item label="类别名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入类别名称" />
        </el-form-item>
        <el-form-item label="类别描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入类别描述"
          />
        </el-form-item>
        <el-form-item label="计量单位" prop="unit">
          <el-input v-model="editForm.unit" placeholder="请输入计量单位，如：吨、公斤等" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCategory">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getProductCategoryList, 
  addProductCategory, 
  updateProductCategory, 
  deleteProductCategory, 
  updateProductCategoryStatus 
} from '@/api/productCategory'

export default {
  name: 'CategoryManagement',
  data() {
    return {
      // 搜索表单
      searchForm: {
        name: ''
      },
      // 表格数据
      tableData: [],
      multipleSelection: [],
      // 分页
      page: 1,
      limit: 10,
      total: 0,
      // 弹窗控制
      editDialog: false,
      // 编辑表单
      editForm: {
        ID: '',
        name: '',
        description: '',
        unit: '',
        status: 1
      },
      // 表单验证规则
      editRules: {
        name: [
          { required: true, message: '请输入类别名称', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入计量单位', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    // 获取表格数据
    async getTableData() {
      try {
        const params = {
          page: this.page,
          pageSize: this.limit,
          ...this.searchForm
        }
        const response = await getProductCategoryList(params)
        if (response.data.code === 200) {
          this.tableData = response.data.data.list || []
          this.total = response.data.data.total || 0
        } else {
          this.$message.error(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取商品分类列表失败:', error)
        this.$message.error('获取数据失败')
      }
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        name: ''
      }
      this.page = 1
      this.getTableData()
    },

    // 新增分类
    addCategory() {
      this.editForm = {
        ID: '',
        name: '',
        description: '',
        unit: '',
        status: 1
      }
      this.editDialog = true
    },

    // 编辑分类
    editCategory(row) {
      this.editForm = {
        ID: row.ID,
        name: row.name,
        description: row.description,
        unit: row.unit,
        status: row.status
      }
      this.editDialog = true
    },

    // 保存分类
    saveCategory() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (this.editForm.ID) {
              // 更新分类
              response = await updateProductCategory(this.editForm)
            } else {
              // 创建分类
              response = await addProductCategory(this.editForm)
            }
            
            if (response.data.code === 200) {
              this.$message.success(this.editForm.ID ? '更新成功' : '创建成功')
              this.editDialog = false
              this.getTableData()
            } else {
              this.$message.error(response.data.msg || '保存失败')
            }
          } catch (error) {
            console.error('保存商品分类失败:', error)
            this.$message.error('保存失败')
          }
        }
      })
    },

    // 删除分类
    deleteCategory(row) {
      this.$confirm('确定要删除该商品分类吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteProductCategory([{ id: row.ID }])
          if (response.data.code === 200) {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.error(response.data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除商品分类失败:', error)
          this.$message.error('删除失败')
        }
      })
    },

    // 批量删除
    batchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的分类')
        return
      }

      this.$confirm('确定要批量删除选中的商品分类吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const ids = this.multipleSelection.map(item => ({ id: item.ID }))
          const response = await deleteProductCategory(ids)
          if (response.data.code === 200) {
            this.$message.success('批量删除成功')
            this.getTableData()
          } else {
            this.$message.error(response.data.msg || '批量删除失败')
          }
        } catch (error) {
          console.error('批量删除失败:', error)
          this.$message.error('批量删除失败')
        }
      })
    },

    // 切换状态
    async switchStatus(row) {
      try {
        const response = await updateProductCategoryStatus([{
          id: row.ID,
          status: row.status
        }])
        if (response.data.code === 200) {
          this.$message.success('状态更新成功')
        } else {
          this.$message.error(response.data.msg || '状态更新失败')
          // 恢复原状态
          row.status = row.status === 1 ? 2 : 1
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败')
        // 恢复原状态
        row.status = row.status === 1 ? 2 : 1
      }
    },

    // 表格选择变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.limit = val
      this.page = 1
      this.getTableData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.page = val
      this.getTableData()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.button-box {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
