{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\components\\AppHeader.vue?vue&type=style&index=0&id=bb50a5e4&lang=scss&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\components\\AppHeader.vue", "mtime": 1757555981677}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757485139209}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757485152120}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757485142383}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757485135872}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hcHAtaGVhZGVyIHsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgcG9zaXRpb246IHN0aWNreTsKICB0b3A6IDA7CiAgei1pbmRleDogMTAwMDsKICBoZWlnaHQ6ICRoZWFkZXItaGVpZ2h0OwoKICAuaGVhZGVyLWNvbnRlbnQgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBoZWlnaHQ6IDEwMCU7CiAgfQoKICAubG9nbyB7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAKICAgIGgyIHsKICAgICAgY29sb3I6ICRwcmltYXJ5LWNvbG9yOwogICAgICBmb250LXNpemU6IDIwcHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICB9CiAgfQoKICAubmF2LW1lbnUgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDMwcHg7CgogICAgLm5hdi1pdGVtIHsKICAgICAgY29sb3I6ICR0ZXh0LXJlZ3VsYXI7CiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTsKICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4zczsKCiAgICAgICY6aG92ZXIsCiAgICAgICYucm91dGVyLWxpbmstYWN0aXZlIHsKICAgICAgICBjb2xvcjogJHByaW1hcnktY29sb3I7CiAgICAgIH0KICAgIH0KICB9CgogIC51c2VyLWluZm8gewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDE1cHg7CgogICAgLnVzZXItbmFtZSB7CiAgICAgIGNvbG9yOiAkdGV4dC1wcmltYXJ5OwogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICB9CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogJG1vYmlsZSkgewogIC5hcHAtaGVhZGVyIHsKICAgIC5uYXYtbWVudSB7CiAgICAgIGRpc3BsYXk6IG5vbmU7CiAgICB9CiAgICAKICAgIC5sb2dvIGgyIHsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["AppHeader.vue"], "names": [], "mappings": ";AA2EA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AppHeader.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <header class=\"app-header\" v-if=\"!$route.meta.hideHeader\">\n    <div class=\"container\" style=\"height: 100%;\">\n      <div class=\"header-content\">\n        <!-- Logo -->\n        <div class=\"logo\" @click=\"$router.push('/')\">\n          <h2>新疆生产建设兵团第一师棉麻有限责任公司竞价平台</h2>\n        </div>\n\n        <!-- 用户信息 -->\n        <div class=\"user-info\">\n          <template v-if=\"isLoggedIn\">\n            <el-dropdown @command=\"handleCommand\">\n              <span class=\"user-name\">\n                {{ userInfo?.name || '用户' }}\n                <i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </span>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"profile\">个人中心</el-dropdown-item>\n                <el-dropdown-item command=\"bids\">我的出价</el-dropdown-item>\n                <el-dropdown-item command=\"projects\">我的项目</el-dropdown-item>\n                <el-dropdown-item divided command=\"logout\">退出登录</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </template>\n          <template v-else>\n            <el-button type=\"text\" @click=\"$router.push('/login')\">登录</el-button>\n            <el-button type=\"primary\" size=\"small\" @click=\"$router.push('/register')\">注册</el-button>\n          </template>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'AppHeader',\n  computed: {\n    ...mapGetters('auth', ['isLoggedIn', 'userInfo'])\n  },\n  methods: {\n    handleCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$router.push('/profile')\n          break\n        case 'bids':\n          this.$router.push('/profile/bids')\n          break\n        case 'projects':\n          this.$router.push('/profile/projects')\n          break\n        case 'logout':\n          this.handleLogout()\n          break\n      }\n    },\n    \n    async handleLogout() {\n      try {\n        await this.$store.dispatch('auth/logout')\n        this.$message.success('退出登录成功')\n        this.$router.push('/home')\n      } catch (error) {\n        console.error('退出登录失败:', error)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-header {\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  height: $header-height;\n\n  .header-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: 100%;\n  }\n\n  .logo {\n    cursor: pointer;\n    \n    h2 {\n      color: $primary-color;\n      font-size: 20px;\n      font-weight: 600;\n    }\n  }\n\n  .nav-menu {\n    display: flex;\n    align-items: center;\n    gap: 30px;\n\n    .nav-item {\n      color: $text-regular;\n      text-decoration: none;\n      font-weight: 500;\n      transition: color 0.3s;\n\n      &:hover,\n      &.router-link-active {\n        color: $primary-color;\n      }\n    }\n  }\n\n  .user-info {\n    display: flex;\n    align-items: center;\n    gap: 15px;\n\n    .user-name {\n      color: $text-primary;\n      cursor: pointer;\n      font-weight: 500;\n    }\n  }\n}\n\n@media (max-width: $mobile) {\n  .app-header {\n    .nav-menu {\n      display: none;\n    }\n    \n    .logo h2 {\n      font-size: 16px;\n    }\n  }\n}\n</style>\n"]}]}