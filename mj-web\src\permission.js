/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 13:36:21
 * @LastEditors: dlg
 * @LastEditTime: 2020-10-09 15:52:55
 */
import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress"; // 进度条
import "nprogress/nprogress.css"; // 进度条样式
import { getToken } from "@/utils/auth"; // 从 cookie 获取 cookie值

// 通过将 showSpinner 设置为false来关闭加载微调器
NProgress.configure({ easing: "ease", speed: 500, showSpinner: false }); // 进度条配置

const whiteList = ["/login", "/forgetPass"]; // 白名单，可根据自己需求添加

// 全局前置守卫
router.beforeEach(async(to, from, next) => {
    NProgress.start(); // 进度条开始
    const hasToken = getToken(); // 判断用户是否登录
    if (hasToken) {
        // token 存在
        if (to.path === "/login") {
            // 如果去往的路由是登陆页面
            next({ path: "/" });
            NProgress.done(); // 如果当前页是登录页，则不会触发 afterEach 钩子函数,需要手动处理 NProgress.done
        } else {
            let hasRouters = store.getters.addRouters && store.getters.addRouters.length > 0;
            // 判断动态路由是否存在，避免重复加载
            if (!hasRouters) {
                try {
                    // 动态生成当前用户具有权限可访问的路由表
                    const accessRoutes = await store.dispatch("GenerateRoutes");
                    router.addRoutes(accessRoutes); // 动态挂载路由
                    // router.addRoutes之后的next()可能会失效，因为可能next()的时候路由并没有完全add完成
                    // 可以通过next(to)避开上面那个问题。下面这行代码执行会重新进入router.beforeEach这个钩子，这时候会进入else，再通过next()来释放钩子，就能确保所有的路由都已经挂在完成了。
                    next({...to, replace: true }); // replace 设置为 true, 它不会向 history 添加新记录
                } catch (error) {
                    await store.dispatch('resetUserCookies') // 清空用户cookie
                    Message.error({ message: error });
                    next({ path: "/login" }); // 发生异常错误定向到登录页
                    NProgress.done();
                }
            } else {
                // 用户权限角色已经存在
                next();
            }
        }
    } else {
        // token 不存在
        if (whiteList.indexOf(to.path) !== -1) {
            // 在免登录白名单，直接进入
            next();
        } else {
            // 既没有 token ，也不在白名单里，则全部重定向到登陆页
            // next(`/login?redirect=${to.path}`);

            // 为了保证登出系统后，重新登录的用户权限没有登出之前所在页面的权限时，出现登录跳转到401或404页面情况。
            // (正常来说每次重新进入系统应该是初始化状态，不应该默认跳到上次退出系统时的页面)
            next("/login");
            NProgress.done(); // 如果当前页是登录页，则不会触发 afterEach 钩子函数,需要手动处理 NProgress.done
        }
    }
});

// 全局后置钩子函数
router.afterEach(() => {
    NProgress.done(); // 进度条结束
});