@import './variables.scss'; //必须先导入样式值文件
@import './sidebar.scss';
@import './switch.scss';
body {
    height: 100%;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

html {
    height: 100%;
    box-sizing: border-box;
}

#app {
    height: 100%;
}

// html节点的所有子节点继承 box-sizing: border-box 属性
*,
*:before,
*:after {
    box-sizing: inherit;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

.fr {
    float: right;
}

.fl {
    float: left;
}

.clearfix {
    zoom: 1;
    &:after {
        visibility: hidden;
        display: block;
        font-size: 0;
        content: " ";
        clear: both;
        height: 0;
    }
}

//main-container全局样式
.app-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
}

// 大屏页面禁止鼠标选中
.full-screen-container {
    user-select: none;
}

// 大屏loading
.bigHomeLoading .el-icon-loading {
    font-size: 36px;
}

.el-dialog{
    border-radius: 4px !important;
}