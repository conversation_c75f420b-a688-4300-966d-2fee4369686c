package model

// 企业对象
type SysCompany struct {
	Model
	Name            string `json:"name" gorm:"comment:'公司名称'"`
	Code            string `json:"code" gorm:"unique;comment:'公司编码'"`
	Address         string `json:"address" gorm:comment:"公司地址"`
	Telephone       string `json:"telephone" gorm:comment:"公司电话"`
	LogoPreviewPath string `json:"logoPreviewPath" gorm:"comment:'公司logo浏览地址'"`
	PersonLiable    string `json:"personLiable" gorm:comment:"公司责任人"`
	Status          int8   `json:"status" gorm:default:0;comment:"状态"`
	Email           string `json:"email" gorm:comment:"公司邮箱"`
	AdminUser       string `json:"adminUser" gorm:"comment:'公司管理员账号'"`
	Postcode        string `json:"postcode" gorm:"comment:'邮编'"`
}

func (SysCompany) TableName() string {
	return "sys_company"
}
