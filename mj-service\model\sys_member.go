package model

import (
	"github.com/Gre-Z/common/jtime"
	uuid "github.com/satori/go.uuid"
)

// 竞价参与者（会员）对象
type SysMember struct {
	Model
	UUID            uuid.UUID      `json:"uuid" gorm:"comment:'会员UUID'"`
	Mobile          string         `json:"mobile" gorm:"comment:'手机号（登录名）'"`
	Password        string         `json:"-" gorm:"comment:'登录密码'"`
	Name            string         `json:"name" gorm:"comment:'姓名'"`
	CompanyName     string         `json:"companyName" gorm:"comment:'竞价企业名称'"`
	BusinessLicense string         `json:"businessLicense" gorm:"comment:'企业营业执照图片路径'"`
	CreditCode      string         `json:"creditCode" gorm:"comment:'统一社会信用代码'"`
	Status          int8           `json:"status" gorm:"default:1;comment:'状态(1:正常,2:禁用)'"`
	AuditStatus     int8           `json:"auditStatus" gorm:"default:0;comment:'审核状态(0:待审核,1:审核通过,2:审核拒绝)'"`
	AuditTime       jtime.JsonTime `json:"auditTime" gorm:"comment:'审核时间'"`
	AuditBy         int            `json:"auditBy" gorm:"comment:'审核人ID'"`
	AuditRemark     string         `json:"auditRemark" gorm:"comment:'审核备注'"`
	RegisterTime    jtime.JsonTime `json:"registerTime" gorm:"comment:'注册时间'"`
	LastLoginTime   jtime.JsonTime `json:"lastLoginTime" gorm:"comment:'最后登录时间'"`
	LoginCount      int            `json:"loginCount" gorm:"default:0;comment:'登录次数'"`
	Email           string         `json:"email" gorm:"comment:'邮箱'"`
	ContactPerson   string         `json:"contactPerson" gorm:"comment:'联系人'"`
	ContactPhone    string         `json:"contactPhone" gorm:"comment:'联系电话'"`
	Address         string         `json:"address" gorm:"comment:'企业地址'"`
	Remark          string         `json:"remark" gorm:"comment:'备注'"`
}

func (SysMember) TableName() string {
	return "sys_members"
}
