<template>
  <div class="login-page">
    <AppHeader />
    
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <h2>用户登录</h2>
          <p>欢迎回到棉副产品竞价平台</p>
        </div>

        <el-form
          ref="loginForm"
          :model="form"
          :rules="rules"
          label-width="0"
          class="login-form"
        >
          <el-form-item prop="mobile">
            <el-input
              v-model="form.mobile"
              placeholder="请输入手机号"
              prefix-icon="el-icon-phone"
              size="large"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="el-icon-lock"
              size="large"
              show-password
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>

          <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="form.rememberMe">记住我</el-checkbox>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              @click="handleLogin"
              :loading="loading"
              class="login-btn"
              size="large"
            >
              登录
            </el-button>
          </el-form-item>

          <div class="register-link">
            还没有账号？
            <router-link to="/register" class="link">立即注册</router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    // 手机号验证
    const validateMobile = (rule, value, callback) => {
      const mobileReg = /^1[3-9]\d{9}$/
      if (!mobileReg.test(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }

    return {
      form: {
        mobile: '',
        password: '',
        rememberMe: false
      },
      rules: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    async handleLogin() {
      try {
        await this.$refs.loginForm.validate()
      } catch (error) {
        return
      }

      this.loading = true
      try {
        const result = await this.$store.dispatch('auth/login', this.form)
        if (result.success) {
          this.$message.success('登录成功')
          
          // 检查是否有重定向地址
          const redirect = this.$route.query.redirect || '/home'
          this.$router.push(redirect)
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        // this.$message.error('登录失败，请稍后重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - #{$header-height});
  padding: 40px 20px;
}

.login-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    color: $text-primary;
    margin-bottom: 10px;
  }

  p {
    color: $text-secondary;
    font-size: 14px;
  }
}

.login-form {
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;

    .forgot-password {
      color: $primary-color;
      text-decoration: none;
      font-size: 14px;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .login-btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
  }

  .register-link {
    text-align: center;
    margin-top: 20px;
    color: $text-secondary;
  }

  .link {
    color: $primary-color;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: $mobile) {
  .login-card {
    padding: 30px 20px;
  }
}
</style>
