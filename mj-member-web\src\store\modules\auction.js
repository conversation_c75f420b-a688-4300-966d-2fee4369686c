import { getAuctionList, getAuctionDetail, placeBid } from '@/api/auction'

const state = {
  auctionList: [],
  currentAuction: null,
  bidHistory: [],
  loading: false
}

const mutations = {
  SET_AUCTION_LIST(state, list) {
    state.auctionList = list
  },
  SET_CURRENT_AUCTION(state, auction) {
    state.currentAuction = auction
  },
  SET_BID_HISTORY(state, history) {
    state.bidHistory = history
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  UPDATE_AUCTION_PRICE(state, { auctionId, newPrice, bidCount }) {
    // 更新竞价列表中的价格
    const auction = state.auctionList.find(item => item.id === auctionId)
    if (auction) {
      auction.currentPrice = newPrice
      auction.bidCount = bidCount
    }
    
    // 更新当前竞价详情中的价格
    if (state.currentAuction && state.currentAuction.id === auctionId) {
      state.currentAuction.currentPrice = newPrice
      state.currentAuction.bidCount = bidCount
    }
  }
}

const actions = {
  // 获取竞价列表
  async fetchAuctionList({ commit }, params = {}) {
    commit('SET_LOADING', true)
    try {
      // 构建请求参数，后端使用POST方法
      const requestData = {
        page: params.page || 1,
        pageSize: params.pageSize || 12,
        status: params.status || '',
        categoryId: params.categoryId || ''
      }

      const response = await getAuctionList(requestData)
      if (response.data.code === 200) {
        commit('SET_AUCTION_LIST', response.data.data.list || [])
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('获取竞价列表失败:', error)
      return { success: false, message: '获取竞价列表失败' }
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 获取竞价详情
  async fetchAuctionDetail({ commit }, auctionId) {
    commit('SET_LOADING', true)
    try {
      const response = await getAuctionDetail({ id: auctionId })
      if (response.data.code === 200) {
        commit('SET_CURRENT_AUCTION', response.data.data)
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('获取竞价详情失败:', error)
      return { success: false, message: '获取竞价详情失败' }
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 出价
  async placeBid({ commit, dispatch }, { auctionId, bidAmount }) {
    try {
      const response = await placeBid({ projectId: auctionId, bidAmount })
      if (response.data.code === 200) {
        // 出价成功后刷新竞价详情
        await dispatch('fetchAuctionDetail', auctionId)
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.msg }
      }
    } catch (error) {
      console.error('出价失败:', error)
      return { success: false, message: '出价失败，请稍后重试' }
    }
  },

  // 实时更新竞价信息（WebSocket）
  updateAuctionPrice({ commit }, data) {
    commit('UPDATE_AUCTION_PRICE', data)
  }
}

const getters = {
  auctionList: state => state.auctionList,
  currentAuction: state => state.currentAuction,
  bidHistory: state => state.bidHistory,
  loading: state => state.loading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
