module.exports = {
  transpileDependencies: [],
  lintOnSave: false,
  devServer: {
    port: 8081,
    proxy: {
      '/auction': {
        target: 'http://localhost:8888',
        changeOrigin: true
      },
      '/admin': {
        target: 'http://localhost:8888',
        changeOrigin: true
      }
    }
  },
  css: {
    loaderOptions: {
      sass: {
        prependData: `
          @import "@/styles/variables.scss";
          @import "@/styles/mixins.scss";
        `
      }
    }
  }
}
