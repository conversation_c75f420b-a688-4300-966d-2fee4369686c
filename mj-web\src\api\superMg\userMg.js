import request from '@/utils/request'

/**
 * @description: 获取所有用户角色信息
 * @param {type}
 * @return: 所有用户角色信息列表
 */
export function getAuthList() {
    return request({
        url: "/admin/authority/getAuthBySelfCompany",
        method: 'post'
    })
}

/**
 * @description: 获取用户列表
 * @param {data:"object"}
 * @return:列表数据
 */
export function getUserList(data) {
    return request({
        url: "/admin/user/getUserList",
        method: 'post',
        data
    })
}

/**
 * @description: 设置用户权限
 * @param {data:"object"}
 * @return: success or fail
 */
export function setUserAuthority(data) {
    return request({
        url: "/admin/user/updateUserAuth",
        method: 'post',
        data
    })
}

/**
 * @description: 用户注册(新增用户)
 * @param {data:"object"}
 * @return:success or fail
 */
export function register(data) {
    return request({
        url: "/admin/user/updateUser",
        method: "post",
        data
    })
}

/**
 * @description:启用或禁用
 * @param {data:"object"}
 * @return: success or fail
 */
export function enableOrDisable(data) {
    return request({
        url: "/admin/user/updateUserStatus",
        method: 'post',
        data
    })
}

/**
 * @description: 重置密码
 * @param {data:"object"}
 * @return: success or fail
 */
export function resetPass(data) {
    return request({
        url: "/admin/user/resetPassword",
        method: 'post',
        data
    })
}

/**
 * @description: 删除用户
 * @param {data:"object"}
 * @return: success or fail
 */
export function deleteUser(data) {
    return request({
        url: "/admin/user/deleteUser",
        method: 'post',
        data
    })
}


/**
 * @description:获取企业列表信息
 * @param {type}
 * @return:
 */
export function getCompany() {
    return request({
        url: "/admin/company/getCompanyCondition",
        method: 'post'
    })
}
/**
 * @description: 修改用户温区权限
 * @param {type}
 * @return {type}
 */
export function sampleUserList(data) {
    return request({
        url: "/admin/user/listSample",
        method: 'post',
        data
    })
}
/**
 * @description: 修改用户温区权限
 * @param {type}
 * @return {type}
 */
export function sendNotes(data) {
    return request({
        url: "/admin/user/senNotes",
        method: 'post',
        data
    })
}

/**
 * @description: 修改用户温区权限
 * @param {type}
 * @return {type}
 */
export function setParams(data) {
    return request({
        url: "/admin/user/setParams",
        method: 'post',
        data
    })
}
