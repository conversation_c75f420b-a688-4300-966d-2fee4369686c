// <AUTHOR>
// @date
// @note
package utils

import (
	"auction-sys/global"
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/axgle/mahonia"
)

// @title         SendSMS
// @description   发送短信
// @auth          赵卫民           时间（2020/9/21  22:07 ）
// @param         CorpID          string         "短信通道用户名"
// @param         Pwd             string         "短信通道密码"
// @param         Mobile          string         "手机号"
// @param         code       	  string         "验证码"
// @return                        无

func SendSMS(CorpID string, Pwd string, Mobile string, content string) {
	if Mobile != "" {
		gbkContent := Utf8ToGbk(content)
		smsContent := "CorpID=" + CorpID + "&Pwd=" + Pwd + "&Mobile=" + Mobile + "&Content=" + gbkContent
		global.GVA_LOG.Info(Mobile + "--" + content)
		body, er := NewRequest("GET", "https://sdk3.028lk.com:9988/BatchSend2.aspx", []byte(smsContent))
		global.GVA_LOG.Debug(string(body), er)
	}
}

// @title         Utf8ToGbk
// @description   UTF-8转GBK字符
// @auth          赵卫民           时间（2020/9/21  22:07 ）
// @param         utfStr          string         "utf字符"
// @return                        string         "GBK字符"
func Utf8ToGbk(utfStr string) string {
	var enc mahonia.Encoder
	enc = mahonia.NewEncoder("gbk")
	ret := enc.ConvertString(utfStr)
	return ret
}

// NewRequest 请求包装

// @title         NewRequest
// @description   向短信平台发送请求
// @auth          赵卫民           时间（2020/9/21  22:07 ）
// @param         method           string              "请求方式"
// @param         data             []byte              "请求数据"
// @return                         []byte  error       "返回数据、错误信息"
func NewRequest(method, url string, data []byte) (body []byte, err error) {

	if method == "GET" {
		url = fmt.Sprint(url, "?", string(data))
		data = nil
	}

	client := http.Client{}
	req, err := http.NewRequest(method, url, bytes.NewBuffer(data))
	req.Header.Add("content-type", "application/json")
	if err != nil {
		return body, err
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	body, err = ioutil.ReadAll(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		return body, err
	}

	return body, err
}
