package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"auction-sys/utils"
	"auction-sys/wechat"
	"bytes"
	ran "crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/Gre-Z/common/jtime"
	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
)

// @Title 用户注册
// @Description
// <AUTHOR>
// @Param u 			model.SysUser	用户信息
// @Param authorityIds 	[]int			角色 id 数组
// @Return err 			error 			错误信息

func Register(u model.SysUser, authorityIds []int) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var user model.SysUser
		// 判断用户名是否注册
		var notRegister bool
		if u.Email != "" {
			notRegister = tx.Where("username = ? or email = ?", u.Username, u.Email).First(&user).RecordNotFound()
		} else {
			notRegister = tx.Where("username = ?", u.Username).First(&user).RecordNotFound()
		}

		// notRegister为false表明读取到了 不能注册
		if !notRegister {
			return errors.New("用户名或邮箱已被使用")
		} else {
			if len(authorityIds) == 0 {
				authorityIds = append(authorityIds, 957)
			}
			// 根据角色ids查询角色数组
			var authorities []model.SysAuthority
			err = tx.Where("id in (?)", authorityIds).Find(&authorities).Error
			u.SysAuthoritys = authorities
			// 否则 附加uuid 密码md5简单加密 注册
			if u.Password == "" {
				u.Password = utils.MD5V([]byte(constants.PASSWORD_DEFAULT))
				u.PasswordStatus = constants.STATUS_ENABLED // 默认需要修改密码
			} else {
				u.Password = utils.MD5V([]byte(u.Password))
			}

			u.LoginStatus = constants.STATUS_DISABLED
			u.UUID = uuid.NewV4()
			// 头像赋值为默认头像
			u.HeaderImg = global.GVA_CONFIG.Logo.User
			u.CompanyCode = "91650100MA789PKK5K"
			u.Status = constants.STATUS_ENABLED
			err = tx.Create(&u).Error
			if err != nil {
				return err
			}
			for _, auth := range authorities {
				// 根据角色id查询当前角色绑定了多少用户，更新角色表中BindingUsers
				var bindUserNum int
				err = tx.Table("sys_authority_users").Where("sys_authority_id = ?", auth.ID).Find(&model.SysAuthorityUsers{}).Count(&bindUserNum).Error
				if err != nil {
					return err
				}
				err = tx.Table("sys_authorities").Where("id = ?", auth.ID).Update("binding_users", bindUserNum).Error
				if err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// @Title 用户登录
// @Description
// <AUTHOR>
// @Param u 			*model.SysUser			账号密码
// @Return err 			error 					错误信息
// @Return userInter 	*resp.LoginStruct 	用户信息

func Login(u *model.SysUser) (err error, userInter *resp.LoginStruct) {
	var userInfo resp.LoginStruct
	var user model.SysUser
	u.Password = utils.MD5V([]byte(u.Password))
	err = global.GVA_DB.Where("(username = ? or email = ?) AND password = ?", u.Username, u.Username, u.Password).Preload("SysAuthoritys").First(&user).Error
	if err != nil {
		return errors.New("用户名密码错误"), &userInfo
	}
	if user.Status == constants.STATUS_DISABLED {
		return errors.New("您的账号已停用"), &userInfo
	}
	userInfo.User.ID = user.ID
	userInfo.User.UUID = user.UUID
	userInfo.User.Username = user.Username
	userInfo.User.NickName = user.NickName
	userInfo.User.HeaderImg = user.HeaderImg
	userInfo.User.CompanyCode = user.CompanyCode
	userInfo.User.PasswordStatus = user.PasswordStatus
	userInfo.User.WxStatus = user.OpenId != ""
	userInfo.User.IsFarmer = user.IsFarmer
	if user.Username == global.GVA_CONFIG.Admin.Username {
		userInfo.User.IsRoot = constants.ROOT
	} else {
		userInfo.User.IsRoot = constants.NOROOT
	}
	var loginUser model.SysUser
	loginUser.ID = user.ID
	loginUser.LoginStatus = constants.STATUS_ENABLED
	loginUser.LoginTime = jtime.JsonTime{time.Now()}
	var company model.SysCompany
	err = global.GVA_DB.Where("code = ? AND status = 1", user.CompanyCode).First(&company).Error
	if err != nil {
		return errors.New("贵公司账号已停用"), &userInfo
	}
	userInfo.User.CompanyName = company.Name
	userInfo.User.CompanyLogo = company.LogoPreviewPath
	// 修改用户登录状态以及登录时间
	err = global.GVA_DB.Model(&loginUser).Updates(&loginUser).Error
	return err, &userInfo
}

// @Title 修改用户密码
// @Description
// <AUTHOR>
// @Param u 			*model.SysUser			用户信息
// @Param newPassword 	string					新密码
// @Return err 			error 					错误信息
// @Return userInter 	*resp.SysUser 		用户信息

func ChangePassword(u *model.SysUser, newPassword string) (err error, userInter *model.SysUser) {
	var user model.SysUser
	// TODO:后期修改jwt+password模式
	u.Password = utils.MD5V([]byte(u.Password))
	err = global.GVA_DB.Where("uuid = ? ", u.UUID).First(&user).Error
	if u.Password != user.Password {
		return errors.New("原密码输入错误"), u
	}
	err = global.GVA_DB.Table("sys_users").Where("uuid = ?", u.UUID).Update(map[string]interface{}{"password": utils.MD5V([]byte(newPassword)), "password_status": constants.STATUS_DISABLED}).Error
	return err, u
}

// @Title 重置用户密码
// @Description
// <AUTHOR>
// @Param id 	int			用户id
// @Return err 	error 		错误信息

func ResetPassword(id int) error {
	password := utils.MD5V([]byte(constants.PASSWORD_DEFAULT))
	err := global.GVA_DB.Table("sys_users").Where("id = ?", id).Updates(map[string]interface{}{"password": password, "password_status": constants.STATUS_ENABLED}).Error
	return err
}

// @Title 分页获取数据
// @Description
// <AUTHOR>
// @Param info 			req.GetUserList		查询条件
// @Param uuid 			string					用户唯一标识
// @Return err 			error 					错误信息
// @Return list 		interface{} 			列表
// @Return total 		int 					总条数
// @Return enableNum 	int 					启用数量
// @Return disableNum 	int 					禁用数量

func GetUserInfoList(info req.GetUserList, uuid string) (err error, list interface{}, total int, enableNum, disableNum int) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var currentUser model.SysUser
	_, currentUser = GetCurrentUser(uuid)
	db := global.GVA_DB
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		db = db.Where("company_code = ? and username != ?", currentUser.CompanyCode, global.GVA_CONFIG.Admin.Username)
	}
	if info.CompanyCode != "" {
		db = db.Where("company_code = ?", info.CompanyCode)
	}
	if info.Username != "" {
		db = db.Where("mobile like ?", "%"+info.Username+"%")
	}

	if info.NickName != "" {
		db = db.Where("nick_name like ?", "%"+info.NickName+"%")
	}

	//if !currentUser.IsRoot {
	//	db = db.Where("create_by = ?", currentUser.ID)
	//}

	db = db.Where("is_farmer = ?", info.IsFarmer)
	db = db.Where("id <> ?", currentUser.ID)
	var userList []model.SysUser

	err = db.Find(&userList).Count(&total).Error
	err = db.Limit(limit).Offset(offset).Preload("SysAuthoritys").Find(&userList).Error
	err = db.Where("status = ?", constants.STATUS_ENABLED).Find(&[]model.SysUser{}).Count(&enableNum).Error
	err = db.Where("status = ?", constants.STATUS_DISABLED).Find(&[]model.SysUser{}).Count(&disableNum).Error

	return err, userList, total, enableNum, disableNum
}

type Count struct {
	Count int
}

// @Title 删除用户
// @Description
// <AUTHOR>
// @Param ids 		[]req.GetById	id 数组
// @Return err 		error 				错误信息

func DeleteUser(ids []req.GetById) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, id := range ids {
			var oldUser model.SysUser
			err = tx.Where("id = ?", id.Id).First(&oldUser).Error
			if err != nil {
				return err
			}
			// 获取此用户绑定的角色
			var authIds []AuthIds
			SQL := "select sys_authority_id as id from sys_authority_users where sys_user_id = ?"
			tx.Raw(SQL, id.Id).Scan(&authIds)
			// 删除用户
			var user model.SysUser
			err = tx.Where("id = ?", id.Id).Delete(&user).Error
			if err != nil {
				return err
			}
			// 删除用户绑定的角色
			deleteSql := "delete from sys_authority_users where sys_user_id = ?"
			err = tx.Exec(deleteSql, id.Id).Error
			if err != nil {
				return err
			}
			for _, authId := range authIds {
				// 根据角色id查询当前角色绑定了多少用户，更新角色表中BindingUsers
				var bindUserNum Count
				SQL := "select count(1) as count from sys_authority_users where sys_authority_id = ?"
				err = tx.Raw(SQL, authId.Id).Scan(&bindUserNum).Error
				if err != nil {
					return err
				}
				// 更新角色表中 BindingUsers
				err = tx.Table("sys_authorities").Where("id = ?", authId.Id).Update("binding_users", bindUserNum.Count).Error
				if err != nil {
					return err
				}
			}
			if oldUser.HeaderImg != global.GVA_CONFIG.Logo.User {
				index := strings.LastIndex(oldUser.HeaderImg, "/")
				key := oldUser.HeaderImg[index+1 : len(oldUser.HeaderImg)]
				err = utils.DeleteFile(key)
			}
		}
		return nil
	})
}

// @Title 用户头像上传更新地址
// @Description
// <AUTHOR>
// @Param uuid 			uuid.UUID		UUID
// @Param imageUrl 		string			图片路径
// @Return err 			error 			错误信息
// @Return userInter 	*model.SysUser 	用户信息

func UploadHeaderImg(uuid uuid.UUID, imageUrl string) (err error, userInter *model.SysUser) {
	var oldUser model.SysUser
	upDataMap := make(map[string]interface{})
	upDataMap["header_img"] = imageUrl
	var user model.SysUser
	db := global.GVA_DB.Where("uuid = ?", uuid).First(&oldUser)
	// 如果修改前并且不是默认头像，删除原来的文件
	if oldUser.HeaderImg != global.GVA_CONFIG.Logo.User {
		index := strings.LastIndex(oldUser.HeaderImg, "/")
		key := oldUser.HeaderImg[index+1 : len(oldUser.HeaderImg)]
		err = utils.DeleteFile(key)
	}
	err = db.Updates(upDataMap).Error
	return err, &user
}

// @Title 更新用户状态
// @Description
// <AUTHOR>
// @Param users 		[]model.SysUser		用户状态数组
// @Return err 			error 				错误信息

func UpdateUserStatus(users []model.SysUser) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, user := range users {
			err = tx.Model(&user).Update("status", user.Status).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// @Title 更新用户
// @Description
// <AUTHOR>
// @Param user 		model.SysUser	用户信息
// @Param authIds 	[]int			角色 id 数组
// @Return err 		error 			错误信息

func UpdateUser(user model.SysUser, authIds []int) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查询用户旧数据
		var oldUser model.SysUser
		err = tx.Where("id = ?", user.ID).First(&oldUser).Error
		if err != nil {
			return errors.New("用户不存在")
		}
		if oldUser.Username != user.Username {
			return errors.New("非法操作")
		}

		upDataMap := make(map[string]interface{})
		upDataMap["nick_name"] = user.NickName
		upDataMap["status"] = user.Status
		// 如果 email 改变了，验证数据库中是否存在 email
		if user.Email != oldUser.Email {
			err = tx.Where("id <> ? and email = ?", user.ID, user.Email).First(&model.SysUser{}).Error
			if err == nil {
				return errors.New("邮箱已存在")
			}
			upDataMap["email"] = user.Email
		}

		// 获取此用户原有绑定，但此次未绑定的角色
		var auths []AuthIds
		SQL := "select sys_authority_id as id from sys_authority_users where sys_user_id = ? and sys_authority_id not in(?)"
		tx.Raw(SQL, user.ID, authIds).Scan(&auths)

		// 删除现有的关联
		SQL = "delete from sys_authority_users where sys_user_id = ?"
		err = tx.Table("sys_authority_users").Exec(SQL, user.ID).Error
		if err != nil {
			return err
		}

		// 更改用户原有绑定，但此次未绑定的角色的绑定用户数
		for _, authId := range auths {
			// 根据角色id查询当前角色绑定了多少用户，更新角色表中BindingUsers
			var bindUserNum Count
			SQL := "select count(1) as count from sys_authority_users where sys_authority_id = ?"
			err = tx.Raw(SQL, authId.Id).Scan(&bindUserNum).Error
			if err != nil {
				return err
			}
			err = tx.Table("sys_authorities").Where("id = ?", authId.Id).Update("binding_users", bindUserNum.Count).Error
			if err != nil {
				return err
			}
		}

		// 根据角色ids查询角色数组
		// 如果有选择角色，更新关联信息
		for _, authId := range authIds {
			var au model.SysAuthorityUsers
			au.SysAuthorityId = authId
			au.SysUserId = int(user.ID)
			err = tx.Create(&au).Error
			if err != nil {
				return err
			}
			// 根据角色id查询当前角色绑定了多少用户，更新角色表中BindingUsers
			var bindUserNum Count
			SQL := "select count(1) as count from sys_authority_users where sys_authority_id = ?"
			err = tx.Raw(SQL, authId).Scan(&bindUserNum).Error
			if err != nil {
				return err
			}
			err = tx.Table("sys_authorities").Where("id = ?", authId).Update("binding_users", bindUserNum.Count).Error
			if err != nil {
				return err
			}
		}
		err = tx.Table("sys_users").Where("id = ?", user.ID).Updates(&user).Error
		if err != nil {
			return errors.New("更新失败")
		}
		return nil
	})
}

// @Title 更新用户
// @Description
// <AUTHOR>
// @Param user 		model.SysUser	用户信息
// @Param authIds 	[]int			角色 id 数组
// @Return err 		error 			错误信息

func UpdateFarmer(user model.SysUser) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查询用户旧数据
		var oldUser model.SysUser
		err = tx.Where("id = ?", user.ID).First(&oldUser).Error
		if err != nil {
			return errors.New("用户不存在")
		}
		if oldUser.Username != user.Username {
			return errors.New("非法操作")
		}

		deptName := fmt.Sprintf("%d师%d团%d连", user.ShiNum, user.TuanNum, user.LianNum)

		upDataMap := make(map[string]interface{})
		upDataMap["nick_name"] = user.NickName
		upDataMap["dept_name"] = deptName
		upDataMap["id_card"] = user.IdCard
		upDataMap["mobile"] = user.Mobile
		upDataMap["desc"] = user.Desc
		upDataMap["address"] = user.Address
		upDataMap["status"] = user.Status
		upDataMap["username"] = user.Username
		upDataMap["shi_num"] = user.ShiNum
		upDataMap["tuan_num"] = user.TuanNum
		upDataMap["lian_num"] = user.LianNum

		err = tx.Table("sys_users").Where("id = ?", user.ID).Updates(upDataMap).Error
		if err != nil {
			return errors.New("更新失败")
		}
		return nil
	})
}

// @Title 修改在线状态，任务调度调用
// @Description
// <AUTHOR>
// @Param uuid 	string	用户唯一标识
// @Return err 	error 	错误信息

func UpdateOnlineStatus(uuid string) (err error) {
	// 设置下线状态
	err = global.GVA_DB.Where("uuid = ?", uuid).First(&model.SysUser{}).Update("login_status", constants.STATUS_DISABLED).Error
	return
}

// @Title 修改用户角色
// @Description
// <AUTHOR>
// @Param r 	req.RegisterStruct	用户信息
// @Return err 	error 					错误信息

func UpdateUserAuth(r req.RegisterStruct) (err error) {
	tx := global.GVA_DB.Begin()
	var oldUser model.SysUser
	err = tx.Where("id = ?", r.ID).First(&oldUser).Error
	if err != nil {
		return errors.New("用户不存在")
	}
	SQL := "delete from sys_authority_users where sys_user_id = ?"
	err = tx.Table("sys_authority_users").Exec(SQL, r.ID).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	for _, authId := range r.AuthorityIds {
		var au model.SysAuthorityUsers
		au.SysAuthorityId = authId
		au.SysUserId = r.ID
		err = tx.Create(&au).Error
		if err != nil {
			tx.Rollback()
			return err
		}
		// 根据角色id查询当前角色绑定了多少用户，更新角色表中BindingUsers
		var bindUserNum int
		err = tx.Table("sys_authority_users").Where("sys_authority_id = ?", authId).Find(&model.SysAuthorityUsers{}).Count(&bindUserNum).Error
		if err != nil {
			tx.Rollback()
			return err
		}
		err = tx.Table("sys_authorities").Where("id = ?", authId).Update("binding_users", bindUserNum).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return err
}

// @Title 个人中心，用户信息
// @Description
// <AUTHOR>
// @Param uuid 			string					用户唯一标识
// @Return err 			error 					错误信息
// @Return personInfo 	resp.PersonInfo 	个人信息

func GetPersonInfo(uuid string) (err error, personInfo resp.PersonInfo) {
	// 查询个人信息
	var userInfo model.SysUser
	err = global.GVA_DB.Where("uuid = ? and status = ?", uuid, constants.STATUS_ENABLED).First(&userInfo).Error
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return errors.New("数据加载失败"), personInfo
	}
	// 查询企业信息
	var company model.SysCompany
	err = global.GVA_DB.Where("code = ? and status = ?", userInfo.CompanyCode, constants.STATUS_ENABLED).First(&company).Error
	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return errors.New("数据加载失败"), personInfo
	}
	personInfo.CompanyCode = company.Code
	personInfo.Age = userInfo.Age
	personInfo.CompanyName = company.Name
	personInfo.Email = userInfo.Email
	personInfo.Gender = userInfo.Gender
	personInfo.NickName = userInfo.NickName
	personInfo.Username = userInfo.Username
	personInfo.QQ = userInfo.QQ
	personInfo.Address = userInfo.Address
	personInfo.IdCard = userInfo.IdCard
	personInfo.ShiNum = userInfo.ShiNum
	personInfo.TuanNum = userInfo.TuanNum
	personInfo.LianNum = userInfo.LianNum
	personInfo.DeptName = userInfo.DeptName
	return err, personInfo
}

// @Title 个人中心，更新信息
// @Description
// <AUTHOR>
// @Param uuid 			string					用户唯一标识
// @Param personInfo 	resp.PersonInfo		用户信息
// @Return err 			error 					错误信息

func UpdatePersonInfo(uuid string, personInfo resp.PersonInfo) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查询用户旧数据
		var oldUser model.SysUser
		err = tx.Where("uuid = ?", uuid).First(&oldUser).Error
		if err != nil {
			global.GVA_LOG.Error(err.Error())
			return errors.New("用户不存在")
		}
		if oldUser.Username != personInfo.Username {
			global.GVA_LOG.Error(err.Error())
			return errors.New("非法操作")
		}

		// 如果 email 改变了，验证数据库中是否存在 email
		if personInfo.Email != oldUser.Email {
			err = tx.Where("id <> ? and email = ?", oldUser.ID, personInfo.Email).First(&model.SysUser{}).Error
			if err == nil {
				return errors.New("邮箱已存在")
			}
		}

		deptName := fmt.Sprintf("%d师%d团%d连", personInfo.ShiNum, personInfo.TuanNum, personInfo.LianNum)
		personInfo.DeptName = deptName
		err = tx.Table("sys_users").Where("id = ?", oldUser.ID).Updates(&personInfo).Error
		if err != nil {
			global.GVA_LOG.Error(err.Error())
			return errors.New("更新失败")
		}

		// 查询此用户是否是管理员
		var flag bool
		var oldCompany model.SysCompany
		flag = tx.Where("admin_user = ? and status = ?", oldUser.Username, constants.STATUS_ENABLED).First(&oldCompany).RecordNotFound()
		if !flag {
			// 更新企业联系人
			oldCompany.PersonLiable = personInfo.NickName
			oldCompany.Email = personInfo.Email
			err = tx.Table("sys_company").Where("id = ?", oldCompany.ID).Updates(&oldCompany).Error
			if err != nil {
				global.GVA_LOG.Error(err.Error())
				return errors.New("更新失败")
			}
		}
		return err
	})
}

// @Title 获取当前用户
// @Description
// <AUTHOR>
// @Param uuid 				string			用户唯一标识
// @Return err 				error 			错误信息
// @Return currentUser 		model.SysUser 	用户信息

func GetCurrentUser(uuid string) (err error, currentUser model.SysUser) {
	// 查询当前用户
	var notRegister bool
	notRegister = global.GVA_DB.Where("uuid = ?", uuid).Preload("SysAuthoritys").First(&currentUser).RecordNotFound()
	if notRegister {
		return errors.New("用户不存在"), currentUser
	}
	isRoot := false
	for _, auth := range currentUser.SysAuthoritys {
		if auth.ID == 888 {
			isRoot = true
			break
		}
	}
	currentUser.IsRoot = isRoot
	return err, currentUser
}

// @Title 检查用户是否存在
// @Description
// <AUTHOR>
// @Param username 	string	用户名
// @Return err 		error 	错误信息

func CheckUserExists(username string) (err error) {
	var user model.SysUser
	var notExists bool
	notExists = global.GVA_DB.Where("username = ? and status = ?", username, constants.STATUS_ENABLED).First(&user).RecordNotFound()
	if notExists {
		return errors.New("该手机号不存在")
	}
	// 查询公司状态是否正常
	var company model.SysCompany
	err = global.GVA_DB.Where("code = ?", user.CompanyCode).First(&company).Error
	if err != nil {
		return errors.New("企业信息错误")
	}
	if company.Status == constants.STATUS_DISABLED {
		return errors.New("该企业服务已到期")
	}
	return err
}

// @Title       根据ID 获取用户信息
// @Description
// <AUTHOR>
// @Param       id      int64
// @Return
func GetUserById(id int) (err error, user model.SysUser) {

	if global.GVA_DB.Table("sys_users").Where("id = ?", id).Preload("SysAuthoritys").First(&user).RecordNotFound() {
		return errors.New("用户不存在"), user
	}
	isRoot := false
	for _, auth := range user.SysAuthoritys {
		if auth.ID == 888 {
			isRoot = true
			break
		}
	}
	user.IsRoot = isRoot
	return nil, user
}

// @Title 设置openId
// @Description
// <AUTHOR>
// @Param username 	string	用户名
// @Return err 		error 	错误信息

func SetOpenId(encrypt req.EncryptedData, id int64) (err error) {

	var user model.SysUser
	var notExists bool
	notExists = global.GVA_DB.Where("id = ? and status = ?", id, constants.STATUS_ENABLED).First(&user).RecordNotFound()
	if notExists {
		return errors.New("该手机号不存在")
	}

	key := global.GVA_REDIS.Get("wx_session_key_" + strconv.Itoa(int(id))).Val()
	wxData := wechat.NewWXUserDataCrypt(global.GVA_CONFIG.Wechat.Appid, key)
	userInfo, err := wxData.Decrypt(encrypt.EncryptedData, encrypt.Iv)

	if err != nil {
		global.GVA_LOG.Error(err.Error())
		return errors.New("保存失败")
	}

	user.OpenId = userInfo.OpenID

	if err = global.GVA_DB.Table("sys_users").
		Where("id = ?", id).
		Update("open_id", userInfo.OpenID).
		Error; err != nil {
		return err
	}

	return nil
}

// @Title       微信登录，设置openId
// @Description 微信登录设置openId
// <AUTHOR>
// @Param
// @Return

func WxLogin(param req.WxLogin, id int64) (err error) {

	var user model.SysUser
	var notExists bool
	notExists = global.GVA_DB.Where("id = ? and status = ?", id, constants.STATUS_ENABLED).First(&user).RecordNotFound()
	if notExists {
		return errors.New("该手机号不存在")
	}

	openId := wechat.JsCode2Session(param.Code)

	if openId == "" {
		return errors.New("保存失败")
	}

	user.OpenId = openId

	if err = global.GVA_DB.Table("sys_users").
		Where("id = ?", id).
		Update("open_id", openId).
		Error; err != nil {
		return err
	}

	return nil
}

// @Title 发送短信
// @Description
// <AUTHOR>
// @Param username 	string	用户名
// @Return err 		error 	错误信息

func SendSms(username string) (err error) {
	// 获取验证码
	code := GenValidateCode(6)

	corpId := global.GVA_CONFIG.Sms.CorpId
	pwd := global.GVA_CONFIG.Sms.Pwd

	msg := fmt.Sprintf("您的验证码是%s,请勿告诉他人", code)
	utils.SendSMS(corpId, pwd, username, msg)
	// 把验证码存储到redis中
	global.GVA_REDIS.Set(fmt.Sprintf("%v-code", username), code, time.Minute*5)
	return err
}

// @Title 生成随机验证码
// @Description
// <AUTHOR>
// @Param username 	string	用户名
// @Return err 		error 	错误信息

func GenValidateCode(width int) string {
	numeric := [10]byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	r := len(numeric)
	rand.Seed(time.Now().UnixNano())

	var sb strings.Builder
	for i := 0; i < width; i++ {
		_, _ = fmt.Fprintf(&sb, "%d", numeric[rand.Intn(r)])
	}
	return sb.String()
}

// @Title 校验验证码
// @Description
// <AUTHOR>
// @Param params 	req.Captcha	用户名
// @Return err 		error 			错误信息
// @Return token 	string 			识别字符串

func CheckCaptcha(params req.Captcha) (err error, token string) {
	// 从redis中获取验证码
	code := global.GVA_REDIS.Get(fmt.Sprintf("%v-code", params.Username)).Val()
	if code == "" {
		return errors.New("验证码错误"), token
	}
	if code != params.Code {
		return errors.New("验证码错误"), token
	}
	global.GVA_REDIS.Del(fmt.Sprintf("%v-code", params.Username))
	str := CreateRandomString(20)
	global.GVA_REDIS.Set(fmt.Sprintf("%v-str", params.Username), str, time.Hour*24)
	return err, str
}

// @Title 随机字符串
// @Description
// <AUTHOR>
// @Param len 		int		长度
// @Return str 		string 	字符串

func CreateRandomString(len int) string {
	var container string
	var str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
	b := bytes.NewBufferString(str)
	length := b.Len()
	bigInt := big.NewInt(int64(length))
	for i := 0; i < len; i++ {
		randomInt, _ := ran.Int(ran.Reader, bigInt)
		container += string(str[randomInt.Int64()])
	}
	return container
}

// @Title 忘记密码
// @Description
// <AUTHOR>
// @Param params 	req.ForgetPassword		密码信息
// @Return err 		error 						错误信息

func ForgetPassword(params req.ForgetPassword) (err error) {
	// 从redis中取出随机字符串校验
	str := global.GVA_REDIS.Get(fmt.Sprintf("%v-str", params.Username)).Val()
	if str == "" {
		return errors.New("请求已失效")
	}
	// 密码加密
	password := utils.MD5V([]byte(params.Password))
	// 通过用户名修改密码
	err = global.GVA_DB.Table("sys_users").Where("username = ?", params.Username).Updates(map[string]interface{}{"password": password, "password_status": constants.STATUS_DISABLED}).Error
	if err != nil {
		return errors.New("密码修改失败")
	}
	global.GVA_REDIS.Del(fmt.Sprintf("%v-str", params.Username))
	return err
}

// @Title 查询用户列表
// @Description
// <AUTHOR>
// @Param
// @Return
func GetUserListSample(uuid string) (err error, users []resp.SampleUserInfo) {

	var (
		user model.SysUser
	)
	users = make([]resp.SampleUserInfo, 0)

	if err, user = GetCurrentUser(uuid); err != nil {
		return
	}
	db := global.GVA_DB.Table("sys_users")
	if user.IsRoot { //root用户查所有，其他只查自己创建的
		db = db.Where("id > 10 and company_code = ? and status = ?", user.CompanyCode, constants.STATUS_ENABLED)
	} else {
		db = db.Where("create_by = ? and status = ?", user.ID, constants.STATUS_ENABLED)
	}

	if err = db.Find(&users).Error; err != nil {
		return err, users
	}
	return nil, users
}

// @Title 查询用户列表
// @Description
// <AUTHOR>
// @Param
// @Return
func GetFarmerListSample() (err error, users []resp.FarmlandUserInfo) {

	users = make([]resp.FarmlandUserInfo, 0)

	db := global.GVA_DB.Table("sys_users")

	db = db.Where("is_farmer = 1 and deleted_at is null")
	if err = db.Find(&users).Error; err != nil {
		return err, users
	}
	return nil, users
}

// @Title 设置用户烤房权限数据
// @Description
// <AUTHOR>
// @Param
// @Return
func SetUserParams(params req.UserSettingReq) (err error) {
	var (
		user model.SysUser
	)

	if params.Id == 0 {
		return errors.New("用户不存在")
	}
	if len(params.Params) < 10 {
		return errors.New("数据有误")
	}

	if err, user = GetUserById(params.Id); err != nil {
		return err
	}

	user.Params = params.Params
	return global.GVA_DB.Table("sys_users").Where("id=?", params.Id).Update("params", params.Params).Error
}
