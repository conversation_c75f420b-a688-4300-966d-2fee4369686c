package model

// 角色对象
type SysAuthority struct {
	Model
	AuthorityName string        `json:"authorityName" gorm:"comment:'角色名'"`
	CompanyCode   string        `json:"companyCode" gorm:"comment:'公司标识'"`
	AuthorityType int           `json:"authorityType" gorm:"comment:'角色类别'"`
	Description   string        `json:"description" grom:"comment:'角色描述'"`
	Status        int8          `json:"status" gorm:"comment:'角色状态'"`
	BindingUsers  int           `json:"bindingUsers" gorm:"comment:'绑定用户数'"`
	SysBaseMenus  []SysBaseMenu `json:"menus" gorm:"many2many:sys_authority_menus;"`
	SysUser       []SysUser     `json:"users" gorm:"many2many:sys_authority_users"`
}
