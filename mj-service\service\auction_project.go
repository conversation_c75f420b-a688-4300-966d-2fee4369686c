package service

import (
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"errors"
	"time"

	"github.com/Gre-Z/common/jtime"
	"github.com/jinzhu/gorm"
)

// 创建竞价项目
func CreateAuctionProject(createReq req.CreateAuctionProjectReq, creatorID int) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 验证商品分类是否存在
		var category model.SysProductCategory
		if err := tx.Where("id = ? AND status = 1", createReq.ProductCategoryID).First(&category).Error; err != nil {
			return errors.New("商品分类不存在或已禁用")
		}

		// 验证时间逻辑
		if createReq.StartTime.Time.After(createReq.EndTime.Time) {
			return errors.New("开始时间不能晚于结束时间")
		}
		if createReq.StartTime.Time.Before(time.Now()) {
			return errors.New("开始时间不能早于当前时间")
		}

		// 创建竞价项目
		project := model.AuctionProject{
			Title:             createReq.Title,
			ProductCategoryID: createReq.ProductCategoryID,
			Quantity:          createReq.Quantity,
			Unit:              category.Unit,
			StartPrice:        createReq.StartPrice,
			CurrentPrice:      createReq.StartPrice,
			MinIncrement:      createReq.MinIncrement,
			StartTime:         createReq.StartTime,
			EndTime:           createReq.EndTime,
			OriginalEndTime:   createReq.EndTime,
			Owner:             createReq.Owner,
			WarehouseAddress:  createReq.WarehouseAddress,
			FeeDescription:    createReq.FeeDescription,
			Remark:            createReq.Remark,
			Status:            0, // 即将开始
			BidCount:          0,
			ViewCount:         0,
			CreateBy:          creatorID,
		}

		if err := tx.Create(&project).Error; err != nil {
			return errors.New("创建竞价项目失败")
		}

		// 分配竞价权限
		for _, memberID := range createReq.MemberIDs {
			permission := model.AuctionPermission{
				ProjectID: project.ID,
				MemberID:  memberID,
				GrantTime: jtime.JsonTime{time.Now()},
				GrantBy:   creatorID,
				Status:    1, // 有效
			}
			if err := tx.Create(&permission).Error; err != nil {
				return errors.New("分配竞价权限失败")
			}
		}

		return nil
	})
}

// 更新竞价项目
func UpdateAuctionProject(updateReq req.UpdateAuctionProjectReq, updaterID int) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 检查项目是否存在
		var project model.AuctionProject
		if err := tx.Where("id = ?", updateReq.ID).First(&project).Error; err != nil {
			return errors.New("竞价项目不存在")
		}

		// 检查项目状态，只有未开始的项目才能修改
		if project.Status != 0 {
			return errors.New("只有未开始的竞价项目才能修改")
		}

		// 验证商品分类是否存在
		var category model.SysProductCategory
		if err := tx.Where("id = ? AND status = 1", updateReq.ProductCategoryID).First(&category).Error; err != nil {
			return errors.New("商品分类不存在或已禁用")
		}

		// 验证时间逻辑
		if updateReq.StartTime.Time.After(updateReq.EndTime.Time) {
			return errors.New("开始时间不能晚于结束时间")
		}

		// 更新项目信息
		updateData := map[string]interface{}{
			"title":               updateReq.Title,
			"product_category_id": updateReq.ProductCategoryID,
			"quantity":            updateReq.Quantity,
			"unit":                category.Unit,
			"start_price":         updateReq.StartPrice,
			"current_price":       updateReq.StartPrice,
			"min_increment":       updateReq.MinIncrement,
			"start_time":          updateReq.StartTime,
			"end_time":            updateReq.EndTime,
			"original_end_time":   updateReq.EndTime,
			"owner":               updateReq.Owner,
			"warehouse_address":   updateReq.WarehouseAddress,
			"fee_description":     updateReq.FeeDescription,
			"remark":              updateReq.Remark,
		}

		if err := tx.Model(&project).Updates(updateData).Error; err != nil {
			return errors.New("更新竞价项目失败")
		}

		// 重新分配权限
		// 先删除原有权限
		if err := tx.Where("project_id = ?", updateReq.ID).Delete(&model.AuctionPermission{}).Error; err != nil {
			return errors.New("删除原有权限失败")
		}

		// 重新分配权限
		for _, memberID := range updateReq.MemberIDs {
			permission := model.AuctionPermission{
				ProjectID: updateReq.ID,
				MemberID:  memberID,
				GrantTime: jtime.JsonTime{time.Now()},
				GrantBy:   updaterID,
				Status:    1, // 有效
			}
			if err := tx.Create(&permission).Error; err != nil {
				return errors.New("重新分配权限失败")
			}
		}

		return nil
	})
}

// 获取竞价项目列表
func GetAuctionProjectList(listReq req.GetAuctionProjectListReq, memberID int) (err error, listResp resp.GetAuctionProjectListResp) {
	var projects []model.AuctionProject
	var total int

	db := global.GVA_DB.Model(&model.AuctionProject{}).Preload("ProductCategory")

	// 构建查询条件
	if listReq.Title != "" {
		db = db.Where("title LIKE ?", "%"+listReq.Title+"%")
	}
	if listReq.Owner != "" {
		db = db.Where("owner LIKE ?", "%"+listReq.Owner+"%")
	}
	if listReq.Status != 0 {
		db = db.Where("`status` = ?", listReq.Status)
	}

	// 如果是会员查询，只显示有权限参与的项目
	if memberID > 0 {
		db = db.Joins("JOIN auction_permissions ON auction_projects.id = auction_permissions.project_id").
			Where("auction_permissions.member_id = ? AND auction_permissions.status = 1", memberID)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 分页查询
	offset := listReq.PageSize * (listReq.Page - 1)
	if err := db.Offset(offset).Limit(listReq.PageSize).Order("created_at DESC").Find(&projects).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 构造响应
	var projectList []resp.AuctionProjectListItem
	for _, project := range projects {
		// 计算剩余时间
		var remainingTime int64 = 0
		if project.Status == 1 { // 竞价中
			remaining := project.EndTime.Time.Sub(time.Now()).Seconds()
			if remaining > 0 {
				remainingTime = int64(remaining)
			}
		}

		// 检查是否可以出价
		canBid := false
		if memberID > 0 && project.Status == 1 {
			// 检查权限
			var permission model.AuctionPermission
			if !global.GVA_DB.Where("project_id = ? AND member_id = ? AND status = 1", project.ID, memberID).First(&permission).RecordNotFound() {
				canBid = true
			}
		}

		projectList = append(projectList, resp.AuctionProjectListItem{
			ID:               project.ID,
			Title:            project.Title,
			ProductCategory:  project.ProductCategory.Name,
			Quantity:         project.Quantity,
			Unit:             project.Unit,
			StartPrice:       project.StartPrice,
			CurrentPrice:     project.CurrentPrice,
			MinIncrement:     project.MinIncrement,
			StartTime:        project.StartTime,
			EndTime:          project.EndTime,
			Owner:            project.Owner,
			WarehouseAddress: project.WarehouseAddress,
			Status:           project.Status,
			BidCount:         project.BidCount,
			ViewCount:        project.ViewCount,
			LastBidTime:      project.LastBidTime,
			RemainingTime:    remainingTime,
			CanBid:           canBid,
		})
	}

	listResp.List = projectList
	listResp.Total = total
	listResp.Page = listReq.Page
	listResp.PageSize = listReq.PageSize

	return nil, listResp
}

// 获取公开竞价项目列表（不包含价格信息，无需登录）
func GetPublicAuctionProjectList(listReq req.GetAuctionProjectListReq) (err error, listResp resp.GetPublicAuctionProjectListResp) {
	var projects []model.AuctionProject
	var total int

	db := global.GVA_DB.Model(&model.AuctionProject{}).Preload("ProductCategory")

	// 构建查询条件
	if listReq.Title != "" {
		db = db.Where("title LIKE ?", "%"+listReq.Title+"%")
	}
	if listReq.Owner != "" {
		db = db.Where("owner LIKE ?", "%"+listReq.Owner+"%")
	}
	if listReq.Status != 0 {
		db = db.Where("`status` = ?", listReq.Status)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 分页查询
	offset := listReq.PageSize * (listReq.Page - 1)
	if err := db.Offset(offset).Limit(listReq.PageSize).Order("created_at DESC").Find(&projects).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 构造响应（不包含价格信息）
	var projectList []resp.PublicAuctionProjectListItem
	for _, project := range projects {
		// 计算剩余时间
		var remainingTime int64 = 0
		if project.Status == 1 { // 竞价中
			remaining := time.Until(project.EndTime.Time).Seconds()
			if remaining > 0 {
				remainingTime = int64(remaining)
			}
		}

		projectList = append(projectList, resp.PublicAuctionProjectListItem{
			ID:               project.ID,
			Title:            project.Title,
			ProductCategory:  project.ProductCategory.Name,
			Quantity:         project.Quantity,
			Unit:             project.Unit,
			StartTime:        project.StartTime,
			EndTime:          project.EndTime,
			Owner:            project.Owner,
			WarehouseAddress: project.WarehouseAddress,
			Status:           project.Status,
			BidCount:         project.BidCount,
			ViewCount:        project.ViewCount,
			LastBidTime:      project.LastBidTime,
			RemainingTime:    remainingTime,
			Description:      project.FeeDescription,
		})
	}

	listResp = resp.GetPublicAuctionProjectListResp{
		List:     projectList,
		Total:    total,
		Page:     listReq.Page,
		PageSize: listReq.PageSize,
	}

	return nil, listResp
}

// 获取竞价项目详情
func GetAuctionProjectDetail(projectID int, memberID int) (error, resp.AuctionProjectDetailResp) {
	var detailResp resp.AuctionProjectDetailResp
	var project model.AuctionProject

	// 查询项目详情
	if err := global.GVA_DB.Where("id = ?", projectID).Preload("ProductCategory").First(&project).Error; err != nil {
		return errors.New("竞价项目不存在"), detailResp
	}

	// 增加浏览次数
	global.GVA_DB.Model(&project).Update("view_count", gorm.Expr("view_count + 1"))

	// 计算剩余时间
	var remainingTime int64 = 0
	if project.Status == 1 { // 竞价中
		remaining := project.EndTime.Time.Sub(time.Now()).Seconds()
		if remaining > 0 {
			remainingTime = int64(remaining)
		}
	}

	// 检查是否可以出价
	canBid := false
	if memberID > 0 && project.Status == 1 {
		var permission model.AuctionPermission
		if !global.GVA_DB.Where("project_id = ? AND member_id = ? AND status = 1", projectID, memberID).First(&permission).RecordNotFound() {
			canBid = true
		}
	}

	// 获取最近出价记录
	var recentBids []model.AuctionBid
	global.GVA_DB.Where("project_id = ? AND status = 1", projectID).
		Preload("Member").
		Order("bid_time DESC").
		Limit(10).
		Find(&recentBids)

	var recentBidList []resp.BidListItem
	for _, bid := range recentBids {
		// 脱敏处理
		memberName := bid.Member.Name
		if len(memberName) > 1 {
			memberName = memberName[:1] + "***"
		}
		ipAddress := bid.IPAddress
		if len(ipAddress) > 7 {
			ipAddress = ipAddress[:7] + "***"
		}

		recentBidList = append(recentBidList, resp.BidListItem{
			ID:        bid.ID,
			ProjectID: bid.ProjectID,
			MemberID:  bid.MemberID,
			Member:    memberName,
			BidAmount: bid.BidAmount,
			BidTime:   bid.BidTime,
			IPAddress: ipAddress,
			Status:    bid.Status,
			IsWinning: bid.IsWinning,
			PrevPrice: bid.PrevPrice,
			Increment: bid.Increment,
		})
	}

	// 构造响应
	detailResp.AuctionProject = project
	detailResp.ProductCategoryName = project.ProductCategory.Name
	detailResp.RemainingTime = remainingTime
	detailResp.CanBid = canBid
	detailResp.RecentBids = recentBidList

	return nil, detailResp
}

// 终止竞价项目
func TerminateAuctionProject(terminateReq req.TerminateAuctionReq, operatorID int) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var project model.AuctionProject
		if err := tx.Where("id = ?", terminateReq.ProjectID).First(&project).Error; err != nil {
			return errors.New("竞价项目不存在")
		}

		// 只有竞价中的项目才能终止
		if project.Status != 1 {
			return errors.New("只有竞价中的项目才能终止")
		}

		// 更新项目状态
		updateData := map[string]interface{}{
			"status": 3, // 已终止
			"remark": project.Remark + "\n终止原因：" + terminateReq.Reason,
		}

		if err := tx.Model(&project).Updates(updateData).Error; err != nil {
			return errors.New("终止竞价失败")
		}

		return nil
	})
}

// 根据项目id 查询已经授权的用户
func GetAuctionProjectPermission(productId int) (err error, result []resp.MemberAuctionProjectListResp) {

	var members []resp.MemberAuctionProjectListResp

	db := global.GVA_DB.Table(model.AuctionPermission{}.TableName()).
		Joins("left join sys_members on sys_members.id = auction_permissions.member_id").
		Where("project_id = ?", productId).Select("auction_permissions.*,sys_members.*")

	err = db.Find(&members).Error
	if err != nil {
		global.GVA_LOG.Error("查询授权用户失败")
		return
	}
	return nil, members
}

// 管理竞价权限
func ManageAuctionPermission(permissionReq req.ManageAuctionPermissionReq, operatorID int) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 检查项目是否存在
		var project model.AuctionProject
		if err := tx.Where("id = ?", permissionReq.ProjectID).First(&project).Error; err != nil {
			return errors.New("竞价项目不存在")
		}

		// 只有未开始的项目才能修改权限
		if project.Status != 0 {
			return errors.New("只有未开始的竞价项目才能修改权限")
		}

		if permissionReq.Action == 1 { // 授权
			for _, memberID := range permissionReq.MemberIDs {
				// 检查是否已有权限
				var existPermission model.AuctionPermission
				if !tx.Where("project_id = ? AND member_id = ?", permissionReq.ProjectID, memberID).First(&existPermission).RecordNotFound() {
					// 如果已存在，更新状态为有效
					if err := tx.Model(&existPermission).Updates(map[string]interface{}{
						"status":     1,
						"grant_time": jtime.JsonTime{time.Now()},
						"grant_by":   operatorID,
					}).Error; err != nil {
						return errors.New("更新权限失败")
					}
				} else {
					// 创建新权限
					permission := model.AuctionPermission{
						ProjectID: permissionReq.ProjectID,
						MemberID:  memberID,
						GrantTime: jtime.JsonTime{time.Now()},
						GrantBy:   operatorID,
						Status:    1,
					}
					if err := tx.Create(&permission).Error; err != nil {
						return errors.New("授权失败")
					}
				}
			}
		} else if permissionReq.Action == 2 { // 撤销
			delSql := "delete from auction_permissions where project_id = ? and member_id in (?)"
			if err := tx.Exec(delSql, permissionReq.ProjectID, permissionReq.MemberIDs).Error; err != nil {
				return errors.New("撤销权限失败")
			}
		}

		return nil
	})
}

// 更新竞价项目状态（定时任务调用）
func UpdateAuctionProjectStatus() error {
	now := time.Now()

	// 更新即将开始的项目为竞价中
	if err := global.GVA_DB.Model(&model.AuctionProject{}).
		Where("status = 0 AND start_time <= ?", now).
		Update("status", 1).Error; err != nil {
		return err
	}

	// 更新竞价中但已过期的项目为已结束
	if err := global.GVA_DB.Model(&model.AuctionProject{}).
		Where("status = 1 AND end_time <= ?", now).
		Update("status", 2).Error; err != nil {
		return err
	}

	return nil
}

// 获取竞价统计信息
func GetAuctionStats() (error, resp.AuctionStatsResp) {
	var stats resp.AuctionStatsResp

	// 统计项目数量
	global.GVA_DB.Model(&model.AuctionProject{}).Count(&stats.TotalProjects)
	global.GVA_DB.Model(&model.AuctionProject{}).Where("status = 1").Count(&stats.ActiveProjects)
	global.GVA_DB.Model(&model.AuctionProject{}).Where("status = 2").Count(&stats.CompletedProjects)

	// 统计会员数量
	global.GVA_DB.Model(&model.SysMember{}).Where("audit_status = 1").Count(&stats.TotalMembers)

	// 统计活跃会员（最近30天有登录的）
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	global.GVA_DB.Model(&model.SysMember{}).
		Where("audit_status = 1 AND last_login_time >= ?", thirtyDaysAgo).
		Count(&stats.ActiveMembers)

	// 统计出价次数
	global.GVA_DB.Model(&model.AuctionBid{}).Where("status = 1").Count(&stats.TotalBids)

	// 统计成交金额
	global.GVA_DB.Model(&model.AuctionProject{}).
		Where("status = 2 AND final_price > 0").
		Select("COALESCE(SUM(final_price), 0)").
		Row().Scan(&stats.TotalAmount)

	return nil, stats
}
