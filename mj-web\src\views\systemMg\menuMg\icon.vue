<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-28 16:29:28
 * @LastEditors: dlg
 * @LastEditTime: 2020-06-29 13:57:46
-->
<template>
  <div>
    <el-form :inline="true"
             label-width="85px"
             ref="menuForm"
             label-position="top">
      <el-form-item prop="icon" style="width:100%">
        <svg
          class="svg-icon"
          aria-hidden="true"
          style="position: absolute; z-index: 9999;top: 14px;left: 10px; "
        >
          <use :xlink:href="`#icon-${ meta.icon }`" />
        </svg>
        <el-select clearable
                   filterable
                   class="gva-select"
                   v-model="meta.icon"
                   placeholder="请选择">
          <el-option v-for="item in options"
                     :key="item"
                     :label="item"
                     :value="item">
            <svg class="svg-icon" aria-hidden="true">
              <use :xlink:href="`#icon-${ item }`" />
            </svg>
            <span style="padding-left: 10px;">{{ item }}</span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { icons } from "./icons"; // 导入项目中已存在的所有图标

export default {
  name: "Icon",
  props: {
    meta: {
      default: function() {
        return {};
      },
      type: Object
    }
  },
  data() {
    return {
      input: "",
      options: icons,
      value: ""
    };
  },
  methods: {}
};
</script>

<style lang="scss">
.gva-select .el-input__inner {
  padding: 0 30px !important;
}
</style>
