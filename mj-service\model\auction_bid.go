package model

import (
	"github.com/Gre-Z/common/jtime"
)

// 竞价出价记录对象
type AuctionBid struct {
	Model
	ProjectID    int            `json:"projectId" gorm:"comment:'竞价项目ID'"`
	Project      AuctionProject `json:"project" gorm:"foreignkey:ProjectID"`
	MemberID     int            `json:"memberId" gorm:"comment:'出价会员ID'"`
	Member       SysMember      `json:"member" gorm:"foreignkey:MemberID"`
	BidAmount    float64        `json:"bidAmount" gorm:"comment:'出价金额'"`
	BidTime      jtime.JsonTime `json:"bidTime" gorm:"comment:'出价时间'"`
	IPAddress    string         `json:"ipAddress" gorm:"comment:'出价IP地址'"`
	UserAgent    string         `json:"userAgent" gorm:"comment:'用户代理'"`
	Status       int8           `json:"status" gorm:"default:1;comment:'状态(1:有效,2:无效)'"`
	IsWinning    bool           `json:"isWinning" gorm:"default:false;comment:'是否为最终中标出价'"`
	PrevPrice    float64        `json:"prevPrice" gorm:"comment:'出价前的价格'"`
	Increment    float64        `json:"increment" gorm:"comment:'加价幅度'"`
	Remark       string         `json:"remark" gorm:"comment:'备注'"`
}

func (AuctionBid) TableName() string {
	return "auction_bids"
}

// 检查出价是否有效
func (ab *AuctionBid) IsValid() bool {
	return ab.Status == 1
}

// 检查是否为中标出价
func (ab *AuctionBid) IsWinningBid() bool {
	return ab.IsWinning
}
