{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\src\\api\\auction.js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\api\\auction.js", "mtime": 1757554255411}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnLi9yZXF1ZXN0JzsKCi8vIOiOt+WPluernuS7t+mhueebruWIl+ihqApleHBvcnQgZnVuY3Rpb24gZ2V0QXVjdGlvbkxpc3QocGFyYW1zKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2F1Y3Rpb24vbWVtYmVyL3Byb2plY3RzJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogcGFyYW1zCiAgfSk7Cn0KCi8vIOiOt+WPluernuS7t+mhueebruivpuaDhQpleHBvcnQgZnVuY3Rpb24gZ2V0QXVjdGlvbkRldGFpbChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2F1Y3Rpb24vbWVtYmVyL3Byb2plY3RzL2RldGFpbCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGEKICB9KTsKfQoKLy8g5Ye65Lu3CmV4cG9ydCBmdW5jdGlvbiBwbGFjZUJpZChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2F1Y3Rpb24vbWVtYmVyL2JpZCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGEKICB9KTsKfQoKLy8g6I635Y+W5Ye65Lu35Y6G5Y+yCmV4cG9ydCBmdW5jdGlvbiBnZXRCaWRIaXN0b3J5KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXVjdGlvbi9tZW1iZXIvcGFydGljaXBhdGlvbicsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGEKICB9KTsKfQoKLy8g6I635Y+W5ZWG5ZOB5YiG57G777yI5L2/55So566h55CG56uv5o6l5Y+j77yJCmV4cG9ydCBmdW5jdGlvbiBnZXRDYXRlZ29yaWVzKCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9wcm9kdWN0Q2F0ZWdvcnlNZW1iZXIvZ2V0UHJvZHVjdENhdGVnb3J5Q29uZGl0aW9uJywKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn0KCi8vIOiOt+WPluWFrOW8gOernuS7t+mhueebruWIl+ihqO+8iOaXoOmcgOeZu+W9le+8jOS4jeWMheWQq+S7t+agvOS/oeaBr++8iQpleHBvcnQgZnVuY3Rpb24gZ2V0UHVibGljQXVjdGlvbkxpc3QocGFyYW1zKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2F1Y3Rpb24vcHVibGljL3Byb2plY3RzJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogcGFyYW1zCiAgfSk7Cn0="}, {"version": 3, "names": ["request", "getAuctionList", "params", "url", "method", "data", "getAuctionDetail", "placeBid", "getBidHistory", "getCategories", "getPublicAuctionList"], "sources": ["E:/pywsp/auction-sys/mj-member-web/src/api/auction.js"], "sourcesContent": ["import request from './request'\r\n\r\n// 获取竞价项目列表\r\nexport function getAuctionList(params) {\r\n  return request({\r\n    url: '/auction/member/projects',\r\n    method: 'post',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 获取竞价项目详情\r\nexport function getAuctionDetail(data) {\r\n  return request({\r\n    url: '/auction/member/projects/detail',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 出价\r\nexport function placeBid(data) {\r\n  return request({\r\n    url: '/auction/member/bid',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取出价历史\r\nexport function getBidHistory(data) {\r\n  return request({\r\n    url: '/auction/member/participation',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取商品分类（使用管理端接口）\r\nexport function getCategories() {\r\n  return request({\r\n    url: '/productCategoryMember/getProductCategoryCondition',\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 获取公开竞价项目列表（无需登录，不包含价格信息）\r\nexport function getPublicAuctionList(params) {\r\n  return request({\r\n    url: '/auction/public/projects',\r\n    method: 'post',\r\n    data: params\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;;AAE/B;AACA,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAE;EACrC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEH;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,QAAQA,CAACF,IAAI,EAAE;EAC7B,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,aAAaA,CAACH,IAAI,EAAE;EAClC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,aAAaA,CAAA,EAAG;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,oDAAoD;IACzDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,oBAAoBA,CAACR,MAAM,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEH;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}