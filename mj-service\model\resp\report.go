// <AUTHOR>
// @date
// @note
package resp

type ReportBase struct {
	Id            int    `json:"id"`
	DeviceCode    string `json:"deviceCode"`
	DeviceName    string `json:"deviceName"`
	Owner         string `json:"owner"`
	OwnerTel      string `json:"ownerTel"`
	ContractNo    string `json:"contractNo"`
	Instructor    string `json:"instructor"`
	InstructorTel string `json:"instructorTel"`
	Variety       string `json:"variety"`   //品种
	Address       string `json:"address"`   //
	Part          string `json:"part"`      //部位
	Ripe          string `json:"ripe"`      //成熟度
	Wet           string `json:"wet"`       //含水量
	Process       string `json:"process"`   //过程
	StartTime     string `json:"startTime"` //开始时间
	EndTime       string `json:"endTime"`   //结束时间
	Email         string `json:"email"`
	ReportData
}

// @Title
// @Description
// <AUTHOR>
// @Param
// @Return
type ReportData struct {
	Times54 float64 `json:"times54"`
	Times65 float64 `json:"times65"`
	Times40 float64 `json:"times40"`

	MaxDryTemp float64 `json:"maxDryTemp"`
	MaxDryTime float64 `json:"maxDryTime"`

	MaxWetTemp float64 `json:"maxWetTemp"`
	MaxWetTime float64 `json:"maxWetTime"`

	SdDryTime38 float64 `json:"sdDryTime38"`
	RtDryTime38 float64 `json:"rtDryTime38"`
	DfDryTime38 float64 `json:"dfDryTime38"`

	SdDryTime42 float64 `json:"sdDryTime42"`
	RtDryTime42 float64 `json:"rtDryTime42"`
	DfDryTime42 float64 `json:"dfDryTime42"`

	SdWetTime35 float64 `json:"sdWetTime35"`
	RtWetTime35 float64 `json:"rtWetTime35"`
	DfWetTime35 float64 `json:"dfWetTime35"`

	SdWetTime38 float64 `json:"sdWetTime38"`
	RtWetTime38 float64 `json:"rtWetTime38"`
	DfWetTime38 float64 `json:"dfWetTime38"`

	SdWetTime40 float64 `json:"sdWetTime40"`
	RtWetTime40 float64 `json:"rtWetTime40"`
	DfWetTime40 float64 `json:"dfWetTime40"`
}
