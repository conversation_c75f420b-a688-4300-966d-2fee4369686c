{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue?vue&type=style&index=0&id=fae5bece&lang=scss&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue", "mtime": 1757556310840}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757485139209}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757485152120}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757485142383}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757485135872}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AAwQA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"home-page\">\n    <AppHeader />\n    \n    <!-- 轮播横幅 -->\n    <section class=\"hero-section\">\n      <div class=\"hero-content\">\n        <h1>新疆生产建设兵团第一师棉麻有限责任公司竞价平台</h1>\n        <p>专业的棉副产品在线竞价交易平台，公开透明，安全可靠</p>\n        <div class=\"hero-actions\">\n          <el-button type=\"primary\" size=\"large\" @click=\"scrollToAuctions\">\n            立即竞价\n          </el-button>\n          <el-button size=\"large\" @click=\"$router.push('/register')\" v-if=\"!isLoggedIn\">\n            免费注册\n          </el-button>\n        </div>\n      </div>\n    </section>\n\n    <!-- 竞价项目列表 -->\n    <section class=\"auctions-section\" ref=\"auctionsSection\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h2>热门竞价</h2>\n          <div class=\"filters\">\n            <el-select v-model=\"filters.status\" placeholder=\"项目状态\" clearable @change=\"fetchAuctions\">\n              <el-option label=\"即将开始\" :value=\"0\" />\n              <el-option label=\"竞价中\" :value=\"1\" />\n              <el-option label=\"已结束\" :value=\"2\" />\n            </el-select>\n            <el-select v-model=\"filters.categoryId\" placeholder=\"商品分类\" clearable @change=\"fetchAuctions\">\n              <el-option\n                v-for=\"category in categories\"\n                :key=\"category.id\"\n                :label=\"category.name\"\n                :value=\"category.id\"\n              />\n            </el-select>\n          </div>\n        </div>\n\n        <div class=\"auctions-grid\" v-loading=\"loading\">\n          <div\n            v-for=\"auction in auctions\"\n            :key=\"auction.id\"\n            class=\"auction-card\"\n            @click=\"goToAuction(auction)\"\n          >\n\n            <div class=\"auction-content\">\n              <StatusTag :status=\"auction.status\" class=\"status-overlay\" />\n              <h3 class=\"auction-title\">{{ auction.title }}</h3>\n              <div class=\"auction-info\">\n                \n                <!-- 已登录用户显示价格信息 -->\n                <template v-if=\"isLoggedIn\">\n                  <div class=\"info-item\">\n                    <span class=\"label\">起拍价</span>\n                    <span class=\"value\">¥{{ formatMoney(auction.startPrice) }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <span class=\"label\">当前价</span>\n                    <span class=\"value current-price\">¥{{ formatMoney(auction.currentPrice) }}</span>\n                  </div>\n                </template>\n                <!-- 未登录用户显示基本信息 -->\n                <template v-else>\n                  <div class=\"info-item\">\n                    <span class=\"label\">数量</span>\n                    <span class=\"value\">{{ auction.quantity }}{{ auction.unit }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <span class=\"label\">商品类别</span>\n                    <span class=\"value\">{{ auction.productCategory }}</span>\n                  </div>\n                </template>\n                <div class=\"info-item\">\n                  <span class=\"label\">出价次数</span>\n                  <span class=\"value\">{{ auction.bidCount }}次</span>\n                </div>\n              </div>\n              \n              <div class=\"auction-time\">\n                <template v-if=\"auction.status === 0\">\n                  <span class=\"time-label\">开始时间：</span>\n                  <span class=\"time-value\">{{ auction.startTime | formatTime('MM-DD HH:mm') }}</span>\n                </template>\n                <template v-else-if=\"auction.status === 1\">\n                  <span class=\"time-label\">剩余时间：</span>\n                  <CountdownTimer :end-time=\"auction.endTime\" />\n                </template>\n                <template v-else>\n                  <span class=\"time-label\">结束时间：</span>\n                  <span class=\"time-value\">{{ auction.endTime | formatTime('MM-DD HH:mm') }}</span>\n                </template>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"load-more\" v-if=\"hasMore\">\n          <el-button @click=\"loadMore\" :loading=\"loadingMore\">加载更多</el-button>\n        </div>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { getCategories, getPublicAuctionList } from '@/api/auction'\n\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      stats: {},\n      auctions: [],\n      categories: [],\n      filters: {\n        status: '',\n        categoryId: ''\n      },\n      loading: false,\n      loadingMore: false,\n      page: 1,\n      pageSize: 12,\n      hasMore: true\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['isLoggedIn'])\n  },\n  async mounted() {\n    await Promise.all([\n      this.fetchStats(),\n      this.fetchCategories(),\n      this.fetchAuctions()\n    ])\n  },\n  methods: {\n    // 获取统计数据\n    async fetchStats() {\n      // 这里可以调用统计API\n      this.stats = {\n        totalProjects: 156,\n        totalMembers: 1280,\n        totalBids: 8960,\n        totalAmount: 2580\n      }\n    },\n\n    // 获取商品分类\n    async fetchCategories() {\n      try {\n        const response = await getCategories()\n        if (response.data.code === 200) {\n          this.categories = response.data.data || []\n        }\n      } catch (error) {\n        console.error('获取分类失败:', error)\n      }\n    },\n\n    // 获取竞价列表\n    async fetchAuctions(reset = false) {\n      if (reset) {\n        this.page = 1\n        this.auctions = []\n        this.hasMore = true\n      }\n\n      this.loading = reset\n      this.loadingMore = !reset\n\n      try {\n        let result\n\n        if (this.isLoggedIn) {\n          // 已登录用户：使用会员接口（包含价格信息和权限）\n          result = await this.$store.dispatch('auction/fetchAuctionList', {\n            page: this.page,\n            pageSize: this.pageSize,\n            ...this.filters\n          })\n        } else {\n          // 未登录用户：使用公开接口（不包含价格信息）\n          const requestData = {\n            page: this.page,\n            pageSize: this.pageSize,\n            status: this.filters.status || '',\n            categoryId: this.filters.categoryId || ''\n          }\n\n          const response = await getPublicAuctionList(requestData)\n          if (response.data.code === 200) {\n            result = { success: true, data: response.data.data }\n          } else {\n            result = { success: false, message: response.data.msg }\n          }\n        }\n\n        if (result.success) {\n          const newAuctions = result.data.list || []\n          if (reset) {\n            this.auctions = newAuctions\n          } else {\n            this.auctions.push(...newAuctions)\n          }\n\n          this.hasMore = newAuctions.length === this.pageSize\n        }\n      } catch (error) {\n        console.error('获取竞价列表失败:', error)\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n      }\n    },\n\n    // 加载更多\n    loadMore() {\n      this.page++\n      this.fetchAuctions()\n    },\n\n    // 跳转到竞价详情\n    goToAuction(auction) {\n      if (!this.isLoggedIn) {\n        this.$message.warning('请先登录')\n        this.$router.push('/login')\n        return\n      }\n      \n      this.$router.push(`/auction/${auction.id}`)\n    },\n\n    // 滚动到竞价区域\n    scrollToAuctions() {\n      this.$refs.auctionsSection.scrollIntoView({ behavior: 'smooth' })\n    },\n\n    // 格式化金额\n    formatMoney(value) {\n      if (!value && value !== 0) return '0'\n      return Number(value).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })\n    }\n  },\n  watch: {\n    filters: {\n      handler() {\n        this.fetchAuctions(true)\n      },\n      deep: true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.home-page {\n  min-height: 100vh;\n}\n\n// 英雄区域\n.hero-section {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 100px 0;\n  text-align: center;\n\n  .hero-content {\n    max-width: 800px;\n    margin: 0 auto;\n    padding: 0 20px;\n\n    h1 {\n      font-size: 48px;\n      font-weight: 700;\n      margin-bottom: 20px;\n    }\n\n    p {\n      font-size: 20px;\n      margin-bottom: 40px;\n      opacity: 0.9;\n    }\n\n    .hero-actions {\n      display: flex;\n      gap: 20px;\n      justify-content: center;\n    }\n  }\n}\n\n// 竞价区域\n.auctions-section {\n  padding: 60px 0;\n  min-height: 300px;\n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 40px;\n\n    h2 {\n      font-size: 32px;\n      color: $text-primary;\n    }\n\n    .filters {\n      display: flex;\n      gap: 15px;\n    }\n  }\n\n  .auctions-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: 30px;\n    margin-bottom: 40px;\n  }\n\n  .auction-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n    cursor: pointer;\n    transition: transform 0.3s, box-shadow 0.3s;\n\n    &:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n    }\n\n    .auction-content {\n      position: relative;\n      overflow: hidden;\n      padding: 20px;\n      .status-overlay {\n        position: absolute;\n        top: 10px;\n        right: 10px;\n      }\n      .auction-title {\n        font-size: 18px;\n        font-weight: 600;\n        color: $text-primary;\n        margin-bottom: 15px;\n        @include text-ellipsis;\n      }\n\n      .auction-info {\n        margin-bottom: 15px;\n\n        .info-item {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n\n          .label {\n            color: $text-secondary;\n          }\n\n          .value {\n            font-weight: 600;\n            color: $text-primary;\n\n            &.current-price {\n              color: $danger-color;\n            }\n          }\n        }\n      }\n\n      .auction-time {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding-top: 15px;\n        border-top: 1px solid $border-color;\n\n        .time-label {\n          color: $text-secondary;\n          font-size: 14px;\n        }\n\n        .time-value {\n          font-weight: 600;\n          color: $text-primary;\n        }\n      }\n    }\n  }\n\n  .load-more {\n    text-align: center;\n  }\n}\n\n@include mobile {\n  .hero-section {\n    padding: 60px 0;\n\n    .hero-content {\n      h1 {\n        font-size: 32px;\n      }\n\n      p {\n        font-size: 16px;\n      }\n\n      .hero-actions {\n        flex-direction: column;\n        align-items: center;\n      }\n    }\n  }\n\n  .stats-section {\n    padding: 40px 0;\n\n    .stats-grid {\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n\n      .stat-item .stat-number {\n        font-size: 24px;\n      }\n    }\n  }\n\n  .auctions-section {\n    padding: 40px 0;\n\n    .section-header {\n      flex-direction: column;\n      gap: 20px;\n      align-items: flex-start;\n\n      .filters {\n        width: 100%;\n        justify-content: space-between;\n      }\n    }\n\n    .auctions-grid {\n      grid-template-columns: 1fr;\n      gap: 20px;\n    }\n  }\n}\n</style>\n"]}]}