package initialize

import (
	"auction-sys/global"
	"auction-sys/model"
)

// 注册数据库表专用
func DBTables() {
	db := global.GVA_DB
	db.AutoMigrate(model.SysUser{},
		model.SysAuthority{},
		model.SysBaseMenu{},
		model.JwtBlacklist{},
		model.SysCompany{},
		model.SysProductCategory{},
		model.SysMember{},
		model.AuctionProject{},
		model.AuctionBid{},
		model.AuctionPermission{},
		//model.NoticeInfo{},

	)
	global.GVA_LOG.Debug("register table success")
}
