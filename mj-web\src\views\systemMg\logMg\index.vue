<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-07-13 11:07:54
 * @LastEditors: dlg
 * @LastEditTime: 2020-11-19 17:07:35
-->
<template>
  <div class="app-container">
    <div style="margin-bottom: 20px; min-width: 1100px;">
      <div class="logMgCls">
        <span style="color:#606266;font-size:14px;">查询区间：</span>
        <el-date-picker
          v-model="datetime"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <el-select
          v-model="companyCode"
          multiple
          collapse-tags
          style="margin-left: 20px;width:300px;"
          placeholder="请选择公司"
          clearable
        >
          <el-option
            v-for="item in companyOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
        <div style="width: 220px;display:inline-block;margin-left: 20px;margin-right:20px;">
          <el-input v-model="username" placeholder="请输入用户名" clearable />
        </div>
        <el-button type="primary" @click="getList">查询</el-button>
      </div>
    </div>
    <el-tabs @tab-click="handleClick" class="tabPane">
      <el-tab-pane label="登录日志" class="tabPane">
        <el-table :data="loginTableData"
                  height="100%"
                  style="width: 100%"
                  border>
          <el-table-column align="center" prop="username" label="用户名" />
          <el-table-column align="center"
                           prop="nickname"
                           label="用户姓名" />
          <el-table-column align="center" prop="loginTime" label="最后一次登录时间" />
          <el-table-column align="center" prop="loginCount" label="登录次数" />
          <el-table-column align="center" prop="webLogin" label="web登录次数" />
          <el-table-column align="center" prop="appletsLogin" label="小程序登录次数" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="操作日志" class="tabPane">
        <el-table :data="operateTableData"
                  height="100%"
                  style="width: 100%"
                  border>
          <el-table-column align="center" prop="username" label="用户名" />
          <el-table-column align="center"
                           prop="nickname"
                           label="用户姓名"
                           min-width="120" />
          <el-table-column align="center"
                           prop="operation"
                           label="操作"
                           min-width="200" />
          <el-table-column align="center"
                           prop="operationTime"
                           label="时间"
                           min-width="180" />
          <el-table-column align="center" label="登录方式">
            <template slot-scope="scope">{{ scope.row.loginMethod === 1?'web':'小程序' }}</template>
          </el-table-column>
          <el-table-column align="center" prop="ip" label="IP" />
          <el-table-column align="center"
                           prop="address"
                           label="地址"
                           min-width="200" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <Page
      :total="total"
      :page.sync="page"
      :limit.sync="limit"
      :page-sizes="pageSizes"
      @search="getList"
    />
  </div>
</template>

<script>
import { getLogList } from "@/api/log";
import { getCompany } from "@/api/superMg/userMg";
import Page from "@/components/page";

export default {
  name: "LogMg",
  components: { Page },
  data() {
    return {
      datetime: null,
      companyCode: "",
      companyOptions: [],
      username: "",
      tab: 0,
      loginTableData: [], //登录日志列表数据
      operateTableData: [], //操作日志列表数据
      page: 1,
      limit: 15,
      pageSizes: [15, 30, 50, 100],
      total: 0
    };
  },
  created() {
    this.getList();
    this.getCompanys();
  },
  methods: {
    async getList() {
      const data = {
        tab: this.tab,
        startTime: this.datetime !== null ? this.datetime[0] : "",
        endTime: this.datetime !== null ? this.datetime[1] : "",
        companyCode: this.companyCode,
        username: this.username,
        page: this.page,
        pageSize: this.limit
      };

      const res = await getLogList(data);
      if (res.data.code == 200) {
        if (res.data.data.tab === 0) {
          this.loginTableData = res.data.data.list;
        } else if (res.data.data.tab === 1) {
          this.operateTableData = res.data.data.list;
        }
        this.page = res.data.data.page;
        this.limit = res.data.data.pageSize;
        this.total = res.data.data.total;
      } else {
        this.$message({
          type: "error",
          message: res.data.msg
        });
      }
    },
    handleClick(tab) {
      this.page = 1;
      this.limit = 15;
      this.tab = Number(tab.index);
      this.getList();
    },
    // 获取企业
    async getCompanys() {
      const res = await getCompany();
      if (res.data.code == 200) {
        this.companyOptions = res.data.data;
      } else {
        this.companyOptions = [];
      }
    }
  }
};
</script>

<style scoped>
.tabPane {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
</style>
<style>
.tabPane .el-tabs__content {
  height: 100%;
}
.logMgCls .el-date-editor .el-range-separator {
  width: 10%;
}
.logMgCls .el-select__tags-text {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.logMgCls .el-select .el-tag__close.el-icon-close {
  top: -7px;
}
</style>