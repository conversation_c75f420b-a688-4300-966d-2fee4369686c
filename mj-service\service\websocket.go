package service

import (
	"auction-sys/global"
	"auction-sys/model"
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许跨域
	},
}

// WebSocket连接管理器
type Hub struct {
	// 注册的客户端连接
	clients map[*Client]bool

	// 广播消息到所有客户端
	broadcast chan []byte

	// 注册客户端
	register chan *Client

	// 注销客户端
	unregister chan *Client

	// 项目订阅映射 projectID -> clients
	projectSubscriptions map[int]map[*Client]bool

	// 互斥锁
	mutex sync.RWMutex
}

// WebSocket客户端
type Client struct {
	// WebSocket连接
	conn *websocket.Conn

	// 发送消息的通道
	send chan []byte

	// 客户端ID（会员ID或管理员ID）
	clientID int

	// 客户端类型（member/admin）
	clientType string

	// 订阅的项目ID列表
	subscribedProjects []int
}

// WebSocket消息类型
type WSMessage struct {
	Type      string      `json:"type"`      // 消息类型
	ProjectID int         `json:"projectId"` // 项目ID
	Data      interface{} `json:"data"`      // 消息数据
}

// 全局Hub实例
var GlobalHub *Hub

// 初始化WebSocket Hub
func InitWebSocketHub() {
	GlobalHub = &Hub{
		clients:              make(map[*Client]bool),
		broadcast:            make(chan []byte),
		register:             make(chan *Client),
		unregister:           make(chan *Client),
		projectSubscriptions: make(map[int]map[*Client]bool),
	}
	go GlobalHub.run()
}

// Hub运行
func (h *Hub) run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()
			log.Printf("Client %d (%s) connected", client.clientID, client.clientType)

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)

				// 从项目订阅中移除
				for projectID := range h.projectSubscriptions {
					if clients, exists := h.projectSubscriptions[projectID]; exists {
						delete(clients, client)
						if len(clients) == 0 {
							delete(h.projectSubscriptions, projectID)
						}
					}
				}
			}
			h.mutex.Unlock()
			log.Printf("Client %d (%s) disconnected", client.clientID, client.clientType)

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// 订阅项目更新
func (h *Hub) SubscribeProject(client *Client, projectID int) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if h.projectSubscriptions[projectID] == nil {
		h.projectSubscriptions[projectID] = make(map[*Client]bool)
	}
	h.projectSubscriptions[projectID][client] = true

	// 添加到客户端订阅列表
	client.subscribedProjects = append(client.subscribedProjects, projectID)
}

// 取消订阅项目
func (h *Hub) UnsubscribeProject(client *Client, projectID int) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if clients, exists := h.projectSubscriptions[projectID]; exists {
		delete(clients, client)
		if len(clients) == 0 {
			delete(h.projectSubscriptions, projectID)
		}
	}

	// 从客户端订阅列表中移除
	for i, id := range client.subscribedProjects {
		if id == projectID {
			client.subscribedProjects = append(client.subscribedProjects[:i], client.subscribedProjects[i+1:]...)
			break
		}
	}
}

// 广播项目更新消息
func (h *Hub) BroadcastProjectUpdate(projectID int, messageType string, data interface{}) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	if clients, exists := h.projectSubscriptions[projectID]; exists {
		message := WSMessage{
			Type:      messageType,
			ProjectID: projectID,
			Data:      data,
		}

		messageBytes, err := json.Marshal(message)
		if err != nil {
			log.Printf("Error marshaling WebSocket message: %v", err)
			return
		}

		for client := range clients {
			select {
			case client.send <- messageBytes:
			default:
				close(client.send)
				delete(h.clients, client)
				delete(clients, client)
			}
		}
	}
}

// 客户端读取消息
func (c *Client) readPump() {
	defer func() {
		GlobalHub.unregister <- c
		c.conn.Close()
	}()

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// 处理客户端消息
		var wsMsg WSMessage
		if err := json.Unmarshal(message, &wsMsg); err != nil {
			log.Printf("Error unmarshaling WebSocket message: %v", err)
			continue
		}

		switch wsMsg.Type {
		case "subscribe":
			GlobalHub.SubscribeProject(c, wsMsg.ProjectID)
		case "unsubscribe":
			GlobalHub.UnsubscribeProject(c, wsMsg.ProjectID)
		}
	}
}

// 客户端写入消息
func (c *Client) writePump() {
	defer c.conn.Close()

	for {
		select {
		case message, ok := <-c.send:
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}
		}
	}
}

// 处理WebSocket连接
func HandleWebSocket(w http.ResponseWriter, r *http.Request, clientID int, clientType string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	client := &Client{
		conn:               conn,
		send:               make(chan []byte, 256),
		clientID:           clientID,
		clientType:         clientType,
		subscribedProjects: make([]int, 0),
	}

	GlobalHub.register <- client

	go client.writePump()
	go client.readPump()
}

// 广播出价更新
func BroadcastBidUpdate(projectID int) {
	// 获取项目最新信息
	var project model.AuctionProject
	if err := global.GVA_DB.Where("id = ?", projectID).Preload("ProductCategory").First(&project).Error; err != nil {
		return
	}

	// 获取最新出价记录
	var latestBid model.AuctionBid
	global.GVA_DB.Where("project_id = ? AND status = 1", projectID).
		Preload("Member").
		Order("bid_time DESC").
		First(&latestBid)

	updateData := map[string]interface{}{
		"currentPrice":  project.CurrentPrice,
		"bidCount":      project.BidCount,
		"endTime":       project.EndTime,
		"lastBidTime":   project.LastBidTime,
		"remainingTime": project.EndTime.Time.Sub(time.Now()).Seconds(),
	}

	if latestBid.ID > 0 {
		// 脱敏处理
		memberName := latestBid.Member.Name
		if len(memberName) > 1 {
			memberName = memberName[:1] + "***"
		}

		updateData["latestBidder"] = memberName
		updateData["latestBidAmount"] = latestBid.BidAmount
		updateData["latestBidTime"] = latestBid.BidTime
	}

	GlobalHub.BroadcastProjectUpdate(projectID, "bid_update", updateData)
}

// 广播项目状态更新
func BroadcastProjectStatusUpdate(projectID int, status int8) {
	updateData := map[string]interface{}{
		"status": status,
	}

	var messageType string
	switch status {
	case 1:
		messageType = "project_started"
	case 2:
		messageType = "project_ended"
	case 3:
		messageType = "project_terminated"
	default:
		messageType = "project_status_update"
	}

	GlobalHub.BroadcastProjectUpdate(projectID, messageType, updateData)
}
