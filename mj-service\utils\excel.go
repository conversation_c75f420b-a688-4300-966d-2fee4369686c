package utils

import (
    "bytes"
    "fmt"
    "github.com/gin-gonic/gin"
    "github.com/tealeg/xlsx"
    "io"
    "net/http"
    "time"
)

// 生成io.ReadSeeker  参数 titleList 为Excel表头，dataList 为数据 ,filename 文件名称
func ToExcel(titleList []string, dataList []interface{}, filename string) (content io.ReadSeeker) {
    // 生成一个新的文件
    file := xlsx.NewFile()
    // 添加sheet页
    sheet, _ := file.AddSheet("Sheet1")
    // 插入表头
    titleRow := sheet.AddRow()
    for _, v := range titleList {
        titleRow.AddCell().Value = v
    }
    // 插入内容
    for _, v := range dataList {
        row := sheet.AddRow()
        row.WriteStruct(v, -1)
    }
    err := file.Save("./excel/" + filename + ".xlsx")
    if err != nil {

    }
    var buffer bytes.Buffer
    err = file.Write(&buffer)
    if err != nil {

    }
    content = bytes.NewReader(buffer.Bytes())
    return
}

// 向前端返回Excel文件
// 参数 content 为上面生成的io.ReadSeeker， fileTag 为返回前端的文件名
func ResponseXls(c *gin.Context, content io.ReadSeeker, fileTag string) {
    fileName := fmt.Sprintf("%s%s%s.xlsx", `-`, fileTag)
    c.Writer.Header().Add("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, fileName))
    c.Writer.Header().Add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    http.ServeContent(c.Writer, c.Request, fileName, time.Now(), content)
}
