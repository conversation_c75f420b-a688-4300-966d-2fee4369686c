/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 13:36:21
 * @LastEditors: dlg
 * @LastEditTime: 2020-07-10 10:25:23
 */
/* eslint-disable */
// 账号
// export function isvalidUsername(str) {
//   const valid_map = ['root', 'admin', 'editor']
//   return valid_map.indexOf(str.trim()) >= 0
// }

// 合法uri
export function validateURL(textval) {
    const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
    return urlregex.test(textval)
}

// 小写字母
export function validateLowerCase(str) {
    const reg = /^[a-z]+$/
    return reg.test(str)
}

// 大写字母
export function validateUpperCase(str) {
    const reg = /^[A-Z]+$/
    return reg.test(str)
}

// 大小写字母
export function validateAlphabets(str) {
    const reg = /^[A-Za-z]+$/
    return reg.test(str)
}

// 邮箱
export function validateEmail(email) {
    const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return reg.test(email)
}

// 手机号
export function validatePhone(phone) {
    const reg = /^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/
    return reg.test(phone)
}

// 身份证号码
export function validateIdCardNum(idCardNum) {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return reg.test(idCardNum)
}

// 中国邮政编码
export function validatePostalcode(postalcode) {
    const reg = /^[1-9]\d{5}(?!\d)$/
    return reg.test(postalcode)
}

// 非零开头的最多带两位小数的数字
export function validateNumberOne(num) {
    const reg = /^([1-9][0-9]*)+(\.[0-9]{1,2})?$/
    return reg.test(num)
}

// IP地址
export function validateIpAddress(ipAddress) {
    const reg = /^((?:(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d))$/
    return reg.test(ipAddress)
}