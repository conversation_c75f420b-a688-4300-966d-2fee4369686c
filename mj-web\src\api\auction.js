/*
 * @Description: 竞价系统API
 * @Author: System
 * @Date: 2024-12-10
 */
import request from '@/utils/request'

// ==================== 会员管理 ====================

/**
 * @description: 获取会员列表
 * @param {Object} data - 查询参数
 * @return: 会员列表数据
 */
export function getMemberList(data) {
    return request({
        url: '/admin/auction/admin/members',
        method: 'post',
        data
    })
}

/**
 * @description: 审核会员
 * @param {Object} data - 审核参数
 * @return: 审核结果
 */
export function auditMember(data) {
    return request({
        url: '/admin/auction/admin/members/audit',
        method: 'post',
        data
    })
}

/**
 * @description: 获取会员详情
 * @param {Object} data - 会员ID
 * @return: 会员详情
 */
export function getMemberDetail(data) {
    return request({
        url: '/admin/auction/admin/members/detail',
        method: 'post',
        data
    })
}

/**
 * @description: 更新会员状态
 * @param {Object} data - 状态更新参数
 * @return: 更新结果
 */
export function updateMemberStatus(data) {
    return request({
        url: '/admin/auction/admin/members/status',
        method: 'post',
        data
    })
}

/**
 * @description: 获取已审核通过的会员列表
 * @param {Object} data - 查询参数
 * @return: 已审核会员列表
 */
export function getApprovedMembers(data) {
    return request({
        url: '/admin/auction/admin/members/approved',
        method: 'post',
        data
    })
}

// ==================== 竞价项目管理 ====================

/**
 * @description: 获取竞价项目列表
 * @param {Object} data - 查询参数
 * @return: 项目列表数据
 */
export function getAuctionProjectList(data) {
    return request({
        url: '/admin/auction/admin/projects',
        method: 'post',
        data
    })
}

/**
 * @description: 创建竞价项目
 * @param {Object} data - 项目信息
 * @return: 创建结果
 */
export function createAuctionProject(data) {
    return request({
        url: '/admin/auction/admin/projects/create',
        method: 'post',
        data
    })
}

/**
 * @description: 更新竞价项目
 * @param {Object} data - 项目信息
 * @return: 更新结果
 */
export function updateAuctionProject(data) {
    return request({
        url: '/admin/auction/admin/projects/update',
        method: 'post',
        data
    })
}

/**
 * @description: 获取竞价项目详情
 * @param {Object} data - 项目ID
 * @return: 项目详情
 */
export function getAuctionProjectDetail(data) {
    return request({
        url: '/admin/auction/admin/projects/detail',
        method: 'post',
        data
    })
}

/**
 * @description: 终止竞价项目
 * @param {Object} data - 项目ID
 * @return: 终止结果
 */
export function terminateAuctionProject(data) {
    return request({
        url: '/admin/auction/admin/projects/terminate',
        method: 'post',
        data
    })
}

// ==================== 权限管理 ====================

/**
 * @description: 查询已经授权的用户
 * @param {Object} data - 权限管理参数
 * @return: 管理结果
 */
export function getAuctionProjectPermission(data) {
    return request({
        url: '/admin/auction/admin/projectsPermission',
        method: 'post',
        data
    })
}


/**
 * @description: 管理竞价权限
 * @param {Object} data - 权限管理参数
 * @return: 管理结果
 */
export function manageAuctionPermission(data) {
    return request({
        url: '/admin/auction/admin/permissions',
        method: 'post',
        data
    })
}

// ==================== 监控管理 ====================

/**
 * @description: 获取出价记录列表
 * @param {Object} data - 查询参数
 * @return: 出价记录列表
 */
export function getBidList(data) {
    return request({
        url: '/admin/auction/admin/bids',
        method: 'post',
        data
    })
}

// ==================== 统计管理 ====================

/**
 * @description: 获取竞价统计信息
 * @param {Object} data - 查询参数
 * @return: 统计数据
 */
export function getAuctionStats(data) {
    return request({
        url: '/admin/auction/admin/stats',
        method: 'post',
        data
    })
}

// ==================== 商品分类 ====================

/**
 * @description: 获取商品分类列表
 * @param {Object} data - 查询参数
 * @return: 分类列表
 */
export function getProductCategories(data) {
    return request({
        url: '/admin/productCategory/getProductCategoryList',
        method: 'post',
        data
    })
}
