{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\views\\Home.vue", "mtime": 1757556310840}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AA8GA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"home-page\">\n    <AppHeader />\n    \n    <!-- 轮播横幅 -->\n    <section class=\"hero-section\">\n      <div class=\"hero-content\">\n        <h1>新疆生产建设兵团第一师棉麻有限责任公司竞价平台</h1>\n        <p>专业的棉副产品在线竞价交易平台，公开透明，安全可靠</p>\n        <div class=\"hero-actions\">\n          <el-button type=\"primary\" size=\"large\" @click=\"scrollToAuctions\">\n            立即竞价\n          </el-button>\n          <el-button size=\"large\" @click=\"$router.push('/register')\" v-if=\"!isLoggedIn\">\n            免费注册\n          </el-button>\n        </div>\n      </div>\n    </section>\n\n    <!-- 竞价项目列表 -->\n    <section class=\"auctions-section\" ref=\"auctionsSection\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h2>热门竞价</h2>\n          <div class=\"filters\">\n            <el-select v-model=\"filters.status\" placeholder=\"项目状态\" clearable @change=\"fetchAuctions\">\n              <el-option label=\"即将开始\" :value=\"0\" />\n              <el-option label=\"竞价中\" :value=\"1\" />\n              <el-option label=\"已结束\" :value=\"2\" />\n            </el-select>\n            <el-select v-model=\"filters.categoryId\" placeholder=\"商品分类\" clearable @change=\"fetchAuctions\">\n              <el-option\n                v-for=\"category in categories\"\n                :key=\"category.id\"\n                :label=\"category.name\"\n                :value=\"category.id\"\n              />\n            </el-select>\n          </div>\n        </div>\n\n        <div class=\"auctions-grid\" v-loading=\"loading\">\n          <div\n            v-for=\"auction in auctions\"\n            :key=\"auction.id\"\n            class=\"auction-card\"\n            @click=\"goToAuction(auction)\"\n          >\n\n            <div class=\"auction-content\">\n              <StatusTag :status=\"auction.status\" class=\"status-overlay\" />\n              <h3 class=\"auction-title\">{{ auction.title }}</h3>\n              <div class=\"auction-info\">\n                \n                <!-- 已登录用户显示价格信息 -->\n                <template v-if=\"isLoggedIn\">\n                  <div class=\"info-item\">\n                    <span class=\"label\">起拍价</span>\n                    <span class=\"value\">¥{{ formatMoney(auction.startPrice) }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <span class=\"label\">当前价</span>\n                    <span class=\"value current-price\">¥{{ formatMoney(auction.currentPrice) }}</span>\n                  </div>\n                </template>\n                <!-- 未登录用户显示基本信息 -->\n                <template v-else>\n                  <div class=\"info-item\">\n                    <span class=\"label\">数量</span>\n                    <span class=\"value\">{{ auction.quantity }}{{ auction.unit }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <span class=\"label\">商品类别</span>\n                    <span class=\"value\">{{ auction.productCategory }}</span>\n                  </div>\n                </template>\n                <div class=\"info-item\">\n                  <span class=\"label\">出价次数</span>\n                  <span class=\"value\">{{ auction.bidCount }}次</span>\n                </div>\n              </div>\n              \n              <div class=\"auction-time\">\n                <template v-if=\"auction.status === 0\">\n                  <span class=\"time-label\">开始时间：</span>\n                  <span class=\"time-value\">{{ auction.startTime | formatTime('MM-DD HH:mm') }}</span>\n                </template>\n                <template v-else-if=\"auction.status === 1\">\n                  <span class=\"time-label\">剩余时间：</span>\n                  <CountdownTimer :end-time=\"auction.endTime\" />\n                </template>\n                <template v-else>\n                  <span class=\"time-label\">结束时间：</span>\n                  <span class=\"time-value\">{{ auction.endTime | formatTime('MM-DD HH:mm') }}</span>\n                </template>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"load-more\" v-if=\"hasMore\">\n          <el-button @click=\"loadMore\" :loading=\"loadingMore\">加载更多</el-button>\n        </div>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { getCategories, getPublicAuctionList } from '@/api/auction'\n\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      stats: {},\n      auctions: [],\n      categories: [],\n      filters: {\n        status: '',\n        categoryId: ''\n      },\n      loading: false,\n      loadingMore: false,\n      page: 1,\n      pageSize: 12,\n      hasMore: true\n    }\n  },\n  computed: {\n    ...mapGetters('auth', ['isLoggedIn'])\n  },\n  async mounted() {\n    await Promise.all([\n      this.fetchStats(),\n      this.fetchCategories(),\n      this.fetchAuctions()\n    ])\n  },\n  methods: {\n    // 获取统计数据\n    async fetchStats() {\n      // 这里可以调用统计API\n      this.stats = {\n        totalProjects: 156,\n        totalMembers: 1280,\n        totalBids: 8960,\n        totalAmount: 2580\n      }\n    },\n\n    // 获取商品分类\n    async fetchCategories() {\n      try {\n        const response = await getCategories()\n        if (response.data.code === 200) {\n          this.categories = response.data.data || []\n        }\n      } catch (error) {\n        console.error('获取分类失败:', error)\n      }\n    },\n\n    // 获取竞价列表\n    async fetchAuctions(reset = false) {\n      if (reset) {\n        this.page = 1\n        this.auctions = []\n        this.hasMore = true\n      }\n\n      this.loading = reset\n      this.loadingMore = !reset\n\n      try {\n        let result\n\n        if (this.isLoggedIn) {\n          // 已登录用户：使用会员接口（包含价格信息和权限）\n          result = await this.$store.dispatch('auction/fetchAuctionList', {\n            page: this.page,\n            pageSize: this.pageSize,\n            ...this.filters\n          })\n        } else {\n          // 未登录用户：使用公开接口（不包含价格信息）\n          const requestData = {\n            page: this.page,\n            pageSize: this.pageSize,\n            status: this.filters.status || '',\n            categoryId: this.filters.categoryId || ''\n          }\n\n          const response = await getPublicAuctionList(requestData)\n          if (response.data.code === 200) {\n            result = { success: true, data: response.data.data }\n          } else {\n            result = { success: false, message: response.data.msg }\n          }\n        }\n\n        if (result.success) {\n          const newAuctions = result.data.list || []\n          if (reset) {\n            this.auctions = newAuctions\n          } else {\n            this.auctions.push(...newAuctions)\n          }\n\n          this.hasMore = newAuctions.length === this.pageSize\n        }\n      } catch (error) {\n        console.error('获取竞价列表失败:', error)\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n      }\n    },\n\n    // 加载更多\n    loadMore() {\n      this.page++\n      this.fetchAuctions()\n    },\n\n    // 跳转到竞价详情\n    goToAuction(auction) {\n      if (!this.isLoggedIn) {\n        this.$message.warning('请先登录')\n        this.$router.push('/login')\n        return\n      }\n      \n      this.$router.push(`/auction/${auction.id}`)\n    },\n\n    // 滚动到竞价区域\n    scrollToAuctions() {\n      this.$refs.auctionsSection.scrollIntoView({ behavior: 'smooth' })\n    },\n\n    // 格式化金额\n    formatMoney(value) {\n      if (!value && value !== 0) return '0'\n      return Number(value).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })\n    }\n  },\n  watch: {\n    filters: {\n      handler() {\n        this.fetchAuctions(true)\n      },\n      deep: true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.home-page {\n  min-height: 100vh;\n}\n\n// 英雄区域\n.hero-section {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 100px 0;\n  text-align: center;\n\n  .hero-content {\n    max-width: 800px;\n    margin: 0 auto;\n    padding: 0 20px;\n\n    h1 {\n      font-size: 48px;\n      font-weight: 700;\n      margin-bottom: 20px;\n    }\n\n    p {\n      font-size: 20px;\n      margin-bottom: 40px;\n      opacity: 0.9;\n    }\n\n    .hero-actions {\n      display: flex;\n      gap: 20px;\n      justify-content: center;\n    }\n  }\n}\n\n// 竞价区域\n.auctions-section {\n  padding: 60px 0;\n  min-height: 300px;\n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 40px;\n\n    h2 {\n      font-size: 32px;\n      color: $text-primary;\n    }\n\n    .filters {\n      display: flex;\n      gap: 15px;\n    }\n  }\n\n  .auctions-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: 30px;\n    margin-bottom: 40px;\n  }\n\n  .auction-card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: $box-shadow;\n    overflow: hidden;\n    cursor: pointer;\n    transition: transform 0.3s, box-shadow 0.3s;\n\n    &:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n    }\n\n    .auction-content {\n      position: relative;\n      overflow: hidden;\n      padding: 20px;\n      .status-overlay {\n        position: absolute;\n        top: 10px;\n        right: 10px;\n      }\n      .auction-title {\n        font-size: 18px;\n        font-weight: 600;\n        color: $text-primary;\n        margin-bottom: 15px;\n        @include text-ellipsis;\n      }\n\n      .auction-info {\n        margin-bottom: 15px;\n\n        .info-item {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n\n          .label {\n            color: $text-secondary;\n          }\n\n          .value {\n            font-weight: 600;\n            color: $text-primary;\n\n            &.current-price {\n              color: $danger-color;\n            }\n          }\n        }\n      }\n\n      .auction-time {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding-top: 15px;\n        border-top: 1px solid $border-color;\n\n        .time-label {\n          color: $text-secondary;\n          font-size: 14px;\n        }\n\n        .time-value {\n          font-weight: 600;\n          color: $text-primary;\n        }\n      }\n    }\n  }\n\n  .load-more {\n    text-align: center;\n  }\n}\n\n@include mobile {\n  .hero-section {\n    padding: 60px 0;\n\n    .hero-content {\n      h1 {\n        font-size: 32px;\n      }\n\n      p {\n        font-size: 16px;\n      }\n\n      .hero-actions {\n        flex-direction: column;\n        align-items: center;\n      }\n    }\n  }\n\n  .stats-section {\n    padding: 40px 0;\n\n    .stats-grid {\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n\n      .stat-item .stat-number {\n        font-size: 24px;\n      }\n    }\n  }\n\n  .auctions-section {\n    padding: 40px 0;\n\n    .section-header {\n      flex-direction: column;\n      gap: 20px;\n      align-items: flex-start;\n\n      .filters {\n        width: 100%;\n        justify-content: space-between;\n      }\n    }\n\n    .auctions-grid {\n      grid-template-columns: 1fr;\n      gap: 20px;\n    }\n  }\n}\n</style>\n"]}]}