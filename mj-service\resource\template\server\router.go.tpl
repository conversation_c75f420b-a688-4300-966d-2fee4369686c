package router

import (
	"auction-sys/api/v1"
	"auction-sys/middleware"
	"github.com/gin-gonic/gin"
)



func Init{{.StructName}}Router(Router *gin.RouterGroup) {
	{{.StructName}}Router := Router.Group("{{.Abbreviation}}").Use(middleware.JWTAuth())
	{
		{{.StructName}}Router.POST("create", v1.Create{{.StructName}})   // 新建{{.Description}}
		{{.StructName}}Router.POST("delete", v1.Delete{{.StructName}}) // 删除{{.Description}}
		{{.StructName}}Router.POST("deleteByIds", v1.Delete{{.StructName}}ByIds) // 批量删除{{.Description}}
		{{.StructName}}Router.POST("update", v1.Update{{.StructName}})    // 更新{{.Description}}
		{{.StructName}}Router.POST("find", v1.Find{{.StructName}})        // 根据ID获取{{.Description}}
		{{.StructName}}Router.POST("getList", v1.Get{{.StructName}}List)  // 获取{{.Description}}列表
		{{.StructName}}Router.POST("getKv", v1.Get{{.StructName}}KvList)  // 获取{{.Description}}下拉列表（Kv）
	}
}
