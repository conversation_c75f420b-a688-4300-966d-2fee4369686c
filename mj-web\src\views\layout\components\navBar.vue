<template>
  <div class="navbar">
    <div class="left-logo">
      <img
        src="@/assets/images/home/<USER>"
        class="company-logo"
      >
      <span style="color:#ffffff;">{{ userInfo.companyName?userInfo.companyName:'' }}</span>
    </div>
    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img
            :src="userInfo.headerImg ? userInfo.headerImg : '@/assets/images/baseImg/defaultPhoto.jpg'"
            class="user-avatar"
          >
          <div class="loginName">{{ userInfo.nickName?userInfo.nickName:'' }}</div>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <span @click="baseMess" style="display:block;">
              <svg class="svg-icon"
                   aria-hidden="true"
                   width="64"
                   height="64">
                <use xlink:href="#icon-personalMess" />
              </svg>
              基础信息
            </span>
          </el-dropdown-item>
          <el-dropdown-item>
            <span @click="modifyPass" style="display:block;">
              <svg class="svg-icon"
                   aria-hidden="true"
                   width="64"
                   height="64">
                <use xlink:href="#icon-modifyPass" />
              </svg>
              修改密码
            </span>
          </el-dropdown-item>
          <el-dropdown-item>
            <span @click="logout" style="display:block;">
              <svg class="svg-icon"
                   aria-hidden="true"
                   width="64"
                   height="64">
                <use xlink:href="#icon-logout" />
              </svg>
              退出登录
            </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog
        :visible.sync="modifyDialogVisible"
        width="36%"
        @close="handleClose"
        :append-to-body="true"
      >
        <el-form :model="passForm"
                 :rules="passRules"
                 ref="passForm"
                 label-width="80px">
          <el-form-item label="原密码" prop="pass">
            <el-input type="password" v-model="passForm.pass" />
          </el-form-item>
          <el-form-item label="新密码" prop="newPass">
            <el-input type="password" v-model="passForm.newPass" />
          </el-form-item>
          <el-form-item label="确认密码" prop="checkPass">
            <el-input type="password" v-model="passForm.checkPass" />
          </el-form-item>
          <el-form-item>
            <el-button @click="modifyDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="modifyPassSure">确 定</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { changePassword } from "@/api/personalMess";

export default {
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.passForm.newPass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      modifyDialogVisible: false,
      passForm: {
        pass: "",
        newPass: "",
        checkPass: "",
      },
      passRules: {
        pass: [{ required: true, message: "请输入原密码", trigger: "blur" }],
        newPass: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          {
            pattern: /^[a-zA-Z]\w{5,20}$/,
            message: "以字母开头，长度在6~20之间，只能包含字母、数字和下划线",
          },
        ],
        checkPass: [{ validator: validatePass, trigger: "blur" }],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    // logout() { //这种刷新页面的方式，才能清空我们之前注册的路由不推荐（虽然能用）
    //   this.$store.dispatch('LogOut').then(() => {
    //     location.reload();  //为了重新实例化vue-router对象以避免错误
    //   })
    // },
    modifyPass() {
      this.modifyDialogVisible = true;
    },
    baseMess() {
      this.$router.push({
        name: "PersonalMess",
      });
    },
    logBigScreen() {
      this.$router.push({
        name: "BigHome",
      });
    },
    modifyPassSure() {
      this.$refs["passForm"].validate(async (valid) => {
        if (valid) {
          const data = {
            password: this.passForm.pass,
            newPassword: this.passForm.newPass,
          };
          const res = await changePassword(data);
          if (res.data.code == 200) {
            this.$message({
              message: res.data.msg,
              type: "success",
            });
            this.modifyDialogVisible = false;
            this.$store.dispatch("resetUserCookies").then(() => {
              location.reload(); // 为了重新实例化vue-router对象 避免bug
            });
          } else {
            this.$message({
              message: res.data.msg,
              type: "error",
            });
          }
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$refs["passForm"].clearValidate();
      this.passForm.pass = "";
      this.passForm.newPass = "";
      this.passForm.checkPass = "";
    },
    async logout() {
      // 强烈推荐 spa 的方式来清空之前注册的路由
      await this.$store.dispatch("LogOut");

      // 此处 ?redirect=${this.$route.fullPath}` 是在退出系统时，将当前所在路由页面的 fullPath 以query的方式作为login页面路由的参数，
      // 下次重新登录时，就会取redirect参数的值，作为刚进去要显示页面的跳转路由
      // this.$router.push(`/login?redirect=${this.$route.fullPath}`);

      // 为了保证登出系统后，重新登录的用户权限没有登出之前所在页面的权限时，出现登录跳转到401或404页面情况。
      // (正常来说每次重新进入系统应该是初始化状态，不应该默认跳到上次退出系统时的页面)
      this.$router.push("/login");
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  line-height: 60px;
  background-color: rgb(20,183,183);
  user-select: none;
  .left-logo {
    float: left;
    height: 100%;
    position: relative;
    color: #ffffff;
    text-align: center;
    padding-left: 20px;
    .company-logo {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      vertical-align: middle;
      margin-right: 10px;
    }
  }
  .right-menu {
    float: right;
    height: 100%;
    &:focus {
      outline: none;
    }
    .avatar-container {
      height: 50px;
      margin-right: 20px;
      .avatar-wrapper {
        border: 0;
        outline: none;
        cursor: pointer;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 20px;
          vertical-align: middle;
        }
        .loginName {
          display: inline-block;
          margin: 0 10px;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
