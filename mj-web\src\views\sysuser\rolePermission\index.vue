<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-19 09:29:39
 * @LastEditors: dlg
 * @LastEditTime: 2020-11-18 18:35:29
-->
<template>
  <div class="app-container">
    <div style="margin-bottom: 20px; min-width: 360px">
      <span style="color: #606266; font-size: 14px">角色名称：</span>
      <el-input
        v-model="roleNameSearch"
        placeholder="请输入角色名称"
        style="width: 200px; margin-right: 12px"
        clearable
      />
      <el-button @click="getRoles" type="primary">查询</el-button>
    </div>
    <div>
      <div style="min-width: 460px">
        <el-button type="primary" @click="handleAddRole">新增角色</el-button>
        <el-button type="success" @click="batchEnable">批量启用</el-button>
        <el-button type="info" @click="batchDisable">批量禁用</el-button>
        <el-button type="danger" @click="batchDel">批量删除</el-button>
      </div>
    </div>

    <el-table
      :data="rolesList"
      style="width: 100%; margin: 30px 0"
      height="100%"
      border
      @selection-change="handleSelectionChange"
      @cell-click="cellClick"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="序号" min-width="80">
        <template slot-scope="scope">{{
          scope.$index + (page - 1) * limit + 1
        }}</template>
      </el-table-column>
      <el-table-column
        align="center"
        label="角色名称"
        width="220"
        prop="authorityName"
      />
      <el-table-column
        align="center"
        label="绑定用户数"
        min-width="100"
        prop="bindingUsers"
      />
      <el-table-column
        align="center"
        label="添加时间"
        min-width="200"
        sortable
        prop="createdAt"
      />
      <el-table-column
        align="center"
        label="权限"
        min-width="300"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.menus" :key="item.id">
            {{ item.meta.title }}
            <span>{{ index === scope.row.menus.length - 1 ? "" : "," }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        fixed="right"
        label="状态"
        :filters="disArray"
        :filter-method="filterStatus"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            class="mySwitch"
            active-text="开"
            inactive-text="关"
            @change="switchDisable(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="300">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button type="danger" size="small" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-drawer
      custom-class="myDrawer"
      :title="dialogType === 'edit' ? '编辑角色' : '新增角色'"
      :visible.sync="dialogVisible"
      direction="rtl"
      @close="addEditClose"
    >
      <div class="drawerDiv" ref="drawerDiv">
        <el-form
          :model="role"
          label-width="80px"
          label-position="left"
          style="height: 100%; overflow: auto"
        >
          <div
            v-if="dialogType === 'new'"
            ref="roleInput"
            style="margin-right: 20px; min-width: 360px"
          >
            <my-input
              v-for="(item, index) in newRole"
              :key="item.value"
              :default-value="{ value: item.value }"
              @change="(ev) => inputChange(ev, index)"
              @focusChange="() => focusChange(index)"
              @deleteItem="() => deleteItem(index)"
              @clearVal="clearVal(index)"
            />
            <div style="margin-left: 80px">
              <el-button
                type="text"
                @click="addRoleItem"
                style="margin-bottom: 20px"
                >+添加角色</el-button
              >
            </div>
          </div>
          <el-form-item
            v-if="dialogType === 'edit'"
            label="角色名称"
            style="margin-right: 20px; min-width: 360px"
          >
            <el-input
              v-model="role.authorityName"
              placeholder="角色名称"
              clearable
            />
          </el-form-item>
          <!-- <el-form-item label="描述" style="margin-right:20px;min-width:360px;">
            <el-input
              v-model="role.description"
              rows="4"
              resize="none"
              type="textarea"
              placeholder="角色描述"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>-->
          <el-form-item label="菜单按钮">
            <el-tree
              ref="tree"
              :default-expand-all="true"
              :check-strictly="checkStrictly"
              :data="treesData"
              :props="defaultProps"
              show-checkbox
              node-key="id"
              class="permission-tree"
              @check="checkClick"
            />
          </el-form-item>
        </el-form>
        <div class="drawerBtn">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRole">确认</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 绑定用户数 -->
    <el-drawer
      title="绑定用户数"
      :visible.sync="bindUserNumVisible"
      direction="rtl"
      size="50%"
      custom-class="myDrawer mysection"
    >
      <el-tabs type="border-card" class="userTabs">
        <el-tab-pane label="用户列表">
          <div style="margin: 20px 0; min-width: 320px">
            <span style="color: #606266; font-size: 14px">用户名：</span>
            <el-input
              v-model="bindUserName"
              placeholder="请输入用户名"
              style="width: 200px; margin-right: 12px"
              clearable
            />
            <el-button @click="getUserSearch" type="primary">查询</el-button>
          </div>
          <el-divider />
          <div>
            <el-button type="primary" @click="transferDialog"
              >变更用户列表</el-button
            >
          </div>
          <el-divider />
          <div class="roleNameTable">
            <span>角色名称：</span>
            <span>{{ drawerRoleName }}</span>
          </div>
          <el-table :data="userList" height="100%">
            <el-table-column property="id" label="id" />
            <el-table-column property="username" label="用户名" />
            <el-table-column property="nickname" label="真实姓名" />
            <el-table-column label="状态">
              <template slot-scope="scope">
                <span :class="{ enClass: scope.row.status === 1 }">
                  {{ scope.row.status === 0 ? "已禁用" : "已启用" }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <Page
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            :page-sizes="pageSizes"
            @search="getUserSearch"
          />
        </el-tab-pane>
        <el-tab-pane label="角色权限">
          <div style="display: flex; flex-direction: column; height: 100%">
            <div class="roleNameTable">
              <span>角色名称：</span>
              <span>{{ drawerRoleName }}</span>
            </div>
            <div style="overflow: auto; margin: 10px 0">
              <el-tree
                :data="treeDatas"
                :props="defaultProps"
                :default-expand-all="true"
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    <!-- 变更用户列表 -->
    <el-dialog
      title="变更用户列表"
      :visible.sync="transferDialogVisible"
      custom-class="dialogClass"
      top="30vh"
    >
      <div>
        <el-transfer
          filterable
          :props="transferProps"
          filter-placeholder="请输入用户名称"
          :titles="['待选用户列表', '授权用户列表']"
          v-model="transferUsers"
          :data="transferUsersData"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="transferDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="changeUaser">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { deepClone } from "@/utils/tools";
import {
  getRoutes,
  getRoles,
  addRole,
  deleteRole,
  updateRole,
  getUserSearch,
  changeStatus,
  getChangeUserList,
  getAuthSearch,
  changeUpdateUser,
} from "@/api/superMg/roleMg";
import MyInput from "./my-input";
import Page from "@/components/page";

const defaultRole = {
  id: "",
  authorityName: "",
  // description: "",
  trees: [],
};

export default {
  name: "Rolepermission",
  components: { MyInput, Page },
  data() {
    return {
      transferProps: {
        key: "id",
        label: "nickname",
      },
      disArray: [
        { text: "禁用", value: 0 },
        { text: "启用", value: 1 },
      ],
      drawerRoleID: "",
      roleNameSearch: "",
      role: Object.assign({}, defaultRole),
      trees: [], //保存初始获取到的原始 trees 数据
      rolesList: [], // 保存页面列表显示数据
      dialogVisible: false, // 控制弹框显示或隐藏
      checkStrictly: true, // 默认父子不互相关联
      dialogType: "new", // 弹框操作类型
      defaultProps: {
        // tree控件 props 属性配置
        children: "children",
        label: "title",
      },
      multipleSelection: [],
      newRole: [
        {
          value: "",
        },
      ],
      addNewRoles: [],
      curRoleflag: "",
      userList: [],
      bindUserNumVisible: false,
      bindUserName: "",
      drawerRoleName: "",
      transferDialogVisible: false,
      transferUsers: [],
      transferUsersData: [],
      treeDatas: [],
      page: 1,
      limit: 15,
      pageSizes: [15, 30, 50, 100],
      total: 0,
    };
  },
  computed: {
    treesData() {
      return this.trees;
    },
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.drawerDiv.style.height = `${
            document.body.clientHeight - 75
          }px`;
        });
        window.onresize = () => {
          this.$refs.drawerDiv.style.height = `${
            document.body.clientHeight - 75
          }px`;
        };
      }
    },
  },
  created() {
    // 获取路由列表和角色列表
    this.getRoutes();
    this.getRoles();
  },
  destroyed() {
    window.onresize = null;
  },
  methods: {
    checkClick(curObj) {
      if (curObj.children !== null) {
        let node = this.$refs.tree.getNode(curObj.id);
        this.lookChildren(node.checked, curObj.children);
      }
      if (curObj.parentId !== 0) {
        this.lookParent(curObj.parentId);
      }
    },
    lookChildren(isChecked, childs) {
      for (const item of childs) {
        this.$refs.tree.setChecked(item.id, isChecked);
        if (item.children !== null) {
          let node = this.$refs.tree.getNode(item.id);
          this.lookChildren(node.checked, item.children);
        }
      }
    },
    lookParent(pId) {
      let node = this.$refs.tree.getNode(pId);
      let checkedsArray = [];
      for (const item of node.childNodes) {
        checkedsArray.push(item.checked);
      }
      let bool = checkedsArray.every(function (curVal) {
        return curVal === false;
      });
      if (bool) {
        this.$refs.tree.setChecked(node.key, false);
      } else {
        this.$refs.tree.setChecked(node.key, true);
      }

      if (node.parent.key !== undefined) {
        this.lookParent(node.parent.key);
      }
    },
    // 列表状态栏过滤
    filterStatus(value, row) {
      return row.status == value;
    },
    // 获取路由页面列表
    async getRoutes() {
      const res = await getRoutes();
      if (res.data.code == 200) {
        this.trees = res.data.data.role.trees;
      } else {
        this.$message.error(res.data.msg);
      }
    },
    // 获取角色列表
    async getRoles() {
      const data = {
        name: this.roleNameSearch,
        page: this.page,
        pageSize: this.limit,
      };
      const res = await getRoles(data);
      if (res.data.code == 200) {
        this.rolesList = res.data.data.list;
        // this.page = res.data.data.page;
        // this.limit = res.data.data.pageSize;
        // this.total = res.data.data.total;
      } else {
        this.$message.error(res.data.msg);
      }
    },
    //生成最终数组形式数据
    generateArr(trees) {
      let data = [];
      trees.forEach((tree) => {
        data.push(tree);
        if (tree.children) {
          const temp = this.generateArr(tree.children);
          if (temp.length > 0) {
            data = [...data, ...temp];
          }
        }
      });
      return data;
    },
    // 筛选出初始选中的数据，返回数组形式
    checkedHandle(data) {
      let arr = [];
      for (let i = 0, len = data.length; i < len; i++) {
        if (data[i].status === 1) {
          arr.push(data[i]);
        }
      }
      return arr;
    },
    // 添加角色
    handleAddRole() {
      // 新增时重置
      this.curRoleflag = "";
      this.newRole = [
        {
          value: "",
        },
      ];
      // this.role.description = "";
      this.role = Object.assign({}, defaultRole);
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedNodes([]); //置空选中数据
      }
      this.addNewRoles = [];
      this.dialogType = "new";
      this.dialogVisible = true;
    },
    // 编辑
    async handleEdit(row) {
      this.dialogType = "edit";
      this.dialogVisible = true;
      this.role.id = row.id;
      this.role.authorityName = row.authorityName;
      const res = await getRoutes({ id: row.id });
      if (res.data.code == 200) {
        this.role.trees = res.data.data.role.trees;
      } else {
        this.$message.error(res.data.msg);
      }
      this.$nextTick(() => {
        let trees = this.role.trees;
        this.$refs.tree.setCheckedNodes(
          this.checkedHandle(this.generateArr(trees)) //  根据原始数据里的状态来处理渲染是否显示选中效果
        );
      });
    },
    // 删除
    handleDelete(row) {
      this.$confirm("确认删除角色?", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await deleteRole([{ id: row.id }]);
          if (res.data.code == 200) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.getRoles();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 根据最终选中的数据生成最终的树形原始数据(其实就是将最终选中结果映射到原始数据)
    generateTree(trees, checkedKeys) {
      let data = [];
      for (const tree of trees) {
        // 如果存在子节点，则递归遍历子节点
        if (tree.children) {
          tree.children = this.generateTree(tree.children, checkedKeys);
        }

        if (checkedKeys.includes(tree.id)) {
          tree.status = 1;
        } else {
          tree.status = 0;
        }
        data.push(tree);
      }
      return data;
    },
    // 确认
    confirmRole() {
      const isEdit = this.dialogType === "edit";
      if (isEdit) {
        if (!this.role.authorityName) {
          this.$notify.error({
            title: "提示",
            duration: 1500,
            message: "角色名称不能为空",
          });
        } else if (
          this.role.authorityName &&
          this.role.authorityName.length > 20
        ) {
          this.$notify.error({
            title: "提示",
            duration: 1500,
            message: "角色名称长度不超过20个字符",
          });
        } else {
          this.confirmTrueth();
        }
      } else {
        const bool = this.newRole.some((curVal) => {
          return !curVal.value === true;
        });
        if (bool) {
          this.$notify.error({
            title: "提示",
            duration: 1500,
            message: "角色名称不能为空",
          });
        }

        const lenthVal = this.newRole.some((curVal) => {
          return curVal.value && curVal.value.length > 20;
        });
        if (lenthVal) {
          this.$notify.error({
            title: "提示",
            duration: 1500,
            message: "角色名称长度不超过20个字符",
          });
        }
        // 校验通过时
        if (!bool && !lenthVal) {
          this.confirmTrueth();
        }
      }
    },
    // 真正执行数据交互的确认操作
    async confirmTrueth() {
      //判断是否重复
      let arrTemp = [];
      for (let i = 0, len = this.addNewRoles.length; i < len; i++) {
        arrTemp.push(this.addNewRoles[i].value);
      }

      for (let i = 0, len = arrTemp.length; i < len; i++) {
        if (arrTemp.indexOf(arrTemp[i]) !== i) {
          this.$notify.error({
            title: "提示",
            duration: 1500,
            message: "角色名称不可重复",
          });
          return;
        }
      }

      const isEdit = this.dialogType === "edit";

      if (isEdit) {
        const data = {
          id: this.role.id,
          authorityName: this.role.authorityName,
          menuIds: this.$refs.tree.getCheckedKeys(),
        };
        const res = await updateRole(data);
        if (res.data.code == 200) {
          this.$message({
            type: "success",
            message: res.data.msg,
          });
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      } else {
        //必须使用严格比较，包括类型校验，不然 0 和 "" 比较结果相等，导致判断错误
        if (this.curRoleflag !== "") {
          // 将确定前的数据更新到 addNewRoles 里
          // this.addNewRoles[
          //   this.curRoleflag
          // ].description = this.role.description;
          const checkedKeys = this.$refs.tree.getCheckedKeys(); // 返回当前已经选中的节点 key 值组成的数组
          // 处理为最终正确的数据
          // this.addNewRoles[this.curRoleflag].menus = this.generateTree(
          //   deepClone(this.trees),
          //   checkedKeys
          // );
          this.addNewRoles[this.curRoleflag].menuIds = checkedKeys;
        }

        let arrRoles = [];
        for (const item of this.addNewRoles) {
          let obj = {
            authorityName: item.value,
            menuIds: item.menuIds,
          };
          arrRoles.push(obj);
        }

        const res = await addRole(arrRoles);
        if (res.data.code == 200) {
          this.$message({
            type: "success",
            message: res.data.msg,
          });
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      }
      this.dialogVisible = false;
      this.getRoles();
    },
    async cellClick(row, column) {
      if (column.label === "绑定用户数") {
        this.drawerRoleID = row.id;
        this.drawerRoleName = row.authorityName;
        this.transferUsers = [];
        this.transferUsersData = [];
        this.getUserList();
        this.getAuthTree();
        this.bindUserNumVisible = true;
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    async switchDisable(row) {
      const res = await changeStatus([{ id: row.id, status: row.status }]);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    // 相应改变角色名称数组中对应index元素的数据
    inputChange(ev, index) {
      this.curRoleflag = index; //设置当前操作的角色索引
      this.newRole[index].value = ev.target.value;

      if (this.addNewRoles[index]) {
        this.addNewRoles[index].value = ev.target.value;
        // this.addNewRoles[index].description = this.role.description;
        const checkedKeys = this.$refs.tree.getCheckedKeys(); // 返回当前已经选中的节点 key 值组成的数组
        // 处理为最终正确的数据
        // this.addNewRoles[index].menus = this.generateTree(
        //   deepClone(this.trees),
        //   checkedKeys
        // );
        this.addNewRoles[index].menuIds = checkedKeys;
      } else {
        const checkedKeys = this.$refs.tree.getCheckedKeys(); // 返回当前已经选中的节点 key 值组成的数组
        const tmpRoles = {
          value: ev.target.value,
          // description: this.role.description,
          // menus: this.generateTree(deepClone(this.trees), checkedKeys)
          menuIds: checkedKeys,
        };
        this.addNewRoles.push(tmpRoles);
      }
    },
    // 点击某一个角色时显示对应的数据
    focusChange(inx) {
      // 确保聚焦是角色数据已经存在于 addNewRoles，否则就是新加角色操作
      if (this.addNewRoles.length > inx) {
        //必须使用严格比较，包括类型校验，不然 0 和 "" 比较结果相等，导致判断错误
        if (this.curRoleflag !== "") {
          //正常的聚焦操作
          //将聚焦前的数据更新到 addNewRoles 里
          // this.addNewRoles[
          //   this.curRoleflag
          // ].description = this.role.description;
          const checkedKeys = this.$refs.tree.getCheckedKeys(); // 返回当前已经选中的节点 key 值组成的数组
          // 处理为最终正确的数据
          // this.addNewRoles[this.curRoleflag].menus = this.generateTree(
          //   deepClone(this.trees),
          //   checkedKeys
          // );
          this.addNewRoles[this.curRoleflag].menuIds = checkedKeys;

          //置空
          // this.role.description = "";
          this.role = Object.assign({}, defaultRole);
          if (this.$refs.tree) {
            this.$refs.tree.setCheckedNodes([]); //置空选中数据
          }
          // 显示对应角色数据
          // this.role.description = this.addNewRoles[inx].description;
          // this.$refs.tree.setCheckedNodes(
          //   this.checkedHandle(this.generateArr(this.addNewRoles[inx].menus)) //  根据原始数据里的状态来处理渲染是否显示选中效果
          // );
          this.$refs.tree.setCheckedKeys(
            this.addNewRoles[inx].menuIds //  根据原始数据里的状态来处理渲染是否显示选中效果
          );
        } else {
          // 表示删除之后进行的某一个聚焦操作
          // 置空数据
          // this.role.description = "";
          this.role = Object.assign({}, defaultRole);
          if (this.$refs.tree) {
            this.$refs.tree.setCheckedNodes([]); //置空选中数据
          }
          // 显示当前数据
          // this.role.description = this.addNewRoles[inx].description;
          // this.$refs.tree.setCheckedNodes(
          //   this.checkedHandle(this.generateArr(this.addNewRoles[inx].menus)) //  根据原始数据里的状态来处理渲染是否显示选中效果
          // );
          this.$refs.tree.setCheckedKeys(
            this.addNewRoles[inx].menuIds //  根据原始数据里的状态来处理渲染是否显示选中效果
          );
        }
      }
    },
    // 新增角色时添加角色
    addRoleItem() {
      //判断是否重复
      let arrTemp = [];
      for (let i = 0, len = this.addNewRoles.length; i < len; i++) {
        arrTemp.push(this.addNewRoles[i].value);
      }

      for (let i = 0, len = arrTemp.length; i < len; i++) {
        if (arrTemp.indexOf(arrTemp[i]) !== i) {
          this.$notify.error({
            title: "提示",
            duration: 1500,
            message: "角色名称不可重复",
          });
          return;
        }
      }

      const bool = this.newRole.some((curVal) => {
        return !curVal.value === true;
      });
      if (bool) {
        this.$notify.error({
          title: "提示",
          duration: 1500,
          message: "角色名称不能为空",
        });
        return;
      }

      const lenthVal = this.newRole.some((curVal) => {
        return curVal.value && curVal.value.length > 20;
      });
      if (lenthVal) {
        this.$notify.error({
          title: "提示",
          duration: 1500,
          message: "角色名称长度不超过20个字符",
        });
        return;
      }

      if (this.newRole.length > 4) {
        this.$notify.error({
          title: "提示",
          duration: 1500,
          message: "一次添加角色名称最多不超过5个",
        });
      } else {
        this.newRole.push({ value: "" });

        //必须使用严格比较，包括类型校验，不然 0 和 "" 比较结果相等，导致判断错误
        if (this.curRoleflag !== "") {
          // 如果之前删除操作，删除时已经更新了数据到 addNewRoles
          // 在添加新的之前将当前的数据更新到 addNewRoles 里
          // this.addNewRoles[
          //   this.curRoleflag
          // ].description = this.role.description;
          const checkedKeys = this.$refs.tree.getCheckedKeys(); // 返回当前已经选中的节点 key 值组成的数组
          // 处理为最终正确的数据
          // this.addNewRoles[this.curRoleflag].menus = this.generateTree(
          //   deepClone(this.trees),
          //   checkedKeys
          // );
          this.addNewRoles[this.curRoleflag].menuIds = checkedKeys;
        }

        // 初始化新的角色数据为空
        // this.role.description = "";
        this.role = Object.assign({}, defaultRole);
        if (this.$refs.tree) {
          this.$refs.tree.setCheckedNodes([]); //置空选中数据
        }
      }

      this.$nextTick(() => {
        if (this.$refs.roleInput) {
          let roleInputDom = this.$refs.roleInput;
          let selfDom =
            roleInputDom.childNodes[roleInputDom.childNodes.length - 2];
          let targetDiv = selfDom.childNodes[1].childNodes[0].children[0];
          targetDiv.focus();
        }
      });
    },
    // 相应删除角色名称数组对应index元素的数据
    deleteItem(index) {
      if (this.newRole.length === 1) {
        this.$notify.error({
          title: "提示",
          duration: 1500,
          message: "角色名称至少存在一个",
        });
      } else {
        //必须使用严格比较，包括类型校验，不然 0 和 "" 比较结果相等，导致判断错误
        if (this.curRoleflag !== "") {
          //之前已经删除操作过了，没有可更新的数据了
          //将删除前最后操作的数据更新到 addNewRoles 里
          // this.addNewRoles[
          //   this.curRoleflag
          // ].description = this.role.description;
          const checkedKeys = this.$refs.tree.getCheckedKeys(); // 返回当前已经选中的节点 key 值组成的数组
          // 处理为最终正确的数据
          // this.addNewRoles[this.curRoleflag].menus = this.generateTree(
          //   deepClone(this.trees),
          //   checkedKeys
          // );
          this.addNewRoles[this.curRoleflag].menuIds = checkedKeys;
        }
        // 删除操作后将标志重置为空，为了在确定操作时做判断处理，确定是不再做数据更新操作
        this.curRoleflag = "";
        //删除数据
        this.newRole.splice(index, 1);
        if (this.addNewRoles.length > index) {
          this.addNewRoles.splice(index, 1);

          // 删除后默认显示已添加角色数据里的最后一条数据信息
          // this.role.description = this.addNewRoles[
          //   this.addNewRoles.length - 1
          // ].description;

          // this.$refs.tree.setCheckedNodes(
          //   this.checkedHandle(
          //     this.generateArr(
          //       this.addNewRoles[this.addNewRoles.length - 1].menus
          //     )
          //   ) //  根据原始数据里的状态来处理渲染是否显示选中效果
          // );
          this.$refs.tree.setCheckedKeys(
            this.addNewRoles[this.addNewRoles.length - 1].menuIds
          );
        }
      }
    },
    // 抽屉关闭时回调
    addEditClose() {
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedNodes([]); //置空选中数据
      }
    },
    // 图标清空
    clearVal(index) {
      this.newRole[index].value = "";
    },
    async getUserList() {
      const data = {
        id: this.drawerRoleID,
        page: this.page,
        pageSize: this.limit,
        username: this.bindUserName,
      };
      const res = await getUserSearch(data);
      if (res.data.code == 200) {
        this.userList = res.data.data.list;
        this.total = res.data.data.total;
        this.page = res.data.data.page;
        this.limit = res.data.data.pageSize;
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    // 绑定用户数用户查询
    async getUserSearch() {
      this.getUserList();
    },
    async getAuthTree() {
      const res = await getAuthSearch([{ id: this.drawerRoleID }]);
      if (res.data.code == 200) {
        this.treeDatas = res.data.data;
      } else {
        this.treeDatas = [];
      }
    },
    async transferDialog() {
      const res = await getChangeUserList({ id: this.drawerRoleID });
      if (res.data.code == 200) {
        // this.transferUsersData = res.data.data.electeds;
        this.transferUsersData = res.data.data.electeds.concat(
          res.data.data.authorizations
        );
        res.data.data.authorizations.map((item) => {
          this.transferUsers.push(item.id);
        });
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
      this.transferDialogVisible = true;
    },
    async changeUaser() {
      const data = {
        authId: this.drawerRoleID,
        userIds: this.transferUsers,
      };
      const res = await changeUpdateUser(data);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.transferDialogVisible = false;
        this.getUserList();
        this.getRoles();
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    //启用或禁用
    async disOrEn(num) {
      let arrSelect = [];
      for (const item of this.multipleSelection) {
        arrSelect.push({ id: item.id, status: num });
      }
      const res = await changeStatus(arrSelect);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.getRoles();
        this.multipleSelection = [];
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
    isEmpty() {
      return this.multipleSelection.length === 0;
    },
    //批量启用
    async batchEnable() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      const bool = this.multipleSelection.every((item) => {
        return item.status == 1;
      });
      if (bool) {
        this.$message({
          type: "warning",
          message: "所选数据全部是已启用状态，请重新选择",
        });
      } else {
        this.disOrEn(1);
      }
    },
    //批量禁用
    async batchDisable() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      const bool = this.multipleSelection.every((item) => {
        return item.status == 0;
      });
      if (bool) {
        this.$message({
          type: "warning",
          message: "所选数据全部是已禁用状态，请重新选择",
        });
      } else {
        this.disOrEn(0);
      }
    },
    //批量删除
    async batchDel() {
      if (this.isEmpty()) {
        this.$message({
          type: "warning",
          message: "请勾选数据",
        });
        return;
      }
      let arrSelect = [];
      for (const item of this.multipleSelection) {
        arrSelect.push({ id: item.id });
      }
      const res = await deleteRole(arrSelect);
      if (res.data.code == 200) {
        this.$message({
          type: "success",
          message: res.data.msg,
        });
        this.getRoles();
        this.multipleSelection = [];
      } else {
        this.$message({
          type: "error",
          message: res.data.msg,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .roles-table {
    margin-top: 30px;
  }
  .permission-tree {
    margin-bottom: 30px;
  }
  .drawerDiv {
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .drawerBtn {
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .userTabs {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .enClass {
    color: #42a7ff;
  }
  .roleNameTable {
    color: #ff0000;
    padding-bottom: 12px;
    font-size: 14px;
  }
}
</style>

<style>
.myDrawer,
.myDrawer .el-drawer__close-btn,
.myDrawer .el-drawer__header span {
  outline: none;
}
.userTabs .el-tabs__content {
  height: 100%;
}
.userTabs .el-tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.mysection .el-drawer__body {
  overflow: hidden;
}
.dialogClass {
  width: 630px;
  margin-right: 6%;
}
.dialogClass .el-dialog__footer {
  text-align: center;
}
</style>
