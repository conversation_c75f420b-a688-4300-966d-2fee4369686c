package service

import (
	"auction-sys/global"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"errors"
	"fmt"
	"time"

	"github.com/Gre-Z/common/jtime"
	"github.com/jinzhu/gorm"
)

// 出价
func PlaceBid(bidReq req.PlaceBidReq, memberID int, ipAddress, userAgent string) (error, resp.PlaceBidResp) {
	var bidResp resp.PlaceBidResp

	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 检查项目是否存在且状态正确
		var project model.AuctionProject
		if err := tx.Where("id = ?", bidReq.ProjectID).First(&project).Error; err != nil {
			bidResp.Success = false
			bidResp.Message = "竞价项目不存在"
			return errors.New("竞价项目不存在")
		}

		// 检查项目状态
		if project.Status != 1 {
			bidResp.Success = false
			bidResp.Message = "竞价未开始或已结束"
			return errors.New("竞价未开始或已结束")
		}

		// 检查是否已过期
		if time.Now().After(project.EndTime.Time) {
			bidResp.Success = false
			bidResp.Message = "竞价已结束"
			return errors.New("竞价已结束")
		}

		// 2. 检查会员权限
		var permission model.AuctionPermission
		if err := tx.Where("project_id = ? AND member_id = ? AND status = 1", bidReq.ProjectID, memberID).First(&permission).Error; err != nil {
			bidResp.Success = false
			bidResp.Message = "您暂无出价权限"
			return errors.New("您暂无出价权限")
		}

		// 3. 金额校验
		if bidReq.BidAmount <= project.CurrentPrice {
			bidResp.Success = false
			bidResp.Message = "出价必须高于当前价格"
			return errors.New("出价必须高于当前价格")
		}

		increment := bidReq.BidAmount - project.CurrentPrice
		if increment < project.MinIncrement {
			bidResp.Success = false
			bidResp.Message = fmt.Sprintf("加价幅度不能少于%.2f", project.MinIncrement)
			return errors.New(fmt.Sprintf("加价幅度不能少于%.2f", project.MinIncrement))
		}

		// 4. 频率控制校验（30秒间隔）
		var lastBid model.AuctionBid
		if !tx.Where("project_id = ? AND status = 1", bidReq.ProjectID).
			Order("bid_time DESC").
			First(&lastBid).RecordNotFound() {

			timeSinceLastBid := time.Now().Sub(lastBid.BidTime.Time)
			if timeSinceLastBid < 30*time.Second {
				bidResp.Success = false
				bidResp.Message = "出价过于频繁，请等待片刻后再试"
				return errors.New("出价过于频繁，请等待片刻后再试")
			}
		}

		// 5. 创建出价记录
		bid := model.AuctionBid{
			ProjectID: bidReq.ProjectID,
			MemberID:  memberID,
			BidAmount: bidReq.BidAmount,
			BidTime:   jtime.JsonTime{time.Now()},
			IPAddress: ipAddress,
			UserAgent: userAgent,
			Status:    1, // 有效
			IsWinning: false,
			PrevPrice: project.CurrentPrice,
			Increment: increment,
		}

		if err := tx.Create(&bid).Error; err != nil {
			bidResp.Success = false
			bidResp.Message = "出价失败"
			return errors.New("出价失败")
		}

		// 6. 更新项目信息
		// 计算新的结束时间（延时规则）
		newEndTime := project.EndTime.Time
		extendedTime := time.Now().Add(5 * time.Minute)
		if extendedTime.Before(project.OriginalEndTime.Time) {
			newEndTime = extendedTime
		} else {
			newEndTime = project.OriginalEndTime.Time
		}

		// 将之前的中标出价标记为非中标
		tx.Model(&model.AuctionBid{}).
			Where("project_id = ? AND is_winning = true", bidReq.ProjectID).
			Update("is_winning", false)

		// 标记当前出价为中标出价
		tx.Model(&bid).Update("is_winning", true)

		// 更新项目信息
		updateData := map[string]interface{}{
			"current_price":    bidReq.BidAmount,
			"end_time":         jtime.JsonTime{newEndTime},
			"last_bid_time":    jtime.JsonTime{time.Now()},
			"bid_count":        gorm.Expr("bid_count + 1"),
			"winner_member_id": memberID,
			"final_price":      bidReq.BidAmount,
		}

		if err := tx.Model(&project).Updates(updateData).Error; err != nil {
			bidResp.Success = false
			bidResp.Message = "更新项目信息失败"
			return errors.New("更新项目信息失败")
		}

		// 7. 构造成功响应
		bidResp.Success = true
		bidResp.Message = "出价成功"
		bidResp.CurrentPrice = bidReq.BidAmount
		bidResp.NewEndTime = jtime.JsonTime{newEndTime}
		bidResp.BidCount = project.BidCount + 1
		bidResp.RemainingTime = int64(newEndTime.Sub(time.Now()).Seconds())

		// 广播出价更新
		go BroadcastBidUpdate(bidReq.ProjectID)

		return nil
	}), bidResp
}

// 获取出价记录列表
func GetBidList(listReq req.GetBidListReq) (error, resp.GetBidListResp) {
	var listResp resp.GetBidListResp
	var bids []model.AuctionBid
	var total int

	db := global.GVA_DB.Model(&model.AuctionBid{}).
		Where("status = 1").
		Preload("Member")

	if listReq.ProjectID != 0 {
		db = db.Where("project_id = ?", listReq.ProjectID)
	}

	// 如果指定了会员ID，只查询该会员的出价记录
	if listReq.MemberID > 0 {
		db = db.Where("member_id = ?", listReq.MemberID)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 分页查询
	offset := listReq.PageSize * (listReq.Page - 1)
	if err := db.Offset(offset).Limit(listReq.PageSize).Order("bid_time DESC").Find(&bids).Error; err != nil {
		return errors.New("查询失败"), listResp
	}

	// 构造响应
	var bidList []resp.BidListItem
	for _, bid := range bids {
		// 脱敏处理
		memberName := bid.Member.Name
		if len(memberName) > 1 {
			memberName = memberName[:1] + "***"
		}
		ipAddress := bid.IPAddress
		if len(ipAddress) > 7 {
			ipAddress = ipAddress[:7] + "***"
		}

		bidList = append(bidList, resp.BidListItem{
			ID:        bid.ID,
			ProjectID: bid.ProjectID,
			MemberID:  bid.MemberID,
			Member:    memberName,
			BidAmount: bid.BidAmount,
			BidTime:   bid.BidTime,
			IPAddress: ipAddress,
			Status:    bid.Status,
			IsWinning: bid.IsWinning,
			PrevPrice: bid.PrevPrice,
			Increment: bid.Increment,
		})
	}

	listResp.List = bidList
	listResp.Total = total
	listResp.Page = listReq.Page
	listResp.PageSize = listReq.PageSize

	return nil, listResp
}

// 获取我的参与记录
func GetMyParticipation(memberID int, page, pageSize int) (error, resp.MyParticipationResp) {
	var participationResp resp.MyParticipationResp
	var total int

	// 查询我参与过的项目（有出价记录的项目）
	subQuery := global.GVA_DB.Model(&model.AuctionBid{}).
		Select("DISTINCT project_id").
		Where("member_id = ? AND status = 1", memberID).
		SubQuery()

	db := global.GVA_DB.Model(&model.AuctionProject{}).
		Where("id IN (?)", subQuery).
		Preload("ProductCategory")

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return errors.New("查询失败"), participationResp
	}

	// 分页查询
	var projects []model.AuctionProject
	offset := pageSize * (page - 1)
	if err := db.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&projects).Error; err != nil {
		return errors.New("查询失败"), participationResp
	}

	// 构造响应
	var participationList []resp.MyParticipationItem
	for _, project := range projects {
		// 查询我在该项目的最高出价和出价次数
		var myHighestBid float64
		var myBidCount int

		global.GVA_DB.Model(&model.AuctionBid{}).
			Where("project_id = ? AND member_id = ? AND status = 1", project.ID, memberID).
			Select("MAX(bid_amount)").
			Row().Scan(&myHighestBid)

		global.GVA_DB.Model(&model.AuctionBid{}).
			Where("project_id = ? AND member_id = ? AND status = 1", project.ID, memberID).
			Count(&myBidCount)

		// 检查是否中标
		isWinner := project.WinnerMemberID == memberID && project.Status == 2

		participationList = append(participationList, resp.MyParticipationItem{
			ProjectID:       project.ID,
			Title:           project.Title,
			ProductCategory: project.ProductCategory.Name,
			StartPrice:      project.StartPrice,
			CurrentPrice:    project.CurrentPrice,
			FinalPrice:      project.FinalPrice,
			MyHighestBid:    myHighestBid,
			MyBidCount:      myBidCount,
			Status:          project.Status,
			IsWinner:        isWinner,
			EndTime:         project.EndTime,
			LastBidTime:     project.LastBidTime,
		})
	}

	participationResp.List = participationList
	participationResp.Total = total
	participationResp.Page = page
	participationResp.PageSize = pageSize

	return nil, participationResp
}

// 检查出价频率限制
func CheckBidFrequency(projectID int) (bool, int) {
	var lastBid model.AuctionBid
	if !global.GVA_DB.Where("project_id = ? AND status = 1", projectID).
		Order("bid_time DESC").
		First(&lastBid).RecordNotFound() {

		timeSinceLastBid := time.Now().Sub(lastBid.BidTime.Time)
		if timeSinceLastBid < 30*time.Second {
			remainingSeconds := int(30 - timeSinceLastBid.Seconds())
			return false, remainingSeconds
		}
	}
	return true, 0
}
