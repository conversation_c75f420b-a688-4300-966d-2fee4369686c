package service

import (
	"auction-sys/constants"
	"auction-sys/global"
	"auction-sys/global/response"
	"auction-sys/model"
	"auction-sys/model/req"
	"auction-sys/model/resp"
	"strings"
	"time"

	"github.com/Gre-Z/common/jtime"
	"github.com/yangtizi/cz88"
)

// @Title 设置操作日志
// @Description
// <AUTHOR>
// @Param uuid 				string		用户唯一标识
// @Param operation 		string		操作模块
// @Param ip 				string		IP 地址
// @Param requestParam 		string		请求参数
// @Param responseParam 	string		响应参数
// @Param result 			int			请求结果
// @Param header 			string		请求方式

func AddOperationLog(uuid string, operation string, ip string, requestParam string, responseParam string, result int, header string) {
	// 通过 uuid 查询用户，获取用户 username 、 nickname 和 companyCode
	var user model.SysUser
	global.GVA_DB.Where("uuid = ?", uuid).First(&user)

	var operationLog model.TLogOperation
	operationLog.Username = user.Username
	operationLog.Nickname = user.NickName
	operationLog.Operation = operation
	operationLog.OperationTime = jtime.JsonTime{time.Now()}
	loginMethod := int8(constants.LOGIN_METHOD_WEB)
	// 如果是小程序请求，更改登录方式
	if strings.Contains(header, constants.APPLETS_HEADER) {
		loginMethod = int8(constants.LOGIN_METHOD_APPLETS)
	}
	operationLog.LoginMethod = loginMethod
	operationLog.Ip = ip
	operationLog.Address = cz88.GetAddress(ip)
	operationLog.RequestParam = requestParam
	operationLog.ResponseParam = responseParam
	operationLog.Result = result
	operationLog.CompanyCode = user.CompanyCode
	global.GVA_DB.Create(&operationLog)

}

// @Title 获取操作日志列表
// @Description
// <AUTHOR>
// @Param uuid 			string						用户唯一标识
// @Param logRequest 	request.TLogListRequest		查询条件
// @Return err 			error 						错误信息
// @Return list 		interface{} 				列表
// @Return total 		int 						总条数

func GetOperationLogList(uuid string, logRequest req.TLogListRequest) (err error, list interface{}, total int) {
	err, currentUser := GetCurrentUser(uuid)
	if err != nil {
		return err, list, total
	}

	limit := logRequest.PageSize
	offset := logRequest.PageSize * (logRequest.Page - 1)
	db := global.GVA_DB
	var operationLogList []resp.OperationLogList
	// 如果不是管理员，只能看自己公司的
	if currentUser.Username != global.GVA_CONFIG.Admin.Username {
		db = db.Where("company_code = ?", currentUser.CompanyCode)
	}
	if logRequest.StartTime != "" {
		db = db.Where("date_format(operation_time,'%Y-%m-%d') >= ?", logRequest.StartTime)
	}
	if logRequest.EndTime != "" {
		db = db.Where("? >= date_format(operation_time,'%Y-%m-%d')", logRequest.EndTime)
	}
	if len(logRequest.CompanyCode) > 0 {
		db = db.Where("company_code in (?)", logRequest.CompanyCode)
	}
	if logRequest.Username != "" {
		db = db.Where("instr(username,?)", logRequest.Username)
	}
	db = db.Where("result = ?", response.SUCCESS)
	err = db.Find(&[]model.TLogOperation{}).Count(&total).Error
	err = db.Select("username, nickname, operation, operation_time, login_method, ip, address").Limit(limit).Offset(offset).Order("operation_time desc").Find(&[]model.TLogOperation{}).Scan(&operationLogList).Error
	list = operationLogList
	return err, list, total
}
