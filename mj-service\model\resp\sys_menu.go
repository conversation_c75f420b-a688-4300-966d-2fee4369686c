package resp

import "auction-sys/model"

// 用于封装菜单对象
type SysBaseMenuResponse struct {
	Menu model.SysBaseMenu `json:"menu"` // 菜单对象
}

// 路由菜单封装
type RouterMenuResponse struct {
	Menus []RouterMenu `json:"menus"` //路由菜单数组
}

// 路由菜单对象
type RouterMenu struct {
	MenuId    int           `json:"menuId"`    // 菜单 id
	Path      string        `json:"path"`      // 路由 path
	Name      string        `json:"name"`      // 路由 name
	Hidden    bool          `json:"hidden"`    // 是否在列表隐藏
	Component string        `json:"component"` // 对应前端文件路径
	Category  int8          `json:"category"`  // 菜单类别 菜单：0  按钮：1
	Keyval    string        `json:"keyval"`    // 按钮标识
	ParentId  int           `json:"parentId"`  // 父级 id
	Meta      `json:"meta"` // 附加属性
	Children  []RouterMenu  `json:"children"` // 子级菜单
}

// 附加属性
type Meta struct {
	KeepAlive bool   `json:"noCache"` // 是否缓存
	Title     string `json:"title"`   // 菜单名
	Icon      string `json:"icon"`    // 菜单图标
	Btns      []Btn  `json:"btns"`    // 按钮数组
}

// 按钮对象
type Btn struct {
	Keyval string `json:"key"`  // 按钮键值
	Name   string `json:"name"` // 按钮名称
}

// 用户信息中菜单信息
type Menu struct {
	Id       int    `json:"id""`       // 菜单 id
	Title    string `json:"title"`     // 菜单标题
	ParentId int    `json:"parentId""` // 父级 id
	Children []Menu `json:"children"`  // 子级菜单
}
