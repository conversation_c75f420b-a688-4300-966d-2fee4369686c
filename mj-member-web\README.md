# 棉副产品竞价平台 - 客户端

这是棉副产品竞价平台的客户端应用，为竞价参与者提供完整的竞价体验。

## 功能特性

### 🔐 用户认证
- **用户注册**：手机号注册，短信验证码验证
- **用户登录**：手机号+密码登录
- **个人资料管理**：完善企业信息，修改密码

### 🏠 竞价首页
- **项目展示**：热门竞价项目展示
- **实时统计**：平台统计数据展示
- **分类筛选**：按状态和分类筛选项目
- **响应式设计**：适配各种设备

### 💰 竞价功能
- **项目详情**：完整的项目信息展示
- **实时出价**：支持实时出价功能
- **倒计时显示**：精确的剩余时间显示
- **出价历史**：查看所有出价记录
- **WebSocket实时更新**：实时价格和状态更新

### 👤 个人中心
- **个人资料**：查看和编辑个人信息
- **我的出价**：查看所有出价记录和状态
- **我的项目**：查看可参与的项目列表
- **统计信息**：个人竞价统计数据

## 技术栈

- **前端框架**：Vue.js 2.6
- **UI组件库**：Element UI 2.15
- **状态管理**：Vuex 3.6
- **路由管理**：Vue Router 3.5
- **HTTP客户端**：Axios
- **实时通信**：Socket.IO Client
- **样式预处理**：Sass/SCSS
- **构建工具**：Vue CLI 5.0

## 项目结构

```
mj-member-web/
├── public/                 # 静态资源
│   └── index.html         # HTML模板
├── src/
│   ├── api/               # API接口
│   │   ├── request.js     # Axios配置
│   │   ├── auth.js        # 认证相关API
│   │   ├── auction.js     # 竞价相关API
│   │   └── user.js        # 用户相关API
│   ├── components/        # 全局组件
│   │   ├── global.js      # 全局组件注册
│   │   ├── AppHeader.vue  # 应用头部
│   │   ├── CountdownTimer.vue  # 倒计时组件
│   │   └── StatusTag.vue  # 状态标签组件
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── store/             # Vuex状态管理
│   │   ├── index.js       # Store入口
│   │   └── modules/       # 模块化Store
│   │       ├── auth.js    # 认证模块
│   │       ├── auction.js # 竞价模块
│   │       └── user.js    # 用户模块
│   ├── styles/            # 样式文件
│   │   ├── index.scss     # 全局样式
│   │   └── variables.scss # 样式变量
│   ├── utils/             # 工具函数
│   │   ├── auth.js        # 认证工具
│   │   ├── filters.js     # 过滤器
│   │   └── websocket.js   # WebSocket服务
│   ├── views/             # 页面组件
│   │   ├── Home.vue       # 竞价首页
│   │   ├── auth/          # 认证页面
│   │   │   ├── Login.vue  # 登录页面
│   │   │   └── Register.vue # 注册页面
│   │   ├── auction/       # 竞价页面
│   │   │   └── AuctionDetail.vue # 竞价详情
│   │   ├── profile/       # 个人中心
│   │   │   ├── Profile.vue # 个人资料
│   │   │   ├── MyBids.vue  # 我的出价
│   │   │   └── MyProjects.vue # 我的项目
│   │   └── error/         # 错误页面
│   │       └── 404.vue    # 404页面
│   ├── App.vue            # 根组件
│   └── main.js            # 应用入口
├── package.json           # 项目配置
├── vue.config.js          # Vue CLI配置
└── README.md              # 项目说明
```

## 安装和运行

### 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0

### 安装依赖
```bash
cd mj-member-web
npm install
```

### 开发环境运行
```bash
npm run serve
```
应用将在 http://localhost:8081 启动

### 生产环境构建
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 配置说明

### 环境变量
创建 `.env.local` 文件配置环境变量：

```env
# API基础URL
VUE_APP_API_BASE_URL=http://localhost:8888

# WebSocket URL
VUE_APP_WS_URL=ws://localhost:8888
```

### 代理配置
开发环境下，API请求会自动代理到后端服务器（vue.config.js中配置）

## 主要功能说明

### 1. 用户注册流程
1. 填写手机号，发送短信验证码
2. 填写个人信息和企业信息
3. 提交注册，等待管理员审核

### 2. 竞价流程
1. 登录后浏览竞价项目
2. 点击项目进入详情页面
3. 查看项目信息和出价历史
4. 输入出价金额并提交
5. 实时查看竞价状态更新

### 3. 实时功能
- 使用WebSocket实现实时价格更新
- 倒计时组件显示精确剩余时间
- 自动刷新竞价状态和出价记录

### 4. 响应式设计
- 支持桌面端、平板和移动端
- 使用CSS Grid和Flexbox布局
- 断点设计：768px（移动端）、992px（平板）、1200px（桌面端）

## API接口说明

### 认证接口
- `POST /auction/member/register` - 用户注册
- `POST /auction/member/login` - 用户登录
- `POST /auction/member/sms` - 发送短信验证码
- `GET /auction/member/info` - 获取用户信息
- `POST /auction/member/logout` - 用户登出

### 竞价接口
- `POST /auction/member/projects` - 获取竞价项目列表
- `POST /auction/member/projects/detail` - 获取竞价项目详情
- `POST /auction/member/bid` - 提交出价
- `POST /auction/member/participation` - 获取参与记录

### 用户接口
- `POST /auction/member/participation` - 获取我的出价记录
- `POST /auction/member/participation` - 获取我的项目
- `PUT /auction/member/profile` - 更新个人资料
- `GET /auction/member/stats` - 获取个人统计

### 商品分类接口
- `POST /admin/productCategory/getProductCategoryCondition` - 获取商品分类下拉选项

## 部署说明

### 1. 构建生产版本
```bash
npm run build
```

### 2. 部署到Web服务器
将 `dist` 目录下的文件部署到Web服务器（如Nginx、Apache等）

### 3. Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://backend-server:8888/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 开发指南

### 1. 添加新页面
1. 在 `src/views/` 下创建Vue组件
2. 在 `src/router/index.js` 中添加路由配置
3. 如需要认证，设置 `meta.requiresAuth: true`

### 2. 添加新API
1. 在对应的API文件中添加接口函数
2. 在Vuex模块中添加相应的actions
3. 在组件中调用store的actions

### 3. 样式规范
- 使用SCSS预处理器
- 遵循BEM命名规范
- 使用样式变量保持一致性
- 优先使用Element UI组件样式

### 4. 组件开发
- 组件名使用PascalCase
- Props使用camelCase
- 事件名使用kebab-case
- 添加适当的注释和文档

## 浏览器支持

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 许可证

本项目采用 MIT 许可证。
