{"remainingRequest": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\pywsp\\auction-sys\\mj-member-web\\src\\components\\AppHeader.vue?vue&type=template&id=bb50a5e4&scoped=true", "dependencies": [{"path": "E:\\pywsp\\auction-sys\\mj-member-web\\src\\components\\AppHeader.vue", "mtime": 1757555981677}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\babel.config.js", "mtime": 1757484828456}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757485142401}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1757485152609}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757485135871}, {"path": "E:\\pywsp\\auction-sys\\mj-member-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757485146588}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm$userInfo", "_vm", "_c", "_self", "$route", "meta", "<PERSON><PERSON>ead<PERSON>", "staticClass", "staticStyle", "height", "on", "click", "$event", "$router", "push", "_v", "isLoggedIn", "command", "handleCommand", "_s", "userInfo", "name", "attrs", "slot", "divided", "type", "size", "_e", "staticRenderFns", "_withStripped"], "sources": ["E:/pywsp/auction-sys/mj-member-web/src/components/AppHeader.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return !_vm.$route.meta.hideHeader\n    ? _c(\"header\", { staticClass: \"app-header\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"container\", staticStyle: { height: \"100%\" } },\n          [\n            _c(\"div\", { staticClass: \"header-content\" }, [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"logo\",\n                  on: {\n                    click: function ($event) {\n                      return _vm.$router.push(\"/\")\n                    },\n                  },\n                },\n                [\n                  _c(\"h2\", [\n                    _vm._v(\"新疆生产建设兵团第一师棉麻有限责任公司竞价平台\"),\n                  ]),\n                ]\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"user-info\" },\n                [\n                  _vm.isLoggedIn\n                    ? [\n                        _c(\n                          \"el-dropdown\",\n                          { on: { command: _vm.handleCommand } },\n                          [\n                            _c(\"span\", { staticClass: \"user-name\" }, [\n                              _vm._v(\n                                \" \" + _vm._s(_vm.userInfo?.name || \"用户\") + \" \"\n                              ),\n                              _c(\"i\", {\n                                staticClass:\n                                  \"el-icon-arrow-down el-icon--right\",\n                              }),\n                            ]),\n                            _c(\n                              \"el-dropdown-menu\",\n                              { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                              [\n                                _c(\n                                  \"el-dropdown-item\",\n                                  { attrs: { command: \"profile\" } },\n                                  [_vm._v(\"个人中心\")]\n                                ),\n                                _c(\n                                  \"el-dropdown-item\",\n                                  { attrs: { command: \"bids\" } },\n                                  [_vm._v(\"我的出价\")]\n                                ),\n                                _c(\n                                  \"el-dropdown-item\",\n                                  { attrs: { command: \"projects\" } },\n                                  [_vm._v(\"我的项目\")]\n                                ),\n                                _c(\n                                  \"el-dropdown-item\",\n                                  { attrs: { divided: \"\", command: \"logout\" } },\n                                  [_vm._v(\"退出登录\")]\n                                ),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    : [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.$router.push(\"/login\")\n                              },\n                            },\n                          },\n                          [_vm._v(\"登录\")]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"primary\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.$router.push(\"/register\")\n                              },\n                            },\n                          },\n                          [_vm._v(\"注册\")]\n                        ),\n                      ],\n                ],\n                2\n              ),\n            ]),\n          ]\n        ),\n      ])\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAAA,IAAAC,YAAA;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAO,CAACD,GAAG,CAACG,MAAM,CAACC,IAAI,CAACC,UAAU,GAC9BJ,EAAE,CAAC,QAAQ,EAAE;IAAEK,WAAW,EAAE;EAAa,CAAC,EAAE,CAC1CL,EAAE,CACA,KAAK,EACL;IAAEK,WAAW,EAAE,WAAW;IAAEC,WAAW,EAAE;MAAEC,MAAM,EAAE;IAAO;EAAE,CAAC,EAC7D,CACEP,EAAE,CAAC,KAAK,EAAE;IAAEK,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CL,EAAE,CACA,KAAK,EACL;IACEK,WAAW,EAAE,MAAM;IACnBG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOX,GAAG,CAACY,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;MAC9B;IACF;EACF,CAAC,EACD,CACEZ,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACc,EAAE,CAAC,yBAAyB,CAAC,CAClC,CAAC,CAEN,CAAC,EACDb,EAAE,CACA,KAAK,EACL;IAAEK,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEN,GAAG,CAACe,UAAU,GACV,CACEd,EAAE,CACA,aAAa,EACb;IAAEQ,EAAE,EAAE;MAAEO,OAAO,EAAEhB,GAAG,CAACiB;IAAc;EAAE,CAAC,EACtC,CACEhB,EAAE,CAAC,MAAM,EAAE;IAAEK,WAAW,EAAE;EAAY,CAAC,EAAE,CACvCN,GAAG,CAACc,EAAE,CACJ,GAAG,GAAGd,GAAG,CAACkB,EAAE,CAAC,EAAAnB,YAAA,GAAAC,GAAG,CAACmB,QAAQ,cAAApB,YAAA,uBAAZA,YAAA,CAAcqB,IAAI,KAAI,IAAI,CAAC,GAAG,GAC7C,CAAC,EACDnB,EAAE,CAAC,GAAG,EAAE;IACNK,WAAW,EACT;EACJ,CAAC,CAAC,CACH,CAAC,EACFL,EAAE,CACA,kBAAkB,EAClB;IAAEoB,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAW,CAAC;IAAEA,IAAI,EAAE;EAAW,CAAC,EACjD,CACErB,EAAE,CACA,kBAAkB,EAClB;IAAEoB,KAAK,EAAE;MAAEL,OAAO,EAAE;IAAU;EAAE,CAAC,EACjC,CAAChB,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDb,EAAE,CACA,kBAAkB,EAClB;IAAEoB,KAAK,EAAE;MAAEL,OAAO,EAAE;IAAO;EAAE,CAAC,EAC9B,CAAChB,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDb,EAAE,CACA,kBAAkB,EAClB;IAAEoB,KAAK,EAAE;MAAEL,OAAO,EAAE;IAAW;EAAE,CAAC,EAClC,CAAChB,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDb,EAAE,CACA,kBAAkB,EAClB;IAAEoB,KAAK,EAAE;MAAEE,OAAO,EAAE,EAAE;MAAEP,OAAO,EAAE;IAAS;EAAE,CAAC,EAC7C,CAAChB,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,GACD,CACEb,EAAE,CACA,WAAW,EACX;IACEoB,KAAK,EAAE;MAAEG,IAAI,EAAE;IAAO,CAAC;IACvBf,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOX,GAAG,CAACY,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;MACnC;IACF;EACF,CAAC,EACD,CAACb,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDb,EAAE,CACA,WAAW,EACX;IACEoB,KAAK,EAAE;MAAEG,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IACzChB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOX,GAAG,CAACY,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;MACtC;IACF;EACF,CAAC,EACD,CAACb,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,CACN,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC,CACF,CAAC,GACFd,GAAG,CAAC0B,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB7B,MAAM,CAAC8B,aAAa,GAAG,IAAI;AAE3B,SAAS9B,MAAM,EAAE6B,eAAe", "ignoreList": []}]}