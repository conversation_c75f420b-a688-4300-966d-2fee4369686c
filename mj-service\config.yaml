# auction-sys Global Configuration

# casbin configuration
casbin:
    model-path: './resource/rbac_model.conf'

# jwt configuration
jwt:
    signing-key: 'auctionSignKey'

# mysql connect configuration
mysql:
    username: root
    password: 'root'
    # password: '123456'
    path: '127.0.0.1:3306'
#    path: '192.168.0.210:3306'
    db-name: 'auction-sys'
    config: 'charset=utf8&parseTime=True&loc=Local'
    max-idle-conns: 10
    max-open-conns: 10
    log-mode: true
    parse-time: true
    time-zone: Asia/Shanghai

# oss configuration
qiniu:
    access-key: 'VD0FdHuwmAtR6F7x4QRTx0yyQA1EoyF4oYyyRViU'
    secret-key: 'lZ6f82rTntFi-CCPitlwE_AJdKAF4n6tDvNcpbkl'
    bucket: 'bogerj'
    img-path: 'http://res.bogerj.cn'

# redis configuration
redis:
    addr: '127.0.0.1:6379'
    password: ''
    db: 0

# system configuration
system:
    use-multipoint: true
    env: 'public'  # Change to "develop" to skip authentication for development mode
    addr: 8888
    rsaLogin: false
    db-type: "mysql"  # support mysql/sqlite

# captcha configuration
captcha:
    key-long: 6
    img-width: 240
    img-height: 80

# logger configuration
log:
    prefix: '[BUG_MONITOR]'
    log-file: true
    stdout: 'DEBUG'
    file: 'DEBUG'

logo:
    company: http://qiniu.mycf.top/company_logo.png
    user: http://qiniu.mycf.top/user_head.png

admin:
    username: admin

# 短信平台
sms:
    corp-id: XAJT00554
    pwd: 123456@

# 小程序
wechat:
    appid-mini: wxb44b48e9707eaad7
    appid: wx7cbcdf773b72af26
    app-secret: cfc68bbf2917873b36acf67c359081a8

