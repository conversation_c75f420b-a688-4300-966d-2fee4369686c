<!--
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-07-03 14:26:30
 * @LastEditors: dlg
 * @LastEditTime: 2020-07-07 11:04:30
-->
<template>
  <div class="role_panel__item clearfix">
    <div class="my__label">角色名称</div>
    <div style="margin-left:80px;">
      <el-input
        v-model="value"
        placeholder="角色名称"
        @blur="blurRole"
        @focus="focusRole"
        @clear="clearVal"
        clearable
      />
      <el-button type="text" @click="deleteItem">一</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "MyInput",
  props: {
    defaultValue: {
      type: Object,
      default: () => {
        return {
          value: ""
        };
      }
    }
  },
  data() {
    return {
      value: ""
    };
  },
  created() {
    this.value = this.defaultValue.value;
  },
  methods: {
    blurRole(e) {
      this.$emit("change", e);
    },
    focusRole() {
      this.$emit("focusChange");
    },
    clearVal() {
      this.$emit("clearVal");
    },
    deleteItem() {
      this.$emit("deleteItem");
    }
  }
};
</script>

<style scoped>
.role_panel__item {
  padding: 20px 0;
}
.my__label {
  width: 80px;
  text-align: left;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
</style>
<style>
.role_panel__item .el-input {
  width: 80%;
  margin-right: 10%;
}
</style>