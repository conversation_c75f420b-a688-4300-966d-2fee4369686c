package req

import (
	"github.com/Gre-Z/common/jtime"
)

// 会员注册请求
type MemberRegisterReq struct {
	Mobile           string `json:"mobile" binding:"required"`           // 手机号
	Password         string `json:"password" binding:"required"`         // 密码
	Name             string `json:"name" binding:"required"`             // 姓名
	CompanyName      string `json:"companyName" binding:"required"`      // 企业名称
	BusinessLicense  string `json:"businessLicense" binding:"required"`  // 营业执照图片
	CreditCode       string `json:"creditCode"`                          // 统一社会信用代码
	Email            string `json:"email"`                               // 邮箱
	ContactPerson    string `json:"contactPerson"`                       // 联系人
	ContactPhone     string `json:"contactPhone"`                        // 联系电话
	Address          string `json:"address"`                             // 企业地址
	Remark           string `json:"remark"`                              // 备注
	VerificationCode string `json:"verificationCode" binding:"required"` // 短信验证码
}

// 会员登录请求
type MemberLoginReq struct {
	Mobile   string `json:"mobile" binding:"required"`   // 手机号
	Password string `json:"password" binding:"required"` // 密码
}

// 会员审核请求
type MemberAuditReq struct {
	MemberID int    `json:"memberId" binding:"required"` // 会员ID
	Status   int8   `json:"status" binding:"required"`   // 审核状态(1:通过,2:拒绝)
	Remark   string `json:"remark"`                      // 审核备注
}

// 会员列表查询请求
type GetMemberListReq struct {
	PageInfo
	Mobile      string `json:"mobile"`      // 手机号
	Name        string `json:"name"`        // 姓名
	CompanyName string `json:"companyName"` // 企业名称
	AuditStatus int8   `json:"auditStatus"` // 审核状态
	Status      int8   `json:"status"`      // 状态
}

// 创建竞价项目请求
type CreateAuctionProjectReq struct {
	Title             string         `json:"title" binding:"required"`             // 项目标题
	ProductCategoryID int            `json:"productCategoryId" binding:"required"` // 商品中类ID
	Quantity          float64        `json:"quantity" binding:"required"`          // 商品数量
	StartPrice        float64        `json:"startPrice" binding:"required"`        // 起拍价
	MinIncrement      float64        `json:"minIncrement" binding:"required"`      // 最小加价幅度
	StartTime         jtime.JsonTime `json:"startTime" binding:"required"`         // 开始时间
	EndTime           jtime.JsonTime `json:"endTime" binding:"required"`           // 结束时间
	Owner             string         `json:"owner" binding:"required"`             // 货权人
	WarehouseAddress  string         `json:"warehouseAddress" binding:"required"`  // 仓库地址
	FeeDescription    string         `json:"feeDescription"`                       // 费用说明
	Remark            string         `json:"remark"`                               // 备注
	MemberIDs         []int          `json:"memberIds" binding:"required"`         // 授权参与的会员ID列表
}

// 更新竞价项目请求
type UpdateAuctionProjectReq struct {
	ID                int            `json:"id" binding:"required"`                // 项目ID
	Title             string         `json:"title" binding:"required"`             // 项目标题
	ProductCategoryID int            `json:"productCategoryId" binding:"required"` // 商品中类ID
	Quantity          float64        `json:"quantity" binding:"required"`          // 商品数量
	StartPrice        float64        `json:"startPrice" binding:"required"`        // 起拍价
	MinIncrement      float64        `json:"minIncrement" binding:"required"`      // 最小加价幅度
	StartTime         jtime.JsonTime `json:"startTime" binding:"required"`         // 开始时间
	EndTime           jtime.JsonTime `json:"endTime" binding:"required"`           // 结束时间
	Owner             string         `json:"owner" binding:"required"`             // 货权人
	WarehouseAddress  string         `json:"warehouseAddress" binding:"required"`  // 仓库地址
	FeeDescription    string         `json:"feeDescription"`                       // 费用说明
	Remark            string         `json:"remark"`                               // 备注
	MemberIDs         []int          `json:"memberIds" binding:"required"`         // 授权参与的会员ID列表
}

// 竞价项目列表查询请求
type GetAuctionProjectListReq struct {
	PageInfo
	Title    string `json:"title"`    // 项目标题
	Owner    string `json:"owner"`    // 货权人
	Status   int8   `json:"status"`   // 状态
	CreateBy int    `json:"createBy"` // 创建人
}

// 出价请求
type PlaceBidReq struct {
	ProjectID int     `json:"projectId" binding:"required"` // 项目ID
	BidAmount float64 `json:"bidAmount" binding:"required"` // 出价金额
}

// 出价记录查询请求
type GetBidListReq struct {
	PageInfo
	ProjectID int `json:"projectId" binding:"required"` // 项目ID
	MemberID  int `json:"memberId"`                     // 会员ID
}

// 终止竞价请求
type TerminateAuctionReq struct {
	ProjectID int    `json:"projectId" binding:"required"` // 项目ID
	Reason    string `json:"reason"`                       // 终止原因
}

// 竞价权限管理请求
type ManageAuctionPermissionReq struct {
	ProjectID int   `json:"projectId" binding:"required"` // 项目ID
	MemberIDs []int `json:"memberIds" binding:"required"` // 会员ID列表
	Action    int8  `json:"action" binding:"required"`    // 操作(1:授权,2:撤销)
}
