// @Title 微信相关
// @Description 请填写文件描述
// <AUTHOR> 2020/11/27 18:18

package req

import uuid "github.com/satori/go.uuid"

type WxLogin struct {
	Code string `json:"code"`
}

// 加密数据
type EncryptedData struct {
	EncryptedData string `json:"encryptedData"`
	Iv            string `json:"iv"`
	Type          int8   `json:"type"`
}

// 微信信息
type WxUserInfo struct {
	NickName  string `json:"nickName"`
	AvatarUrl string `json:"avatarUrl"`
	Gender    int    `json:"gender"`
	Country   string `json:"country"`
	Province  string `json:"province"`
	City      string `json:"city"`
	Language  string `json:"language"`
}

// 游客登录
type AppletTouristLogin struct {
	Code string `json:"code"`
}

// 登录
type AppletLogin struct {
	Mobile            string `json:"mobile"`        // 手机号
	Captcha           string `json:"captcha"`       // 验证码
	Category          int8   `json:"category"`      // 登录方式，0-授权登录，1-手机号登录
	EncryptedData     string `json:"encryptedData"` // 手机号解密
	Iv                string `json:"iv"`            // 手机号解密
	Token             string `json:"token"`         // 游客登录标识
	EncryptedUserInfo string `json:"encryptedUserInfo"`
	IvUser            string `json:"ivUser"`
}

// 登录
type AppletLoginMember struct {
	ID             int       `json:"id"`
	LoginStatus    int8      `json:"loginStatus"` // 0-游客登录，1-用户登录
	UUID           uuid.UUID `json:"uuid"`
	Username       string    `json:"userName"`       // 用户名
	NickName       string    `json:"nickName"`       // 真实姓名
	HeaderImg      string    `json:"headerImg"`      // 头像
	CompanyCode    string    `json:"companyCode"`    // 企业编码
	CompanyLogo    string    `json:"companyLogo"`    // 企业 logo
	CompanyName    string    `json:"companyName"`    // 企业名称
	PasswordStatus int8      `json:"passwordStatus"` // 密码状态
	IsRoot         int8      `json:"isRoot"`         // 是否超管
	IsFarmer       int       `json:"isFarmer"`       // 是否农户
	OpenID         string    `json:"-"`
	SessionKey     string    `json:"-"`
}

// 登录响应
type AppletLoginMemberResp struct {
	User      AppletLoginMember `json:"user"`      // 用户id
	Token     string            `json:"token"`     // token
	ExpiresAt int64             `json:"expiresAt"` // 过期时间戳
}
