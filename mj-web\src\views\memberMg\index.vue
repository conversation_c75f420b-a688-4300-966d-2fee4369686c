<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div style="margin-bottom: 20px;min-width:720px;">
      <span style="color:#606266;font-size:14px;">手机号/姓名：</span>
      <el-input
        v-model="searchForm.keyword"
        placeholder="请输入手机号或姓名"
        style="width: 200px;margin-right:32px;"
        clearable
      />
      <span style="color:#606266;font-size:14px;">企业名称：</span>
      <el-input
        v-model="searchForm.companyName"
        placeholder="请输入企业名称"
        style="width: 200px;margin-right:32px;"
        clearable
      />
      <span style="color:#606266;font-size:14px;">审核状态：</span>
      <el-select v-model="searchForm.auditStatus"
                 placeholder="请选择审核状态"
                 style="margin-right:12px;"
                 clearable>
        <el-option label="待审核" :value="0" />
        <el-option label="审核通过" :value="1" />
        <el-option label="审核拒绝" :value="2" />
      </el-select>
      <el-button @click="getTableData" type="primary">查询</el-button>
      <el-button @click="resetSearch" type="default">重置</el-button>
    </div>

    <!-- 操作按钮 -->
    <div class="button-box">
      <el-button type="success" @click="batchAudit(1)">批量通过</el-button>
      <el-button type="warning" @click="batchAudit(2)">批量拒绝</el-button>
      <el-button type="info" @click="batchEnable">批量启用</el-button>
      <el-button type="danger" @click="batchDisable">批量禁用</el-button>
    </div>

    <!-- 会员列表表格 -->
    <el-table :data="tableData" height="100%" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="序号" min-width="80">
        <template slot-scope="scope">{{ scope.$index + (page - 1) * limit + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="手机号" min-width="100" prop="mobile" />
      <el-table-column align="center" label="姓名" min-width="100" prop="name" />
      <el-table-column align="center" label="企业名称" min-width="150" prop="companyName" />
      <el-table-column align="center" label="信用代码" min-width="100" prop="creditCode" />
      <el-table-column align="center" label="注册时间" min-width="100" sortable prop="registerTime" />
      <el-table-column align="center" label="审核状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getAuditStatusType(scope.row.auditStatus)">
            {{ getAuditStatusText(scope.row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" min-width="150">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            :disabled="scope.row.auditStatus !== 1"
            active-text="启用"
            inactive-text="禁用"
            @change="switchStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="280">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="viewDetail(scope.row)">查看详情</el-button>
          <el-button 
            v-if="scope.row.auditStatus === 0" 
            type="success" 
            size="small" 
            @click="auditMember(scope.row, 1)">
            通过
          </el-button>
          <el-button 
            v-if="scope.row.auditStatus === 0" 
            type="warning" 
            size="small" 
            @click="auditMember(scope.row, 2)">
            拒绝
          </el-button>
          <el-button 
            v-if="scope.row.auditStatus === 2" 
            type="success" 
            size="small" 
            @click="auditMember(scope.row, 1)">
            重新审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="limit"
        :total="total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 会员详情弹窗 -->
    <el-dialog
      title="会员详情"
      :visible.sync="detailDialog"
      width="700px"
      :close-on-click-modal="false"
    >
      <div v-if="memberDetail" class="member-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>手机号：</label>
              <span>{{ memberDetail.mobile }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>姓名：</label>
              <span>{{ memberDetail.name }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>企业名称：</label>
              <span>{{ memberDetail.companyName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>营业执照号：</label>
              <span>{{ memberDetail.businessLicense }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>联系地址：</label>
              <span>{{ memberDetail.address }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>注册时间：</label>
              <span>{{ memberDetail.createdAt }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>审核状态：</label>
              <el-tag :type="getAuditStatusType(memberDetail.auditStatus)">
                {{ getAuditStatusText(memberDetail.auditStatus) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>账户状态：</label>
              <el-tag :type="memberDetail.status === 1 ? 'success' : 'danger'">
                {{ memberDetail.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="memberDetail.auditRemark">
          <el-col :span="24">
            <div class="detail-item">
              <label>审核备注：</label>
              <span>{{ memberDetail.auditRemark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 审核备注弹窗 -->
    <el-dialog
      title="审核备注"
      :visible.sync="auditDialog"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form :model="auditForm" label-width="80px">
        <el-form-item label="审核结果">
          <el-tag :type="auditForm.auditStatus === 1 ? 'success' : 'warning'">
            {{ auditForm.auditStatus === 1 ? '审核通过' : '审核拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="备注" v-if="auditForm.auditStatus === 2">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAudit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMemberList, auditMember, getMemberDetail, updateMemberStatus } from '@/api/auction'

export default {
  name: 'MemberManagement',
  data() {
    return {
      // 搜索表单
      searchForm: {
        keyword: '',
        companyName: '',
        auditStatus: ''
      },
      // 表格数据
      tableData: [],
      multipleSelection: [],
      // 分页
      page: 1,
      limit: 10,
      total: 0,
      // 弹窗控制
      detailDialog: false,
      auditDialog: false,
      // 会员详情
      memberDetail: null,
      // 审核表单
      auditForm: {
        memberId: '',
        auditStatus: 1,
        auditRemark: ''
      },
      currentAuditMember: null
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    // 获取表格数据
    async getTableData() {
      try {
        const params = {
          page: this.page,
          pageSize: this.limit,
          ...this.searchForm
        }
        const response = await getMemberList(params)
        if (response.data.code === 200) {
          this.tableData = response.data.data.list || []
          this.total = response.data.data.total || 0
        } else {
          this.$message.error(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取会员列表失败:', error)
        this.$message.error('获取数据失败')
      }
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        companyName: '',
        auditStatus: ''
      }
      this.page = 1
      this.getTableData()
    },

    // 查看详情
    async viewDetail(row) {
      try {
        const response = await getMemberDetail({ id: row.id })
        if (response.data.code === 200) {
          this.memberDetail = response.data.data
          this.detailDialog = true
        } else {
          this.$message.error(response.data.msg || '获取详情失败')
        }
      } catch (error) {
        console.error('获取会员详情失败:', error)
        this.$message.error('获取详情失败')
      }
    },

    // 审核会员
    auditMember(row, auditStatus) {
      this.currentAuditMember = row
      this.auditForm = {
        memberId: row.id,
        status: auditStatus,
      }
      
      if (auditStatus === 1) {
        // 直接通过
        this.confirmAudit()
      } else {
        // 需要填写拒绝原因
        this.auditDialog = true
      }
    },

    // 确认审核
    async confirmAudit() {
      try {
        const response = await auditMember(this.auditForm)
        if (response.data.code === 200) {
          this.$message.success('审核成功')
          this.auditDialog = false
          this.getTableData()
        } else {
          this.$message.error(response.data.msg || '审核失败')
        }
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error('审核失败')
      }
    },

    // 切换状态
    async switchStatus(row) {
      try {
        const response = await updateMemberStatus({
          memberId: row.id,
          status: row.status
        })
        if (response.data.code === 200) {
          this.$message.success('状态更新成功')
        } else {
          this.$message.error(response.data.msg || '状态更新失败')
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败')
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    },

    // 批量审核
    batchAudit(auditStatus) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要审核的会员')
        return
      }

      const statusText = auditStatus === 1 ? '通过' : '拒绝'
      this.$confirm(`确定要批量${statusText}选中的会员吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const memberIds = this.multipleSelection.map(item => item.id)
          const response = await auditMember({
            memberIds: memberIds,
            auditStatus: auditStatus,
            auditRemark: auditStatus === 2 ? '批量拒绝' : ''
          })
          if (response.data.code === 200) {
            this.$message.success(`批量${statusText}成功`)
            this.getTableData()
          } else {
            this.$message.error(response.data.msg || `批量${statusText}失败`)
          }
        } catch (error) {
          console.error(`批量${statusText}失败:`, error)
          this.$message.error(`批量${statusText}失败`)
        }
      })
    },

    // 批量启用
    batchEnable() {
      this.batchUpdateStatus(1, '启用')
    },

    // 批量禁用
    batchDisable() {
      this.batchUpdateStatus(0, '禁用')
    },

    // 批量更新状态
    batchUpdateStatus(status, statusText) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning(`请选择要${statusText}的会员`)
        return
      }

      this.$confirm(`确定要批量${statusText}选中的会员吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const memberIds = this.multipleSelection.map(item => item.id)
          const response = await updateMemberStatus({
            memberIds: memberIds,
            status: status
          })
          if (response.data.code === 200) {
            this.$message.success(`批量${statusText}成功`)
            this.getTableData()
          } else {
            this.$message.error(response.data.msg || `批量${statusText}失败`)
          }
        } catch (error) {
          console.error(`批量${statusText}失败:`, error)
          this.$message.error(`批量${statusText}失败`)
        }
      })
    },

    // 获取审核状态类型
    getAuditStatusType(status) {
      const typeMap = {
        0: 'warning',  // 待审核
        1: 'success',  // 审核通过
        2: 'danger'    // 审核拒绝
      }
      return typeMap[status] || 'info'
    },

    // 获取审核状态文本
    getAuditStatusText(status) {
      const textMap = {
        0: '待审核',
        1: '审核通过',
        2: '审核拒绝'
      }
      return textMap[status] || '未知'
    },

    // 表格选择变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.limit = val
      this.page = 1
      this.getTableData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.page = val
      this.getTableData()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.button-box {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.member-detail {
  padding: 20px;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: bold;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.detail-item span {
  color: #303133;
}
</style>
