/*
 * @Descripttion:
 * @version:
 * @Author: dlg
 * @Date: 2020-06-15 13:36:21
 * @LastEditors: dlg
 * @LastEditTime: 2020-08-25 10:54:04
 */
import { constantRouterMap } from '@/router';
import { asyncMenu } from '@/api/menus';
import Layout from '@/views/layout';
import { loadView } from '@/utils/_import'

const formatRouter = (routes, num = 0) => {
    let data = [];
    routes && routes.map(item => {
        let obj = {};
        if (!item.children) {
            obj.path = '/' + item.path;
            if (num === 1) {
                obj.component = loadView(item.component);
                obj.name = item.name;
                obj.meta = {
                        title: item.meta.title,
                        icon: item.meta.icon,
                        btns: item.meta.btns,
                        noCache: item.meta.noCache
                    },
                    obj.hidden = item.hidden
            } else {
                obj.component = Layout;
                obj.children = [{
                    path: 'index',
                    component: loadView(item.component),
                    name: item.name,
                    meta: {
                        title: item.meta.title,
                        icon: item.meta.icon,
                        btns: item.meta.btns,
                        noCache: item.meta.noCache
                    },
                    hidden: item.hidden
                }]
            }

        }
        if (item.children && item.children.length > 0) {
            obj.path = '/' + item.path;
            if (item.component == "Layout") {
                obj.component = Layout;
            } else {
                obj.component = loadView(item.component);
            }
            obj.name = item.name;
            obj.meta = {
                title: item.meta.title,
                icon: item.meta.icon,
                btns: item.meta.btns,
                noCache: item.meta.noCache
            };
            obj.hidden = item.hidden;
            obj.children = formatRouter(item.children, 1);
        }
        data.push(obj)
    });
    return data;
}

const permission = {
    state: {
        routers: constantRouterMap, //初始默认显示的路由(一般不需要权限的页面)
        addRouters: [] //缓存动态添加的路由
    },
    mutations: {
        SET_ROUTERS: (state, routers) => {
            state.addRouters = routers;
            state.routers = constantRouterMap.concat(routers); //拼接动态新添加的路由为最终的routers
        },
        RESET_ROUTERS: (state, routers) => {
            state.addRouters = routers;
        }
    },
    actions: {
        // 生成路由表
        async GenerateRoutes({ commit }) {
            const asyncRouterRes = await asyncMenu();
            const asyncRouter = asyncRouterRes.data.data.menus;
            let routersMap = formatRouter(asyncRouter);
            routersMap.push({
                path: '*',
                redirect: '/404',
                hidden: true
            });
            commit('SET_ROUTERS', routersMap);
            return routersMap;
        },
        // 重置动态路由缓存
        async resetRoutes({ commit }) {
            commit('RESET_ROUTERS', []);
        }

    }
}

export default permission;