<template>
  <div :class="classObj" class="app-wrapper">
    <nav-bar></nav-bar>
    <div class="main-container">
      <side-bar></side-bar>
      <tags-view></tags-view>
      <app-main></app-main>
    </div>
  </div>
</template>

<script>
import { NavBar, SideBar, AppMain, TagsView } from './components'
export default {
  name: 'Layout',
  components: {
    NavBar,
    SideBar, 
    AppMain, 
    TagsView
  },
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper{
  position: relative;
  height: 100%;
  width: 100%;
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}
</style>
