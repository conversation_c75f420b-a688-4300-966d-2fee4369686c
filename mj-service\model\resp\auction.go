package resp

import (
	"auction-sys/model"

	"github.com/Gre-Z/common/jtime"
	uuid "github.com/satori/go.uuid"
)

// 会员登录响应
type MemberLoginResp struct {
	Member    MemberInfo `json:"member"`    // 会员信息
	Token     string     `json:"token"`     // token
	ExpiresAt int64      `json:"expiresAt"` // 过期时间戳
}

// 会员信息
type MemberInfo struct {
	ID          int       `json:"id"`
	UUID        uuid.UUID `json:"uuid"`
	Mobile      string    `json:"mobile"`
	Name        string    `json:"name"`
	CompanyName string    `json:"companyName"`
	AuditStatus int8      `json:"auditStatus"`
	Status      int8      `json:"status"`
}

// 会员列表响应
type GetMemberListResp struct {
	List     []MemberListItem `json:"list"`     // 列表
	Total    int              `json:"total"`    // 总条数
	Page     int              `json:"page"`     // 当前页
	PageSize int              `json:"pageSize"` // 每页条数
}

// 会员列表项
type MemberListItem struct {
	ID            int            `json:"id"`
	Mobile        string         `json:"mobile"`
	Name          string         `json:"name"`
	CompanyName   string         `json:"companyName"`
	CreditCode    string         `json:"creditCode"`
	AuditStatus   int8           `json:"auditStatus"`
	Status        int8           `json:"status"`
	RegisterTime  jtime.JsonTime `json:"registerTime"`
	AuditTime     jtime.JsonTime `json:"auditTime"`
	AuditRemark   string         `json:"auditRemark"`
	LastLoginTime jtime.JsonTime `json:"lastLoginTime"`
	LoginCount    int            `json:"loginCount"`
}

// 竞价项目列表响应
type GetAuctionProjectListResp struct {
	List     []AuctionProjectListItem `json:"list"`     // 列表
	Total    int                      `json:"total"`    // 总条数
	Page     int                      `json:"page"`     // 当前页
	PageSize int                      `json:"pageSize"` // 每页条数
}

// 公开竞价项目列表响应（不包含价格信息）
type GetPublicAuctionProjectListResp struct {
	List     []PublicAuctionProjectListItem `json:"list"`     // 列表
	Total    int                            `json:"total"`    // 总条数
	Page     int                            `json:"page"`     // 当前页
	PageSize int                            `json:"pageSize"` // 每页条数
}

// 竞价项目列表项
type AuctionProjectListItem struct {
	ID               int            `json:"id"`
	Title            string         `json:"title"`
	ProductCategory  string         `json:"productCategory"` // 商品分类名称
	Quantity         float64        `json:"quantity"`
	Unit             string         `json:"unit"`
	StartPrice       float64        `json:"startPrice"`
	CurrentPrice     float64        `json:"currentPrice"`
	MinIncrement     float64        `json:"minIncrement"`
	StartTime        jtime.JsonTime `json:"startTime"`
	EndTime          jtime.JsonTime `json:"endTime"`
	Owner            string         `json:"owner"`
	WarehouseAddress string         `json:"warehouseAddress"`
	Status           int8           `json:"status"`
	BidCount         int            `json:"bidCount"`
	ViewCount        int            `json:"viewCount"`
	LastBidTime      jtime.JsonTime `json:"lastBidTime"`
	RemainingTime    int64          `json:"remainingTime"` // 剩余时间（秒）
	CanBid           bool           `json:"canBid"`        // 当前用户是否可以出价
}

// 公开竞价项目列表项（不包含价格信息）
type PublicAuctionProjectListItem struct {
	ID               int            `json:"id"`
	Title            string         `json:"title"`
	ProductCategory  string         `json:"productCategory"` // 商品分类名称
	Quantity         float64        `json:"quantity"`
	Unit             string         `json:"unit"`
	StartTime        jtime.JsonTime `json:"startTime"`
	EndTime          jtime.JsonTime `json:"endTime"`
	Owner            string         `json:"owner"`
	WarehouseAddress string         `json:"warehouseAddress"`
	Status           int8           `json:"status"`
	BidCount         int            `json:"bidCount"`
	ViewCount        int            `json:"viewCount"`
	LastBidTime      jtime.JsonTime `json:"lastBidTime"`
	RemainingTime    int64          `json:"remainingTime"` // 剩余时间（秒）
	Description      string         `json:"description"`   // 项目描述
}

// 竞价项目详情响应
type AuctionProjectDetailResp struct {
	model.AuctionProject
	ProductCategoryName string        `json:"productCategoryName"` // 商品分类名称
	RemainingTime       int64         `json:"remainingTime"`       // 剩余时间（秒）
	CanBid              bool          `json:"canBid"`              // 当前用户是否可以出价
	RecentBids          []BidListItem `json:"recentBids"`          // 最近出价记录
	BidHistory          []BidListItem `json:"bidHistory"`          // 出价历史
}

// 出价记录列表响应
type GetBidListResp struct {
	List     []BidListItem `json:"list"`     // 列表
	Total    int           `json:"total"`    // 总条数
	Page     int           `json:"page"`     // 当前页
	PageSize int           `json:"pageSize"` // 每页条数
}

// 出价记录列表项
type BidListItem struct {
	ID        int            `json:"id"`
	ProjectID int            `json:"projectId"`
	MemberID  int            `json:"memberId"`
	Member    string         `json:"member"` // 会员名称（脱敏）
	BidAmount float64        `json:"bidAmount"`
	BidTime   jtime.JsonTime `json:"bidTime"`
	IPAddress string         `json:"ipAddress"` // 脱敏IP
	Status    int8           `json:"status"`
	IsWinning bool           `json:"isWinning"`
	PrevPrice float64        `json:"prevPrice"`
	Increment float64        `json:"increment"`
}

// 我的参与响应
type MyParticipationResp struct {
	List     []MyParticipationItem `json:"list"`     // 列表
	Total    int                   `json:"total"`    // 总条数
	Page     int                   `json:"page"`     // 当前页
	PageSize int                   `json:"pageSize"` // 每页条数
}

// 我的参与项
type MyParticipationItem struct {
	ProjectID       int            `json:"projectId"`
	Title           string         `json:"title"`
	ProductCategory string         `json:"productCategory"`
	StartPrice      float64        `json:"startPrice"`
	CurrentPrice    float64        `json:"currentPrice"`
	FinalPrice      float64        `json:"finalPrice"`
	MyHighestBid    float64        `json:"myHighestBid"` // 我的最高出价
	MyBidCount      int            `json:"myBidCount"`   // 我的出价次数
	Status          int8           `json:"status"`
	IsWinner        bool           `json:"isWinner"` // 是否中标
	EndTime         jtime.JsonTime `json:"endTime"`
	LastBidTime     jtime.JsonTime `json:"lastBidTime"`
}

// 竞价统计响应
type AuctionStatsResp struct {
	TotalProjects     int     `json:"totalProjects"`     // 总项目数
	ActiveProjects    int     `json:"activeProjects"`    // 进行中项目数
	CompletedProjects int     `json:"completedProjects"` // 已完成项目数
	TotalMembers      int     `json:"totalMembers"`      // 总会员数
	ActiveMembers     int     `json:"activeMembers"`     // 活跃会员数
	TotalBids         int     `json:"totalBids"`         // 总出价次数
	TotalAmount       float64 `json:"totalAmount"`       // 总成交金额
}

// 出价结果响应
type PlaceBidResp struct {
	Success       bool           `json:"success"`       // 是否成功
	Message       string         `json:"message"`       // 消息
	CurrentPrice  float64        `json:"currentPrice"`  // 当前价格
	NewEndTime    jtime.JsonTime `json:"newEndTime"`    // 新的结束时间
	BidCount      int            `json:"bidCount"`      // 出价次数
	RemainingTime int64          `json:"remainingTime"` // 剩余时间
}

type MemberAuctionProjectListResp struct {
	ProjectID       int            `json:"projectId" gorm:"comment:'竞价项目ID'"`
	MemberID        int            `json:"memberId" gorm:"comment:'会员ID'"`
	GrantTime       jtime.JsonTime `json:"grantTime" gorm:"comment:'授权时间'"`
	GrantBy         int            `json:"grantBy" gorm:"comment:'授权人ID'"`
	Mobile          string         `json:"mobile" gorm:"comment:'手机号（登录名）'"`
	Password        string         `json:"-" gorm:"comment:'登录密码'"`
	Name            string         `json:"name" gorm:"comment:'姓名'"`
	CompanyName     string         `json:"companyName" gorm:"comment:'竞价企业名称'"`
	BusinessLicense string         `json:"businessLicense" gorm:"comment:'企业营业执照图片路径'"`
	CreditCode      string         `json:"creditCode" gorm:"comment:'统一社会信用代码'"`
	AuditStatus     int8           `json:"auditStatus" gorm:"default:0;comment:'审核状态(0:待审核,1:审核通过,2:审核拒绝)'"`
	AuditTime       jtime.JsonTime `json:"auditTime" gorm:"comment:'审核时间'"`
	AuditBy         int            `json:"auditBy" gorm:"comment:'审核人ID'"`
	AuditRemark     string         `json:"auditRemark" gorm:"comment:'审核备注'"`
	RegisterTime    jtime.JsonTime `json:"registerTime" gorm:"comment:'注册时间'"`
	LastLoginTime   jtime.JsonTime `json:"lastLoginTime" gorm:"comment:'最后登录时间'"`
	LoginCount      int            `json:"loginCount" gorm:"default:0;comment:'登录次数'"`
	Email           string         `json:"email" gorm:"comment:'邮箱'"`
	ContactPerson   string         `json:"contactPerson" gorm:"comment:'联系人'"`
	ContactPhone    string         `json:"contactPhone" gorm:"comment:'联系电话'"`
	Address         string         `json:"address" gorm:"comment:'企业地址'"`
	Remark          string         `json:"remark" gorm:"comment:'备注'"`
}
