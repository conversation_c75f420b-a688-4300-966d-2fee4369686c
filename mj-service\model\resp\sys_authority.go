package resp

import (
	"auction-sys/model"
)

// 角色对象返回封装
type SysAuthorityResponse struct {
	Authority model.SysAuthority `json:"authority"` // 角色对象
}

// 用于添加修改角色时，返回角色和菜单数据
type SysAuthorityMenuResponse struct {
	Role AuthorityResponse `json:"role"` // 角色和菜单数组
}

// 用于添加修改角色时，组装角色数据
type AuthorityResponse struct {
	RoleId      int         `json:"roleId"`      // 角色id
	RoleName    string      `json:"roleName"`    // 角色名称
	Description string      `json:"description"` // 角色描述
	Status      int8        `json:"status"`      // 角色状态 1：启用 0：禁用
	Trees       []MenuTrees `json:"trees"`       // 菜单
}

// 用于添加修改角色时，组装菜单数据
type MenuTrees struct {
	ID       int         `json:"id"`                      // 菜单
	Title    string      `json:"title"`                   // 菜单名称
	Status   int         `json:"status" gorm:"default:0"` // 菜单状态 1：禁用 0：启用
	ParentId int         `json:"parentId"`                // 父级菜单id
	Disabled bool        `json:"disabled"`                // 是否必选权限
	Children []MenuTrees `json:"children"`                // 子级菜单数组
}

// 角色绑定用户，返回结果
type BindingUsersResult struct {
	List     interface{} `json:"list"`     // 用户数组
	Total    int         `json:"total"`    // 总条数
	Page     int         `json:"page"`     // 当前页
	PageSize int         `json:"pageSize"` // 每页条数
	AuthId   int         `json:"authId"`   // 角色id
	AuthName string      `json:"authName"` // 角色名称
}

// 角色绑定用户，用户数据
type BindingUsers struct {
	ID       uint   `json:"id"`       // 用户id
	Username string `json:"username"` // 用户名
	NickName string `json:"nickname"` // 真实姓名
	Status   int8   `json:"status"`   // 用户状态 1：启用 0：禁用
}

// 获取变更角色用户列表
type GetAuthBindingUsers struct {
	Electeds       []BindingUsers `json:"electeds"`       // 待选用户对象
	Authorizations []BindingUsers `json:"authorizations"` // 授权用户对象
}

// 自己公司的角色数据
type AuthCondition struct {
	ID            int    `json:"id"`            // 角色id
	AuthorityName string `json:"authorityName"` // 角色名称
}

// 角色列表
type GetAuthorityList struct {
	List       interface{} `json:"list"`       // 列表数据
	Total      int         `json:"total"`      // 总条数
	Page       int         `json:"page"`       // 当前页
	PageSize   int         `json:"pageSize"`   // 每页条数
	Name       string      `json:"name"`       // 角色名
	EnableNum  int         `json:"enableNum"`  // 启用数量
	DisableNum int         `json:"disableNum"` // 禁用数量
}
